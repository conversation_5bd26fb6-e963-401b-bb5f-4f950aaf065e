'use client';

import React from 'react';
import BuildTest from '@/components/sysml/editor/BuildTest';

const BuildTestPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Build Compatibility Test
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Testing module imports and Next.js build compatibility
              </p>
            </div>
            <div className="flex space-x-3">
              <a
                href="/sysml/diagnostics"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Diagnostics
              </a>
              <a
                href="/sysml/editor-test"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                Editor Test
              </a>
              <a
                href="/sysml"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                Back to Workspace
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="py-6">
        <BuildTest />
      </main>

      {/* Footer with build information */}
      <footer className="bg-white border-t mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="text-center text-sm text-gray-600">
            <p>
              This test verifies that all required modules can be imported correctly in the Next.js environment.
            </p>
            <p className="mt-1">
              If all tests pass, the SysML v2 Langium editor should build successfully without vscode module errors.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BuildTestPage;
