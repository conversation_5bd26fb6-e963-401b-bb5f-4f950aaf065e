//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package test{
	alias A for A1;
	classifier A1{
		classifier A{
			//XPECT linkedName at A --> test.A1.A
			//* XPECT scope at A ---
			A.B.B, A1.A.B.B, B.B, test.A.A.B.B, test.A1.A.B.B,
		    A, A.B, A1, A1.A, A1.A.B, B, 
		    test.A, test.A.A, test.A.A.B, test.A1, test.A1.A, test.A1.A.B,
		--- */
			classifier B specializes A{}
		}
	}
}
