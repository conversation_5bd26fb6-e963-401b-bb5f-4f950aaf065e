// Enhanced Langium Web Worker for SysML v2 Language Server
// This file now delegates to the main worker implementation

// 启动语言服务器
let languageServerStarted = false;

// SysML v2 关键字和语法规则
const SYSML_KEYWORDS = [
  'package', 'part', 'def', 'attribute', 'connection', 'interface', 'port',
  'action', 'state', 'requirement', 'constraint', 'case', 'view', 'viewpoint',
  'analysis', 'verification', 'use', 'concern', 'item', 'allocation', 'flow',
  'binding', 'succession', 'metadata', 'abstract', 'individual', 'variation',
  'snapshot', 'timeslice', 'ordered', 'nonunique', 'derived', 'readonly',
  'composite', 'end', 'in', 'out', 'inout', 'ref', 'redefines', 'subsets',
  'specializes', 'conjugates', 'references', 'typed', 'by', 'featured',
  'chains', 'inverse', 'of', 'disjoint', 'from', 'import', 'private',
  'protected', 'public', 'all', 'filter', 'alias', 'for', 'about', 'doc',
  'comment', 'language', 'standard', 'library', 'then', 'if', 'else',
  'while', 'do', 'entry', 'exit', 'when', 'at', 'after', 'trigger',
  'guard', 'effect', 'assume', 'require', 'verify', 'satisfy', 'expose',
  'render', 'as', 'hastype', 'istype', 'meta', 'null', 'true', 'false',
  'and', 'or', 'xor', 'not', 'implies'
];

// 简化的语法验证
function validateSysMLCode(code: string) {
  const lines = code.split('\n');
  const diagnostics = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    const lineNumber = i + 1;

    // 检查基本语法错误
    if (line.includes('syntax error')) {
      diagnostics.push({
        severity: 1, // Error
        message: 'Syntax error detected',
        range: {
          start: { line: i, character: 0 },
          end: { line: i, character: line.length }
        }
      });
    }

    // 检查未闭合的大括号
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    if (openBraces > closeBraces && !line.includes('//')) {
      // 这是一个简化的检查，实际应该跨行检查
    }

    // 检查缺少分号的语句
    if (line.includes('attribute') && !line.includes(';') && !line.includes('{') && line.length > 0) {
      diagnostics.push({
        severity: 2, // Warning
        message: 'Missing semicolon after attribute declaration',
        range: {
          start: { line: i, character: line.length },
          end: { line: i, character: line.length }
        }
      });
    }

    // 检查未知关键字（简化版）
    const words = line.split(/\s+/);
    for (const word of words) {
      const cleanWord = word.replace(/[^\w]/g, '');
      if (cleanWord.length > 0 &&
          cleanWord.toLowerCase() !== cleanWord &&
          !SYSML_KEYWORDS.includes(cleanWord) &&
          !/^[A-Z][a-zA-Z0-9]*$/.test(cleanWord)) {
        // 可能是用户定义的类型，不报错
      }
    }
  }

  return diagnostics;
}

// 生成代码补全建议
function generateCompletions(code: string, position: { line: number; character: number }) {
  const completions = [];

  // 基本关键字补全
  for (const keyword of SYSML_KEYWORDS) {
    completions.push({
      label: keyword,
      kind: 14, // Keyword
      detail: `SysML v2 keyword`,
      insertText: keyword
    });
  }

  // 常用代码片段
  const snippets = [
    {
      label: 'package',
      kind: 15, // Snippet
      detail: 'Package definition',
      insertText: 'package ${1:PackageName} {\n\t$0\n}',
      insertTextRules: 4 // InsertAsSnippet
    },
    {
      label: 'part def',
      kind: 15,
      detail: 'Part definition',
      insertText: 'part def ${1:PartName} {\n\t$0\n}',
      insertTextRules: 4
    },
    {
      label: 'attribute',
      kind: 15,
      detail: 'Attribute declaration',
      insertText: 'attribute ${1:name} : ${2:Type};',
      insertTextRules: 4
    },
    {
      label: 'connection',
      kind: 15,
      detail: 'Connection definition',
      insertText: 'connection ${1:name} connect ${2:source} to ${3:target};',
      insertTextRules: 4
    }
  ];

  completions.push(...snippets);

  return completions;
}

// 启动完整的 Langium 语言服务器
function startLanguageServer() {
  if (!languageServerStarted) {
    try {
      startSysMLLanguageServer();
      languageServerStarted = true;
      console.log('SysML Langium Language Server started');

      self.postMessage({
        type: 'server-started',
        data: { success: true }
      });
    } catch (error) {
      console.error('Failed to start language server:', error);
      self.postMessage({
        type: 'server-error',
        data: { error: error.message }
      });
    }
  }
}

// 监听消息
self.addEventListener('message', (event) => {
  const { type, data } = event.data;

  switch (type) {
    case 'start':
      startLanguageServer();
      break;
    case 'validate':
      // 处理代码验证请求
      handleValidation(data);
      break;
    case 'complete':
      // 处理代码补全请求
      handleCompletion(data);
      break;
    default:
      console.warn('Unknown message type:', type);
  }
});

// 处理代码验证
async function handleValidation(data: { code: string; uri: string }) {
  try {
    const diagnostics = validateSysMLCode(data.code);

    // 发送结果
    self.postMessage({
      type: 'validation-result',
      data: {
        uri: data.uri,
        diagnostics: diagnostics.map(d => ({
          severity: d.severity,
          message: d.message,
          range: d.range,
          source: 'SysML'
        }))
      }
    });
  } catch (error) {
    console.error('Validation error:', error);
    self.postMessage({
      type: 'validation-error',
      data: { uri: data.uri, error: error.message }
    });
  }
}

// 处理代码补全
async function handleCompletion(data: { code: string; uri: string; position: { line: number; character: number } }) {
  try {
    const completions = generateCompletions(data.code, data.position);

    // 发送结果
    self.postMessage({
      type: 'completion-result',
      data: {
        uri: data.uri,
        items: completions
      }
    });
  } catch (error) {
    console.error('Completion error:', error);
    self.postMessage({
      type: 'completion-error',
      data: { uri: data.uri, error: error.message }
    });
  }
}

// 错误处理
self.addEventListener('error', (event) => {
  console.error('Worker error:', event.error);
  self.postMessage({
    type: 'worker-error',
    data: { error: event.error.message }
  });
});

// 未处理的 Promise 拒绝
self.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection in worker:', event.reason);
  self.postMessage({
    type: 'worker-error',
    data: { error: event.reason }
  });
});

export {};
