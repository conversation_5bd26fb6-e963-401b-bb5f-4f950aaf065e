//* 
XPECT_SETUP org.omg.kerml.xpect.tests.scoping.KerMLScopingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package Test {

	package Test1 {
		public import VP::VP1::*;
		//* XPECT scope at c_Public_Id ---
		A, A.c_public, Test1.A, Test1.A.c_public, Test.Test1.A, Test.Test1.A.c_public, 
		c_Public, Test1.c_Public, Test.Test1.c_Public, VP.VP1.c_Public, Test.VP.VP1.c_Public,  
		c_Public.c_public, Test1.c_Public.c_public, Test.Test1.c_Public.c_public, VP.VP1.c_Public.c_public, Test.VP.VP1.c_Public.c_public,
		c_Public_Id, Test1.c_Public_Id, Test.Test1.c_Public_Id, VP.VP1.c_Public_Id, Test.VP.VP1.c_Public_Id,
		c_Public_Id.c_public, Test1.c_Public_Id.c_public, Test.Test1.c_Public_Id.c_public, VP.VP1.c_Public_Id.c_public, Test.VP.VP1.c_Public_Id.c_public,
		VP.VP2.A, Test.VP.VP2.A, VP.VP2.A_Id, Test.VP.VP2.A_Id
		--- */
		classifier A specializes c_Public_Id;
	}

	package VP {
			
		package VP1 {	
			public classifier <'c_Public_Id'> c_Public {
				private classifier c_private{}
				public classifier c_public{}
				protected classifier c_protected{}
			}
		}
		
		package VP2 {
			public classifier <'A_Id'> A;
		}
	}
	
}

	