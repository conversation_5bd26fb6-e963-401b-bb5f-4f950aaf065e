'use client';

import React from 'react';
import MonacoEditorTest from '@/components/workspace/MonacoEditorTest';

export default function TestEditorPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Monaco Editor 测试页面</h1>
        
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            @monaco-editor/react 集成测试
          </h2>
          <p className="text-gray-600 mb-6">
            这个页面用于测试 Monaco Editor 是否正确集成到 Next.js 项目中。
          </p>
          
          <MonacoEditorTest />
          
          <div className="mt-6 p-4 bg-gray-50 rounded">
            <h3 className="font-medium text-gray-800 mb-2">测试说明：</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 如果编辑器正常加载，您应该看到一个带有语法高亮的代码编辑器</li>
              <li>• 编辑器应该支持代码补全、语法高亮等功能</li>
              <li>• 您可以在编辑器中输入代码来测试功能</li>
              <li>• 打开浏览器开发者工具查看控制台输出</li>
            </ul>
          </div>

          <div className="mt-6 p-4 bg-blue-50 rounded">
            <h3 className="font-medium text-blue-800 mb-2">🤖 AI 功能测试：</h3>
            <p className="text-sm text-blue-700 mb-3">
              要测试完整的 CodeEditor 组件（包括 AI 提示词功能），请访问工作区页面。
            </p>
            <div className="text-sm text-blue-600 space-y-1">
              <p><strong>AI 提示词示例：</strong></p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>"创建一个车辆系统模型"</li>
                <li>"添加一个机器人手臂的定义"</li>
                <li>"为当前系统添加性能需求"</li>
                <li>"修改引擎功率限制为600马力"</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
