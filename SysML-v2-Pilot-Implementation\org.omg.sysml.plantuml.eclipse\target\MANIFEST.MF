Manifest-Version: 1.0
Automatic-Module-Name: org.omg.sysml.plantuml
Bundle-ManifestVersion: 2
Bundle-Name: SysML 2 PlantUML visualization for Eclipse
Bundle-SymbolicName: org.omg.sysml.plantuml.eclipse;singleton:=true
Bundle-Version: 0.51.0.202509041722
Import-Package: net.sourceforge.plantuml.eclipse.utils;version="1.1.25.h
 imi1",net.sourceforge.plantuml.ecore,net.sourceforge.plantuml.text,org.
 eclipse.core.resources,org.eclipse.core.runtime;version="3.5.0",org.ecl
 ipse.emf.common.notify,org.eclipse.emf.common.notify.impl,org.eclipse.e
 mf.common.util,org.eclipse.emf.ecore,org.eclipse.emf.ecore.resource,org
 .eclipse.emf.ecore.resource.impl,org.eclipse.emf.ecore.util,org.eclipse
 .emf.edit.domain,org.eclipse.jface.text,org.eclipse.jface.viewers,org.e
 clipse.ui,org.eclipse.ui.editors.text,org.eclipse.ui.ide,org.eclipse.ui
 .texteditor,org.eclipse.xtext.nodemodel,org.eclipse.xtext.nodemodel.uti
 l,org.eclipse.xtext.resource,org.eclipse.xtext.ui.editor,org.eclipse.xt
 ext.ui.editor.model,org.eclipse.xtext.util.concurrent,org.omg.sysml.lan
 g.sysml,org.omg.sysml.plantuml,org.osgi.framework;version="1.9.0"
Bundle-Activator: org.omg.sysml.plantuml.eclipse.Activator
Bundle-ActivationPolicy: lazy
Bundle-Vendor: SysML v2 Submission Team
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

