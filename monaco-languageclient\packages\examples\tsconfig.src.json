{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "rootDir": "src",
    "outDir": "dist",
    "declarationDir": "dist",
    // because vscode-jsonrpc requires DedicatedWorkerGlobalScope
    // we are required to include both DOM and WebWorker libs
    // the only way out currently is to disable lib checking
    "skipLibCheck": true
  },
  "references": [{
    "path": "../client/tsconfig.src.json",
  },
  {
    "path": "../vscode-ws-jsonrpc/tsconfig.src.json"
  },
  {
    "path": "../wrapper/tsconfig.src.json"
  },
  {
    "path": "../wrapper-react/tsconfig.src.json"
  }],
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.json",
    "src/**/*.langium"
  ]
}
