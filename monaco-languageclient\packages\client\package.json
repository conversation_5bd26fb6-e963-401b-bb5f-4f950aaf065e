{"name": "monaco-languageclient", "version": "9.8.0", "description": "Monaco Language client implementation", "author": {"name": "TypeFox GmbH", "url": "http://www.typefox.io"}, "homepage": "https://github.com/TypeFox/monaco-languageclient/blob/main/packages/client/README.md", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TypeFox/monaco-languageclient.git", "directory": "packages/client"}, "bugs": {"url": "https://github.com/TypeFox/monaco-languageclient/issues"}, "type": "module", "main": "./lib/index.js", "module": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./tools": {"types": "./lib/tools/index.d.ts", "default": "./lib/tools/index.js"}, "./vscode/services": {"types": "./lib/vscode/index.d.ts", "default": "./lib/vscode/index.js"}, "./fs": {"types": "./lib/fs/index.d.ts", "default": "./lib/fs/index.js"}, "./workerFactory": {"types": "./lib/workerFactory.d.ts", "default": "./lib/workerFactory.js"}, "./wrapper": {"types": "./lib/languageClientWrapper.test.d.ts", "default": "./lib/languageClientWrapper.test.js"}}, "typesVersions": {"*": {".": ["lib/index.d.ts"], "tools": ["lib/tools/index"], "vscode/services": ["lib/vscode/index"], "fs": ["lib/fs/index"], "workerFactory": ["lib/workerFactory"], "wrapper": ["lib/languageClientWrapper"]}}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "files": ["lib", "src", "README.md", "CHANGELOG.md", "LICENSE"], "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-configuration-service-override": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extensions-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-languages-service-override": "~18.1.0", "@codingame/monaco-vscode-localization-service-override": "~18.1.0", "@codingame/monaco-vscode-log-service-override": "~18.1.0", "@codingame/monaco-vscode-model-service-override": "~18.1.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1"}, "scripts": {"clean": "shx rm -fr ./lib *.tsbuildinfo", "compile": "tsc --build tsconfig.src.json", "build:msg": "echo Building monaco-languageclient:", "build": "npm run build:msg && npm run clean && npm run compile"}}