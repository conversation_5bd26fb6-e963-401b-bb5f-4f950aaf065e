{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [{"open": "{", "close": "}"}, {"open": "[", "close": "]"}, {"open": "(", "close": ")"}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}, {"open": "\"", "close": "\"", "notIn": ["string"]}, {"open": "/**", "close": " */", "notIn": ["string"]}], "autoCloseBefore": "}])`\n\t", "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"]], "colorizedBracketPairs": [["(", ")"], ["[", "]"], ["{", "}"], ["<", ">"], ["'", "'"], ["\"", "\""], ["<", ">"]], "onEnterRules": [{"": "// e.g. /** | */", "beforeText": {"pattern": "^\\s*/\\*\\*(?!/)([^\\*]|\\*(?!/))*$"}, "afterText": {"pattern": "^\\s*\\*/$"}, "action": {"indent": "indentOutdent", "appendText": " * "}}, {"": "// e.g. /** ...|", "beforeText": {"pattern": "^\\s*/\\*\\*(?!/)([^\\*]|\\*(?!/))*$"}, "action": {"indent": "none", "appendText": " * "}}, {"": "// e.g.  * ...|", "beforeText": {"pattern": "^(\\t|[ ])*[ ]\\*([ ]([^\\*]|\\*(?!/))*)?$"}, "previousLineText": {"pattern": "(?=^(\\s*(/\\*\\*|\\*)).*)(?=(?!(\\s*\\*/)))"}, "action": {"indent": "none", "appendText": "* "}}, {"": "// e.g.  */|", "beforeText": {"pattern": "^(\\t|[ ])*[ ]\\*/\\s*$"}, "action": {"indent": "none", "removeText": 1}}, {"beforeText": ":\\s*$", "action": {"indent": "indent"}}, {"beforeText": ";\\s*$", "action": {"indent": "outdent"}}]}