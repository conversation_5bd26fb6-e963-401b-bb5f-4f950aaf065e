Manifest-Version: 1.0
Bundle-ManifestVersion: 2
Bundle-Name: %pluginName
Bundle-Vendor: %providerName
Bundle-Localization: plugin
Bundle-Version: 0.51.0.202509041722
Bundle-ClassPath: .,lib/sysml-v2-api-client-all.jar
Bundle-SymbolicName: org.omg.sysml;singleton:=true
Bundle-ActivationPolicy: lazy
Require-Bundle: org.eclipse.xtext,org.eclipse.xtext.xbase,org.eclipse.xt
 ext.xbase.lib;bundle-version="2.14.0",org.eclipse.xtext.util,org.eclips
 e.xtend.lib;bundle-version="2.14.0",org.antlr.runtime;bundle-version="[
 3.2.0,3.2.1)",org.eclipse.core.runtime,org.eclipse.emf.ecore;visibility
 :=reexport,org.eclipse.emf.ecore.xmi;visibility:=reexport,org.eclipse.u
 ml2.common;visibility:=reexport,org.eclipse.ocl.ecore;visibility:=reexp
 ort,org.eclipse.emf.mwe2.launch;bundle-version="2.9.1",org.eclipse.xtex
 t.xtext.generator;bundle-version="2.12.0",org.eclipse.m2m.qvt.oml,org.e
 clipse.uml2.uml.resources,org.eclipse.equinox.common;bundle-version="3.
 5.0"
Export-Package: org.omg.sysml,org.omg.sysml.adapter,org.omg.sysml.api,or
 g.omg.sysml.delegate.invocation,org.omg.sysml.delegate.setting,org.omg.
 sysml.expressions,org.omg.sysml.expressions.util,org.omg.sysml.lang.sys
 ml,org.omg.sysml.lang.sysml.impl,org.omg.sysml.lang.sysml.util,org.omg.
 sysml.lang.types,org.omg.sysml.lang.types.impl,org.omg.sysml.model,org.
 omg.sysml.qvt,org.omg.sysml.util,org.omg.sysml.util.repository,org.omg.
 sysml.util.traversal,org.omg.sysml.util.traversal.facade,org.omg.sysml.
 util.traversal.facade.impl
Import-Package: org.apache.commons.logging,org.apache.log4j
Automatic-Module-Name: org.omg.sysml
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

