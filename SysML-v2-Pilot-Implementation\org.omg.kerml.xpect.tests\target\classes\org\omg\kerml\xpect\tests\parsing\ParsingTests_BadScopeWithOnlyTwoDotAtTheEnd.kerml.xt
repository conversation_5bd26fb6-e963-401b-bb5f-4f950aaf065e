//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	classifier non{}
	classifier A{
		// XPECT errors ---> "Couldn't resolve reference to Feature 'non'." at "non"
		feature aa subsets non;
		feature a: A;
	}
	classifier B{
		//* XPECT errors ---
			"Couldn't resolve reference to Type 'Test3::A'." at "Test3::A"
			"Couldn't resolve reference to Type 'a'." at "a"
		--- */
		feature b: Test3::A:a;
	}
}
