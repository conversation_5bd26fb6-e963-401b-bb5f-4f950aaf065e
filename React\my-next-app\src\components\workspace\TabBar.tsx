'use client';

import React from 'react';
import { useApp } from '@/contexts/AppContext';
import { EditorTab } from '@/types';

const TabBar: React.FC = () => {
  const { state, dispatch } = useApp();
  const { tabs, activeTabId } = state.editor;

  // 切换标签页
  const handleTabClick = (tabId: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tabId });
  };

  // 关闭标签页
  const handleTabClose = (tabId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    const tab = tabs.find(t => t.id === tabId);
    if (tab?.isDirty) {
      // 如果有未保存的更改，应该显示确认对话框
      const confirmed = window.confirm('文件有未保存的更改，确定要关闭吗？');
      if (!confirmed) {
        return;
      }
    }
    
    dispatch({ type: 'REMOVE_EDITOR_TAB', payload: tabId });
  };

  // 获取文件图标
  const getFileIcon = (tab: EditorTab) => {
    // 根据文件类型返回不同的图标
    return '📄';
  };

  if (tabs.length === 0) {
    return null;
  }

  return (
    <div className="tab-bar">
      {tabs.map(tab => (
        <div
          key={tab.id}
          className={`tab ${activeTabId === tab.id ? 'active' : ''}`}
          onClick={() => handleTabClick(tab.id)}
          title={tab.title}
        >
          <span className="tab-icon">{getFileIcon(tab)}</span>
          <span className="tab-title">{tab.title}</span>
          {tab.isDirty && (
            <span className="tab-dirty-indicator text-orange-500 ml-1">●</span>
          )}
          <button
            className="tab-close ml-2"
            onClick={(e) => handleTabClose(tab.id, e)}
            title="关闭"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      ))}
    </div>
  );
};

export default TabBar;
