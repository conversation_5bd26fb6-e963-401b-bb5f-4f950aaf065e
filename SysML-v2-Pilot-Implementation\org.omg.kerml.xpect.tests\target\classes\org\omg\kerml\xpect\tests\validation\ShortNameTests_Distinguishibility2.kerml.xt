//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

package Test {
	 //name and id are the same
	 //XPECT warnings --> "Duplicate of other owned member name" at "two"
	 classifier <one> two;
	  //XPECT warnings --> "Duplicate of other owned member name" at "two"
	 classifier <two> three;
}
