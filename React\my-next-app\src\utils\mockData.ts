import { Project, User, SysMLDiagram, Namespace } from '@/types';

// 模拟用户数据
export const mockUsers: User[] = [
  {
    id: 'user-1',
    username: 'admin',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'user-2',
    username: 'developer',
    email: '<EMAIL>',
    createdAt: new Date('2024-01-02'),
  },
];

// 模拟图数据
export const mockDiagrams: SysMLDiagram[] = [
  {
    id: 'diagram-1',
    name: '系统架构图',
    namespaceId: 'namespace-1',
    content: `package SystemArchitecture {
  part def Vehicle {
    part engine : Engine;
    part transmission : Transmission;
    part wheels : Wheel[4];
  }
  
  part def Engine {
    attribute power : Real;
    attribute fuelType : String;
  }
  
  part def Transmission {
    attribute type : String;
    attribute gears : Integer;
  }
  
  part def Wheel {
    attribute diameter : Real;
    attribute material : String;
  }
}`,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 'diagram-2',
    name: '需求模型',
    namespaceId: 'namespace-1',
    content: `package Requirements {
  requirement def VehicleRequirement {
    doc /* 车辆基本需求定义 */
    
    requirement maxSpeed {
      doc /* 最大速度要求 */
      attribute value : Real = 120.0;
      attribute unit : String = "km/h";
    }
    
    requirement fuelEfficiency {
      doc /* 燃油效率要求 */
      attribute value : Real = 6.0;
      attribute unit : String = "L/100km";
    }
  }
}`,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
  {
    id: 'diagram-3',
    name: '状态机图',
    namespaceId: 'namespace-2',
    content: `package StateMachine {
  state def EngineState {
    entry / startEngine();
    exit / stopEngine();
    
    state off;
    state starting;
    state running;
    state stopping;
    
    transition off to starting
      when startSignal;
    
    transition starting to running
      when engineStarted;
    
    transition running to stopping
      when stopSignal;
    
    transition stopping to off
      when engineStopped;
  }
}`,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  },
];

// 模拟命名空间数据
export const mockNamespaces: Namespace[] = [
  {
    id: 'namespace-1',
    name: 'VehicleSystem',
    projectId: 'project-1',
    createdAt: new Date('2024-01-01'),
    diagrams: mockDiagrams.filter(d => d.namespaceId === 'namespace-1'),
  },
  {
    id: 'namespace-2',
    name: 'ControlSystem',
    projectId: 'project-1',
    createdAt: new Date('2024-01-01'),
    diagrams: mockDiagrams.filter(d => d.namespaceId === 'namespace-2'),
  },
  {
    id: 'namespace-3',
    name: 'SafetySystem',
    projectId: 'project-2',
    createdAt: new Date('2024-01-02'),
    diagrams: [],
  },
];

// 模拟项目数据
export const mockProjects: Project[] = [
  {
    id: 'project-1',
    name: '智能汽车系统',
    description: '基于 SysML v2 的智能汽车系统建模项目',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-03'),
    ownerId: 'user-1',
    namespaces: mockNamespaces.filter(ns => ns.projectId === 'project-1'),
  },
  {
    id: 'project-2',
    name: '航空航天系统',
    description: '航空航天系统的 SysML v2 建模',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
    ownerId: 'user-1',
    namespaces: mockNamespaces.filter(ns => ns.projectId === 'project-2'),
  },
];

// 模拟 SVG 内容
export const mockSVGContent = `
<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  
  <!-- Vehicle -->
  <rect x="50" y="50" width="200" height="100" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="5"/>
  <text x="150" y="80" text-anchor="middle" font-family="Arial" font-size="14" font-weight="bold">Vehicle</text>
  <text x="150" y="100" text-anchor="middle" font-family="Arial" font-size="12">part def</text>
  
  <!-- Engine -->
  <rect x="300" y="30" width="150" height="80" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="5"/>
  <text x="375" y="55" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Engine</text>
  <text x="375" y="75" text-anchor="middle" font-family="Arial" font-size="10">power: Real</text>
  <text x="375" y="90" text-anchor="middle" font-family="Arial" font-size="10">fuelType: String</text>
  
  <!-- Transmission -->
  <rect x="300" y="130" width="150" height="80" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" rx="5"/>
  <text x="375" y="155" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Transmission</text>
  <text x="375" y="175" text-anchor="middle" font-family="Arial" font-size="10">type: String</text>
  <text x="375" y="190" text-anchor="middle" font-family="Arial" font-size="10">gears: Integer</text>
  
  <!-- Wheels -->
  <rect x="300" y="230" width="150" height="80" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="5"/>
  <text x="375" y="255" text-anchor="middle" font-family="Arial" font-size="12" font-weight="bold">Wheel[4]</text>
  <text x="375" y="275" text-anchor="middle" font-family="Arial" font-size="10">diameter: Real</text>
  <text x="375" y="290" text-anchor="middle" font-family="Arial" font-size="10">material: String</text>
  
  <!-- Connections -->
  <line x1="250" y1="70" x2="300" y2="70" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="250" y1="100" x2="300" y2="170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="250" y1="130" x2="300" y2="270" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Labels -->
  <text x="275" y="65" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">engine</text>
  <text x="275" y="135" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">transmission</text>
  <text x="275" y="200" text-anchor="middle" font-family="Arial" font-size="10" fill="#666">wheels</text>
  
  <!-- Title -->
  <text x="400" y="25" text-anchor="middle" font-family="Arial" font-size="16" font-weight="bold" fill="#333">
    Vehicle System Architecture
  </text>
</svg>
`;

// 模拟 API 延迟
export const mockDelay = (ms: number = 500) => 
  new Promise(resolve => setTimeout(resolve, ms));

// 模拟登录验证
export const mockLogin = async (username: string, password: string) => {
  await mockDelay();
  
  const user = mockUsers.find(u => u.username === username);
  if (user && password === 'password123') {
    return {
      success: true,
      data: {
        user,
        token: 'mock-jwt-token-' + Date.now(),
      },
    };
  }
  
  return {
    success: false,
    error: '用户名或密码错误',
  };
};

// 模拟注册
export const mockRegister = async (username: string, password: string) => {
  await mockDelay();
  
  const existingUser = mockUsers.find(u => u.username === username);
  if (existingUser) {
    return {
      success: false,
      error: '用户名已存在',
    };
  }
  
  const newUser: User = {
    id: 'user-' + Date.now(),
    username,
    email: `${username}@example.com`,
    createdAt: new Date(),
  };
  
  mockUsers.push(newUser);
  
  return {
    success: true,
    data: newUser,
  };
};

// 模拟代码验证
export const mockValidateCode = async (code: string) => {
  await mockDelay(300);
  
  // 简单的语法检查
  const errors = [];
  
  if (!code.trim()) {
    errors.push({
      severity: 'warning',
      message: '代码为空',
      line: 1,
      column: 1,
    });
  }
  
  if (code.includes('syntax error')) {
    errors.push({
      severity: 'error',
      message: '语法错误',
      line: 1,
      column: 1,
    });
  }
  
  return {
    success: true,
    data: {
      isValid: errors.length === 0,
      errors,
    },
  };
};

// 模拟 SVG 生成
export const mockGenerateSVG = async (code: string) => {
  await mockDelay(1000);
  
  return {
    success: true,
    data: {
      svg: mockSVGContent,
    },
  };
};

// 模拟 AI 代码生成
export const mockAIGenerate = async (prompt: string) => {
  await mockDelay(2000);

  const sampleCode = `package GeneratedModel {
  // AI 生成的 SysML v2 代码示例
  part def System {
    doc /* 这是一个由 AI 生成的系统定义 */

    part subsystem1 : SubSystem;
    part subsystem2 : SubSystem;

    connection c1 : Connection connect subsystem1.port1 to subsystem2.port2;
  }

  part def SubSystem {
    port port1 : Port;
    port port2 : Port;

    attribute performance : Real;
    attribute reliability : Real;
  }

  port def Port {
    attribute dataType : String;
    attribute direction : String;
  }

  connection def Connection {
    attribute bandwidth : Real;
    attribute protocol : String;
  }
}`;

  return {
    success: true,
    data: {
      code: sampleCode,
    },
  };
};

// 模拟使用自定义提示词的AI代码生成
export const mockAIGenerateWithPrompt = async (userPrompt: string, currentCode?: string) => {
  await mockDelay(2000);

  // 根据用户提示词生成不同的代码示例
  let generatedCode = '';

  if (userPrompt.toLowerCase().includes('车辆') || userPrompt.toLowerCase().includes('vehicle')) {
    generatedCode = `package VehicleSystem {
  // 根据用户要求"${userPrompt}"生成的车辆系统模型

  part def Vehicle {
    doc /* 车辆系统定义 */

    part engine : Engine;
    part transmission : Transmission;
    part wheels : Wheel[4];

    attribute mass : Real;
    attribute maxSpeed : Real;

    port fuelInput : FuelPort;
    port powerOutput : PowerPort;
  }

  part def Engine {
    doc /* 引擎定义 */
    attribute power : Real;
    attribute fuelConsumption : Real;

    constraint powerConstraint {
      power <= 500.0 // 最大功率限制
    }
  }

  requirement def PerformanceRequirement {
    doc /* 性能需求 */
    subject vehicle : Vehicle;

    constraint {
      vehicle.maxSpeed >= 120.0 // 最高速度要求
    }
  }
}`;
  } else if (userPrompt.toLowerCase().includes('机器人') || userPrompt.toLowerCase().includes('robot')) {
    generatedCode = `package RobotSystem {
  // 根据用户要求"${userPrompt}"生成的机器人系统模型

  part def Robot {
    doc /* 机器人系统定义 */

    part controller : Controller;
    part sensors : Sensor[*];
    part actuators : Actuator[*];
    part arm : RobotArm;

    attribute batteryLevel : Real;
    attribute operationalStatus : String;
  }

  part def RobotArm {
    doc /* 机器人手臂 */
    part joints : Joint[6];
    part endEffector : EndEffector;

    attribute reachRadius : Real;
    attribute payload : Real;
  }

  part def Sensor {
    doc /* 传感器定义 */
    attribute sensorType : String;
    attribute accuracy : Real;
    attribute range : Real;
  }

  action def PickAndPlace {
    doc /* 抓取和放置动作 */
    in item targetObject : Object;
    in item targetLocation : Location;

    action approach;
    action grasp;
    action move;
    action release;
  }
}`;
  } else {
    // 默认生成通用系统
    generatedCode = `package CustomSystem {
  // 根据用户要求"${userPrompt}"生成的系统模型

  part def System {
    doc /* 根据用户需求定制的系统 */

    part component1 : Component;
    part component2 : Component;

    attribute systemProperty : Real;
    attribute status : String;

    connection c1 : Connection connect component1.port1 to component2.port2;
  }

  part def Component {
    doc /* 系统组件 */
    port port1 : Port;
    port port2 : Port;

    attribute componentProperty : Real;
  }

  port def Port {
    attribute dataType : String;
    attribute direction : String;
  }

  connection def Connection {
    attribute connectionType : String;
    attribute bandwidth : Real;
  }

  requirement def SystemRequirement {
    doc /* 系统需求 */
    subject system : System;

    constraint {
      system.systemProperty >= 0.0
    }
  }
}`;
  }

  // 如果有当前代码，模拟基于当前代码的修改
  if (currentCode && currentCode.trim()) {
    generatedCode = `${currentCode}

// === 基于用户要求"${userPrompt}"的扩展 ===

part def EnhancedComponent {
  doc /* 根据用户要求添加的增强组件 */

  attribute enhancedProperty : Real;
  attribute userRequestedFeature : String;

  constraint enhancementConstraint {
    enhancedProperty > 0.0
  }
}`;
  }

  return {
    success: true,
    data: {
      code: generatedCode,
    },
  };
};
