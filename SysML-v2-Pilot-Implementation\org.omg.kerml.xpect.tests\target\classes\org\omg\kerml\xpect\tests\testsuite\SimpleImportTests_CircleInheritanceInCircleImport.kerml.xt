//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	classifier A{
		public import test::B::*;
		//XPECT linkedName at b --> test.B.b
		//*XPECT scope at b ---
		   A.b, B.a, test.A.b, test.B.a,
 		   a, b,
 		   A, A.a, B, B.b, 
 		   test.A, test.A.a, test.B, test.B.b,
		   --- */
		classifier a specializes b{}
	}
	classifier B {
		public import test::A::*;
		//xpect throw an error for testing a
		//linkedName at a --> test.A.a
		//*scope at a ---
 		   a, b,
 		   A, A.a, B, B.b, 
 		   test.A, test.A.a, test.B, test.B.b,
		   --- */
		classifier b specializes a, Base::Anything{}
	}
}
