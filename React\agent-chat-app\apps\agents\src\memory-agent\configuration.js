"use strict";
// Define the configurable parameters for the agent
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConfigurationAnnotation = void 0;
exports.ensureConfiguration = ensureConfiguration;
var langgraph_1 = require("@langchain/langgraph");
var prompts_js_1 = require("./prompts.js");
exports.ConfigurationAnnotation = langgraph_1.Annotation.Root({
    userId: (0, langgraph_1.Annotation)(),
    model: (0, langgraph_1.Annotation)(),
    systemPrompt: (0, langgraph_1.Annotation)(),
});
function ensureConfiguration(config) {
    var configurable = (config === null || config === void 0 ? void 0 : config.configurable) || {};
    return {
        userId: (configurable === null || configurable === void 0 ? void 0 : configurable.userId) || "default",
        model: (configurable === null || configurable === void 0 ? void 0 : configurable.model) || "anthropic/claude-3-7-sonnet-latest",
        systemPrompt: (configurable === null || configurable === void 0 ? void 0 : configurable.systemPrompt) || prompts_js_1.SYSTEM_PROMPT,
    };
}
