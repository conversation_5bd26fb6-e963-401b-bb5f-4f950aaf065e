//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.local.KerMLImportLocalTest
	ResourceSet {
		ThisFile {}
			File {from ="/library/Base.kerml"}
			File {from ="/library/Performances.kerml"}
			File {from ="/library/BaseFunctions.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			   	File {from ="/library/Performances.kerml"}
				File {from ="/library/BaseFunctions.kerml"}
			}
		}
	}
END_SETUP 
*/
package Import_Filtered {
	public import P1::*;
	
	package P1 {
		feature f {
            feature a;
        }
		
	}
	
	package P2 {
		public import P1::*[false];
		feature x :> f.a;		
	}
}