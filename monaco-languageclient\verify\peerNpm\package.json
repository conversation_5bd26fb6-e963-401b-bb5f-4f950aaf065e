{"name": "@typefox/peer-check-npm", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "dependencies": {"monaco-languageclient-examples": "~2025.6.2", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0"}, "devDependencies": {"typescript": "~5.8.2"}, "scripts": {"build": "tsc --build tsconfig.json", "verify:ci": "npm install && npm run build"}}