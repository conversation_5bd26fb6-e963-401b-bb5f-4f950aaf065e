/**
 * @param {Strong} node
 * @param {Parents | undefined} _
 * @param {State} state
 * @param {Info} info
 * @returns {string}
 */
export function strong(node: Strong, _: Parents | undefined, state: State, info: Info): string;
export namespace strong {
    export { strongPeek as peek };
}
import type { Strong } from 'mdast';
import type { Parents } from 'mdast';
import type { State } from 'mdast-util-to-markdown';
import type { Info } from 'mdast-util-to-markdown';
/**
 * @param {Strong} _
 * @param {Parents | undefined} _1
 * @param {State} state
 * @returns {string}
 */
declare function strongPeek(_: Strong, _1: Parents | undefined, state: State): string;
export {};
//# sourceMappingURL=strong.d.ts.map