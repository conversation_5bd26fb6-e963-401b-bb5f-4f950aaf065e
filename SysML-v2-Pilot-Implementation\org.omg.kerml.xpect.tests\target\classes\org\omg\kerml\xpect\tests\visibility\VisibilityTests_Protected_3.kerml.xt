//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	classifier P{
		classifier PP {
			protected classifier PPP{}
		}
	}
	classifier C specializes P::PP {
		feature CC: PPP;
	}
	
	classifier A specializes P {
		// XPECT errors --> "Couldn't resolve reference to Classifier 'PP::PPP'." at "PP::PPP"
 		classifier AA specializes PP::PPP{}
	}
	classifier B {
		// XPECT errors --> "Couldn't resolve reference to Type 'P::PP::PPP'." at "P::PP::PPP"
		feature BB2: P::PP::PPP;
	}
}
