//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/library/Links.kerml"}
       	File {from ="/library/Occurrences.kerml"}
       	File {from ="/library/Objects.kerml"}
      	File {from ="/library/Metaobjects.kerml"}
       	File {from ="/library/Performances.kerml"}
       	File {from ="/library/ScalarValues.kerml"}
       	File {from ="/library/BaseFunctions.kerml"}
       	File {from ="/library/DataFunctions.kerml"}
       	File {from ="/library/ScalarFunctions.kerml"}
       	File {from ="/library/ControlFunctions.kerml"}
        File {from ="/library/KerML.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/library/Links.kerml"}
		       	File {from ="/library/Occurrences.kerml"}
		       	File {from ="/library/Objects.kerml"}
		       	File {from ="/library/Metaobjects.kerml"}
		       	File {from ="/library/Performances.kerml"}
		       	File {from ="/library/ScalarValues.kerml"}
		       	File {from ="/library/BaseFunctions.kerml"}
		       	File {from ="/library/DataFunctions.kerml"}
		       	File {from ="/library/ScalarFunctions.kerml"}
		       	File {from ="/library/ControlFunctions.kerml"}
		       	File {from ="/library/KerML.kerml"}
			}
		}
	}
END_SETUP 
*/
package MetadataFeatureTest {
	
	metaclass A {
		feature :>> annotatedElement : KerML::Feature;
		feature x : ScalarValues::Integer;
		feature y : ScalarValues::String;
		feature z : ScalarValues::Boolean;
		feature u {
			feature v : ScalarValues::Integer;
		}
	}
	
	function f { in x; return : ScalarValues::Boolean; }
	
	// XPECT errors --> "Must be model-level evaluable" at "filter f(A::y);"
	filter f(A::y);
	// XPECT errors --> "Must be model-level evaluable" at "filter ~A::z;"
	filter ~A::z;
	// XPECT errors --> "Must be model-level evaluable" at "filter A::y->ControlFunctions::collect {in x; x};"
	filter A::y->ControlFunctions::collect {in x; x};
	// XPECT errors --> "Must have a Boolean result" at "filter new A(null, 1, "", false);"
	filter new A(null, 1, "", false);
	
	feature bad;
	
	feature a {
		@A {
			// XPECT errors --> "Must be model-level evaluable" at "= ~3"
			x = ~3;
			y = "e";
			// XPECT errors --> "Must be model-level evaluable" at "= f(A::y)"
			z = f(A::y);
			u {
				v = 1;
				// XPECT errors --> "Must redefine an owning-type feature" at "bad;"
				bad;
			}		
		}
	}
	
	classifier B {
		// XPECT errors --> "Cannot annotate Classifier" at "@A;"
		@A;
		// XPECT errors --> "Must have a concrete type" at "@Metaobjects::Metaobject;"
		@Metaobjects::Metaobject;
	}
	
}
