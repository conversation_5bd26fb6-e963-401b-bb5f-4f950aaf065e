//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

package Classes {
	// XPECT errors ---> "Couldn't resolve reference to Membership 'VisibilityPackage::c_Private::c_private'." at "VisibilityPackage::c_Private::c_private"
	public import VisibilityPackage::c_Private::c_private;
	classifier Try{
		// XPECT errors ---> "Couldn't resolve reference to Type 'c_private'." at "c_private"
		feature feature1 : c_private;
	}
}

