//*
XPECT_SETUP org.omg.sysml.xpect.tests.validation.invalid.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
       	File {from ="/library.kernel/Occurrences.kerml"}
       	File {from ="/library.kernel/Objects.kerml"}
       	File {from ="/library.kernel/Performances.kerml"}
		File {from ="/library.kernel/BaseFunctions.kerml"}
		File {from ="/library.systems/Items.sysml"}
		File {from ="/library.systems/Parts.sysml"}
		File {from ="/library.systems/Ports.sysml"}
		File {from ="/library.systems/Connections.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
		       	File {from ="/library.kernel/Occurrences.kerml"}
		       	File {from ="/library.kernel/Objects.kerml"}
 		      	File {from ="/library.kernel/Performances.kerml"}
				File {from ="/library.kernel/BaseFunctions.kerml"}
				File {from ="/library.systems/Items.sysml"}
				File {from ="/library.systems/Parts.sysml"}
				File {from ="/library.systems/Ports.sysml"}
				File {from ="/library.systems/Connections.sysml"}
			}
		}
	}
END_SETUP 
*/
package P {
	part def A {
		part x;
	}
	part def B {
		part y {
			part z;
		}
		//* XPECT errors ---
		    "Must be an accessible feature (use dot notation for nesting)" at "A::x"
		    "Must be an accessible feature (use dot notation for nesting)" at "y::z"
		    --- */
		connect A::x to y::z;
	}
	part b {
		part y : A {
			// XPECT errors --> "Must be an accessible feature (use dot notation for nesting)" at "z"
			connect x to z;
		}
		part z;
		// XPECT errors --> "Must be an accessible feature (use dot notation for nesting)" at "y::x"
		connect y::x to z;
	}
}
