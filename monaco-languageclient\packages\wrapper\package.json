{"name": "monaco-editor-wrapper", "version": "6.9.0", "license": "MIT", "description": "Wrapper for monaco-vscode-editor-api and monaco-languageclient", "keywords": ["monaco-editor", "monaco-languageclient", "typescript", "vscode"], "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./vscode/services": {"types": "./dist/vscode/index.d.ts", "default": "./dist/vscode/index.js"}, "./vscode/locale": {"types": "./dist/vscode/localeLoader.d.ts", "default": "./dist/vscode/localeLoader.js"}, "./workers/workerLoaders": {"types": "./dist/workers/workerLoaders.d.ts", "default": "./dist/workers/workerLoaders.js"}}, "typesVersions": {"*": {".": ["dist/index"], "vscode/services": ["dist/vscode/services"], "vscode/locale": ["dist/vscode/localeLoader"], "workers/workerLoaders": ["dist/workers/workerLoaders"]}}, "files": ["dist", "src", "README.md", "CHANGELOG.md", "LICENSE"], "scripts": {"clean": "shx rm -fr ./dist ./bundle *.tsbuildinfo", "compile": "tsc --build tsconfig.src.json && tsc --build tsconfig.test.json", "build": "npm run clean && npm run compile"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-language-pack-cs": "~18.1.0", "@codingame/monaco-vscode-language-pack-de": "~18.1.0", "@codingame/monaco-vscode-language-pack-es": "~18.1.0", "@codingame/monaco-vscode-language-pack-fr": "~18.1.0", "@codingame/monaco-vscode-language-pack-it": "~18.1.0", "@codingame/monaco-vscode-language-pack-ja": "~18.1.0", "@codingame/monaco-vscode-language-pack-ko": "~18.1.0", "@codingame/monaco-vscode-language-pack-pl": "~18.1.0", "@codingame/monaco-vscode-language-pack-pt-br": "~18.1.0", "@codingame/monaco-vscode-language-pack-qps-ploc": "~18.1.0", "@codingame/monaco-vscode-language-pack-ru": "~18.1.0", "@codingame/monaco-vscode-language-pack-tr": "~18.1.0", "@codingame/monaco-vscode-language-pack-zh-hans": "~18.1.0", "@codingame/monaco-vscode-language-pack-zh-hant": "~18.1.0", "@codingame/monaco-vscode-monarch-service-override": "~18.1.0", "@codingame/monaco-vscode-textmate-service-override": "~18.1.0", "@codingame/monaco-vscode-theme-defaults-default-extension": "~18.1.0", "@codingame/monaco-vscode-theme-service-override": "~18.1.0", "@codingame/monaco-vscode-views-service-override": "~18.1.0", "@codingame/monaco-vscode-workbench-service-override": "~18.1.0", "monaco-languageclient": "~9.8.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1", "vscode-languageserver-protocol": "~3.17.5", "vscode-ws-jsonrpc": "~3.4.0"}, "devDependencies": {"@codingame/monaco-vscode-standalone-languages": "~18.1.0", "@codingame/monaco-vscode-standalone-css-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-html-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-json-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-typescript-language-features": "~18.1.0", "monaco-languageclient-examples": "~2025.6.2"}, "repository": {"type": "git", "url": "git+https://github.com/TypeFox/monaco-languageclient.git", "directory": "packages/wrapper"}, "homepage": "https://github.com/TypeFox/monaco-languageclient/blob/main/packages/wrapper/README.md", "bugs": "https://github.com/TypeFox/monaco-languageclient/issues", "author": {"name": "TypeFox", "url": "https://www.typefox.io"}}