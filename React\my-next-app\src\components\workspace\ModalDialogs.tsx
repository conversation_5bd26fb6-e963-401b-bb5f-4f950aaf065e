'use client';

import React, { useState, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useToast } from '@/components/ui/Toast';
import Modal from '@/components/ui/Modal';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { projectApi, namespaceApi, diagramApi } from '@/utils/api';
import { 
  validateProjectName, 
  validateNamespaceName, 
  validateDiagramName,
  hasValidationErrors,
  getFieldError 
} from '@/utils/validation';
import { ValidationError, Project, Namespace, SysMLDiagram } from '@/types';
import { generateId } from '@/utils/helpers';

const ModalDialogs: React.FC = () => {
  const { state, dispatch } = useApp();
  const { showToast } = useToast();
  const { modal } = state;

  const [formData, setFormData] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 重置表单
  const resetForm = () => {
    setFormData({});
    setErrors([]);
    setIsSubmitting(false);
  };

  // 关闭模态框
  const closeModal = () => {
    dispatch({ type: 'HIDE_MODAL' });
    resetForm();
  };

  // 当模态框打开时初始化表单数据
  useEffect(() => {
    if (modal.isOpen && modal.data) {
      setFormData(modal.data as Record<string, string>);
    } else if (!modal.isOpen) {
      resetForm();
    }
  }, [modal.isOpen, modal.data]);

  // 处理输入变化
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setErrors(prev => prev.filter(error => error.field !== field));
  };

  // 创建项目
  const handleCreateProject = async () => {
    const name = formData.name || '';
    const description = formData.description || '';
    
    const validationErrors = validateProjectName(name);
    if (hasValidationErrors(validationErrors)) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await projectApi.createProject(name, description);
      if (response.success && response.data) {
        dispatch({ type: 'ADD_PROJECT', payload: response.data });
        showToast({
          type: 'success',
          message: '项目创建成功',
          duration: 2000,
        });
        closeModal();
      } else {
        showToast({
          type: 'error',
          title: '创建失败',
          message: response.error || '创建项目时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '创建失败',
        message: '网络错误，请重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 重命名项目
  const handleRenameProject = async () => {
    const projectId = formData.projectId || '';
    const newName = formData.name || '';
    
    const validationErrors = validateProjectName(newName);
    if (hasValidationErrors(validationErrors)) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await projectApi.updateProject(projectId, { name: newName });
      if (response.success && response.data) {
        dispatch({ 
          type: 'UPDATE_PROJECT', 
          payload: { 
            id: projectId, 
            updates: { name: newName } 
          } 
        });
        showToast({
          type: 'success',
          message: '项目重命名成功',
          duration: 2000,
        });
        closeModal();
      } else {
        showToast({
          type: 'error',
          title: '重命名失败',
          message: response.error || '重命名项目时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '重命名失败',
        message: '网络错误，请重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 删除确认
  const handleDeleteConfirm = async () => {
    const { type, id, name } = modal.data as any;
    
    setIsSubmitting(true);
    try {
      let response;
      
      if (type === 'project') {
        response = await projectApi.deleteProject(id);
        if (response.success) {
          dispatch({ type: 'REMOVE_PROJECT', payload: id });
        }
      } else if (type === 'namespace') {
        response = await namespaceApi.deleteNamespace(id);
        // 这里需要更新项目数据
      } else if (type === 'diagram') {
        response = await diagramApi.deleteDiagram(id);
        // 这里需要更新项目数据
      }

      if (response?.success) {
        showToast({
          type: 'success',
          message: `${name} 删除成功`,
          duration: 2000,
        });
        closeModal();
      } else {
        showToast({
          type: 'error',
          title: '删除失败',
          message: response?.error || '删除时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '删除失败',
        message: '网络错误，请重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 创建命名空间
  const handleCreateNamespace = async () => {
    const projectId = formData.projectId || '';
    const name = formData.name || '';
    
    const validationErrors = validateNamespaceName(name);
    if (hasValidationErrors(validationErrors)) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await namespaceApi.createNamespace(projectId, name);
      if (response.success && response.data) {
        // 更新项目数据
        const project = state.projects.find(p => p.id === projectId);
        if (project) {
          const updatedProject = {
            ...project,
            namespaces: [...project.namespaces, response.data],
          };
          dispatch({ 
            type: 'UPDATE_PROJECT', 
            payload: { 
              id: projectId, 
              updates: updatedProject 
            } 
          });
        }
        
        showToast({
          type: 'success',
          message: '命名空间创建成功',
          duration: 2000,
        });
        closeModal();
      } else {
        showToast({
          type: 'error',
          title: '创建失败',
          message: response.error || '创建命名空间时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '创建失败',
        message: '网络错误，请重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 创建图
  const handleCreateDiagram = async () => {
    const namespaceId = formData.namespaceId || '';
    const name = formData.name || '';
    
    const validationErrors = validateDiagramName(name);
    if (hasValidationErrors(validationErrors)) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await diagramApi.createDiagram(namespaceId, name);
      if (response.success && response.data) {
        // 更新项目数据
        const project = state.projects.find(p => 
          p.namespaces.some(ns => ns.id === namespaceId)
        );
        
        if (project) {
          const updatedNamespaces = project.namespaces.map(ns => 
            ns.id === namespaceId 
              ? { ...ns, diagrams: [...ns.diagrams, response.data!] }
              : ns
          );
          
          dispatch({ 
            type: 'UPDATE_PROJECT', 
            payload: { 
              id: project.id, 
              updates: { namespaces: updatedNamespaces } 
            } 
          });
        }

        // 在编辑器中打开新图
        const newTab = {
          id: generateId(),
          title: name,
          content: '',
          isDirty: false,
          diagramId: response.data.id,
        };
        
        dispatch({ type: 'ADD_EDITOR_TAB', payload: newTab });
        
        showToast({
          type: 'success',
          message: '图创建成功',
          duration: 2000,
        });
        closeModal();
      } else {
        showToast({
          type: 'error',
          title: '创建失败',
          message: response.error || '创建图时发生错误',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '创建失败',
        message: '网络错误，请重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 渲染模态框内容
  const renderModalContent = () => {
    switch (modal.type) {
      case 'create-project':
        return (
          <Modal isOpen={modal.isOpen} onClose={closeModal} title="新建项目">
            <div className="space-y-4">
              <Input
                label="项目名称"
                placeholder="请输入项目名称"
                value={formData.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
                error={getFieldError(errors, 'name')}
                required
              />
              
              <Input
                label="项目描述"
                placeholder="请输入项目描述（可选）"
                value={formData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </div>
            
            <div className="modal-footer">
              <Button variant="ghost" onClick={closeModal}>
                取消
              </Button>
              <Button 
                variant="primary" 
                onClick={handleCreateProject}
                loading={isSubmitting}
              >
                创建
              </Button>
            </div>
          </Modal>
        );

      case 'rename-project':
        return (
          <Modal isOpen={modal.isOpen} onClose={closeModal} title="重命名项目">
            <Input
              label="新项目名称"
              placeholder="请输入新的项目名称"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={getFieldError(errors, 'name')}
              required
            />
            
            <div className="modal-footer">
              <Button variant="ghost" onClick={closeModal}>
                取消
              </Button>
              <Button 
                variant="primary" 
                onClick={handleRenameProject}
                loading={isSubmitting}
              >
                保存
              </Button>
            </div>
          </Modal>
        );

      case 'delete-confirm':
        const { type, name } = modal.data as any;
        return (
          <Modal isOpen={modal.isOpen} onClose={closeModal} title="确认删除">
            <div className="text-center">
              <div className="text-red-500 mb-4">
                <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                确认删除{type === 'project' ? '项目' : type === 'namespace' ? '命名空间' : '图'}
              </h3>
              <p className="text-gray-600 mb-6">
                您确定要删除 <strong>{name}</strong> 吗？此操作不可撤销。
              </p>
            </div>
            
            <div className="modal-footer">
              <Button variant="ghost" onClick={closeModal}>
                取消
              </Button>
              <Button 
                variant="danger" 
                onClick={handleDeleteConfirm}
                loading={isSubmitting}
              >
                确认删除
              </Button>
            </div>
          </Modal>
        );

      case 'create-namespace':
        return (
          <Modal isOpen={modal.isOpen} onClose={closeModal} title="新建命名空间">
            <Input
              label="命名空间名称"
              placeholder="请输入命名空间名称"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={getFieldError(errors, 'name')}
              required
            />
            
            <div className="modal-footer">
              <Button variant="ghost" onClick={closeModal}>
                取消
              </Button>
              <Button 
                variant="primary" 
                onClick={handleCreateNamespace}
                loading={isSubmitting}
              >
                创建
              </Button>
            </div>
          </Modal>
        );

      case 'create-diagram':
        return (
          <Modal isOpen={modal.isOpen} onClose={closeModal} title="新建 SysMLv2 图">
            <Input
              label="图名称"
              placeholder="请输入图名称"
              value={formData.name || ''}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={getFieldError(errors, 'name')}
              required
            />
            
            <div className="modal-footer">
              <Button variant="ghost" onClick={closeModal}>
                取消
              </Button>
              <Button 
                variant="primary" 
                onClick={handleCreateDiagram}
                loading={isSubmitting}
              >
                创建
              </Button>
            </div>
          </Modal>
        );

      default:
        return null;
    }
  };

  return renderModalContent();
};

export default ModalDialogs;
