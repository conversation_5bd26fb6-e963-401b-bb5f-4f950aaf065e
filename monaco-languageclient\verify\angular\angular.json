{
  "$schema": "./node_modules/@angular/cli/lib/config/schema.json",
  "version": 1,
  "newProjectRoot": "projects",
  "projects": {
    "ng-mlc": {
      "projectType": "application",
      "schematics": {},
      "root": "",
      "sourceRoot": "src",
      "prefix": "app",
      "architect": {
        "build": {
          "builder": "@angular-builders/custom-esbuild:application",
          "options": {
            "outputPath": "dist/ng-mlc",
            "index": "src/index.html",
            "browser": "src/main.ts",
            "tsConfig": "tsconfig.json",
            // "plugins": ["./node_modules/@codingame/esbuild-import-meta-url-plugin/dist/esbuildImportMetaUrlPlugin.js"],
            "polyfills": [
              "zone.js"
            ],
            "assets": [],
            "styles": [],
            "scripts": [],
            "aot": false,
            "optimization": false,
            "sourceMap": true,
            "namedChunks": false,
            "extractLicenses": false,
            "allowedCommonJsDependencies": [
              "vscode-languageclient",
              "vscode-languageserver-protocol",
              "vscode-jsonrpc",
              "vscode-oniguruma",
              "vscode-textmate",
              "@vscode/iconv-lite-umd"
            ]
          },
          "configurations": {
            "production": {
              "aot": true,
              "optimization": true,
              "sourceMap": false,
              "namedChunks": false,
              "extractLicenses": true
            },
            "development": {
            }
          },
          "defaultConfiguration": "development"
        },
        "serve": {
          "builder": "@angular-builders/custom-esbuild:dev-server",
          "configurations": {
            "production": {
              "buildTarget": "ng-mlc:build:production"
            },
            "development": {
              "buildTarget": "ng-mlc:build:development"
            }
          },
          "defaultConfiguration": "development"
        },
        "extract-i18n": {
          "builder": "@angular/build:extract-i18n"
        }
      }
    }
  },
  "cli": {
    "analytics": false
  }
}
