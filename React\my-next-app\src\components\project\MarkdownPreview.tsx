'use client';

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';

const MarkdownPreview: React.FC = () => {
  const [markdownText, setMarkdownText] = useState(`# SysML v2 文本描述示例

## 利益相关者需求

### 主要利益相关者
1. **最终用户**：系统的直接使用者
2. **系统管理员**：负责系统维护和配置
3. **监管机构**：确保系统符合相关法规

### 核心需求
- **功能性需求**：系统必须提供的基本功能
- **性能需求**：响应时间、吞吐量等指标
- **安全需求**：数据保护和访问控制

> **重要提示**：所有需求都必须是可测试和可验证的。

### 需求优先级
| 优先级 | 需求类型 | 描述 |
|--------|----------|------|
| 高 | 功能性 | 核心业务功能 |
| 中 | 性能 | 响应时间要求 |
| 低 | 可用性 | 用户界面友好性 |

### 验收标准
- [ ] 功能测试通过率 ≥ 95%
- [ ] 性能测试满足指标要求
- [ ] 安全测试无高危漏洞

\`\`\`sysml
requirement def StakeholderNeeds {
  doc /* 利益相关者需求定义 */
  
  requirement functionalReq {
    doc /* 功能性需求 */
    subject system : System;
  }
  
  requirement performanceReq {
    doc /* 性能需求 */
    subject system : System;
    
    constraint {
      system.responseTime <= 2.0 // 响应时间不超过2秒
    }
  }
}
\`\`\`

---

**注意**：这是一个*示例文档*，展示了如何使用 **Markdown** 格式来编写结构化的文本描述。`);

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          📝 Markdown 文本显示器测试
        </h2>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* 输入区域 */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Markdown 输入</h3>
            <textarea
              value={markdownText}
              onChange={(e) => setMarkdownText(e.target.value)}
              className="w-full h-96 p-3 border border-gray-300 rounded-md font-mono text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="输入 Markdown 文本..."
            />
          </div>
          
          {/* 预览区域 */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">渲染预览</h3>
            <div className="h-96 p-3 border border-gray-300 rounded-md overflow-auto bg-gray-50">
              <div className="prose prose-gray max-w-none">
                <ReactMarkdown
                  components={{
                    // 自定义组件样式
                    h1: ({ children }) => (
                      <h1 className="text-2xl font-bold text-gray-900 mb-4 mt-6 first:mt-0">
                        {children}
                      </h1>
                    ),
                    h2: ({ children }) => (
                      <h2 className="text-xl font-semibold text-gray-800 mb-3 mt-5 first:mt-0">
                        {children}
                      </h2>
                    ),
                    h3: ({ children }) => (
                      <h3 className="text-lg font-medium text-gray-800 mb-2 mt-4 first:mt-0">
                        {children}
                      </h3>
                    ),
                    p: ({ children }) => (
                      <p className="mb-3 text-gray-700 leading-relaxed">
                        {children}
                      </p>
                    ),
                    ul: ({ children }) => (
                      <ul className="list-disc list-inside mb-3 space-y-1 text-gray-700">
                        {children}
                      </ul>
                    ),
                    ol: ({ children }) => (
                      <ol className="list-decimal list-inside mb-3 space-y-1 text-gray-700">
                        {children}
                      </ol>
                    ),
                    li: ({ children }) => (
                      <li className="text-gray-700">
                        {children}
                      </li>
                    ),
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-blue-500 pl-4 py-2 mb-3 bg-blue-50 text-gray-700 italic">
                        {children}
                      </blockquote>
                    ),
                    code: ({ children, className }) => {
                      const isInline = !className;
                      return isInline ? (
                        <code className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono">
                          {children}
                        </code>
                      ) : (
                        <code className="block bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto">
                          {children}
                        </code>
                      );
                    },
                    pre: ({ children }) => (
                      <pre className="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto mb-3">
                        {children}
                      </pre>
                    ),
                    strong: ({ children }) => (
                      <strong className="font-semibold text-gray-900">
                        {children}
                      </strong>
                    ),
                    em: ({ children }) => (
                      <em className="italic text-gray-700">
                        {children}
                      </em>
                    ),
                    hr: () => (
                      <hr className="border-gray-300 my-4" />
                    ),
                    table: ({ children }) => (
                      <div className="overflow-x-auto mb-3">
                        <table className="min-w-full border border-gray-300">
                          {children}
                        </table>
                      </div>
                    ),
                    thead: ({ children }) => (
                      <thead className="bg-gray-50">
                        {children}
                      </thead>
                    ),
                    tbody: ({ children }) => (
                      <tbody className="bg-white">
                        {children}
                      </tbody>
                    ),
                    tr: ({ children }) => (
                      <tr className="border-b border-gray-200">
                        {children}
                      </tr>
                    ),
                    th: ({ children }) => (
                      <th className="px-4 py-2 text-left font-medium text-gray-900 border-r border-gray-300 last:border-r-0">
                        {children}
                      </th>
                    ),
                    td: ({ children }) => (
                      <td className="px-4 py-2 text-gray-700 border-r border-gray-300 last:border-r-0">
                        {children}
                      </td>
                    ),
                  }}
                >
                  {markdownText}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">支持的 Markdown 功能：</h4>
          <div className="text-sm text-blue-800 grid md:grid-cols-2 gap-2">
            <div>
              <ul className="space-y-1">
                <li>• 标题 (H1-H6)</li>
                <li>• 段落和换行</li>
                <li>• 粗体和斜体</li>
                <li>• 有序和无序列表</li>
                <li>• 引用块</li>
              </ul>
            </div>
            <div>
              <ul className="space-y-1">
                <li>• 内联代码和代码块</li>
                <li>• 表格</li>
                <li>• 水平分割线</li>
                <li>• 任务列表</li>
                <li>• 自定义样式</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownPreview;
