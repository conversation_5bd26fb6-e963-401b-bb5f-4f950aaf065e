# Monaco Language Client Examples

[![Gitpod - Code Now](https://img.shields.io/badge/Gitpod-code%20now-blue.svg?longCache=true)](https://gitpod.io#https://github.com/TypeFox/monaco-languageclient)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg?longCache=true)](https://github.com/TypeFox/monaco-languageclient/labels/help%20wanted)
[![monaco-languageclient](https://github.com/TypeFox/monaco-languageclient/actions/workflows/actions.yml/badge.svg)](https://github.com/TypeFox/monaco-languageclient/actions/workflows/actions.yml)
[![NPM Version](https://img.shields.io/npm/v/monaco-languageclient-examples.svg)](https://www.npmjs.com/package/monaco-languageclient-examples)
[![NPM Download](https://img.shields.io/npm/dt/monaco-languageclient-examples.svg)](https://www.npmjs.com/package/monaco-languageclient-examples)

This package contains [all examples from the monaco-languageclient repository](https://github.com/TypeFox/monaco-languageclient/blob/main/README.md#examples).

## CHANGELOG

All changes are noted in the [CHANGELOG](https://github.com/TypeFox/monaco-languageclient/blob/main/packages/examples/CHANGELOG.md).

## Getting Started

This is npm package is part of the <https://github.com/TypeFox/monaco-languageclient> mono repo. Please follow the main repositories [instructions]](<https://github.com/TypeFox/monaco-languageclient#getting-started>) to get started with local development.

## Usage

Needs to be added.

## License

[MIT](https://github.com/TypeFox/monaco-languageclient/blob/main/packages/examples/LICENSE)
