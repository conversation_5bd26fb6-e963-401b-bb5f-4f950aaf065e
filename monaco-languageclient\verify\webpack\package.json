{"name": "example-client-webpack", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "dependencies": {"monaco-languageclient-examples": "~2025.6.2"}, "devDependencies": {"css-loader": "~7.1.2", "http-server": "~14.1.1", "shx": "~0.3.4", "source-map-loader": "~5.0.0", "style-loader": "~4.0.0", "ts-loader": "~9.5.2", "webpack-cli": "~6.0.1"}, "scripts": {"verify": "npm install && npm run build && npm run start", "verify:ci": "npm install && npm run build", "clean": "shx rm -fr dist *.tsbuildinfo", "build:msg": "echo Building client-webpack example:", "build": "npm run build:msg && npm run clean && webpack", "start": "http-server ./ --port 8082"}}