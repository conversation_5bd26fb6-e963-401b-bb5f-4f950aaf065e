/*
 * generated by Xtext 2.24.0
 */
package org.omg.kerml.expressions.xtext

import com.google.inject.Binder
import com.google.inject.name.Names
import org.eclipse.xtext.validation.CompositeEValidator

/**
 * Use this class to register components to be used at runtime / without the Equinox extension registry.
 */
class KerMLExpressionsRuntimeModule extends AbstractKerMLExpressionsRuntimeModule {
	
	def void configureUseEObjectValidator(Binder binder) {
		binder.bind(Boolean).annotatedWith(Names.named(CompositeEValidator.USE_EOBJECT_VALIDATOR)).toInstance(false);
	}
}
