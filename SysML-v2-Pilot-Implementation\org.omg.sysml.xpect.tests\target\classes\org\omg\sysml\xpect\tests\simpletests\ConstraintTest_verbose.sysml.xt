//* 
XPECT_SETUP org.omg.sysml.xpect.tests.simpletests.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/Links.kerml"}
       	File {from ="/library.kernel/Occurrences.kerml"}
		File {from ="/library.kernel/Objects.kerml"}
		File {from ="/library.kernel/Performances.kerml"}
		File {from ="/library.kernel/ScalarValues.kerml"}
		File {from ="/library.kernel/BaseFunctions.kerml"}
		File {from ="/library.kernel/DataFunctions.kerml"}
		File {from ="/library.kernel/ScalarFunctions.kerml"}
		File {from ="/library.kernel/NumericalFunctions.kerml"}
		File {from ="/library.kernel/ControlFunctions.kerml"}
		File {from ="/library.systems/Items.sysml"}
		File {from ="/library.systems/Parts.sysml"}
		File {from ="/library.systems/Ports.sysml"}
		File {from ="/library.systems/Constraints.sysml"}
		File {from ="/library.domain/Quantities and Units/Quantities.sysml"}
		File {from ="/library.domain/Quantities and Units/MeasurementReferences.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQBase.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQ.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQAtomicNuclear.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQChemistryMolecular.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQElectromagnetism.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQLight.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQMechanics.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQSpaceTime.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQThermodynamics.sysml"}
		File {from ="/library.domain/Quantities and Units/SI.sysml"}
		File {from ="/library.domain/Quantities and Units/SIPrefixes.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/Links.kerml"}
       			File {from ="/library.kernel/Occurrences.kerml"}
				File {from ="/library.kernel/Objects.kerml"}
				File {from ="/library.kernel/Performances.kerml"}
				File {from ="/library.kernel/ScalarValues.kerml"}
				File {from ="/library.kernel/BaseFunctions.kerml"}
				File {from ="/library.kernel/DataFunctions.kerml"}
				File {from ="/library.kernel/ScalarFunctions.kerml"}
				File {from ="/library.kernel/NumericalFunctions.kerml"}
				File {from ="/library.kernel/ControlFunctions.kerml"}
				File {from ="/library.systems/Items.sysml"}
				File {from ="/library.systems/Parts.sysml"}
				File {from ="/library.systems/Ports.sysml"}
				File {from ="/library.systems/Constraints.sysml"}
				File {from ="/library.domain/Quantities and Units/Quantities.sysml"}
				File {from ="/library.domain/Quantities and Units/MeasurementReferences.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQBase.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQ.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQAtomicNuclear.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQChemistryMolecular.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQElectromagnetism.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQLight.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQMechanics.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQSpaceTime.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQThermodynamics.sysml"}
				File {from ="/library.domain/Quantities and Units/SI.sysml"}
				File {from ="/library.domain/Quantities and Units/SIPrefixes.sysml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package ConstraintTest {
	public import ISQ::MassValue;
	public import SI::kg;
	public import NumericalFunctions::sum;
	
	constraint def MassAnalysis {
		attribute totalMass: MassValue;
		attribute componentMasses: MassValue[0..*];		

		totalMass == sum(componentMasses)
	}
	
	part def Component {
		attribute mass: MassValue;
	}
	
	part vehicle : Component {	
		part engine : Component;
		part frontAxleAssembly : Component;
		part rearAxleAssembly : Component;	
	}
	part vehicle1a :> vehicle {
		assert constraint massAnalysis : MassAnalysis {
			attribute totalMass redefines MassAnalysis::totalMass;
			attribute componentMasses redefines MassAnalysis::componentMasses;
		}
		attribute mass redefines vehicle::mass;
		part engine redefines vehicle::engine {
			attribute mass redefines vehicle::engine::mass;
		}
		part frontAxleAssembly redefines vehicle::frontAxleAssembly {
			attribute mass redefines vehicle::frontAxleAssembly::mass;
		}
		part rearAxleAssembly redefines vehicle::rearAxleAssembly {
			attribute mass redefines vehicle::rearAxleAssembly::mass;
		}
		
		bind massAnalysis.totalMass = mass;
		bind massAnalysis.componentMasses = engine.mass;
		bind massAnalysis.componentMasses = frontAxleAssembly.mass;
		bind massAnalysis.componentMasses = rearAxleAssembly.mass;
	}
	part vehicle1b :> vehicle {
		assert constraint massAnalysis : MassAnalysis {
			attribute totalMass redefines totalMass = mass;
			attribute componentMasses redefines MassAnalysis::componentMasses = (engine.mass, frontAxleAssembly.mass, rearAxleAssembly.mass);		
		}	
		attribute mass redefines vehicle::mass;
		part engine redefines engine {
			attribute mass redefines mass;
		}
		part frontAxleAssembly redefines frontAxleAssembly {
			attribute mass redefines mass;
		}
		part rearAxleAssembly redefines rearAxleAssembly {
			attribute mass redefines mass;
		}
		
	}
		
	constraint def MassAnalysis2 { 
		in totalMass : MassValue;
		in componentMasses: MassValue[0..*];
		
		totalMass == sum(componentMasses)
	}
	
	part vehicle2a :> vehicle {
		assert constraint massConstraint : MassAnalysis2;
		
		attribute mass redefines mass;
		part engine redefines engine {
			attribute mass redefines mass;
		}
		part frontAxleAssembly redefines frontAxleAssembly {
			attribute mass redefines mass;
		}
		part rearAxleAssembly redefines rearAxleAssembly {
			attribute mass redefines mass;
		}
		
		bind massConstraint.totalMass = mass;
		bind massConstraint.componentMasses = engine.mass;
		bind massConstraint.componentMasses = frontAxleAssembly.mass;
		bind massConstraint.componentMasses = rearAxleAssembly.mass;
	}
		
	part vehicle2b :> vehicle {
		assert constraint massAnalysis2 : MassAnalysis2 {
			in totalMass = mass;
			in componentMasses = (engine.mass, frontAxleAssembly.mass, rearAxleAssembly.mass);
		}
		attribute mass redefines mass;
		part engine redefines engine {
			attribute mass redefines mass;
		}
		part frontAxleAssembly redefines frontAxleAssembly {
			attribute mass redefines mass;
		}
		part rearAxleAssembly redefines rearAxleAssembly {
			attribute mass redefines mass;
		}
	}
	
	constraint def MassAnalysis3 {
		in totalMass : MassValue;
		in componentMasses: MassValue[0..*];
	}
	
	constraint massAnalysis3 : MassAnalysis3 {
		in totalMass : MassValue;
		in componentMasses: MassValue[0..*];
		
		totalMass == sum(componentMasses)
	}
	
	part vehicle3 :> vehicle {
		assert massAnalysis3 {
			in totalMass = mass;
			in componentMasses = (engine.mass, frontAxleAssembly.mass, rearAxleAssembly.mass);
		}
		attribute mass redefines mass;
		part engine redefines engine {
			attribute mass redefines mass;
		}
		part frontAxleAssembly redefines frontAxleAssembly {
			attribute mass redefines mass;
		}
		part rearAxleAssembly redefines rearAxleAssembly {
			attribute mass redefines mass;
		}
	}
	
	part vehicle4 :> vehicle {
		assert constraint { mass == engine.mass + frontAxleAssembly.mass + rearAxleAssembly.mass }
		attribute mass redefines mass;
		part engine redefines engine {
			attribute mass redefines mass;
		}
		part frontAxleAssembly redefines frontAxleAssembly {
			attribute mass redefines mass;
		}
		part rearAxleAssembly redefines vehicle::rearAxleAssembly {
			attribute mass redefines mass;
		}
	}
	
}