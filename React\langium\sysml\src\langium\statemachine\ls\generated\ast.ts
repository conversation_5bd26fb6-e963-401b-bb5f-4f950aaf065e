/******************************************************************************
 * This file was generated by langium-cli 3.5.2.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/

/* eslint-disable */
import * as langium from 'langium';

export const SysmlTerminals = {
    DECIMAL_VALUE: /([0-9][0-9]*)/,
    EXP_VALUE: /((([0-9][0-9]*))(e|E)(\+|-)?(([0-9][0-9]*)))/,
    ID: /((([a-z]|[A-Z])|_)((([a-z]|[A-Z])|_)|[0-9])*)/,
    UNRESTRICTED_NAME: /('((\\(((((((b|t)|n)|f)|r)|")|')|\\))|((?!(\\|'))[\s\S]*?))*')/,
    STRING_VALUE: /("((\\(((((((b|t)|n)|f)|r)|")|')|\\))|((?!(\\|"))[\s\S]*?))*")/,
    REGULAR_COMMENT: /(\/\*([\s\S]*?\*\/))/,
    ML_NOTE: /(\/\/\*([\s\S]*?\*\/))/,
    SL_NOTE: /(\/\/(((?!(\n|\r))[\s\S]*?)((?!(\n|\r))[\s\S]*?))?(\r?\n)?)/,
    WS: /((( |	)|\r)|\n)+/,
};

export type SysmlTerminalNames = keyof typeof SysmlTerminals;

export type SysmlKeywordNames =
    | "!="
    | "!=="
    | "#"
    | "$"
    | "%"
    | "&"
    | "("
    | ")"
    | "*"
    | "**"
    | "+"
    | ","
    | "-"
    | "->"
    | "."
    | ".."
    | ".?"
    | "/"
    | ":"
    | "::"
    | "::>"
    | ":="
    | ":>"
    | ":>>"
    | ";"
    | "<"
    | "<="
    | "="
    | "=="
    | "==="
    | "=>"
    | ">"
    | ">="
    | "?"
    | "??"
    | "@"
    | "@@"
    | "["
    | "]"
    | "^"
    | "about"
    | "abstract"
    | "accept"
    | "action"
    | "actor"
    | "after"
    | "alias"
    | "all"
    | "allocate"
    | "allocation"
    | "analysis"
    | "and"
    | "as"
    | "assert"
    | "assign"
    | "assume"
    | "at"
    | "attribute"
    | "bind"
    | "binding"
    | "by"
    | "calc"
    | "case"
    | "comment"
    | "concern"
    | "connect"
    | "connection"
    | "constant"
    | "constraint"
    | "crosses"
    | "decide"
    | "def"
    | "default"
    | "defined"
    | "dependency"
    | "derived"
    | "do"
    | "doc"
    | "else"
    | "end"
    | "entry"
    | "enum"
    | "event"
    | "exhibit"
    | "exit"
    | "expose"
    | "false"
    | "filter"
    | "first"
    | "flow"
    | "for"
    | "fork"
    | "frame"
    | "from"
    | "hastype"
    | "if"
    | "implies"
    | "import"
    | "in"
    | "include"
    | "individual"
    | "inout"
    | "interface"
    | "istype"
    | "item"
    | "join"
    | "language"
    | "library"
    | "locale"
    | "loop"
    | "merge"
    | "message"
    | "meta"
    | "metadata"
    | "new"
    | "nonunique"
    | "not"
    | "null"
    | "objective"
    | "occurrence"
    | "of"
    | "or"
    | "ordered"
    | "out"
    | "package"
    | "parallel"
    | "part"
    | "perform"
    | "port"
    | "private"
    | "protected"
    | "public"
    | "redefines"
    | "ref"
    | "references"
    | "render"
    | "rendering"
    | "rep"
    | "require"
    | "requirement"
    | "return"
    | "satisfy"
    | "send"
    | "snapshot"
    | "specializes"
    | "stakeholder"
    | "standard"
    | "state"
    | "subject"
    | "subsets"
    | "succession"
    | "terminate"
    | "then"
    | "timeslice"
    | "to"
    | "transition"
    | "true"
    | "until"
    | "use"
    | "variant"
    | "variation"
    | "verification"
    | "verify"
    | "via"
    | "view"
    | "viewpoint"
    | "when"
    | "while"
    | "xor"
    | "{"
    | "|"
    | "}"
    | "~";

export type SysmlTokenNames = SysmlTerminalNames | SysmlKeywordNames;

export type ActionDefKeyword = string;

export function isActionDefKeyword(item: unknown): item is ActionDefKeyword {
    return typeof item === 'string';
}

export type ActionKeyword = 'action';

export function isActionKeyword(item: unknown): item is ActionKeyword {
    return item === 'action';
}

export type ActionUsageKeyword = ActionKeyword;


export type AdditiveOperator = '+' | '-';

export function isAdditiveOperator(item: unknown): item is AdditiveOperator {
    return item === '+' || item === '-';
}

export type AllocateKeyword = 'allocate';

export function isAllocateKeyword(item: unknown): item is AllocateKeyword {
    return item === 'allocate';
}

export type AllocationDefKeyword = string;

export function isAllocationDefKeyword(item: unknown): item is AllocationDefKeyword {
    return typeof item === 'string';
}

export type AllocationKeyword = 'allocation';

export function isAllocationKeyword(item: unknown): item is AllocationKeyword {
    return item === 'allocation';
}

export type AllocationUsageKeyword = AllocationKeyword;


export type AnalysisCaseDefKeyword = string;

export function isAnalysisCaseDefKeyword(item: unknown): item is AnalysisCaseDefKeyword {
    return typeof item === 'string';
}

export type AnalysisCaseKeyword = 'analysis';

export function isAnalysisCaseKeyword(item: unknown): item is AnalysisCaseKeyword {
    return item === 'analysis';
}

export type AnalysisCaseUsageKeyword = AnalysisCaseKeyword;


export type AndOperator = '&';

export function isAndOperator(item: unknown): item is AndOperator {
    return item === '&';
}

export type AttributeDefKeyword = string;

export function isAttributeDefKeyword(item: unknown): item is AttributeDefKeyword {
    return typeof item === 'string';
}

export type AttributeKeyword = 'attribute';

export function isAttributeKeyword(item: unknown): item is AttributeKeyword {
    return item === 'attribute';
}

export type AttributeUsageKeyword = AttributeKeyword;


export type BindingKeyword = 'binding';

export function isBindingKeyword(item: unknown): item is BindingKeyword {
    return item === 'binding';
}

export type BooleanValue = boolean;

export function isBooleanValue(item: unknown): item is BooleanValue {
    return typeof item === 'boolean';
}

export type CalculationDefKeyword = string;

export function isCalculationDefKeyword(item: unknown): item is CalculationDefKeyword {
    return typeof item === 'string';
}

export type CalculationKeyword = 'calc';

export function isCalculationKeyword(item: unknown): item is CalculationKeyword {
    return item === 'calc';
}

export type CalculationUsageKeyword = CalculationKeyword;


export type CaseDefKeyword = string;

export function isCaseDefKeyword(item: unknown): item is CaseDefKeyword {
    return typeof item === 'string';
}

export type CaseKeyword = 'case';

export function isCaseKeyword(item: unknown): item is CaseKeyword {
    return item === 'case';
}

export type CaseUsageKeyword = CaseKeyword;


export type CastOperator = 'as';

export function isCastOperator(item: unknown): item is CastOperator {
    return item === 'as';
}

export type ChangeTriggerKind = 'when';

export type ClassificationTestOperator = '@' | 'hastype' | 'istype';

export function isClassificationTestOperator(item: unknown): item is ClassificationTestOperator {
    return item === 'hastype' || item === 'istype' || item === '@';
}

export type ConcernDefKeyword = string;

export function isConcernDefKeyword(item: unknown): item is ConcernDefKeyword {
    return typeof item === 'string';
}

export type ConcernKeyword = 'concern';

export function isConcernKeyword(item: unknown): item is ConcernKeyword {
    return item === 'concern';
}

export type ConcernUsageKeyword = ConcernKeyword;


export type ConditionalAndOperator = 'and';

export function isConditionalAndOperator(item: unknown): item is ConditionalAndOperator {
    return item === 'and';
}

export type ConditionalOperator = 'if';

export function isConditionalOperator(item: unknown): item is ConditionalOperator {
    return item === 'if';
}

export type ConditionalOrOperator = 'or';

export function isConditionalOrOperator(item: unknown): item is ConditionalOrOperator {
    return item === 'or';
}

export type ConjugatedQualifiedName = string;

export function isConjugatedQualifiedName(item: unknown): item is ConjugatedQualifiedName {
    return typeof item === 'string';
}

export type ConnectionDefKeyword = string;

export function isConnectionDefKeyword(item: unknown): item is ConnectionDefKeyword {
    return typeof item === 'string';
}

export type ConnectionKeyword = 'connection';

export function isConnectionKeyword(item: unknown): item is ConnectionKeyword {
    return item === 'connection';
}

export type ConnectionUsageKeyword = ConnectionKeyword;


export type ConnectorKeyword = 'connect';

export function isConnectorKeyword(item: unknown): item is ConnectorKeyword {
    return item === 'connect';
}

export type ConstraintDefKeyword = string;

export function isConstraintDefKeyword(item: unknown): item is ConstraintDefKeyword {
    return typeof item === 'string';
}

export type ConstraintKeyword = 'constraint';

export function isConstraintKeyword(item: unknown): item is ConstraintKeyword {
    return item === 'constraint';
}

export type ConstraintUsageKeyword = ConstraintKeyword;


export type CrossesKeyword = '=>' | 'crosses';

export function isCrossesKeyword(item: unknown): item is CrossesKeyword {
    return item === '=>' || item === 'crosses';
}

export type DefinedByKeyword = string;

export function isDefinedByKeyword(item: unknown): item is DefinedByKeyword {
    return typeof item === 'string';
}

export type DoActionKind = 'do';

export type EffectFeatureKind = EffectFeatureKind_effect;

export type EffectFeatureKind_effect = 'do';

export function isEffectFeatureKind_effect(item: unknown): item is EffectFeatureKind_effect {
    return item === 'do';
}

export type EntryActionKind = 'entry';

export type EnumerationDefKeyword = string;

export function isEnumerationDefKeyword(item: unknown): item is EnumerationDefKeyword {
    return typeof item === 'string';
}

export type EnumerationKeyword = 'enum';

export function isEnumerationKeyword(item: unknown): item is EnumerationKeyword {
    return item === 'enum';
}

export type EnumerationUsageKeyword = EnumerationKeyword;


export type EqualityOperator = '!=' | '!==' | '==' | '===';

export function isEqualityOperator(item: unknown): item is EqualityOperator {
    return item === '==' || item === '!=' || item === '===' || item === '!==';
}

export type ExitActionKind = 'exit';

export type ExponentiationOperator = '**' | '^';

export function isExponentiationOperator(item: unknown): item is ExponentiationOperator {
    return item === '**' || item === '^';
}

export type FeatureDirection = FeatureDirection_in | FeatureDirection_inout | FeatureDirection_out;

export type FeatureDirection_in = 'in';

export type FeatureDirection_inout = 'inout';

export type FeatureDirection_out = 'out';

export type FeatureDirectionKind = FeatureDirectionKind_in | FeatureDirectionKind_inout | FeatureDirectionKind_out;

export type FeatureDirectionKind_in = 'in';

export type FeatureDirectionKind_inout = 'inout';

export type FeatureDirectionKind_out = 'out';

export type FlowDefKeyword = string;

export function isFlowDefKeyword(item: unknown): item is FlowDefKeyword {
    return typeof item === 'string';
}

export type FlowKeyword = 'flow';

export function isFlowKeyword(item: unknown): item is FlowKeyword {
    return item === 'flow';
}

export type FramedConcernKind = FramedConcernKind_requirement;

export type FramedConcernKind_requirement = 'frame';

export function isFramedConcernKind_requirement(item: unknown): item is FramedConcernKind_requirement {
    return item === 'frame';
}

export type GlobalQualification = string;

export function isGlobalQualification(item: unknown): item is GlobalQualification {
    return typeof item === 'string';
}

export type GuardFeatureKind = GuardFeatureKind_guard;

export type GuardFeatureKind_guard = 'if';

export function isGuardFeatureKind_guard(item: unknown): item is GuardFeatureKind_guard {
    return item === 'if';
}

export type ImpliesOperator = 'implies';

export function isImpliesOperator(item: unknown): item is ImpliesOperator {
    return item === 'implies';
}

export type InterfaceDefKeyword = string;

export function isInterfaceDefKeyword(item: unknown): item is InterfaceDefKeyword {
    return typeof item === 'string';
}

export type InterfaceKeyword = 'interface';

export function isInterfaceKeyword(item: unknown): item is InterfaceKeyword {
    return item === 'interface';
}

export type InterfaceUsageKeyword = InterfaceKeyword;


export type ItemDefKeyword = string;

export function isItemDefKeyword(item: unknown): item is ItemDefKeyword {
    return typeof item === 'string';
}

export type ItemKeyword = 'item';

export function isItemKeyword(item: unknown): item is ItemKeyword {
    return item === 'item';
}

export type ItemUsageKeyword = ItemKeyword;


export type MessageKeyword = 'message';

export function isMessageKeyword(item: unknown): item is MessageKeyword {
    return item === 'message';
}

export type MetaCastOperator = 'meta';

export function isMetaCastOperator(item: unknown): item is MetaCastOperator {
    return item === 'meta';
}

export type MetaClassificationTestOperator = '@@';

export function isMetaClassificationTestOperator(item: unknown): item is MetaClassificationTestOperator {
    return item === '@@';
}

export type MetadataDefKeyword = string;

export function isMetadataDefKeyword(item: unknown): item is MetadataDefKeyword {
    return typeof item === 'string';
}

export type MetadataKeyword = 'metadata';

export function isMetadataKeyword(item: unknown): item is MetadataKeyword {
    return item === 'metadata';
}

export type MetadataUsageKeyword = '@' | MetadataKeyword;

export function isMetadataUsageKeyword(item: unknown): item is MetadataUsageKeyword {
    return isMetadataKeyword(item) || item === '@';
}

export type MultiplicativeOperator = '%' | '*' | '/';

export function isMultiplicativeOperator(item: unknown): item is MultiplicativeOperator {
    return item === '*' || item === '/' || item === '%';
}

export type Name = string;

export function isName(item: unknown): item is Name {
    return (typeof item === 'string' && (/((([a-z]|[A-Z])|_)((([a-z]|[A-Z])|_)|[0-9])*)/.test(item) || /('((\\(((((((b|t)|n)|f)|r)|")|')|\\))|((?!(\\|'))[\s\S]*?))*')/.test(item)));
}

export type NullCoalescingOperator = '??';

export function isNullCoalescingOperator(item: unknown): item is NullCoalescingOperator {
    return item === '??';
}

export type NullExpression = 'null';

export function isNullExpression(item: unknown): item is NullExpression {
    return item === 'null';
}

export type OccurrenceDefKeyword = string;

export function isOccurrenceDefKeyword(item: unknown): item is OccurrenceDefKeyword {
    return typeof item === 'string';
}

export type OccurrenceKeyword = 'occurrence';

export function isOccurrenceKeyword(item: unknown): item is OccurrenceKeyword {
    return item === 'occurrence';
}

export type OccurrenceUsageKeyword = OccurrenceKeyword;


export type OrOperator = '|';

export function isOrOperator(item: unknown): item is OrOperator {
    return item === '|';
}

export type PartDefKeyword = string;

export function isPartDefKeyword(item: unknown): item is PartDefKeyword {
    return typeof item === 'string';
}

export type PartKeyword = 'part';

export function isPartKeyword(item: unknown): item is PartKeyword {
    return item === 'part';
}

export type PartUsageKeyword = PartKeyword;


export type PortDefKeyword = string;

export function isPortDefKeyword(item: unknown): item is PortDefKeyword {
    return typeof item === 'string';
}

export type PortionKind = PortionKind_snapshot | PortionKind_timeslice;

export type PortionKind_snapshot = 'snapshot';

export type PortionKind_timeslice = 'timeslice';

export type PortKeyword = 'port';

export function isPortKeyword(item: unknown): item is PortKeyword {
    return item === 'port';
}

export type PortUsageKeyword = PortKeyword;


export type Qualification = string;

export function isQualification(item: unknown): item is Qualification {
    return typeof item === 'string';
}

export type QualifiedName = string;

export function isQualifiedName(item: unknown): item is QualifiedName {
    return typeof item === 'string';
}

export type RealValue = number;

export function isRealValue(item: unknown): item is RealValue {
    return typeof item === 'number';
}

export type RedefinesKeyword = ':>>' | 'redefines';

export function isRedefinesKeyword(item: unknown): item is RedefinesKeyword {
    return item === ':>>' || item === 'redefines';
}

export type ReferenceKeyword = 'ref';

export function isReferenceKeyword(item: unknown): item is ReferenceKeyword {
    return item === 'ref';
}

export type ReferencesKeyword = '::>' | 'references';

export function isReferencesKeyword(item: unknown): item is ReferencesKeyword {
    return item === '::>' || item === 'references';
}

export type ReferenceUsageKeyword = ReferenceKeyword;


export type RelationalOperator = '<' | '<=' | '>' | '>=';

export function isRelationalOperator(item: unknown): item is RelationalOperator {
    return item === '<' || item === '>' || item === '<=' || item === '>=';
}

export type RenderingDefKeyword = string;

export function isRenderingDefKeyword(item: unknown): item is RenderingDefKeyword {
    return typeof item === 'string';
}

export type RenderingKeyword = 'rendering';

export function isRenderingKeyword(item: unknown): item is RenderingKeyword {
    return item === 'rendering';
}

export type RenderingUsageKeyword = RenderingKeyword;


export type RequirementConstraintKind = RequirementConstraintKind_assumption | RequirementConstraintKind_requirement;

export type RequirementConstraintKind_assumption = 'assume';

export type RequirementConstraintKind_requirement = 'require';

export type RequirementConstraintKind_verify = 'verify';

export type RequirementDefKeyword = string;

export function isRequirementDefKeyword(item: unknown): item is RequirementDefKeyword {
    return typeof item === 'string';
}

export type RequirementKeyword = 'requirement';

export function isRequirementKeyword(item: unknown): item is RequirementKeyword {
    return item === 'requirement';
}

export type RequirementUsageKeyword = RequirementKeyword;


export type SpecializesKeyword = ':>' | 'specializes';

export function isSpecializesKeyword(item: unknown): item is SpecializesKeyword {
    return item === ':>' || item === 'specializes';
}

export type StateDefKeyword = string;

export function isStateDefKeyword(item: unknown): item is StateDefKeyword {
    return typeof item === 'string';
}

export type StateKeyword = 'state';

export function isStateKeyword(item: unknown): item is StateKeyword {
    return item === 'state';
}

export type StateSubactionKind = StateSubactionKind_do | StateSubactionKind_entry | StateSubactionKind_exit;

export type StateSubactionKind_do = 'do';

export type StateSubactionKind_entry = 'entry';

export type StateSubactionKind_exit = 'exit';

export type StateUsageKeyword = StateKeyword;


export type SubsetsKeyword = ':>' | 'subsets';

export function isSubsetsKeyword(item: unknown): item is SubsetsKeyword {
    return item === ':>' || item === 'subsets';
}

export type SuccessionFlowKeyword = string;

export function isSuccessionFlowKeyword(item: unknown): item is SuccessionFlowKeyword {
    return typeof item === 'string';
}

export type SuccessionKeyword = 'succession';

export function isSuccessionKeyword(item: unknown): item is SuccessionKeyword {
    return item === 'succession';
}

export type TimeTriggerKind = 'after' | 'at';

export type TransitionFeatureKind = TransitionFeatureKind_effect | TransitionFeatureKind_guard | TransitionFeatureKind_trigger;

export type TransitionFeatureKind_effect = 'effect';

export type TransitionFeatureKind_guard = 'guard';

export type TransitionFeatureKind_trigger = 'trigger';

export type TransitionUsageKeyword = 'transition';

export function isTransitionUsageKeyword(item: unknown): item is TransitionUsageKeyword {
    return item === 'transition';
}

export type TriggerFeatureKind = TriggerFeatureKind_trigger;

export type TriggerFeatureKind_trigger = 'accept';

export function isTriggerFeatureKind_trigger(item: unknown): item is TriggerFeatureKind_trigger {
    return item === 'accept';
}

export type TriggerKind = TriggerKind_after | TriggerKind_at | TriggerKind_when;

export type TriggerKind_after = 'after';

export type TriggerKind_at = 'at';

export type TriggerKind_when = 'when';

export type UnaryOperator = '+' | '-' | 'not' | '~';

export function isUnaryOperator(item: unknown): item is UnaryOperator {
    return item === '+' || item === '-' || item === '~' || item === 'not';
}

export type UseCaseDefKeyword = string;

export function isUseCaseDefKeyword(item: unknown): item is UseCaseDefKeyword {
    return typeof item === 'string';
}

export type UseCaseKeyword = string;

export function isUseCaseKeyword(item: unknown): item is UseCaseKeyword {
    return typeof item === 'string';
}

export type UseCaseUsageKeyword = UseCaseKeyword;


export type VerificationCaseDefKeyword = string;

export function isVerificationCaseDefKeyword(item: unknown): item is VerificationCaseDefKeyword {
    return typeof item === 'string';
}

export type VerificationCaseKeyword = 'verification';

export function isVerificationCaseKeyword(item: unknown): item is VerificationCaseKeyword {
    return item === 'verification';
}

export type VerificationCaseUsageKeyword = VerificationCaseKeyword;


export type ViewDefKeyword = string;

export function isViewDefKeyword(item: unknown): item is ViewDefKeyword {
    return typeof item === 'string';
}

export type ViewKeyword = 'view';

export function isViewKeyword(item: unknown): item is ViewKeyword {
    return item === 'view';
}

export type ViewpointDefKeyword = string;

export function isViewpointDefKeyword(item: unknown): item is ViewpointDefKeyword {
    return typeof item === 'string';
}

export type ViewpointKeyword = 'viewpoint';

export function isViewpointKeyword(item: unknown): item is ViewpointKeyword {
    return item === 'viewpoint';
}

export type ViewpointUsageKeyword = ViewpointKeyword;


export type ViewUsageKeyword = ViewKeyword;


export type VisibilityIndicator = VisibilityIndicator_private | VisibilityIndicator_protected | VisibilityIndicator_public;

export type VisibilityIndicator_private = 'private';

export type VisibilityIndicator_protected = 'protected';

export type VisibilityIndicator_public = 'public';

export type VisibilityKind = VisibilityKind_private | VisibilityKind_protected | VisibilityKind_public;

export type VisibilityKind_private = 'private';

export type VisibilityKind_protected = 'protected';

export type VisibilityKind_public = 'public';

export type XorOperator = 'xor';

export function isXorOperator(item: unknown): item is XorOperator {
    return item === 'xor';
}

export interface Element extends langium.AstNode {
    readonly $type: 'AcceptActionUsage' | 'ActionDefinition' | 'ActionUsage' | 'ActorMembership' | 'AllocationDefinition' | 'AllocationUsage' | 'AnalysisCaseDefinition' | 'AnalysisCaseUsage' | 'AnnotatingElement' | 'Annotation' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'Association' | 'AssociationStructure' | 'AttributeDefinition' | 'AttributeUsage' | 'Behavior' | 'BindingConnector' | 'BindingConnectorAsUsage' | 'BooleanExpression' | 'CalculationDefinition' | 'CalculationUsage' | 'CaseDefinition' | 'CaseUsage' | 'Class' | 'Classifier' | 'CollectExpression' | 'Comment' | 'ConcernDefinition' | 'ConcernUsage' | 'ConjugatedPortDefinition' | 'ConjugatedPortTyping' | 'Conjugation' | 'ConnectionDefinition' | 'ConnectionUsage' | 'Connector' | 'ConnectorAsUsage' | 'ConstraintDefinition' | 'ConstraintUsage' | 'ControlNode' | 'CrossSubsetting' | 'DataType' | 'DecisionNode' | 'Definition' | 'Dependency' | 'Documentation' | 'Element' | 'ElementFilterMembership' | 'EndFeatureMembership' | 'EnumerationDefinition' | 'EnumerationUsage' | 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'Expose' | 'Expression' | 'Feature' | 'FeatureChainExpression' | 'FeatureChaining' | 'FeatureMembership' | 'FeatureReferenceExpression' | 'FeatureTyping' | 'FeatureValue' | 'Flow' | 'FlowDefinition' | 'FlowEnd' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'FramedConcernMembership' | 'IfActionUsage' | 'Import' | 'IncludeUseCaseUsage' | 'InstantiationExpression' | 'Interaction' | 'InterfaceDefinition' | 'InterfaceUsage' | 'Invariant' | 'InvocationExpression' | 'ItemDefinition' | 'ItemUsage' | 'JoinNode' | 'LibraryPackage' | 'LiteralBoolean' | 'LiteralInteger' | 'LiteralReal' | 'LiteralString' | 'LoopActionUsage' | 'Membership' | 'MembershipExpose' | 'MembershipImport' | 'MergeNode' | 'Metaclass' | 'MetadataAccessExpression' | 'MetadataDefinition' | 'MetadataFeature' | 'MetadataUsage' | 'Multiplicity' | 'MultiplicityRange' | 'Namespace' | 'NamespaceExpose' | 'NamespaceImport' | 'ObjectiveMembership' | 'OccurrenceDefinition' | 'OccurrenceUsage' | 'OperatorExpression' | 'OwningMembership' | 'Package' | 'ParameterMembership' | 'PartDefinition' | 'PartUsage' | 'PayloadFeature' | 'PerformActionUsage' | 'PortConjugation' | 'PortDefinition' | 'PortUsage' | 'Predicate' | 'Redefinition' | 'ReferenceSubsetting' | 'ReferenceUsage' | 'Relationship' | 'RenderingDefinition' | 'RenderingUsage' | 'RequirementConstraintMembership' | 'RequirementDefinition' | 'RequirementUsage' | 'RequirementVerificationMembership' | 'ResultExpressionMembership' | 'ReturnParameterMembership' | 'SatisfyRequirementUsage' | 'SelectExpression' | 'SendActionUsage' | 'Specialization' | 'StakeholderMembership' | 'StateDefinition' | 'StateSubactionMembership' | 'StateUsage' | 'Step' | 'Structure' | 'Subclassification' | 'SubjectMembership' | 'Subsetting' | 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage' | 'SysMLFunction' | 'TerminateActionUsage' | 'TextualRepresentation' | 'TransitionFeatureMembership' | 'TransitionUsage' | 'TriggerInvocationExpression' | 'Type' | 'Usage' | 'UseCaseDefinition' | 'UseCaseUsage' | 'VariantMembership' | 'VerificationCaseDefinition' | 'VerificationCaseUsage' | 'ViewDefinition' | 'ViewRenderingMembership' | 'ViewUsage' | 'ViewpointDefinition' | 'ViewpointUsage' | 'WhileLoopActionUsage';
    declaredName?: string;
    declaredShortName?: string;
    ownedRelationship: Array<Relationship>;
    owningRelationship?: langium.Reference<Relationship>;
}

export const Element = 'Element';

export function isElement(item: unknown): item is Element {
    return reflection.isInstance(item, Element);
}

export interface AnnotatingElement extends Element {
    readonly $type: 'AnnotatingElement' | 'Comment' | 'Documentation' | 'MetadataFeature' | 'MetadataUsage' | 'TextualRepresentation';
}

export const AnnotatingElement = 'AnnotatingElement';

export function isAnnotatingElement(item: unknown): item is AnnotatingElement {
    return reflection.isInstance(item, AnnotatingElement);
}

export interface Namespace extends Element {
    readonly $type: 'AcceptActionUsage' | 'ActionDefinition' | 'ActionUsage' | 'AllocationDefinition' | 'AllocationUsage' | 'AnalysisCaseDefinition' | 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'Association' | 'AssociationStructure' | 'AttributeDefinition' | 'AttributeUsage' | 'Behavior' | 'BindingConnector' | 'BindingConnectorAsUsage' | 'BooleanExpression' | 'CalculationDefinition' | 'CalculationUsage' | 'CaseDefinition' | 'CaseUsage' | 'Class' | 'Classifier' | 'CollectExpression' | 'ConcernDefinition' | 'ConcernUsage' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'ConnectionUsage' | 'Connector' | 'ConnectorAsUsage' | 'ConstraintDefinition' | 'ConstraintUsage' | 'ControlNode' | 'DataType' | 'DecisionNode' | 'Definition' | 'EnumerationDefinition' | 'EnumerationUsage' | 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'Expression' | 'Feature' | 'FeatureChainExpression' | 'FeatureReferenceExpression' | 'Flow' | 'FlowDefinition' | 'FlowEnd' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'InstantiationExpression' | 'Interaction' | 'InterfaceDefinition' | 'InterfaceUsage' | 'Invariant' | 'InvocationExpression' | 'ItemDefinition' | 'ItemUsage' | 'JoinNode' | 'LibraryPackage' | 'LiteralBoolean' | 'LiteralInteger' | 'LiteralReal' | 'LiteralString' | 'LoopActionUsage' | 'MergeNode' | 'Metaclass' | 'MetadataAccessExpression' | 'MetadataDefinition' | 'MetadataFeature' | 'MetadataUsage' | 'Multiplicity' | 'MultiplicityRange' | 'Namespace' | 'OccurrenceDefinition' | 'OccurrenceUsage' | 'OperatorExpression' | 'Package' | 'PartDefinition' | 'PartUsage' | 'PayloadFeature' | 'PerformActionUsage' | 'PortDefinition' | 'PortUsage' | 'Predicate' | 'ReferenceUsage' | 'RenderingDefinition' | 'RenderingUsage' | 'RequirementDefinition' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SelectExpression' | 'SendActionUsage' | 'StateDefinition' | 'StateUsage' | 'Step' | 'Structure' | 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage' | 'SysMLFunction' | 'TerminateActionUsage' | 'TransitionUsage' | 'TriggerInvocationExpression' | 'Type' | 'Usage' | 'UseCaseDefinition' | 'UseCaseUsage' | 'VerificationCaseDefinition' | 'VerificationCaseUsage' | 'ViewDefinition' | 'ViewUsage' | 'ViewpointDefinition' | 'ViewpointUsage' | 'WhileLoopActionUsage';
}

export const Namespace = 'Namespace';

export function isNamespace(item: unknown): item is Namespace {
    return reflection.isInstance(item, Namespace);
}

export interface Relationship extends Element {
    readonly $type: 'ActorMembership' | 'AllocationDefinition' | 'AllocationUsage' | 'Annotation' | 'Association' | 'AssociationStructure' | 'BindingConnector' | 'BindingConnectorAsUsage' | 'ConjugatedPortTyping' | 'Conjugation' | 'ConnectionDefinition' | 'ConnectionUsage' | 'Connector' | 'ConnectorAsUsage' | 'CrossSubsetting' | 'Dependency' | 'ElementFilterMembership' | 'EndFeatureMembership' | 'Expose' | 'FeatureChaining' | 'FeatureMembership' | 'FeatureTyping' | 'FeatureValue' | 'Flow' | 'FlowDefinition' | 'FlowUsage' | 'FramedConcernMembership' | 'Import' | 'Interaction' | 'InterfaceDefinition' | 'InterfaceUsage' | 'Membership' | 'MembershipExpose' | 'MembershipImport' | 'NamespaceExpose' | 'NamespaceImport' | 'ObjectiveMembership' | 'OwningMembership' | 'ParameterMembership' | 'PortConjugation' | 'Redefinition' | 'ReferenceSubsetting' | 'Relationship' | 'RequirementConstraintMembership' | 'RequirementVerificationMembership' | 'ResultExpressionMembership' | 'ReturnParameterMembership' | 'Specialization' | 'StakeholderMembership' | 'StateSubactionMembership' | 'Subclassification' | 'SubjectMembership' | 'Subsetting' | 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage' | 'TransitionFeatureMembership' | 'VariantMembership' | 'ViewRenderingMembership';
    ownedRelatedElement: Array<Element>;
    owningRelatedElement?: langium.Reference<Element>;
}

export const Relationship = 'Relationship';

export function isRelationship(item: unknown): item is Relationship {
    return reflection.isInstance(item, Relationship);
}

export interface Comment extends AnnotatingElement {
    readonly $type: 'Comment' | 'Documentation';
    body?: string;
    locale?: string;
}

export const Comment = 'Comment';

export function isComment(item: unknown): item is Comment {
    return reflection.isInstance(item, Comment);
}

export interface MetadataFeature extends AnnotatingElement, Feature {
    readonly $type: 'MetadataFeature' | 'MetadataUsage';
}

export const MetadataFeature = 'MetadataFeature';

export function isMetadataFeature(item: unknown): item is MetadataFeature {
    return reflection.isInstance(item, MetadataFeature);
}

export interface TextualRepresentation extends AnnotatingElement {
    readonly $type: 'TextualRepresentation';
    body: string;
    language: string;
}

export const TextualRepresentation = 'TextualRepresentation';

export function isTextualRepresentation(item: unknown): item is TextualRepresentation {
    return reflection.isInstance(item, TextualRepresentation);
}

export interface Package extends Namespace {
    readonly $type: 'LibraryPackage' | 'Package';
}

export const Package = 'Package';

export function isPackage(item: unknown): item is Package {
    return reflection.isInstance(item, Package);
}

export interface Type extends Namespace {
    readonly $type: 'AcceptActionUsage' | 'ActionDefinition' | 'ActionUsage' | 'AllocationDefinition' | 'AllocationUsage' | 'AnalysisCaseDefinition' | 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'Association' | 'AssociationStructure' | 'AttributeDefinition' | 'AttributeUsage' | 'Behavior' | 'BindingConnector' | 'BindingConnectorAsUsage' | 'BooleanExpression' | 'CalculationDefinition' | 'CalculationUsage' | 'CaseDefinition' | 'CaseUsage' | 'Class' | 'Classifier' | 'CollectExpression' | 'ConcernDefinition' | 'ConcernUsage' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'ConnectionUsage' | 'Connector' | 'ConnectorAsUsage' | 'ConstraintDefinition' | 'ConstraintUsage' | 'ControlNode' | 'DataType' | 'DecisionNode' | 'Definition' | 'EnumerationDefinition' | 'EnumerationUsage' | 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'Expression' | 'Feature' | 'FeatureChainExpression' | 'FeatureReferenceExpression' | 'Flow' | 'FlowDefinition' | 'FlowEnd' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'InstantiationExpression' | 'Interaction' | 'InterfaceDefinition' | 'InterfaceUsage' | 'Invariant' | 'InvocationExpression' | 'ItemDefinition' | 'ItemUsage' | 'JoinNode' | 'LiteralBoolean' | 'LiteralInteger' | 'LiteralReal' | 'LiteralString' | 'LoopActionUsage' | 'MergeNode' | 'Metaclass' | 'MetadataAccessExpression' | 'MetadataDefinition' | 'MetadataFeature' | 'MetadataUsage' | 'Multiplicity' | 'MultiplicityRange' | 'OccurrenceDefinition' | 'OccurrenceUsage' | 'OperatorExpression' | 'PartDefinition' | 'PartUsage' | 'PayloadFeature' | 'PerformActionUsage' | 'PortDefinition' | 'PortUsage' | 'Predicate' | 'ReferenceUsage' | 'RenderingDefinition' | 'RenderingUsage' | 'RequirementDefinition' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SelectExpression' | 'SendActionUsage' | 'StateDefinition' | 'StateUsage' | 'Step' | 'Structure' | 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage' | 'SysMLFunction' | 'TerminateActionUsage' | 'TransitionUsage' | 'TriggerInvocationExpression' | 'Type' | 'Usage' | 'UseCaseDefinition' | 'UseCaseUsage' | 'VerificationCaseDefinition' | 'VerificationCaseUsage' | 'ViewDefinition' | 'ViewUsage' | 'ViewpointDefinition' | 'ViewpointUsage' | 'WhileLoopActionUsage';
    isAbstract: boolean;
}

export const Type = 'Type';

export function isType(item: unknown): item is Type {
    return reflection.isInstance(item, Type);
}

export interface Annotation extends Relationship {
    readonly $type: 'Annotation';
    annotatedElement?: langium.Reference<Element>;
}

export const Annotation = 'Annotation';

export function isAnnotation(item: unknown): item is Annotation {
    return reflection.isInstance(item, Annotation);
}

export interface Association extends Classifier, Relationship {
    readonly $type: 'AllocationDefinition' | 'Association' | 'AssociationStructure' | 'ConnectionDefinition' | 'FlowDefinition' | 'Interaction' | 'InterfaceDefinition';
}

export const Association = 'Association';

export function isAssociation(item: unknown): item is Association {
    return reflection.isInstance(item, Association);
}

export interface Conjugation extends Relationship {
    readonly $type: 'Conjugation' | 'PortConjugation';
}

export const Conjugation = 'Conjugation';

export function isConjugation(item: unknown): item is Conjugation {
    return reflection.isInstance(item, Conjugation);
}

export interface Connector extends Feature, Relationship {
    readonly $type: 'AllocationUsage' | 'BindingConnector' | 'BindingConnectorAsUsage' | 'ConnectionUsage' | 'Connector' | 'ConnectorAsUsage' | 'Flow' | 'FlowUsage' | 'InterfaceUsage' | 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage';
}

export const Connector = 'Connector';

export function isConnector(item: unknown): item is Connector {
    return reflection.isInstance(item, Connector);
}

export interface Dependency extends Relationship {
    readonly $type: 'Dependency';
    client: Array<langium.Reference<Element>>;
    supplier: Array<langium.Reference<Element>>;
}

export const Dependency = 'Dependency';

export function isDependency(item: unknown): item is Dependency {
    return reflection.isInstance(item, Dependency);
}

export interface FeatureChaining extends Relationship {
    readonly $type: 'FeatureChaining';
    chainingFeature?: string;
}

export const FeatureChaining = 'FeatureChaining';

export function isFeatureChaining(item: unknown): item is FeatureChaining {
    return reflection.isInstance(item, FeatureChaining);
}

export interface Import extends Relationship {
    readonly $type: 'Expose' | 'Import' | 'MembershipExpose' | 'MembershipImport' | 'NamespaceExpose' | 'NamespaceImport';
    isImportAll: boolean;
    isRecursive: boolean;
    visibility?: VisibilityKind;
}

export const Import = 'Import';

export function isImport(item: unknown): item is Import {
    return reflection.isInstance(item, Import);
}

export interface Membership extends Relationship {
    readonly $type: 'ActorMembership' | 'ElementFilterMembership' | 'EndFeatureMembership' | 'FeatureMembership' | 'FeatureValue' | 'FramedConcernMembership' | 'Membership' | 'ObjectiveMembership' | 'OwningMembership' | 'ParameterMembership' | 'RequirementConstraintMembership' | 'RequirementVerificationMembership' | 'ResultExpressionMembership' | 'ReturnParameterMembership' | 'StakeholderMembership' | 'StateSubactionMembership' | 'SubjectMembership' | 'TransitionFeatureMembership' | 'VariantMembership' | 'ViewRenderingMembership';
    memberElement?: langium.Reference<Element>;
    memberName?: string;
    memberShortName?: string;
    visibility?: VisibilityKind;
}

export const Membership = 'Membership';

export function isMembership(item: unknown): item is Membership {
    return reflection.isInstance(item, Membership);
}

export interface Specialization extends Relationship {
    readonly $type: 'ConjugatedPortTyping' | 'CrossSubsetting' | 'FeatureTyping' | 'Redefinition' | 'ReferenceSubsetting' | 'Specialization' | 'Subclassification' | 'Subsetting';
}

export const Specialization = 'Specialization';

export function isSpecialization(item: unknown): item is Specialization {
    return reflection.isInstance(item, Specialization);
}

export interface Documentation extends Comment {
    readonly $type: 'Documentation';
}

export const Documentation = 'Documentation';

export function isDocumentation(item: unknown): item is Documentation {
    return reflection.isInstance(item, Documentation);
}

export interface MetadataUsage extends ItemUsage, MetadataFeature {
    readonly $type: 'MetadataUsage';
}

export const MetadataUsage = 'MetadataUsage';

export function isMetadataUsage(item: unknown): item is MetadataUsage {
    return reflection.isInstance(item, MetadataUsage);
}

export interface LibraryPackage extends Package {
    readonly $type: 'LibraryPackage';
    isStandard: boolean;
}

export const LibraryPackage = 'LibraryPackage';

export function isLibraryPackage(item: unknown): item is LibraryPackage {
    return reflection.isInstance(item, LibraryPackage);
}

export interface Classifier extends Type {
    readonly $type: 'ActionDefinition' | 'AllocationDefinition' | 'AnalysisCaseDefinition' | 'Association' | 'AssociationStructure' | 'AttributeDefinition' | 'Behavior' | 'CalculationDefinition' | 'CaseDefinition' | 'Class' | 'Classifier' | 'ConcernDefinition' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'ConstraintDefinition' | 'DataType' | 'Definition' | 'EnumerationDefinition' | 'FlowDefinition' | 'Interaction' | 'InterfaceDefinition' | 'ItemDefinition' | 'Metaclass' | 'MetadataDefinition' | 'OccurrenceDefinition' | 'PartDefinition' | 'PortDefinition' | 'Predicate' | 'RenderingDefinition' | 'RequirementDefinition' | 'StateDefinition' | 'Structure' | 'SysMLFunction' | 'UseCaseDefinition' | 'VerificationCaseDefinition' | 'ViewDefinition' | 'ViewpointDefinition';
}

export const Classifier = 'Classifier';

export function isClassifier(item: unknown): item is Classifier {
    return reflection.isInstance(item, Classifier);
}

export interface Feature extends Type {
    readonly $type: 'AcceptActionUsage' | 'ActionUsage' | 'AllocationUsage' | 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'AttributeUsage' | 'BindingConnector' | 'BindingConnectorAsUsage' | 'BooleanExpression' | 'CalculationUsage' | 'CaseUsage' | 'CollectExpression' | 'ConcernUsage' | 'ConnectionUsage' | 'Connector' | 'ConnectorAsUsage' | 'ConstraintUsage' | 'ControlNode' | 'DecisionNode' | 'EnumerationUsage' | 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'Expression' | 'Feature' | 'FeatureChainExpression' | 'FeatureReferenceExpression' | 'Flow' | 'FlowEnd' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'InstantiationExpression' | 'InterfaceUsage' | 'Invariant' | 'InvocationExpression' | 'ItemUsage' | 'JoinNode' | 'LiteralBoolean' | 'LiteralInteger' | 'LiteralReal' | 'LiteralString' | 'LoopActionUsage' | 'MergeNode' | 'MetadataAccessExpression' | 'MetadataFeature' | 'MetadataUsage' | 'Multiplicity' | 'MultiplicityRange' | 'OccurrenceUsage' | 'OperatorExpression' | 'PartUsage' | 'PayloadFeature' | 'PerformActionUsage' | 'PortUsage' | 'ReferenceUsage' | 'RenderingUsage' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SelectExpression' | 'SendActionUsage' | 'StateUsage' | 'Step' | 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage' | 'TerminateActionUsage' | 'TransitionUsage' | 'TriggerInvocationExpression' | 'Usage' | 'UseCaseUsage' | 'VerificationCaseUsage' | 'ViewUsage' | 'ViewpointUsage' | 'WhileLoopActionUsage';
    direction?: FeatureDirectionKind;
    isComposite: boolean;
    isConstant: boolean;
    isDerived: boolean;
    isEnd: boolean;
    isNonunique: boolean;
    isOrdered: boolean;
    isPortion: boolean;
    isVariation: boolean;
}

export const Feature = 'Feature';

export function isFeature(item: unknown): item is Feature {
    return reflection.isInstance(item, Feature);
}

export interface AssociationStructure extends Association, Structure {
    readonly $type: 'AllocationDefinition' | 'AssociationStructure' | 'ConnectionDefinition' | 'InterfaceDefinition';
}

export const AssociationStructure = 'AssociationStructure';

export function isAssociationStructure(item: unknown): item is AssociationStructure {
    return reflection.isInstance(item, AssociationStructure);
}

export interface Interaction extends Association, Behavior {
    readonly $type: 'FlowDefinition' | 'Interaction';
}

export const Interaction = 'Interaction';

export function isInteraction(item: unknown): item is Interaction {
    return reflection.isInstance(item, Interaction);
}

export interface PortConjugation extends Conjugation {
    readonly $type: 'PortConjugation';
}

export const PortConjugation = 'PortConjugation';

export function isPortConjugation(item: unknown): item is PortConjugation {
    return reflection.isInstance(item, PortConjugation);
}

export interface BindingConnector extends Connector {
    readonly $type: 'BindingConnector' | 'BindingConnectorAsUsage';
}

export const BindingConnector = 'BindingConnector';

export function isBindingConnector(item: unknown): item is BindingConnector {
    return reflection.isInstance(item, BindingConnector);
}

export interface ConnectorAsUsage extends Connector, Usage {
    readonly $type: 'AllocationUsage' | 'BindingConnectorAsUsage' | 'ConnectionUsage' | 'ConnectorAsUsage' | 'FlowUsage' | 'InterfaceUsage' | 'SuccessionAsUsage' | 'SuccessionFlowUsage';
}

export const ConnectorAsUsage = 'ConnectorAsUsage';

export function isConnectorAsUsage(item: unknown): item is ConnectorAsUsage {
    return reflection.isInstance(item, ConnectorAsUsage);
}

export interface Flow extends Connector, Step {
    readonly $type: 'Flow' | 'FlowUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage';
}

export const Flow = 'Flow';

export function isFlow(item: unknown): item is Flow {
    return reflection.isInstance(item, Flow);
}

export interface Succession extends Connector {
    readonly $type: 'Succession' | 'SuccessionAsUsage' | 'SuccessionFlow' | 'SuccessionFlowUsage';
}

export const Succession = 'Succession';

export function isSuccession(item: unknown): item is Succession {
    return reflection.isInstance(item, Succession);
}

export interface Expose extends Import {
    readonly $type: 'Expose' | 'MembershipExpose' | 'NamespaceExpose';
}

export const Expose = 'Expose';

export function isExpose(item: unknown): item is Expose {
    return reflection.isInstance(item, Expose);
}

export interface MembershipImport extends Import {
    readonly $type: 'MembershipExpose' | 'MembershipImport';
    importedMembership?: langium.Reference<Membership>;
}

export const MembershipImport = 'MembershipImport';

export function isMembershipImport(item: unknown): item is MembershipImport {
    return reflection.isInstance(item, MembershipImport);
}

export interface NamespaceImport extends Import {
    readonly $type: 'NamespaceExpose' | 'NamespaceImport';
    importedNamespace?: langium.Reference<Namespace>;
}

export const NamespaceImport = 'NamespaceImport';

export function isNamespaceImport(item: unknown): item is NamespaceImport {
    return reflection.isInstance(item, NamespaceImport);
}

export interface OwningMembership extends Membership {
    readonly $type: 'ActorMembership' | 'ElementFilterMembership' | 'EndFeatureMembership' | 'FeatureMembership' | 'FeatureValue' | 'FramedConcernMembership' | 'ObjectiveMembership' | 'OwningMembership' | 'ParameterMembership' | 'RequirementConstraintMembership' | 'RequirementVerificationMembership' | 'ResultExpressionMembership' | 'ReturnParameterMembership' | 'StakeholderMembership' | 'StateSubactionMembership' | 'SubjectMembership' | 'TransitionFeatureMembership' | 'VariantMembership' | 'ViewRenderingMembership';
}

export const OwningMembership = 'OwningMembership';

export function isOwningMembership(item: unknown): item is OwningMembership {
    return reflection.isInstance(item, OwningMembership);
}

export interface FeatureTyping extends Specialization {
    readonly $type: 'ConjugatedPortTyping' | 'FeatureTyping';
    type: langium.Reference<Type>;
}

export const FeatureTyping = 'FeatureTyping';

export function isFeatureTyping(item: unknown): item is FeatureTyping {
    return reflection.isInstance(item, FeatureTyping);
}

export interface Subclassification extends Specialization {
    readonly $type: 'Subclassification';
    subclassifier?: langium.Reference<Classifier>;
    superclassifier?: langium.Reference<Classifier>;
}

export const Subclassification = 'Subclassification';

export function isSubclassification(item: unknown): item is Subclassification {
    return reflection.isInstance(item, Subclassification);
}

export interface Subsetting extends Specialization {
    readonly $type: 'CrossSubsetting' | 'Redefinition' | 'ReferenceSubsetting' | 'Subsetting';
    subsettedFeature?: langium.Reference<Feature>;
}

export const Subsetting = 'Subsetting';

export function isSubsetting(item: unknown): item is Subsetting {
    return reflection.isInstance(item, Subsetting);
}

export interface Class extends Classifier {
    readonly $type: 'ActionDefinition' | 'AllocationDefinition' | 'AnalysisCaseDefinition' | 'AssociationStructure' | 'Behavior' | 'CalculationDefinition' | 'CaseDefinition' | 'Class' | 'ConcernDefinition' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'ConstraintDefinition' | 'FlowDefinition' | 'Interaction' | 'InterfaceDefinition' | 'ItemDefinition' | 'Metaclass' | 'MetadataDefinition' | 'OccurrenceDefinition' | 'PartDefinition' | 'PortDefinition' | 'Predicate' | 'RenderingDefinition' | 'RequirementDefinition' | 'StateDefinition' | 'Structure' | 'SysMLFunction' | 'UseCaseDefinition' | 'VerificationCaseDefinition' | 'ViewDefinition' | 'ViewpointDefinition';
}

export const Class = 'Class';

export function isClass(item: unknown): item is Class {
    return reflection.isInstance(item, Class);
}

export interface DataType extends Classifier {
    readonly $type: 'AttributeDefinition' | 'DataType' | 'EnumerationDefinition';
}

export const DataType = 'DataType';

export function isDataType(item: unknown): item is DataType {
    return reflection.isInstance(item, DataType);
}

export interface Definition extends Classifier {
    readonly $type: 'ActionDefinition' | 'AllocationDefinition' | 'AnalysisCaseDefinition' | 'AttributeDefinition' | 'CalculationDefinition' | 'CaseDefinition' | 'ConcernDefinition' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'ConstraintDefinition' | 'Definition' | 'EnumerationDefinition' | 'FlowDefinition' | 'InterfaceDefinition' | 'ItemDefinition' | 'MetadataDefinition' | 'OccurrenceDefinition' | 'PartDefinition' | 'PortDefinition' | 'RenderingDefinition' | 'RequirementDefinition' | 'StateDefinition' | 'UseCaseDefinition' | 'VerificationCaseDefinition' | 'ViewDefinition' | 'ViewpointDefinition';
    isVariation: boolean;
}

export const Definition = 'Definition';

export function isDefinition(item: unknown): item is Definition {
    return reflection.isInstance(item, Definition);
}

export interface FlowEnd extends Feature {
    readonly $type: 'FlowEnd';
}

export const FlowEnd = 'FlowEnd';

export function isFlowEnd(item: unknown): item is FlowEnd {
    return reflection.isInstance(item, FlowEnd);
}

export interface Multiplicity extends Feature {
    readonly $type: 'Multiplicity' | 'MultiplicityRange';
}

export const Multiplicity = 'Multiplicity';

export function isMultiplicity(item: unknown): item is Multiplicity {
    return reflection.isInstance(item, Multiplicity);
}

export interface PayloadFeature extends Feature {
    readonly $type: 'PayloadFeature';
}

export const PayloadFeature = 'PayloadFeature';

export function isPayloadFeature(item: unknown): item is PayloadFeature {
    return reflection.isInstance(item, PayloadFeature);
}

export interface Step extends Feature {
    readonly $type: 'AcceptActionUsage' | 'ActionUsage' | 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'BooleanExpression' | 'CalculationUsage' | 'CaseUsage' | 'CollectExpression' | 'ConcernUsage' | 'ConstraintUsage' | 'ControlNode' | 'DecisionNode' | 'ExhibitStateUsage' | 'Expression' | 'FeatureChainExpression' | 'FeatureReferenceExpression' | 'Flow' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'InstantiationExpression' | 'Invariant' | 'InvocationExpression' | 'JoinNode' | 'LiteralBoolean' | 'LiteralInteger' | 'LiteralReal' | 'LiteralString' | 'LoopActionUsage' | 'MergeNode' | 'MetadataAccessExpression' | 'OperatorExpression' | 'PerformActionUsage' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SelectExpression' | 'SendActionUsage' | 'StateUsage' | 'Step' | 'SuccessionFlow' | 'SuccessionFlowUsage' | 'TerminateActionUsage' | 'TransitionUsage' | 'TriggerInvocationExpression' | 'UseCaseUsage' | 'VerificationCaseUsage' | 'ViewpointUsage' | 'WhileLoopActionUsage';
}

export const Step = 'Step';

export function isStep(item: unknown): item is Step {
    return reflection.isInstance(item, Step);
}

export interface Usage extends Feature {
    readonly $type: 'AcceptActionUsage' | 'ActionUsage' | 'AllocationUsage' | 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'AttributeUsage' | 'BindingConnectorAsUsage' | 'CalculationUsage' | 'CaseUsage' | 'ConcernUsage' | 'ConnectionUsage' | 'ConnectorAsUsage' | 'ConstraintUsage' | 'ControlNode' | 'DecisionNode' | 'EnumerationUsage' | 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'InterfaceUsage' | 'ItemUsage' | 'JoinNode' | 'LoopActionUsage' | 'MergeNode' | 'MetadataUsage' | 'OccurrenceUsage' | 'PartUsage' | 'PerformActionUsage' | 'PortUsage' | 'ReferenceUsage' | 'RenderingUsage' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SendActionUsage' | 'StateUsage' | 'SuccessionAsUsage' | 'SuccessionFlowUsage' | 'TerminateActionUsage' | 'TransitionUsage' | 'Usage' | 'UseCaseUsage' | 'VerificationCaseUsage' | 'ViewUsage' | 'ViewpointUsage' | 'WhileLoopActionUsage';
    isReference: boolean;
}

export const Usage = 'Usage';

export function isUsage(item: unknown): item is Usage {
    return reflection.isInstance(item, Usage);
}

export interface ConnectionDefinition extends AssociationStructure, PartDefinition {
    readonly $type: 'AllocationDefinition' | 'ConnectionDefinition' | 'InterfaceDefinition';
}

export const ConnectionDefinition = 'ConnectionDefinition';

export function isConnectionDefinition(item: unknown): item is ConnectionDefinition {
    return reflection.isInstance(item, ConnectionDefinition);
}

export interface FlowDefinition extends ActionDefinition, Interaction {
    readonly $type: 'FlowDefinition';
}

export const FlowDefinition = 'FlowDefinition';

export function isFlowDefinition(item: unknown): item is FlowDefinition {
    return reflection.isInstance(item, FlowDefinition);
}

export interface BindingConnectorAsUsage extends BindingConnector, ConnectorAsUsage {
    readonly $type: 'BindingConnectorAsUsage';
}

export const BindingConnectorAsUsage = 'BindingConnectorAsUsage';

export function isBindingConnectorAsUsage(item: unknown): item is BindingConnectorAsUsage {
    return reflection.isInstance(item, BindingConnectorAsUsage);
}

export interface ConnectionUsage extends ConnectorAsUsage, PartUsage {
    readonly $type: 'AllocationUsage' | 'ConnectionUsage' | 'InterfaceUsage';
}

export const ConnectionUsage = 'ConnectionUsage';

export function isConnectionUsage(item: unknown): item is ConnectionUsage {
    return reflection.isInstance(item, ConnectionUsage);
}

export interface FlowUsage extends ActionUsage, ConnectorAsUsage, Flow {
    readonly $type: 'FlowUsage' | 'SuccessionFlowUsage';
}

export const FlowUsage = 'FlowUsage';

export function isFlowUsage(item: unknown): item is FlowUsage {
    return reflection.isInstance(item, FlowUsage);
}

export interface SuccessionAsUsage extends ConnectorAsUsage, Succession {
    readonly $type: 'SuccessionAsUsage';
}

export const SuccessionAsUsage = 'SuccessionAsUsage';

export function isSuccessionAsUsage(item: unknown): item is SuccessionAsUsage {
    return reflection.isInstance(item, SuccessionAsUsage);
}

export interface SuccessionFlow extends Flow, Succession {
    readonly $type: 'SuccessionFlow' | 'SuccessionFlowUsage';
}

export const SuccessionFlow = 'SuccessionFlow';

export function isSuccessionFlow(item: unknown): item is SuccessionFlow {
    return reflection.isInstance(item, SuccessionFlow);
}

export interface MembershipExpose extends Expose, MembershipImport {
    readonly $type: 'MembershipExpose';
}

export const MembershipExpose = 'MembershipExpose';

export function isMembershipExpose(item: unknown): item is MembershipExpose {
    return reflection.isInstance(item, MembershipExpose);
}

export interface NamespaceExpose extends Expose, NamespaceImport {
    readonly $type: 'NamespaceExpose';
}

export const NamespaceExpose = 'NamespaceExpose';

export function isNamespaceExpose(item: unknown): item is NamespaceExpose {
    return reflection.isInstance(item, NamespaceExpose);
}

export interface ElementFilterMembership extends OwningMembership {
    readonly $type: 'ElementFilterMembership';
}

export const ElementFilterMembership = 'ElementFilterMembership';

export function isElementFilterMembership(item: unknown): item is ElementFilterMembership {
    return reflection.isInstance(item, ElementFilterMembership);
}

export interface FeatureMembership extends OwningMembership {
    readonly $type: 'ActorMembership' | 'EndFeatureMembership' | 'FeatureMembership' | 'FramedConcernMembership' | 'ObjectiveMembership' | 'ParameterMembership' | 'RequirementConstraintMembership' | 'RequirementVerificationMembership' | 'ResultExpressionMembership' | 'ReturnParameterMembership' | 'StakeholderMembership' | 'StateSubactionMembership' | 'SubjectMembership' | 'TransitionFeatureMembership' | 'ViewRenderingMembership';
}

export const FeatureMembership = 'FeatureMembership';

export function isFeatureMembership(item: unknown): item is FeatureMembership {
    return reflection.isInstance(item, FeatureMembership);
}

export interface FeatureValue extends OwningMembership {
    readonly $type: 'FeatureValue';
    isDefault: boolean;
    isInitial: boolean;
}

export const FeatureValue = 'FeatureValue';

export function isFeatureValue(item: unknown): item is FeatureValue {
    return reflection.isInstance(item, FeatureValue);
}

export interface VariantMembership extends OwningMembership {
    readonly $type: 'VariantMembership';
}

export const VariantMembership = 'VariantMembership';

export function isVariantMembership(item: unknown): item is VariantMembership {
    return reflection.isInstance(item, VariantMembership);
}

export interface ConjugatedPortTyping extends FeatureTyping {
    readonly $type: 'ConjugatedPortTyping';
    conjugatedPortDefinition?: langium.Reference<ConjugatedPortDefinition>;
}

export const ConjugatedPortTyping = 'ConjugatedPortTyping';

export function isConjugatedPortTyping(item: unknown): item is ConjugatedPortTyping {
    return reflection.isInstance(item, ConjugatedPortTyping);
}

export interface CrossSubsetting extends Subsetting {
    readonly $type: 'CrossSubsetting';
    crossedFeature?: langium.Reference<Feature>;
}

export const CrossSubsetting = 'CrossSubsetting';

export function isCrossSubsetting(item: unknown): item is CrossSubsetting {
    return reflection.isInstance(item, CrossSubsetting);
}

export interface Redefinition extends Subsetting {
    readonly $type: 'Redefinition';
    redefinedFeature?: langium.Reference<Feature>;
    redefiningFeature?: langium.Reference<Feature>;
}

export const Redefinition = 'Redefinition';

export function isRedefinition(item: unknown): item is Redefinition {
    return reflection.isInstance(item, Redefinition);
}

export interface ReferenceSubsetting extends Subsetting {
    readonly $type: 'ReferenceSubsetting';
    referencedFeature?: langium.Reference<Feature>;
}

export const ReferenceSubsetting = 'ReferenceSubsetting';

export function isReferenceSubsetting(item: unknown): item is ReferenceSubsetting {
    return reflection.isInstance(item, ReferenceSubsetting);
}

export interface Behavior extends Class {
    readonly $type: 'ActionDefinition' | 'AnalysisCaseDefinition' | 'Behavior' | 'CalculationDefinition' | 'CaseDefinition' | 'ConcernDefinition' | 'ConstraintDefinition' | 'FlowDefinition' | 'Interaction' | 'Predicate' | 'RequirementDefinition' | 'StateDefinition' | 'SysMLFunction' | 'UseCaseDefinition' | 'VerificationCaseDefinition' | 'ViewpointDefinition';
}

export const Behavior = 'Behavior';

export function isBehavior(item: unknown): item is Behavior {
    return reflection.isInstance(item, Behavior);
}

export interface OccurrenceDefinition extends Class, Definition {
    readonly $type: 'ActionDefinition' | 'AllocationDefinition' | 'AnalysisCaseDefinition' | 'CalculationDefinition' | 'CaseDefinition' | 'ConcernDefinition' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'ConstraintDefinition' | 'FlowDefinition' | 'InterfaceDefinition' | 'ItemDefinition' | 'MetadataDefinition' | 'OccurrenceDefinition' | 'PartDefinition' | 'PortDefinition' | 'RenderingDefinition' | 'RequirementDefinition' | 'StateDefinition' | 'UseCaseDefinition' | 'VerificationCaseDefinition' | 'ViewDefinition' | 'ViewpointDefinition';
    isIndividual: boolean;
}

export const OccurrenceDefinition = 'OccurrenceDefinition';

export function isOccurrenceDefinition(item: unknown): item is OccurrenceDefinition {
    return reflection.isInstance(item, OccurrenceDefinition);
}

export interface Structure extends Class {
    readonly $type: 'AllocationDefinition' | 'AssociationStructure' | 'ConjugatedPortDefinition' | 'ConnectionDefinition' | 'InterfaceDefinition' | 'ItemDefinition' | 'Metaclass' | 'MetadataDefinition' | 'PartDefinition' | 'PortDefinition' | 'RenderingDefinition' | 'Structure' | 'ViewDefinition';
}

export const Structure = 'Structure';

export function isStructure(item: unknown): item is Structure {
    return reflection.isInstance(item, Structure);
}

export interface AttributeDefinition extends DataType, Definition {
    readonly $type: 'AttributeDefinition' | 'EnumerationDefinition';
}

export const AttributeDefinition = 'AttributeDefinition';

export function isAttributeDefinition(item: unknown): item is AttributeDefinition {
    return reflection.isInstance(item, AttributeDefinition);
}

export interface MultiplicityRange extends Multiplicity {
    readonly $type: 'MultiplicityRange';
}

export const MultiplicityRange = 'MultiplicityRange';

export function isMultiplicityRange(item: unknown): item is MultiplicityRange {
    return reflection.isInstance(item, MultiplicityRange);
}

export interface ActionUsage extends OccurrenceUsage, Step {
    readonly $type: 'AcceptActionUsage' | 'ActionUsage' | 'AnalysisCaseUsage' | 'AssignmentActionUsage' | 'CalculationUsage' | 'CaseUsage' | 'ControlNode' | 'DecisionNode' | 'ExhibitStateUsage' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'JoinNode' | 'LoopActionUsage' | 'MergeNode' | 'PerformActionUsage' | 'SendActionUsage' | 'StateUsage' | 'SuccessionFlowUsage' | 'TerminateActionUsage' | 'TransitionUsage' | 'UseCaseUsage' | 'VerificationCaseUsage' | 'WhileLoopActionUsage';
}

export const ActionUsage = 'ActionUsage';

export function isActionUsage(item: unknown): item is ActionUsage {
    return reflection.isInstance(item, ActionUsage);
}

export interface Expression extends Step {
    readonly $type: 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'BooleanExpression' | 'CalculationUsage' | 'CaseUsage' | 'CollectExpression' | 'ConcernUsage' | 'ConstraintUsage' | 'Expression' | 'FeatureChainExpression' | 'FeatureReferenceExpression' | 'IncludeUseCaseUsage' | 'InstantiationExpression' | 'Invariant' | 'InvocationExpression' | 'LiteralBoolean' | 'LiteralInteger' | 'LiteralReal' | 'LiteralString' | 'MetadataAccessExpression' | 'OperatorExpression' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SelectExpression' | 'TriggerInvocationExpression' | 'UseCaseUsage' | 'VerificationCaseUsage' | 'ViewpointUsage';
}

export const Expression = 'Expression';

export function isExpression(item: unknown): item is Expression {
    return reflection.isInstance(item, Expression);
}

export interface AttributeUsage extends Usage {
    readonly $type: 'AttributeUsage' | 'EnumerationUsage';
}

export const AttributeUsage = 'AttributeUsage';

export function isAttributeUsage(item: unknown): item is AttributeUsage {
    return reflection.isInstance(item, AttributeUsage);
}

export interface OccurrenceUsage extends Usage {
    readonly $type: 'AcceptActionUsage' | 'ActionUsage' | 'AllocationUsage' | 'AnalysisCaseUsage' | 'AssertConstraintUsage' | 'AssignmentActionUsage' | 'CalculationUsage' | 'CaseUsage' | 'ConcernUsage' | 'ConnectionUsage' | 'ConstraintUsage' | 'ControlNode' | 'DecisionNode' | 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'FlowUsage' | 'ForLoopActionUsage' | 'ForkNode' | 'IfActionUsage' | 'IncludeUseCaseUsage' | 'InterfaceUsage' | 'ItemUsage' | 'JoinNode' | 'LoopActionUsage' | 'MergeNode' | 'MetadataUsage' | 'OccurrenceUsage' | 'PartUsage' | 'PerformActionUsage' | 'PortUsage' | 'RenderingUsage' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'SendActionUsage' | 'StateUsage' | 'SuccessionFlowUsage' | 'TerminateActionUsage' | 'TransitionUsage' | 'UseCaseUsage' | 'VerificationCaseUsage' | 'ViewUsage' | 'ViewpointUsage' | 'WhileLoopActionUsage';
    isIndividual: boolean;
    portionKind?: PortionKind;
}

export const OccurrenceUsage = 'OccurrenceUsage';

export function isOccurrenceUsage(item: unknown): item is OccurrenceUsage {
    return reflection.isInstance(item, OccurrenceUsage);
}

export interface ReferenceUsage extends Usage {
    readonly $type: 'ReferenceUsage';
}

export const ReferenceUsage = 'ReferenceUsage';

export function isReferenceUsage(item: unknown): item is ReferenceUsage {
    return reflection.isInstance(item, ReferenceUsage);
}

export interface AllocationDefinition extends ConnectionDefinition {
    readonly $type: 'AllocationDefinition';
}

export const AllocationDefinition = 'AllocationDefinition';

export function isAllocationDefinition(item: unknown): item is AllocationDefinition {
    return reflection.isInstance(item, AllocationDefinition);
}

export interface InterfaceDefinition extends ConnectionDefinition {
    readonly $type: 'InterfaceDefinition';
}

export const InterfaceDefinition = 'InterfaceDefinition';

export function isInterfaceDefinition(item: unknown): item is InterfaceDefinition {
    return reflection.isInstance(item, InterfaceDefinition);
}

export interface AllocationUsage extends ConnectionUsage {
    readonly $type: 'AllocationUsage';
}

export const AllocationUsage = 'AllocationUsage';

export function isAllocationUsage(item: unknown): item is AllocationUsage {
    return reflection.isInstance(item, AllocationUsage);
}

export interface InterfaceUsage extends ConnectionUsage {
    readonly $type: 'InterfaceUsage';
}

export const InterfaceUsage = 'InterfaceUsage';

export function isInterfaceUsage(item: unknown): item is InterfaceUsage {
    return reflection.isInstance(item, InterfaceUsage);
}

export interface SuccessionFlowUsage extends FlowUsage, SuccessionFlow {
    readonly $type: 'SuccessionFlowUsage';
}

export const SuccessionFlowUsage = 'SuccessionFlowUsage';

export function isSuccessionFlowUsage(item: unknown): item is SuccessionFlowUsage {
    return reflection.isInstance(item, SuccessionFlowUsage);
}

export interface EndFeatureMembership extends FeatureMembership {
    readonly $type: 'EndFeatureMembership';
}

export const EndFeatureMembership = 'EndFeatureMembership';

export function isEndFeatureMembership(item: unknown): item is EndFeatureMembership {
    return reflection.isInstance(item, EndFeatureMembership);
}

export interface ObjectiveMembership extends FeatureMembership {
    readonly $type: 'ObjectiveMembership';
}

export const ObjectiveMembership = 'ObjectiveMembership';

export function isObjectiveMembership(item: unknown): item is ObjectiveMembership {
    return reflection.isInstance(item, ObjectiveMembership);
}

export interface ParameterMembership extends FeatureMembership {
    readonly $type: 'ActorMembership' | 'ParameterMembership' | 'ReturnParameterMembership' | 'StakeholderMembership' | 'SubjectMembership';
}

export const ParameterMembership = 'ParameterMembership';

export function isParameterMembership(item: unknown): item is ParameterMembership {
    return reflection.isInstance(item, ParameterMembership);
}

export interface RequirementConstraintMembership extends FeatureMembership {
    readonly $type: 'FramedConcernMembership' | 'RequirementConstraintMembership' | 'RequirementVerificationMembership';
    kind?: RequirementConstraintKind;
}

export const RequirementConstraintMembership = 'RequirementConstraintMembership';

export function isRequirementConstraintMembership(item: unknown): item is RequirementConstraintMembership {
    return reflection.isInstance(item, RequirementConstraintMembership);
}

export interface ResultExpressionMembership extends FeatureMembership {
    readonly $type: 'ResultExpressionMembership';
}

export const ResultExpressionMembership = 'ResultExpressionMembership';

export function isResultExpressionMembership(item: unknown): item is ResultExpressionMembership {
    return reflection.isInstance(item, ResultExpressionMembership);
}

export interface StateSubactionMembership extends FeatureMembership {
    readonly $type: 'StateSubactionMembership';
    kind?: StateSubactionKind;
}

export const StateSubactionMembership = 'StateSubactionMembership';

export function isStateSubactionMembership(item: unknown): item is StateSubactionMembership {
    return reflection.isInstance(item, StateSubactionMembership);
}

export interface TransitionFeatureMembership extends FeatureMembership {
    readonly $type: 'TransitionFeatureMembership';
    kind?: TransitionFeatureKind;
}

export const TransitionFeatureMembership = 'TransitionFeatureMembership';

export function isTransitionFeatureMembership(item: unknown): item is TransitionFeatureMembership {
    return reflection.isInstance(item, TransitionFeatureMembership);
}

export interface ViewRenderingMembership extends FeatureMembership {
    readonly $type: 'ViewRenderingMembership';
}

export const ViewRenderingMembership = 'ViewRenderingMembership';

export function isViewRenderingMembership(item: unknown): item is ViewRenderingMembership {
    return reflection.isInstance(item, ViewRenderingMembership);
}

export interface ActionDefinition extends Behavior, OccurrenceDefinition {
    readonly $type: 'ActionDefinition' | 'AnalysisCaseDefinition' | 'CalculationDefinition' | 'CaseDefinition' | 'FlowDefinition' | 'StateDefinition' | 'UseCaseDefinition' | 'VerificationCaseDefinition';
}

export const ActionDefinition = 'ActionDefinition';

export function isActionDefinition(item: unknown): item is ActionDefinition {
    return reflection.isInstance(item, ActionDefinition);
}

export interface SysMLFunction extends Behavior {
    readonly $type: 'AnalysisCaseDefinition' | 'CalculationDefinition' | 'CaseDefinition' | 'ConcernDefinition' | 'ConstraintDefinition' | 'Predicate' | 'RequirementDefinition' | 'SysMLFunction' | 'UseCaseDefinition' | 'VerificationCaseDefinition' | 'ViewpointDefinition';
}

export const SysMLFunction = 'SysMLFunction';

export function isSysMLFunction(item: unknown): item is SysMLFunction {
    return reflection.isInstance(item, SysMLFunction);
}

export interface ConstraintDefinition extends OccurrenceDefinition, Predicate {
    readonly $type: 'ConcernDefinition' | 'ConstraintDefinition' | 'RequirementDefinition' | 'ViewpointDefinition';
}

export const ConstraintDefinition = 'ConstraintDefinition';

export function isConstraintDefinition(item: unknown): item is ConstraintDefinition {
    return reflection.isInstance(item, ConstraintDefinition);
}

export interface ItemDefinition extends OccurrenceDefinition, Structure {
    readonly $type: 'AllocationDefinition' | 'ConnectionDefinition' | 'InterfaceDefinition' | 'ItemDefinition' | 'MetadataDefinition' | 'PartDefinition' | 'RenderingDefinition' | 'ViewDefinition';
}

export const ItemDefinition = 'ItemDefinition';

export function isItemDefinition(item: unknown): item is ItemDefinition {
    return reflection.isInstance(item, ItemDefinition);
}

export interface PortDefinition extends OccurrenceDefinition, Structure {
    readonly $type: 'ConjugatedPortDefinition' | 'PortDefinition';
}

export const PortDefinition = 'PortDefinition';

export function isPortDefinition(item: unknown): item is PortDefinition {
    return reflection.isInstance(item, PortDefinition);
}

export interface Metaclass extends Structure {
    readonly $type: 'Metaclass' | 'MetadataDefinition';
}

export const Metaclass = 'Metaclass';

export function isMetaclass(item: unknown): item is Metaclass {
    return reflection.isInstance(item, Metaclass);
}

export interface EnumerationDefinition extends AttributeDefinition {
    readonly $type: 'EnumerationDefinition';
}

export const EnumerationDefinition = 'EnumerationDefinition';

export function isEnumerationDefinition(item: unknown): item is EnumerationDefinition {
    return reflection.isInstance(item, EnumerationDefinition);
}

export interface AcceptActionUsage extends ActionUsage {
    readonly $type: 'AcceptActionUsage';
}

export const AcceptActionUsage = 'AcceptActionUsage';

export function isAcceptActionUsage(item: unknown): item is AcceptActionUsage {
    return reflection.isInstance(item, AcceptActionUsage);
}

export interface AssignmentActionUsage extends ActionUsage {
    readonly $type: 'AssignmentActionUsage';
}

export const AssignmentActionUsage = 'AssignmentActionUsage';

export function isAssignmentActionUsage(item: unknown): item is AssignmentActionUsage {
    return reflection.isInstance(item, AssignmentActionUsage);
}

export interface CalculationUsage extends ActionUsage, Expression {
    readonly $type: 'AnalysisCaseUsage' | 'CalculationUsage' | 'CaseUsage' | 'IncludeUseCaseUsage' | 'UseCaseUsage' | 'VerificationCaseUsage';
}

export const CalculationUsage = 'CalculationUsage';

export function isCalculationUsage(item: unknown): item is CalculationUsage {
    return reflection.isInstance(item, CalculationUsage);
}

export interface ControlNode extends ActionUsage {
    readonly $type: 'ControlNode' | 'DecisionNode' | 'ForkNode' | 'JoinNode' | 'MergeNode';
}

export const ControlNode = 'ControlNode';

export function isControlNode(item: unknown): item is ControlNode {
    return reflection.isInstance(item, ControlNode);
}

export interface IfActionUsage extends ActionUsage {
    readonly $type: 'IfActionUsage';
}

export const IfActionUsage = 'IfActionUsage';

export function isIfActionUsage(item: unknown): item is IfActionUsage {
    return reflection.isInstance(item, IfActionUsage);
}

export interface LoopActionUsage extends ActionUsage {
    readonly $type: 'ForLoopActionUsage' | 'LoopActionUsage' | 'WhileLoopActionUsage';
}

export const LoopActionUsage = 'LoopActionUsage';

export function isLoopActionUsage(item: unknown): item is LoopActionUsage {
    return reflection.isInstance(item, LoopActionUsage);
}

export interface PerformActionUsage extends ActionUsage, EventOccurrenceUsage {
    readonly $type: 'ExhibitStateUsage' | 'IncludeUseCaseUsage' | 'PerformActionUsage';
}

export const PerformActionUsage = 'PerformActionUsage';

export function isPerformActionUsage(item: unknown): item is PerformActionUsage {
    return reflection.isInstance(item, PerformActionUsage);
}

export interface SendActionUsage extends ActionUsage {
    readonly $type: 'SendActionUsage';
}

export const SendActionUsage = 'SendActionUsage';

export function isSendActionUsage(item: unknown): item is SendActionUsage {
    return reflection.isInstance(item, SendActionUsage);
}

export interface StateUsage extends ActionUsage {
    readonly $type: 'ExhibitStateUsage' | 'StateUsage';
    isParallel: boolean;
}

export const StateUsage = 'StateUsage';

export function isStateUsage(item: unknown): item is StateUsage {
    return reflection.isInstance(item, StateUsage);
}

export interface TerminateActionUsage extends ActionUsage {
    readonly $type: 'TerminateActionUsage';
}

export const TerminateActionUsage = 'TerminateActionUsage';

export function isTerminateActionUsage(item: unknown): item is TerminateActionUsage {
    return reflection.isInstance(item, TerminateActionUsage);
}

export interface TransitionUsage extends ActionUsage {
    readonly $type: 'TransitionUsage';
}

export const TransitionUsage = 'TransitionUsage';

export function isTransitionUsage(item: unknown): item is TransitionUsage {
    return reflection.isInstance(item, TransitionUsage);
}

export interface BooleanExpression extends Expression {
    readonly $type: 'AssertConstraintUsage' | 'BooleanExpression' | 'ConcernUsage' | 'ConstraintUsage' | 'Invariant' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'ViewpointUsage';
}

export const BooleanExpression = 'BooleanExpression';

export function isBooleanExpression(item: unknown): item is BooleanExpression {
    return reflection.isInstance(item, BooleanExpression);
}

export interface FeatureReferenceExpression extends Expression {
    readonly $type: 'FeatureReferenceExpression';
}

export const FeatureReferenceExpression = 'FeatureReferenceExpression';

export function isFeatureReferenceExpression(item: unknown): item is FeatureReferenceExpression {
    return reflection.isInstance(item, FeatureReferenceExpression);
}

export interface InstantiationExpression extends Expression {
    readonly $type: 'CollectExpression' | 'FeatureChainExpression' | 'InstantiationExpression' | 'InvocationExpression' | 'OperatorExpression' | 'SelectExpression' | 'TriggerInvocationExpression';
}

export const InstantiationExpression = 'InstantiationExpression';

export function isInstantiationExpression(item: unknown): item is InstantiationExpression {
    return reflection.isInstance(item, InstantiationExpression);
}

export interface LiteralBoolean extends Expression {
    readonly $type: 'LiteralBoolean';
    value: BooleanValue;
}

export const LiteralBoolean = 'LiteralBoolean';

export function isLiteralBoolean(item: unknown): item is LiteralBoolean {
    return reflection.isInstance(item, LiteralBoolean);
}

export interface LiteralInteger extends Expression {
    readonly $type: 'LiteralInteger';
    value: number;
}

export const LiteralInteger = 'LiteralInteger';

export function isLiteralInteger(item: unknown): item is LiteralInteger {
    return reflection.isInstance(item, LiteralInteger);
}

export interface LiteralReal extends Expression {
    readonly $type: 'LiteralReal';
    value: RealValue;
}

export const LiteralReal = 'LiteralReal';

export function isLiteralReal(item: unknown): item is LiteralReal {
    return reflection.isInstance(item, LiteralReal);
}

export interface LiteralString extends Expression {
    readonly $type: 'LiteralString';
    value: string;
}

export const LiteralString = 'LiteralString';

export function isLiteralString(item: unknown): item is LiteralString {
    return reflection.isInstance(item, LiteralString);
}

export interface MetadataAccessExpression extends Expression {
    readonly $type: 'MetadataAccessExpression';
}

export const MetadataAccessExpression = 'MetadataAccessExpression';

export function isMetadataAccessExpression(item: unknown): item is MetadataAccessExpression {
    return reflection.isInstance(item, MetadataAccessExpression);
}

export interface EnumerationUsage extends AttributeUsage {
    readonly $type: 'EnumerationUsage';
}

export const EnumerationUsage = 'EnumerationUsage';

export function isEnumerationUsage(item: unknown): item is EnumerationUsage {
    return reflection.isInstance(item, EnumerationUsage);
}

export interface ConstraintUsage extends BooleanExpression, OccurrenceUsage {
    readonly $type: 'AssertConstraintUsage' | 'ConcernUsage' | 'ConstraintUsage' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'ViewpointUsage';
}

export const ConstraintUsage = 'ConstraintUsage';

export function isConstraintUsage(item: unknown): item is ConstraintUsage {
    return reflection.isInstance(item, ConstraintUsage);
}

export interface EventOccurrenceUsage extends OccurrenceUsage {
    readonly $type: 'EventOccurrenceUsage' | 'ExhibitStateUsage' | 'IncludeUseCaseUsage' | 'PerformActionUsage';
}

export const EventOccurrenceUsage = 'EventOccurrenceUsage';

export function isEventOccurrenceUsage(item: unknown): item is EventOccurrenceUsage {
    return reflection.isInstance(item, EventOccurrenceUsage);
}

export interface ItemUsage extends OccurrenceUsage {
    readonly $type: 'AllocationUsage' | 'ConnectionUsage' | 'InterfaceUsage' | 'ItemUsage' | 'MetadataUsage' | 'PartUsage' | 'RenderingUsage' | 'ViewUsage';
}

export const ItemUsage = 'ItemUsage';

export function isItemUsage(item: unknown): item is ItemUsage {
    return reflection.isInstance(item, ItemUsage);
}

export interface PortUsage extends OccurrenceUsage {
    readonly $type: 'PortUsage';
}

export const PortUsage = 'PortUsage';

export function isPortUsage(item: unknown): item is PortUsage {
    return reflection.isInstance(item, PortUsage);
}

export interface ActorMembership extends ParameterMembership {
    readonly $type: 'ActorMembership';
}

export const ActorMembership = 'ActorMembership';

export function isActorMembership(item: unknown): item is ActorMembership {
    return reflection.isInstance(item, ActorMembership);
}

export interface ReturnParameterMembership extends ParameterMembership {
    readonly $type: 'ReturnParameterMembership';
}

export const ReturnParameterMembership = 'ReturnParameterMembership';

export function isReturnParameterMembership(item: unknown): item is ReturnParameterMembership {
    return reflection.isInstance(item, ReturnParameterMembership);
}

export interface StakeholderMembership extends ParameterMembership {
    readonly $type: 'StakeholderMembership';
}

export const StakeholderMembership = 'StakeholderMembership';

export function isStakeholderMembership(item: unknown): item is StakeholderMembership {
    return reflection.isInstance(item, StakeholderMembership);
}

export interface SubjectMembership extends ParameterMembership {
    readonly $type: 'SubjectMembership';
}

export const SubjectMembership = 'SubjectMembership';

export function isSubjectMembership(item: unknown): item is SubjectMembership {
    return reflection.isInstance(item, SubjectMembership);
}

export interface FramedConcernMembership extends RequirementConstraintMembership {
    readonly $type: 'FramedConcernMembership';
}

export const FramedConcernMembership = 'FramedConcernMembership';

export function isFramedConcernMembership(item: unknown): item is FramedConcernMembership {
    return reflection.isInstance(item, FramedConcernMembership);
}

export interface RequirementVerificationMembership extends RequirementConstraintMembership {
    readonly $type: 'RequirementVerificationMembership';
}

export const RequirementVerificationMembership = 'RequirementVerificationMembership';

export function isRequirementVerificationMembership(item: unknown): item is RequirementVerificationMembership {
    return reflection.isInstance(item, RequirementVerificationMembership);
}

export interface CalculationDefinition extends ActionDefinition, SysMLFunction {
    readonly $type: 'AnalysisCaseDefinition' | 'CalculationDefinition' | 'CaseDefinition' | 'UseCaseDefinition' | 'VerificationCaseDefinition';
}

export const CalculationDefinition = 'CalculationDefinition';

export function isCalculationDefinition(item: unknown): item is CalculationDefinition {
    return reflection.isInstance(item, CalculationDefinition);
}

export interface StateDefinition extends ActionDefinition {
    readonly $type: 'StateDefinition';
    isParallel: boolean;
}

export const StateDefinition = 'StateDefinition';

export function isStateDefinition(item: unknown): item is StateDefinition {
    return reflection.isInstance(item, StateDefinition);
}

export interface Predicate extends SysMLFunction {
    readonly $type: 'ConcernDefinition' | 'ConstraintDefinition' | 'Predicate' | 'RequirementDefinition' | 'ViewpointDefinition';
}

export const Predicate = 'Predicate';

export function isPredicate(item: unknown): item is Predicate {
    return reflection.isInstance(item, Predicate);
}

export interface RequirementDefinition extends ConstraintDefinition {
    readonly $type: 'ConcernDefinition' | 'RequirementDefinition' | 'ViewpointDefinition';
}

export const RequirementDefinition = 'RequirementDefinition';

export function isRequirementDefinition(item: unknown): item is RequirementDefinition {
    return reflection.isInstance(item, RequirementDefinition);
}

export interface MetadataDefinition extends ItemDefinition, Metaclass {
    readonly $type: 'MetadataDefinition';
}

export const MetadataDefinition = 'MetadataDefinition';

export function isMetadataDefinition(item: unknown): item is MetadataDefinition {
    return reflection.isInstance(item, MetadataDefinition);
}

export interface PartDefinition extends ItemDefinition {
    readonly $type: 'AllocationDefinition' | 'ConnectionDefinition' | 'InterfaceDefinition' | 'PartDefinition' | 'RenderingDefinition' | 'ViewDefinition';
}

export const PartDefinition = 'PartDefinition';

export function isPartDefinition(item: unknown): item is PartDefinition {
    return reflection.isInstance(item, PartDefinition);
}

export interface ConjugatedPortDefinition extends PortDefinition {
    readonly $type: 'ConjugatedPortDefinition';
}

export const ConjugatedPortDefinition = 'ConjugatedPortDefinition';

export function isConjugatedPortDefinition(item: unknown): item is ConjugatedPortDefinition {
    return reflection.isInstance(item, ConjugatedPortDefinition);
}

export interface CaseUsage extends CalculationUsage {
    readonly $type: 'AnalysisCaseUsage' | 'CaseUsage' | 'IncludeUseCaseUsage' | 'UseCaseUsage' | 'VerificationCaseUsage';
}

export const CaseUsage = 'CaseUsage';

export function isCaseUsage(item: unknown): item is CaseUsage {
    return reflection.isInstance(item, CaseUsage);
}

export interface DecisionNode extends ControlNode {
    readonly $type: 'DecisionNode';
}

export const DecisionNode = 'DecisionNode';

export function isDecisionNode(item: unknown): item is DecisionNode {
    return reflection.isInstance(item, DecisionNode);
}

export interface ForkNode extends ControlNode {
    readonly $type: 'ForkNode';
}

export const ForkNode = 'ForkNode';

export function isForkNode(item: unknown): item is ForkNode {
    return reflection.isInstance(item, ForkNode);
}

export interface JoinNode extends ControlNode {
    readonly $type: 'JoinNode';
}

export const JoinNode = 'JoinNode';

export function isJoinNode(item: unknown): item is JoinNode {
    return reflection.isInstance(item, JoinNode);
}

export interface MergeNode extends ControlNode {
    readonly $type: 'MergeNode';
}

export const MergeNode = 'MergeNode';

export function isMergeNode(item: unknown): item is MergeNode {
    return reflection.isInstance(item, MergeNode);
}

export interface ForLoopActionUsage extends LoopActionUsage {
    readonly $type: 'ForLoopActionUsage';
}

export const ForLoopActionUsage = 'ForLoopActionUsage';

export function isForLoopActionUsage(item: unknown): item is ForLoopActionUsage {
    return reflection.isInstance(item, ForLoopActionUsage);
}

export interface WhileLoopActionUsage extends LoopActionUsage {
    readonly $type: 'WhileLoopActionUsage';
}

export const WhileLoopActionUsage = 'WhileLoopActionUsage';

export function isWhileLoopActionUsage(item: unknown): item is WhileLoopActionUsage {
    return reflection.isInstance(item, WhileLoopActionUsage);
}

export interface ExhibitStateUsage extends PerformActionUsage, StateUsage {
    readonly $type: 'ExhibitStateUsage';
}

export const ExhibitStateUsage = 'ExhibitStateUsage';

export function isExhibitStateUsage(item: unknown): item is ExhibitStateUsage {
    return reflection.isInstance(item, ExhibitStateUsage);
}

export interface IncludeUseCaseUsage extends PerformActionUsage, UseCaseUsage {
    readonly $type: 'IncludeUseCaseUsage';
}

export const IncludeUseCaseUsage = 'IncludeUseCaseUsage';

export function isIncludeUseCaseUsage(item: unknown): item is IncludeUseCaseUsage {
    return reflection.isInstance(item, IncludeUseCaseUsage);
}

export interface Invariant extends BooleanExpression {
    readonly $type: 'AssertConstraintUsage' | 'Invariant' | 'SatisfyRequirementUsage';
    isNegated: boolean;
}

export const Invariant = 'Invariant';

export function isInvariant(item: unknown): item is Invariant {
    return reflection.isInstance(item, Invariant);
}

export interface InvocationExpression extends InstantiationExpression {
    readonly $type: 'CollectExpression' | 'FeatureChainExpression' | 'InvocationExpression' | 'OperatorExpression' | 'SelectExpression' | 'TriggerInvocationExpression';
}

export const InvocationExpression = 'InvocationExpression';

export function isInvocationExpression(item: unknown): item is InvocationExpression {
    return reflection.isInstance(item, InvocationExpression);
}

export interface AssertConstraintUsage extends ConstraintUsage, Invariant {
    readonly $type: 'AssertConstraintUsage' | 'SatisfyRequirementUsage';
}

export const AssertConstraintUsage = 'AssertConstraintUsage';

export function isAssertConstraintUsage(item: unknown): item is AssertConstraintUsage {
    return reflection.isInstance(item, AssertConstraintUsage);
}

export interface RequirementUsage extends ConstraintUsage {
    readonly $type: 'ConcernUsage' | 'RequirementUsage' | 'SatisfyRequirementUsage' | 'ViewpointUsage';
}

export const RequirementUsage = 'RequirementUsage';

export function isRequirementUsage(item: unknown): item is RequirementUsage {
    return reflection.isInstance(item, RequirementUsage);
}

export interface PartUsage extends ItemUsage {
    readonly $type: 'AllocationUsage' | 'ConnectionUsage' | 'InterfaceUsage' | 'PartUsage' | 'RenderingUsage' | 'ViewUsage';
}

export const PartUsage = 'PartUsage';

export function isPartUsage(item: unknown): item is PartUsage {
    return reflection.isInstance(item, PartUsage);
}

export interface CaseDefinition extends CalculationDefinition {
    readonly $type: 'AnalysisCaseDefinition' | 'CaseDefinition' | 'UseCaseDefinition' | 'VerificationCaseDefinition';
}

export const CaseDefinition = 'CaseDefinition';

export function isCaseDefinition(item: unknown): item is CaseDefinition {
    return reflection.isInstance(item, CaseDefinition);
}

export interface ConcernDefinition extends RequirementDefinition {
    readonly $type: 'ConcernDefinition';
}

export const ConcernDefinition = 'ConcernDefinition';

export function isConcernDefinition(item: unknown): item is ConcernDefinition {
    return reflection.isInstance(item, ConcernDefinition);
}

export interface ViewpointDefinition extends RequirementDefinition {
    readonly $type: 'ViewpointDefinition';
}

export const ViewpointDefinition = 'ViewpointDefinition';

export function isViewpointDefinition(item: unknown): item is ViewpointDefinition {
    return reflection.isInstance(item, ViewpointDefinition);
}

export interface RenderingDefinition extends PartDefinition {
    readonly $type: 'RenderingDefinition';
}

export const RenderingDefinition = 'RenderingDefinition';

export function isRenderingDefinition(item: unknown): item is RenderingDefinition {
    return reflection.isInstance(item, RenderingDefinition);
}

export interface ViewDefinition extends PartDefinition {
    readonly $type: 'ViewDefinition';
}

export const ViewDefinition = 'ViewDefinition';

export function isViewDefinition(item: unknown): item is ViewDefinition {
    return reflection.isInstance(item, ViewDefinition);
}

export interface AnalysisCaseUsage extends CaseUsage {
    readonly $type: 'AnalysisCaseUsage';
}

export const AnalysisCaseUsage = 'AnalysisCaseUsage';

export function isAnalysisCaseUsage(item: unknown): item is AnalysisCaseUsage {
    return reflection.isInstance(item, AnalysisCaseUsage);
}

export interface UseCaseUsage extends CaseUsage {
    readonly $type: 'IncludeUseCaseUsage' | 'UseCaseUsage';
}

export const UseCaseUsage = 'UseCaseUsage';

export function isUseCaseUsage(item: unknown): item is UseCaseUsage {
    return reflection.isInstance(item, UseCaseUsage);
}

export interface VerificationCaseUsage extends CaseUsage {
    readonly $type: 'VerificationCaseUsage';
}

export const VerificationCaseUsage = 'VerificationCaseUsage';

export function isVerificationCaseUsage(item: unknown): item is VerificationCaseUsage {
    return reflection.isInstance(item, VerificationCaseUsage);
}

export interface OperatorExpression extends InvocationExpression {
    readonly $type: 'CollectExpression' | 'FeatureChainExpression' | 'OperatorExpression' | 'SelectExpression';
    operand: Array<Expression>;
    operator?: string;
}

export const OperatorExpression = 'OperatorExpression';

export function isOperatorExpression(item: unknown): item is OperatorExpression {
    return reflection.isInstance(item, OperatorExpression);
}

export interface TriggerInvocationExpression extends InvocationExpression {
    readonly $type: 'TriggerInvocationExpression';
    kind?: TriggerKind;
}

export const TriggerInvocationExpression = 'TriggerInvocationExpression';

export function isTriggerInvocationExpression(item: unknown): item is TriggerInvocationExpression {
    return reflection.isInstance(item, TriggerInvocationExpression);
}

export interface SatisfyRequirementUsage extends AssertConstraintUsage, RequirementUsage {
    readonly $type: 'SatisfyRequirementUsage';
}

export const SatisfyRequirementUsage = 'SatisfyRequirementUsage';

export function isSatisfyRequirementUsage(item: unknown): item is SatisfyRequirementUsage {
    return reflection.isInstance(item, SatisfyRequirementUsage);
}

export interface ConcernUsage extends RequirementUsage {
    readonly $type: 'ConcernUsage';
}

export const ConcernUsage = 'ConcernUsage';

export function isConcernUsage(item: unknown): item is ConcernUsage {
    return reflection.isInstance(item, ConcernUsage);
}

export interface ViewpointUsage extends RequirementUsage {
    readonly $type: 'ViewpointUsage';
}

export const ViewpointUsage = 'ViewpointUsage';

export function isViewpointUsage(item: unknown): item is ViewpointUsage {
    return reflection.isInstance(item, ViewpointUsage);
}

export interface RenderingUsage extends PartUsage {
    readonly $type: 'RenderingUsage';
}

export const RenderingUsage = 'RenderingUsage';

export function isRenderingUsage(item: unknown): item is RenderingUsage {
    return reflection.isInstance(item, RenderingUsage);
}

export interface ViewUsage extends PartUsage {
    readonly $type: 'ViewUsage';
}

export const ViewUsage = 'ViewUsage';

export function isViewUsage(item: unknown): item is ViewUsage {
    return reflection.isInstance(item, ViewUsage);
}

export interface AnalysisCaseDefinition extends CaseDefinition {
    readonly $type: 'AnalysisCaseDefinition';
}

export const AnalysisCaseDefinition = 'AnalysisCaseDefinition';

export function isAnalysisCaseDefinition(item: unknown): item is AnalysisCaseDefinition {
    return reflection.isInstance(item, AnalysisCaseDefinition);
}

export interface UseCaseDefinition extends CaseDefinition {
    readonly $type: 'UseCaseDefinition';
}

export const UseCaseDefinition = 'UseCaseDefinition';

export function isUseCaseDefinition(item: unknown): item is UseCaseDefinition {
    return reflection.isInstance(item, UseCaseDefinition);
}

export interface VerificationCaseDefinition extends CaseDefinition {
    readonly $type: 'VerificationCaseDefinition';
}

export const VerificationCaseDefinition = 'VerificationCaseDefinition';

export function isVerificationCaseDefinition(item: unknown): item is VerificationCaseDefinition {
    return reflection.isInstance(item, VerificationCaseDefinition);
}

export interface CollectExpression extends OperatorExpression {
    readonly $type: 'CollectExpression';
}

export const CollectExpression = 'CollectExpression';

export function isCollectExpression(item: unknown): item is CollectExpression {
    return reflection.isInstance(item, CollectExpression);
}

export interface FeatureChainExpression extends OperatorExpression {
    readonly $type: 'FeatureChainExpression';
}

export const FeatureChainExpression = 'FeatureChainExpression';

export function isFeatureChainExpression(item: unknown): item is FeatureChainExpression {
    return reflection.isInstance(item, FeatureChainExpression);
}

export interface SelectExpression extends OperatorExpression {
    readonly $type: 'SelectExpression';
}

export const SelectExpression = 'SelectExpression';

export function isSelectExpression(item: unknown): item is SelectExpression {
    return reflection.isInstance(item, SelectExpression);
}

export type SysmlAstType = {
    AcceptActionUsage: AcceptActionUsage
    ActionDefinition: ActionDefinition
    ActionUsage: ActionUsage
    ActorMembership: ActorMembership
    AllocationDefinition: AllocationDefinition
    AllocationUsage: AllocationUsage
    AnalysisCaseDefinition: AnalysisCaseDefinition
    AnalysisCaseUsage: AnalysisCaseUsage
    AnnotatingElement: AnnotatingElement
    Annotation: Annotation
    AssertConstraintUsage: AssertConstraintUsage
    AssignmentActionUsage: AssignmentActionUsage
    Association: Association
    AssociationStructure: AssociationStructure
    AttributeDefinition: AttributeDefinition
    AttributeUsage: AttributeUsage
    Behavior: Behavior
    BindingConnector: BindingConnector
    BindingConnectorAsUsage: BindingConnectorAsUsage
    BooleanExpression: BooleanExpression
    CalculationDefinition: CalculationDefinition
    CalculationUsage: CalculationUsage
    CaseDefinition: CaseDefinition
    CaseUsage: CaseUsage
    Class: Class
    Classifier: Classifier
    CollectExpression: CollectExpression
    Comment: Comment
    ConcernDefinition: ConcernDefinition
    ConcernUsage: ConcernUsage
    ConjugatedPortDefinition: ConjugatedPortDefinition
    ConjugatedPortTyping: ConjugatedPortTyping
    Conjugation: Conjugation
    ConnectionDefinition: ConnectionDefinition
    ConnectionUsage: ConnectionUsage
    Connector: Connector
    ConnectorAsUsage: ConnectorAsUsage
    ConstraintDefinition: ConstraintDefinition
    ConstraintUsage: ConstraintUsage
    ControlNode: ControlNode
    CrossSubsetting: CrossSubsetting
    DataType: DataType
    DecisionNode: DecisionNode
    Definition: Definition
    Dependency: Dependency
    Documentation: Documentation
    Element: Element
    ElementFilterMembership: ElementFilterMembership
    EndFeatureMembership: EndFeatureMembership
    EnumerationDefinition: EnumerationDefinition
    EnumerationUsage: EnumerationUsage
    EventOccurrenceUsage: EventOccurrenceUsage
    ExhibitStateUsage: ExhibitStateUsage
    Expose: Expose
    Expression: Expression
    Feature: Feature
    FeatureChainExpression: FeatureChainExpression
    FeatureChaining: FeatureChaining
    FeatureMembership: FeatureMembership
    FeatureReferenceExpression: FeatureReferenceExpression
    FeatureTyping: FeatureTyping
    FeatureValue: FeatureValue
    Flow: Flow
    FlowDefinition: FlowDefinition
    FlowEnd: FlowEnd
    FlowUsage: FlowUsage
    ForLoopActionUsage: ForLoopActionUsage
    ForkNode: ForkNode
    FramedConcernMembership: FramedConcernMembership
    IfActionUsage: IfActionUsage
    Import: Import
    IncludeUseCaseUsage: IncludeUseCaseUsage
    InstantiationExpression: InstantiationExpression
    Interaction: Interaction
    InterfaceDefinition: InterfaceDefinition
    InterfaceUsage: InterfaceUsage
    Invariant: Invariant
    InvocationExpression: InvocationExpression
    ItemDefinition: ItemDefinition
    ItemUsage: ItemUsage
    JoinNode: JoinNode
    LibraryPackage: LibraryPackage
    LiteralBoolean: LiteralBoolean
    LiteralInteger: LiteralInteger
    LiteralReal: LiteralReal
    LiteralString: LiteralString
    LoopActionUsage: LoopActionUsage
    Membership: Membership
    MembershipExpose: MembershipExpose
    MembershipImport: MembershipImport
    MergeNode: MergeNode
    Metaclass: Metaclass
    MetadataAccessExpression: MetadataAccessExpression
    MetadataDefinition: MetadataDefinition
    MetadataFeature: MetadataFeature
    MetadataUsage: MetadataUsage
    Multiplicity: Multiplicity
    MultiplicityRange: MultiplicityRange
    Namespace: Namespace
    NamespaceExpose: NamespaceExpose
    NamespaceImport: NamespaceImport
    ObjectiveMembership: ObjectiveMembership
    OccurrenceDefinition: OccurrenceDefinition
    OccurrenceUsage: OccurrenceUsage
    OperatorExpression: OperatorExpression
    OwningMembership: OwningMembership
    Package: Package
    ParameterMembership: ParameterMembership
    PartDefinition: PartDefinition
    PartUsage: PartUsage
    PayloadFeature: PayloadFeature
    PerformActionUsage: PerformActionUsage
    PortConjugation: PortConjugation
    PortDefinition: PortDefinition
    PortUsage: PortUsage
    Predicate: Predicate
    Redefinition: Redefinition
    ReferenceSubsetting: ReferenceSubsetting
    ReferenceUsage: ReferenceUsage
    Relationship: Relationship
    RenderingDefinition: RenderingDefinition
    RenderingUsage: RenderingUsage
    RequirementConstraintMembership: RequirementConstraintMembership
    RequirementDefinition: RequirementDefinition
    RequirementUsage: RequirementUsage
    RequirementVerificationMembership: RequirementVerificationMembership
    ResultExpressionMembership: ResultExpressionMembership
    ReturnParameterMembership: ReturnParameterMembership
    SatisfyRequirementUsage: SatisfyRequirementUsage
    SelectExpression: SelectExpression
    SendActionUsage: SendActionUsage
    Specialization: Specialization
    StakeholderMembership: StakeholderMembership
    StateDefinition: StateDefinition
    StateSubactionMembership: StateSubactionMembership
    StateUsage: StateUsage
    Step: Step
    Structure: Structure
    Subclassification: Subclassification
    SubjectMembership: SubjectMembership
    Subsetting: Subsetting
    Succession: Succession
    SuccessionAsUsage: SuccessionAsUsage
    SuccessionFlow: SuccessionFlow
    SuccessionFlowUsage: SuccessionFlowUsage
    SysMLFunction: SysMLFunction
    TerminateActionUsage: TerminateActionUsage
    TextualRepresentation: TextualRepresentation
    TransitionFeatureMembership: TransitionFeatureMembership
    TransitionUsage: TransitionUsage
    TriggerInvocationExpression: TriggerInvocationExpression
    Type: Type
    Usage: Usage
    UseCaseDefinition: UseCaseDefinition
    UseCaseUsage: UseCaseUsage
    VariantMembership: VariantMembership
    VerificationCaseDefinition: VerificationCaseDefinition
    VerificationCaseUsage: VerificationCaseUsage
    ViewDefinition: ViewDefinition
    ViewRenderingMembership: ViewRenderingMembership
    ViewUsage: ViewUsage
    ViewpointDefinition: ViewpointDefinition
    ViewpointUsage: ViewpointUsage
    WhileLoopActionUsage: WhileLoopActionUsage
}

export class SysmlAstReflection extends langium.AbstractAstReflection {

    getAllTypes(): string[] {
        return [AcceptActionUsage, ActionDefinition, ActionUsage, ActorMembership, AllocationDefinition, AllocationUsage, AnalysisCaseDefinition, AnalysisCaseUsage, AnnotatingElement, Annotation, AssertConstraintUsage, AssignmentActionUsage, Association, AssociationStructure, AttributeDefinition, AttributeUsage, Behavior, BindingConnector, BindingConnectorAsUsage, BooleanExpression, CalculationDefinition, CalculationUsage, CaseDefinition, CaseUsage, Class, Classifier, CollectExpression, Comment, ConcernDefinition, ConcernUsage, ConjugatedPortDefinition, ConjugatedPortTyping, Conjugation, ConnectionDefinition, ConnectionUsage, Connector, ConnectorAsUsage, ConstraintDefinition, ConstraintUsage, ControlNode, CrossSubsetting, DataType, DecisionNode, Definition, Dependency, Documentation, Element, ElementFilterMembership, EndFeatureMembership, EnumerationDefinition, EnumerationUsage, EventOccurrenceUsage, ExhibitStateUsage, Expose, Expression, Feature, FeatureChainExpression, FeatureChaining, FeatureMembership, FeatureReferenceExpression, FeatureTyping, FeatureValue, Flow, FlowDefinition, FlowEnd, FlowUsage, ForLoopActionUsage, ForkNode, FramedConcernMembership, IfActionUsage, Import, IncludeUseCaseUsage, InstantiationExpression, Interaction, InterfaceDefinition, InterfaceUsage, Invariant, InvocationExpression, ItemDefinition, ItemUsage, JoinNode, LibraryPackage, LiteralBoolean, LiteralInteger, LiteralReal, LiteralString, LoopActionUsage, Membership, MembershipExpose, MembershipImport, MergeNode, Metaclass, MetadataAccessExpression, MetadataDefinition, MetadataFeature, MetadataUsage, Multiplicity, MultiplicityRange, Namespace, NamespaceExpose, NamespaceImport, ObjectiveMembership, OccurrenceDefinition, OccurrenceUsage, OperatorExpression, OwningMembership, Package, ParameterMembership, PartDefinition, PartUsage, PayloadFeature, PerformActionUsage, PortConjugation, PortDefinition, PortUsage, Predicate, Redefinition, ReferenceSubsetting, ReferenceUsage, Relationship, RenderingDefinition, RenderingUsage, RequirementConstraintMembership, RequirementDefinition, RequirementUsage, RequirementVerificationMembership, ResultExpressionMembership, ReturnParameterMembership, SatisfyRequirementUsage, SelectExpression, SendActionUsage, Specialization, StakeholderMembership, StateDefinition, StateSubactionMembership, StateUsage, Step, Structure, Subclassification, SubjectMembership, Subsetting, Succession, SuccessionAsUsage, SuccessionFlow, SuccessionFlowUsage, SysMLFunction, TerminateActionUsage, TextualRepresentation, TransitionFeatureMembership, TransitionUsage, TriggerInvocationExpression, Type, Usage, UseCaseDefinition, UseCaseUsage, VariantMembership, VerificationCaseDefinition, VerificationCaseUsage, ViewDefinition, ViewRenderingMembership, ViewUsage, ViewpointDefinition, ViewpointUsage, WhileLoopActionUsage];
    }

    protected override computeIsSubtype(subtype: string, supertype: string): boolean {
        switch (subtype) {
            case AcceptActionUsage:
            case AssignmentActionUsage:
            case ControlNode:
            case IfActionUsage:
            case LoopActionUsage:
            case SendActionUsage:
            case StateUsage:
            case TerminateActionUsage:
            case TransitionUsage: {
                return this.isSubtype(ActionUsage, supertype);
            }
            case ActionDefinition: {
                return this.isSubtype(Behavior, supertype) || this.isSubtype(OccurrenceDefinition, supertype);
            }
            case ActionUsage: {
                return this.isSubtype(OccurrenceUsage, supertype) || this.isSubtype(Step, supertype);
            }
            case ActorMembership:
            case ReturnParameterMembership:
            case StakeholderMembership:
            case SubjectMembership: {
                return this.isSubtype(ParameterMembership, supertype);
            }
            case AllocationDefinition:
            case InterfaceDefinition: {
                return this.isSubtype(ConnectionDefinition, supertype);
            }
            case AllocationUsage:
            case InterfaceUsage: {
                return this.isSubtype(ConnectionUsage, supertype);
            }
            case AnalysisCaseDefinition:
            case UseCaseDefinition:
            case VerificationCaseDefinition: {
                return this.isSubtype(CaseDefinition, supertype);
            }
            case AnalysisCaseUsage:
            case UseCaseUsage:
            case VerificationCaseUsage: {
                return this.isSubtype(CaseUsage, supertype);
            }
            case AnnotatingElement:
            case Namespace:
            case Relationship: {
                return this.isSubtype(Element, supertype);
            }
            case Annotation:
            case Conjugation:
            case Dependency:
            case FeatureChaining:
            case Import:
            case Membership:
            case Specialization: {
                return this.isSubtype(Relationship, supertype);
            }
            case AssertConstraintUsage: {
                return this.isSubtype(ConstraintUsage, supertype) || this.isSubtype(Invariant, supertype);
            }
            case Association: {
                return this.isSubtype(Classifier, supertype) || this.isSubtype(Relationship, supertype);
            }
            case AssociationStructure: {
                return this.isSubtype(Association, supertype) || this.isSubtype(Structure, supertype);
            }
            case AttributeDefinition: {
                return this.isSubtype(DataType, supertype) || this.isSubtype(Definition, supertype);
            }
            case AttributeUsage:
            case OccurrenceUsage:
            case ReferenceUsage: {
                return this.isSubtype(Usage, supertype);
            }
            case Behavior:
            case Structure: {
                return this.isSubtype(Class, supertype);
            }
            case BindingConnector:
            case Succession: {
                return this.isSubtype(Connector, supertype);
            }
            case BindingConnectorAsUsage: {
                return this.isSubtype(BindingConnector, supertype) || this.isSubtype(ConnectorAsUsage, supertype);
            }
            case BooleanExpression:
            case FeatureReferenceExpression:
            case InstantiationExpression:
            case LiteralBoolean:
            case LiteralInteger:
            case LiteralReal:
            case LiteralString:
            case MetadataAccessExpression: {
                return this.isSubtype(Expression, supertype);
            }
            case CalculationDefinition: {
                return this.isSubtype(ActionDefinition, supertype) || this.isSubtype(SysMLFunction, supertype);
            }
            case CalculationUsage: {
                return this.isSubtype(ActionUsage, supertype) || this.isSubtype(Expression, supertype);
            }
            case CaseDefinition: {
                return this.isSubtype(CalculationDefinition, supertype);
            }
            case CaseUsage: {
                return this.isSubtype(CalculationUsage, supertype);
            }
            case Class:
            case DataType:
            case Definition: {
                return this.isSubtype(Classifier, supertype);
            }
            case Classifier:
            case Feature: {
                return this.isSubtype(Type, supertype);
            }
            case CollectExpression:
            case FeatureChainExpression:
            case SelectExpression: {
                return this.isSubtype(OperatorExpression, supertype);
            }
            case Comment:
            case TextualRepresentation: {
                return this.isSubtype(AnnotatingElement, supertype);
            }
            case ConcernDefinition:
            case ViewpointDefinition: {
                return this.isSubtype(RequirementDefinition, supertype);
            }
            case ConcernUsage:
            case ViewpointUsage: {
                return this.isSubtype(RequirementUsage, supertype);
            }
            case ConjugatedPortDefinition: {
                return this.isSubtype(PortDefinition, supertype);
            }
            case ConjugatedPortTyping: {
                return this.isSubtype(FeatureTyping, supertype);
            }
            case ConnectionDefinition: {
                return this.isSubtype(AssociationStructure, supertype) || this.isSubtype(PartDefinition, supertype);
            }
            case ConnectionUsage: {
                return this.isSubtype(ConnectorAsUsage, supertype) || this.isSubtype(PartUsage, supertype);
            }
            case Connector: {
                return this.isSubtype(Feature, supertype) || this.isSubtype(Relationship, supertype);
            }
            case ConnectorAsUsage: {
                return this.isSubtype(Connector, supertype) || this.isSubtype(Usage, supertype);
            }
            case ConstraintDefinition: {
                return this.isSubtype(OccurrenceDefinition, supertype) || this.isSubtype(Predicate, supertype);
            }
            case ConstraintUsage: {
                return this.isSubtype(BooleanExpression, supertype) || this.isSubtype(OccurrenceUsage, supertype);
            }
            case CrossSubsetting:
            case Redefinition:
            case ReferenceSubsetting: {
                return this.isSubtype(Subsetting, supertype);
            }
            case DecisionNode:
            case ForkNode:
            case JoinNode:
            case MergeNode: {
                return this.isSubtype(ControlNode, supertype);
            }
            case Documentation: {
                return this.isSubtype(Comment, supertype);
            }
            case ElementFilterMembership:
            case FeatureMembership:
            case FeatureValue:
            case VariantMembership: {
                return this.isSubtype(OwningMembership, supertype);
            }
            case EndFeatureMembership:
            case ObjectiveMembership:
            case ParameterMembership:
            case RequirementConstraintMembership:
            case ResultExpressionMembership:
            case StateSubactionMembership:
            case TransitionFeatureMembership:
            case ViewRenderingMembership: {
                return this.isSubtype(FeatureMembership, supertype);
            }
            case EnumerationDefinition: {
                return this.isSubtype(AttributeDefinition, supertype);
            }
            case EnumerationUsage: {
                return this.isSubtype(AttributeUsage, supertype);
            }
            case EventOccurrenceUsage:
            case ItemUsage:
            case PortUsage: {
                return this.isSubtype(OccurrenceUsage, supertype);
            }
            case ExhibitStateUsage: {
                return this.isSubtype(PerformActionUsage, supertype) || this.isSubtype(StateUsage, supertype);
            }
            case Expose:
            case MembershipImport:
            case NamespaceImport: {
                return this.isSubtype(Import, supertype);
            }
            case Expression: {
                return this.isSubtype(Step, supertype);
            }
            case FeatureTyping:
            case Subclassification:
            case Subsetting: {
                return this.isSubtype(Specialization, supertype);
            }
            case Flow: {
                return this.isSubtype(Connector, supertype) || this.isSubtype(Step, supertype);
            }
            case FlowDefinition: {
                return this.isSubtype(ActionDefinition, supertype) || this.isSubtype(Interaction, supertype);
            }
            case FlowEnd:
            case Multiplicity:
            case PayloadFeature:
            case Step:
            case Usage: {
                return this.isSubtype(Feature, supertype);
            }
            case FlowUsage: {
                return this.isSubtype(ActionUsage, supertype) || this.isSubtype(ConnectorAsUsage, supertype) || this.isSubtype(Flow, supertype);
            }
            case ForLoopActionUsage:
            case WhileLoopActionUsage: {
                return this.isSubtype(LoopActionUsage, supertype);
            }
            case FramedConcernMembership:
            case RequirementVerificationMembership: {
                return this.isSubtype(RequirementConstraintMembership, supertype);
            }
            case IncludeUseCaseUsage: {
                return this.isSubtype(PerformActionUsage, supertype) || this.isSubtype(UseCaseUsage, supertype);
            }
            case Interaction: {
                return this.isSubtype(Association, supertype) || this.isSubtype(Behavior, supertype);
            }
            case Invariant: {
                return this.isSubtype(BooleanExpression, supertype);
            }
            case InvocationExpression: {
                return this.isSubtype(InstantiationExpression, supertype);
            }
            case ItemDefinition:
            case PortDefinition: {
                return this.isSubtype(OccurrenceDefinition, supertype) || this.isSubtype(Structure, supertype);
            }
            case LibraryPackage: {
                return this.isSubtype(Package, supertype);
            }
            case MembershipExpose: {
                return this.isSubtype(Expose, supertype) || this.isSubtype(MembershipImport, supertype);
            }
            case Metaclass: {
                return this.isSubtype(Structure, supertype);
            }
            case MetadataDefinition: {
                return this.isSubtype(ItemDefinition, supertype) || this.isSubtype(Metaclass, supertype);
            }
            case MetadataFeature: {
                return this.isSubtype(AnnotatingElement, supertype) || this.isSubtype(Feature, supertype);
            }
            case MetadataUsage: {
                return this.isSubtype(ItemUsage, supertype) || this.isSubtype(MetadataFeature, supertype);
            }
            case MultiplicityRange: {
                return this.isSubtype(Multiplicity, supertype);
            }
            case NamespaceExpose: {
                return this.isSubtype(Expose, supertype) || this.isSubtype(NamespaceImport, supertype);
            }
            case OccurrenceDefinition: {
                return this.isSubtype(Class, supertype) || this.isSubtype(Definition, supertype);
            }
            case OperatorExpression:
            case TriggerInvocationExpression: {
                return this.isSubtype(InvocationExpression, supertype);
            }
            case OwningMembership: {
                return this.isSubtype(Membership, supertype);
            }
            case Package:
            case Type: {
                return this.isSubtype(Namespace, supertype);
            }
            case PartDefinition: {
                return this.isSubtype(ItemDefinition, supertype);
            }
            case PartUsage: {
                return this.isSubtype(ItemUsage, supertype);
            }
            case PerformActionUsage: {
                return this.isSubtype(ActionUsage, supertype) || this.isSubtype(EventOccurrenceUsage, supertype);
            }
            case PortConjugation: {
                return this.isSubtype(Conjugation, supertype);
            }
            case Predicate: {
                return this.isSubtype(SysMLFunction, supertype);
            }
            case RenderingDefinition:
            case ViewDefinition: {
                return this.isSubtype(PartDefinition, supertype);
            }
            case RenderingUsage:
            case ViewUsage: {
                return this.isSubtype(PartUsage, supertype);
            }
            case RequirementDefinition: {
                return this.isSubtype(ConstraintDefinition, supertype);
            }
            case RequirementUsage: {
                return this.isSubtype(ConstraintUsage, supertype);
            }
            case SatisfyRequirementUsage: {
                return this.isSubtype(AssertConstraintUsage, supertype) || this.isSubtype(RequirementUsage, supertype);
            }
            case StateDefinition: {
                return this.isSubtype(ActionDefinition, supertype);
            }
            case SuccessionAsUsage: {
                return this.isSubtype(ConnectorAsUsage, supertype) || this.isSubtype(Succession, supertype);
            }
            case SuccessionFlow: {
                return this.isSubtype(Flow, supertype) || this.isSubtype(Succession, supertype);
            }
            case SuccessionFlowUsage: {
                return this.isSubtype(FlowUsage, supertype) || this.isSubtype(SuccessionFlow, supertype);
            }
            case SysMLFunction: {
                return this.isSubtype(Behavior, supertype);
            }
            default: {
                return false;
            }
        }
    }

    getReferenceType(refInfo: langium.ReferenceInfo): string {
        const referenceId = `${refInfo.container.$type}:${refInfo.property}`;
        switch (referenceId) {
            case 'AcceptActionUsage:owningRelationship':
            case 'ActionDefinition:owningRelationship':
            case 'ActionUsage:owningRelationship':
            case 'ActorMembership:owningRelationship':
            case 'AllocationDefinition:owningRelationship':
            case 'AllocationUsage:owningRelationship':
            case 'AnalysisCaseDefinition:owningRelationship':
            case 'AnalysisCaseUsage:owningRelationship':
            case 'AnnotatingElement:owningRelationship':
            case 'Annotation:owningRelationship':
            case 'AssertConstraintUsage:owningRelationship':
            case 'AssignmentActionUsage:owningRelationship':
            case 'Association:owningRelationship':
            case 'AssociationStructure:owningRelationship':
            case 'AttributeDefinition:owningRelationship':
            case 'AttributeUsage:owningRelationship':
            case 'Behavior:owningRelationship':
            case 'BindingConnector:owningRelationship':
            case 'BindingConnectorAsUsage:owningRelationship':
            case 'BooleanExpression:owningRelationship':
            case 'CalculationDefinition:owningRelationship':
            case 'CalculationUsage:owningRelationship':
            case 'CaseDefinition:owningRelationship':
            case 'CaseUsage:owningRelationship':
            case 'Class:owningRelationship':
            case 'Classifier:owningRelationship':
            case 'CollectExpression:owningRelationship':
            case 'Comment:owningRelationship':
            case 'ConcernDefinition:owningRelationship':
            case 'ConcernUsage:owningRelationship':
            case 'ConjugatedPortDefinition:owningRelationship':
            case 'ConjugatedPortTyping:owningRelationship':
            case 'Conjugation:owningRelationship':
            case 'ConnectionDefinition:owningRelationship':
            case 'ConnectionUsage:owningRelationship':
            case 'Connector:owningRelationship':
            case 'ConnectorAsUsage:owningRelationship':
            case 'ConstraintDefinition:owningRelationship':
            case 'ConstraintUsage:owningRelationship':
            case 'ControlNode:owningRelationship':
            case 'CrossSubsetting:owningRelationship':
            case 'DataType:owningRelationship':
            case 'DecisionNode:owningRelationship':
            case 'Definition:owningRelationship':
            case 'Dependency:owningRelationship':
            case 'Documentation:owningRelationship':
            case 'Element:owningRelationship':
            case 'ElementFilterMembership:owningRelationship':
            case 'EndFeatureMembership:owningRelationship':
            case 'EnumerationDefinition:owningRelationship':
            case 'EnumerationUsage:owningRelationship':
            case 'EventOccurrenceUsage:owningRelationship':
            case 'ExhibitStateUsage:owningRelationship':
            case 'Expose:owningRelationship':
            case 'Expression:owningRelationship':
            case 'Feature:owningRelationship':
            case 'FeatureChainExpression:owningRelationship':
            case 'FeatureChaining:owningRelationship':
            case 'FeatureMembership:owningRelationship':
            case 'FeatureReferenceExpression:owningRelationship':
            case 'FeatureTyping:owningRelationship':
            case 'FeatureValue:owningRelationship':
            case 'Flow:owningRelationship':
            case 'FlowDefinition:owningRelationship':
            case 'FlowEnd:owningRelationship':
            case 'FlowUsage:owningRelationship':
            case 'ForkNode:owningRelationship':
            case 'ForLoopActionUsage:owningRelationship':
            case 'FramedConcernMembership:owningRelationship':
            case 'IfActionUsage:owningRelationship':
            case 'Import:owningRelationship':
            case 'IncludeUseCaseUsage:owningRelationship':
            case 'InstantiationExpression:owningRelationship':
            case 'Interaction:owningRelationship':
            case 'InterfaceDefinition:owningRelationship':
            case 'InterfaceUsage:owningRelationship':
            case 'Invariant:owningRelationship':
            case 'InvocationExpression:owningRelationship':
            case 'ItemDefinition:owningRelationship':
            case 'ItemUsage:owningRelationship':
            case 'JoinNode:owningRelationship':
            case 'LibraryPackage:owningRelationship':
            case 'LiteralBoolean:owningRelationship':
            case 'LiteralInteger:owningRelationship':
            case 'LiteralReal:owningRelationship':
            case 'LiteralString:owningRelationship':
            case 'LoopActionUsage:owningRelationship':
            case 'Membership:owningRelationship':
            case 'MembershipExpose:owningRelationship':
            case 'MembershipImport:owningRelationship':
            case 'MergeNode:owningRelationship':
            case 'Metaclass:owningRelationship':
            case 'MetadataAccessExpression:owningRelationship':
            case 'MetadataDefinition:owningRelationship':
            case 'MetadataFeature:owningRelationship':
            case 'MetadataUsage:owningRelationship':
            case 'Multiplicity:owningRelationship':
            case 'MultiplicityRange:owningRelationship':
            case 'Namespace:owningRelationship':
            case 'NamespaceExpose:owningRelationship':
            case 'NamespaceImport:owningRelationship':
            case 'ObjectiveMembership:owningRelationship':
            case 'OccurrenceDefinition:owningRelationship':
            case 'OccurrenceUsage:owningRelationship':
            case 'OperatorExpression:owningRelationship':
            case 'OwningMembership:owningRelationship':
            case 'Package:owningRelationship':
            case 'ParameterMembership:owningRelationship':
            case 'PartDefinition:owningRelationship':
            case 'PartUsage:owningRelationship':
            case 'PayloadFeature:owningRelationship':
            case 'PerformActionUsage:owningRelationship':
            case 'PortConjugation:owningRelationship':
            case 'PortDefinition:owningRelationship':
            case 'PortUsage:owningRelationship':
            case 'Predicate:owningRelationship':
            case 'Redefinition:owningRelationship':
            case 'ReferenceSubsetting:owningRelationship':
            case 'ReferenceUsage:owningRelationship':
            case 'Relationship:owningRelationship':
            case 'RenderingDefinition:owningRelationship':
            case 'RenderingUsage:owningRelationship':
            case 'RequirementConstraintMembership:owningRelationship':
            case 'RequirementDefinition:owningRelationship':
            case 'RequirementUsage:owningRelationship':
            case 'RequirementVerificationMembership:owningRelationship':
            case 'ResultExpressionMembership:owningRelationship':
            case 'ReturnParameterMembership:owningRelationship':
            case 'SatisfyRequirementUsage:owningRelationship':
            case 'SelectExpression:owningRelationship':
            case 'SendActionUsage:owningRelationship':
            case 'Specialization:owningRelationship':
            case 'StakeholderMembership:owningRelationship':
            case 'StateDefinition:owningRelationship':
            case 'StateSubactionMembership:owningRelationship':
            case 'StateUsage:owningRelationship':
            case 'Step:owningRelationship':
            case 'Structure:owningRelationship':
            case 'Subclassification:owningRelationship':
            case 'SubjectMembership:owningRelationship':
            case 'Subsetting:owningRelationship':
            case 'Succession:owningRelationship':
            case 'SuccessionAsUsage:owningRelationship':
            case 'SuccessionFlow:owningRelationship':
            case 'SuccessionFlowUsage:owningRelationship':
            case 'SysMLFunction:owningRelationship':
            case 'TerminateActionUsage:owningRelationship':
            case 'TextualRepresentation:owningRelationship':
            case 'TransitionFeatureMembership:owningRelationship':
            case 'TransitionUsage:owningRelationship':
            case 'TriggerInvocationExpression:owningRelationship':
            case 'Type:owningRelationship':
            case 'Usage:owningRelationship':
            case 'UseCaseDefinition:owningRelationship':
            case 'UseCaseUsage:owningRelationship':
            case 'VariantMembership:owningRelationship':
            case 'VerificationCaseDefinition:owningRelationship':
            case 'VerificationCaseUsage:owningRelationship':
            case 'ViewDefinition:owningRelationship':
            case 'ViewpointDefinition:owningRelationship':
            case 'ViewpointUsage:owningRelationship':
            case 'ViewRenderingMembership:owningRelationship':
            case 'ViewUsage:owningRelationship':
            case 'WhileLoopActionUsage:owningRelationship': {
                return Relationship;
            }
            case 'ActorMembership:memberElement':
            case 'ActorMembership:owningRelatedElement':
            case 'AllocationDefinition:owningRelatedElement':
            case 'AllocationUsage:owningRelatedElement':
            case 'Annotation:annotatedElement':
            case 'Annotation:owningRelatedElement':
            case 'Association:owningRelatedElement':
            case 'AssociationStructure:owningRelatedElement':
            case 'BindingConnector:owningRelatedElement':
            case 'BindingConnectorAsUsage:owningRelatedElement':
            case 'ConjugatedPortTyping:owningRelatedElement':
            case 'Conjugation:owningRelatedElement':
            case 'ConnectionDefinition:owningRelatedElement':
            case 'ConnectionUsage:owningRelatedElement':
            case 'Connector:owningRelatedElement':
            case 'ConnectorAsUsage:owningRelatedElement':
            case 'CrossSubsetting:owningRelatedElement':
            case 'Dependency:client':
            case 'Dependency:supplier':
            case 'Dependency:owningRelatedElement':
            case 'ElementFilterMembership:memberElement':
            case 'ElementFilterMembership:owningRelatedElement':
            case 'EndFeatureMembership:memberElement':
            case 'EndFeatureMembership:owningRelatedElement':
            case 'Expose:owningRelatedElement':
            case 'FeatureChaining:owningRelatedElement':
            case 'FeatureMembership:memberElement':
            case 'FeatureMembership:owningRelatedElement':
            case 'FeatureTyping:owningRelatedElement':
            case 'FeatureValue:memberElement':
            case 'FeatureValue:owningRelatedElement':
            case 'Flow:owningRelatedElement':
            case 'FlowDefinition:owningRelatedElement':
            case 'FlowUsage:owningRelatedElement':
            case 'FramedConcernMembership:memberElement':
            case 'FramedConcernMembership:owningRelatedElement':
            case 'Import:owningRelatedElement':
            case 'Interaction:owningRelatedElement':
            case 'InterfaceDefinition:owningRelatedElement':
            case 'InterfaceUsage:owningRelatedElement':
            case 'Membership:memberElement':
            case 'Membership:owningRelatedElement':
            case 'MembershipExpose:owningRelatedElement':
            case 'MembershipImport:owningRelatedElement':
            case 'NamespaceExpose:owningRelatedElement':
            case 'NamespaceImport:owningRelatedElement':
            case 'ObjectiveMembership:memberElement':
            case 'ObjectiveMembership:owningRelatedElement':
            case 'OwningMembership:memberElement':
            case 'OwningMembership:owningRelatedElement':
            case 'ParameterMembership:memberElement':
            case 'ParameterMembership:owningRelatedElement':
            case 'PortConjugation:owningRelatedElement':
            case 'Redefinition:owningRelatedElement':
            case 'ReferenceSubsetting:owningRelatedElement':
            case 'Relationship:owningRelatedElement':
            case 'RequirementConstraintMembership:memberElement':
            case 'RequirementConstraintMembership:owningRelatedElement':
            case 'RequirementVerificationMembership:memberElement':
            case 'RequirementVerificationMembership:owningRelatedElement':
            case 'ResultExpressionMembership:memberElement':
            case 'ResultExpressionMembership:owningRelatedElement':
            case 'ReturnParameterMembership:memberElement':
            case 'ReturnParameterMembership:owningRelatedElement':
            case 'Specialization:owningRelatedElement':
            case 'StakeholderMembership:memberElement':
            case 'StakeholderMembership:owningRelatedElement':
            case 'StateSubactionMembership:memberElement':
            case 'StateSubactionMembership:owningRelatedElement':
            case 'Subclassification:owningRelatedElement':
            case 'SubjectMembership:memberElement':
            case 'SubjectMembership:owningRelatedElement':
            case 'Subsetting:owningRelatedElement':
            case 'Succession:owningRelatedElement':
            case 'SuccessionAsUsage:owningRelatedElement':
            case 'SuccessionFlow:owningRelatedElement':
            case 'SuccessionFlowUsage:owningRelatedElement':
            case 'TransitionFeatureMembership:memberElement':
            case 'TransitionFeatureMembership:owningRelatedElement':
            case 'VariantMembership:memberElement':
            case 'VariantMembership:owningRelatedElement':
            case 'ViewRenderingMembership:memberElement':
            case 'ViewRenderingMembership:owningRelatedElement': {
                return Element;
            }
            case 'ConjugatedPortTyping:conjugatedPortDefinition': {
                return ConjugatedPortDefinition;
            }
            case 'ConjugatedPortTyping:type':
            case 'FeatureTyping:type': {
                return Type;
            }
            case 'CrossSubsetting:crossedFeature':
            case 'CrossSubsetting:subsettedFeature':
            case 'Redefinition:redefinedFeature':
            case 'Redefinition:redefiningFeature':
            case 'Redefinition:subsettedFeature':
            case 'ReferenceSubsetting:referencedFeature':
            case 'ReferenceSubsetting:subsettedFeature':
            case 'Subsetting:subsettedFeature': {
                return Feature;
            }
            case 'MembershipExpose:importedMembership':
            case 'MembershipImport:importedMembership': {
                return Membership;
            }
            case 'NamespaceExpose:importedNamespace':
            case 'NamespaceImport:importedNamespace': {
                return Namespace;
            }
            case 'Subclassification:subclassifier':
            case 'Subclassification:superclassifier': {
                return Classifier;
            }
            default: {
                throw new Error(`${referenceId} is not a valid reference id.`);
            }
        }
    }

    getTypeMetaData(type: string): langium.TypeMetaData {
        switch (type) {
            case Element: {
                return {
                    name: Element,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AnnotatingElement: {
                return {
                    name: AnnotatingElement,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Namespace: {
                return {
                    name: Namespace,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Relationship: {
                return {
                    name: Relationship,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Comment: {
                return {
                    name: Comment,
                    properties: [
                        { name: 'body' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'locale' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case MetadataFeature: {
                return {
                    name: MetadataFeature,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case TextualRepresentation: {
                return {
                    name: TextualRepresentation,
                    properties: [
                        { name: 'body' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'language' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Package: {
                return {
                    name: Package,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Type: {
                return {
                    name: Type,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Annotation: {
                return {
                    name: Annotation,
                    properties: [
                        { name: 'annotatedElement' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Association: {
                return {
                    name: Association,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Conjugation: {
                return {
                    name: Conjugation,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Connector: {
                return {
                    name: Connector,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Dependency: {
                return {
                    name: Dependency,
                    properties: [
                        { name: 'client', defaultValue: [] },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'supplier', defaultValue: [] }
                    ]
                };
            }
            case FeatureChaining: {
                return {
                    name: FeatureChaining,
                    properties: [
                        { name: 'chainingFeature' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Import: {
                return {
                    name: Import,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isImportAll', defaultValue: false },
                        { name: 'isRecursive', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case Membership: {
                return {
                    name: Membership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case Specialization: {
                return {
                    name: Specialization,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Documentation: {
                return {
                    name: Documentation,
                    properties: [
                        { name: 'body' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'locale' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case MetadataUsage: {
                return {
                    name: MetadataUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case LibraryPackage: {
                return {
                    name: LibraryPackage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isStandard', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Classifier: {
                return {
                    name: Classifier,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Feature: {
                return {
                    name: Feature,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AssociationStructure: {
                return {
                    name: AssociationStructure,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Interaction: {
                return {
                    name: Interaction,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case PortConjugation: {
                return {
                    name: PortConjugation,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case BindingConnector: {
                return {
                    name: BindingConnector,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConnectorAsUsage: {
                return {
                    name: ConnectorAsUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Flow: {
                return {
                    name: Flow,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Succession: {
                return {
                    name: Succession,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Expose: {
                return {
                    name: Expose,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isImportAll', defaultValue: false },
                        { name: 'isRecursive', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case MembershipImport: {
                return {
                    name: MembershipImport,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'importedMembership' },
                        { name: 'isImportAll', defaultValue: false },
                        { name: 'isRecursive', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case NamespaceImport: {
                return {
                    name: NamespaceImport,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'importedNamespace' },
                        { name: 'isImportAll', defaultValue: false },
                        { name: 'isRecursive', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case OwningMembership: {
                return {
                    name: OwningMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case FeatureTyping: {
                return {
                    name: FeatureTyping,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'type' }
                    ]
                };
            }
            case Subclassification: {
                return {
                    name: Subclassification,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'subclassifier' },
                        { name: 'superclassifier' }
                    ]
                };
            }
            case Subsetting: {
                return {
                    name: Subsetting,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'subsettedFeature' }
                    ]
                };
            }
            case Class: {
                return {
                    name: Class,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case DataType: {
                return {
                    name: DataType,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Definition: {
                return {
                    name: Definition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case FlowEnd: {
                return {
                    name: FlowEnd,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Multiplicity: {
                return {
                    name: Multiplicity,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case PayloadFeature: {
                return {
                    name: PayloadFeature,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Step: {
                return {
                    name: Step,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Usage: {
                return {
                    name: Usage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConnectionDefinition: {
                return {
                    name: ConnectionDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case FlowDefinition: {
                return {
                    name: FlowDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case BindingConnectorAsUsage: {
                return {
                    name: BindingConnectorAsUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConnectionUsage: {
                return {
                    name: ConnectionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case FlowUsage: {
                return {
                    name: FlowUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case SuccessionAsUsage: {
                return {
                    name: SuccessionAsUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case SuccessionFlow: {
                return {
                    name: SuccessionFlow,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case MembershipExpose: {
                return {
                    name: MembershipExpose,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'importedMembership' },
                        { name: 'isImportAll', defaultValue: false },
                        { name: 'isRecursive', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case NamespaceExpose: {
                return {
                    name: NamespaceExpose,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'importedNamespace' },
                        { name: 'isImportAll', defaultValue: false },
                        { name: 'isRecursive', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ElementFilterMembership: {
                return {
                    name: ElementFilterMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case FeatureMembership: {
                return {
                    name: FeatureMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case FeatureValue: {
                return {
                    name: FeatureValue,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isDefault', defaultValue: false },
                        { name: 'isInitial', defaultValue: false },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case VariantMembership: {
                return {
                    name: VariantMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ConjugatedPortTyping: {
                return {
                    name: ConjugatedPortTyping,
                    properties: [
                        { name: 'conjugatedPortDefinition' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'type' }
                    ]
                };
            }
            case CrossSubsetting: {
                return {
                    name: CrossSubsetting,
                    properties: [
                        { name: 'crossedFeature' },
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'subsettedFeature' }
                    ]
                };
            }
            case Redefinition: {
                return {
                    name: Redefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'redefinedFeature' },
                        { name: 'redefiningFeature' },
                        { name: 'subsettedFeature' }
                    ]
                };
            }
            case ReferenceSubsetting: {
                return {
                    name: ReferenceSubsetting,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'referencedFeature' },
                        { name: 'subsettedFeature' }
                    ]
                };
            }
            case Behavior: {
                return {
                    name: Behavior,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case OccurrenceDefinition: {
                return {
                    name: OccurrenceDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Structure: {
                return {
                    name: Structure,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AttributeDefinition: {
                return {
                    name: AttributeDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case MultiplicityRange: {
                return {
                    name: MultiplicityRange,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ActionUsage: {
                return {
                    name: ActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case Expression: {
                return {
                    name: Expression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AttributeUsage: {
                return {
                    name: AttributeUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case OccurrenceUsage: {
                return {
                    name: OccurrenceUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ReferenceUsage: {
                return {
                    name: ReferenceUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AllocationDefinition: {
                return {
                    name: AllocationDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case InterfaceDefinition: {
                return {
                    name: InterfaceDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AllocationUsage: {
                return {
                    name: AllocationUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case InterfaceUsage: {
                return {
                    name: InterfaceUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case SuccessionFlowUsage: {
                return {
                    name: SuccessionFlowUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case EndFeatureMembership: {
                return {
                    name: EndFeatureMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ObjectiveMembership: {
                return {
                    name: ObjectiveMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ParameterMembership: {
                return {
                    name: ParameterMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case RequirementConstraintMembership: {
                return {
                    name: RequirementConstraintMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'kind' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ResultExpressionMembership: {
                return {
                    name: ResultExpressionMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case StateSubactionMembership: {
                return {
                    name: StateSubactionMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'kind' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case TransitionFeatureMembership: {
                return {
                    name: TransitionFeatureMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'kind' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ViewRenderingMembership: {
                return {
                    name: ViewRenderingMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ActionDefinition: {
                return {
                    name: ActionDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case SysMLFunction: {
                return {
                    name: SysMLFunction,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConstraintDefinition: {
                return {
                    name: ConstraintDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ItemDefinition: {
                return {
                    name: ItemDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case PortDefinition: {
                return {
                    name: PortDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Metaclass: {
                return {
                    name: Metaclass,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case EnumerationDefinition: {
                return {
                    name: EnumerationDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AcceptActionUsage: {
                return {
                    name: AcceptActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case AssignmentActionUsage: {
                return {
                    name: AssignmentActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case CalculationUsage: {
                return {
                    name: CalculationUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ControlNode: {
                return {
                    name: ControlNode,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case IfActionUsage: {
                return {
                    name: IfActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case LoopActionUsage: {
                return {
                    name: LoopActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case PerformActionUsage: {
                return {
                    name: PerformActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case SendActionUsage: {
                return {
                    name: SendActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case StateUsage: {
                return {
                    name: StateUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isParallel', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case TerminateActionUsage: {
                return {
                    name: TerminateActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case TransitionUsage: {
                return {
                    name: TransitionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case BooleanExpression: {
                return {
                    name: BooleanExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case FeatureReferenceExpression: {
                return {
                    name: FeatureReferenceExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case InstantiationExpression: {
                return {
                    name: InstantiationExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case LiteralBoolean: {
                return {
                    name: LiteralBoolean,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'value' }
                    ]
                };
            }
            case LiteralInteger: {
                return {
                    name: LiteralInteger,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'value' }
                    ]
                };
            }
            case LiteralReal: {
                return {
                    name: LiteralReal,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'value' }
                    ]
                };
            }
            case LiteralString: {
                return {
                    name: LiteralString,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'value' }
                    ]
                };
            }
            case MetadataAccessExpression: {
                return {
                    name: MetadataAccessExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case EnumerationUsage: {
                return {
                    name: EnumerationUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConstraintUsage: {
                return {
                    name: ConstraintUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case EventOccurrenceUsage: {
                return {
                    name: EventOccurrenceUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ItemUsage: {
                return {
                    name: ItemUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case PortUsage: {
                return {
                    name: PortUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ActorMembership: {
                return {
                    name: ActorMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case ReturnParameterMembership: {
                return {
                    name: ReturnParameterMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case StakeholderMembership: {
                return {
                    name: StakeholderMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case SubjectMembership: {
                return {
                    name: SubjectMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case FramedConcernMembership: {
                return {
                    name: FramedConcernMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'kind' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case RequirementVerificationMembership: {
                return {
                    name: RequirementVerificationMembership,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'kind' },
                        { name: 'memberElement' },
                        { name: 'memberName' },
                        { name: 'memberShortName' },
                        { name: 'ownedRelatedElement', defaultValue: [] },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelatedElement' },
                        { name: 'owningRelationship' },
                        { name: 'visibility' }
                    ]
                };
            }
            case CalculationDefinition: {
                return {
                    name: CalculationDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case StateDefinition: {
                return {
                    name: StateDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isParallel', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case Predicate: {
                return {
                    name: Predicate,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case RequirementDefinition: {
                return {
                    name: RequirementDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case MetadataDefinition: {
                return {
                    name: MetadataDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case PartDefinition: {
                return {
                    name: PartDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConjugatedPortDefinition: {
                return {
                    name: ConjugatedPortDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case CaseUsage: {
                return {
                    name: CaseUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case DecisionNode: {
                return {
                    name: DecisionNode,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ForkNode: {
                return {
                    name: ForkNode,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case JoinNode: {
                return {
                    name: JoinNode,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case MergeNode: {
                return {
                    name: MergeNode,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ForLoopActionUsage: {
                return {
                    name: ForLoopActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case WhileLoopActionUsage: {
                return {
                    name: WhileLoopActionUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ExhibitStateUsage: {
                return {
                    name: ExhibitStateUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isParallel', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case IncludeUseCaseUsage: {
                return {
                    name: IncludeUseCaseUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case Invariant: {
                return {
                    name: Invariant,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNegated', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case InvocationExpression: {
                return {
                    name: InvocationExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AssertConstraintUsage: {
                return {
                    name: AssertConstraintUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNegated', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case RequirementUsage: {
                return {
                    name: RequirementUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case PartUsage: {
                return {
                    name: PartUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case CaseDefinition: {
                return {
                    name: CaseDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ConcernDefinition: {
                return {
                    name: ConcernDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ViewpointDefinition: {
                return {
                    name: ViewpointDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case RenderingDefinition: {
                return {
                    name: RenderingDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case ViewDefinition: {
                return {
                    name: ViewDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case AnalysisCaseUsage: {
                return {
                    name: AnalysisCaseUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case UseCaseUsage: {
                return {
                    name: UseCaseUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case VerificationCaseUsage: {
                return {
                    name: VerificationCaseUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case OperatorExpression: {
                return {
                    name: OperatorExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'operand', defaultValue: [] },
                        { name: 'operator' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case TriggerInvocationExpression: {
                return {
                    name: TriggerInvocationExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'kind' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case SatisfyRequirementUsage: {
                return {
                    name: SatisfyRequirementUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNegated', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ConcernUsage: {
                return {
                    name: ConcernUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ViewpointUsage: {
                return {
                    name: ViewpointUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case RenderingUsage: {
                return {
                    name: RenderingUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case ViewUsage: {
                return {
                    name: ViewUsage,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isReference', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' },
                        { name: 'portionKind' }
                    ]
                };
            }
            case AnalysisCaseDefinition: {
                return {
                    name: AnalysisCaseDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case UseCaseDefinition: {
                return {
                    name: UseCaseDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case VerificationCaseDefinition: {
                return {
                    name: VerificationCaseDefinition,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isIndividual', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case CollectExpression: {
                return {
                    name: CollectExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'operand', defaultValue: [] },
                        { name: 'operator' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case FeatureChainExpression: {
                return {
                    name: FeatureChainExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'operand', defaultValue: [] },
                        { name: 'operator' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            case SelectExpression: {
                return {
                    name: SelectExpression,
                    properties: [
                        { name: 'declaredName' },
                        { name: 'declaredShortName' },
                        { name: 'direction' },
                        { name: 'isAbstract', defaultValue: false },
                        { name: 'isComposite', defaultValue: false },
                        { name: 'isConstant', defaultValue: false },
                        { name: 'isDerived', defaultValue: false },
                        { name: 'isEnd', defaultValue: false },
                        { name: 'isNonunique', defaultValue: false },
                        { name: 'isOrdered', defaultValue: false },
                        { name: 'isPortion', defaultValue: false },
                        { name: 'isVariation', defaultValue: false },
                        { name: 'operand', defaultValue: [] },
                        { name: 'operator' },
                        { name: 'ownedRelationship', defaultValue: [] },
                        { name: 'owningRelationship' }
                    ]
                };
            }
            default: {
                return {
                    name: type,
                    properties: []
                };
            }
        }
    }
}

export const reflection = new SysmlAstReflection();
