//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/
 
package Test3{
	classifier P{
		protected classifier PP{}
	}
	classifier A specializes P {
 		feature DD: PP;
	}
	classifier B {
		// XPECT errors --> "Couldn't resolve reference to Type 'P::PP'." at "P::PP"
		feature BB2: P::PP;
	}
}
