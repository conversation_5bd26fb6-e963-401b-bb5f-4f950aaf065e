Manifest-Version: 1.0
Automatic-Module-Name: org.omg.sysml.xtext
Bundle-ManifestVersion: 2
Bundle-Name: org.omg.sysml.xtext
Bundle-Vendor: SysML v2 Submission Team
Bundle-Version: 0.51.0.202509041722
Bundle-SymbolicName: org.omg.sysml.xtext; singleton:=true
Bundle-ActivationPolicy: lazy
Require-Bundle: org.eclipse.xtext,org.eclipse.xtext.xbase,org.eclipse.eq
 uinox.common;bundle-version="3.5.0",org.eclipse.xtext.xbase.lib;bundle-
 version="2.14.0",org.eclipse.xtext.util,org.eclipse.xtend.lib;bundle-ve
 rsion="2.14.0",org.antlr.runtime;bundle-version="[3.2.0,3.2.1)",org.omg
 .sysml;bundle-version="0.2.0",org.eclipse.uml2.uml,org.eclipse.uml2.uml
 .resources,org.omg.kerml.xtext;bundle-version="0.2.0",org.omg.kerml.exp
 ressions.xtext;bundle-version="0.2.0",org.eclipse.xtext.common.types,or
 g.objectweb.asm;bundle-version="9.3.0";resolution:=optional,org.apache.
 commons.cli
Export-Package: org.omg.sysml.xtext,org.omg.sysml.xtext.generator,org.om
 g.sysml.xtext.library,org.omg.sysml.xtext.parser.antlr,org.omg.sysml.xt
 ext.parser.antlr.internal,org.omg.sysml.xtext.scoping,org.omg.sysml.xte
 xt.serializer,org.omg.sysml.xtext.services,org.omg.sysml.xtext.util,org
 .omg.sysml.xtext.validation,org.omg.sysml.xtext.xmi
Import-Package: org.apache.log4j
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

