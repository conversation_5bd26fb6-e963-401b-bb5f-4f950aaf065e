'use client';

import React, { useState, useEffect } from 'react';
import { useSysML } from '@/contexts/SysMLContext';
import { useSysMLToast } from '@/components/ui/sysml-toast';
import SysMLButton from '@/components/ui/sysml-button';

// 工作流信息接口
interface WorkflowInfo {
  id: string;
  name: string;
  description: string;
  category: 'generation' | 'analysis' | 'validation' | 'transformation';
  dependencies?: string[];
}

// 执行结果接口
interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  steps: any[];
}

const WorkflowPanel: React.FC = () => {
  const { state } = useSysML();
  const { showToast } = useSysMLToast();
  const [workflows, setWorkflows] = useState<WorkflowInfo[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResults, setExecutionResults] = useState<Record<string, ExecutionResult>>({});
  const [userInput, setUserInput] = useState('');

  // 获取当前活动标签页的代码
  const getCurrentCode = () => {
    const activeTab = state.editor.tabs.find(tab => tab.id === state.editor.activeTabId);
    return activeTab?.content || '';
  };

  // 加载工作流列表
  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      const response = await fetch('/api/langgraph?action=list');
      const data = await response.json();
      
      if (data.success) {
        setWorkflows(data.workflows);
        if (data.workflows.length > 0) {
          setSelectedWorkflow(data.workflows[0].id);
        }
      } else {
        showToast({
          type: 'error',
          title: '加载失败',
          message: '无法加载工作流列表'
        });
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
      showToast({
        type: 'error',
        title: '网络错误',
        message: '无法连接到服务器'
      });
    }
  };

  // 执行工作流
  const executeWorkflow = async (workflowId: string, input?: any) => {
    if (!workflowId) return;

    setIsExecuting(true);
    
    try {
      const requestBody = {
        action: 'execute',
        workflowId,
        input: {
          message: input?.message || userInput,
          sysmlCode: input?.sysmlCode || getCurrentCode(),
          ...input
        }
      };

      const response = await fetch('/api/langgraph', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      
      if (data.success) {
        setExecutionResults(prev => ({
          ...prev,
          [workflowId]: data.result
        }));

        showToast({
          type: 'success',
          title: '执行成功',
          message: `工作流 "${getWorkflowName(workflowId)}" 执行完成`,
          duration: 3000
        });

        // 如果是代码生成工作流，将结果插入编辑器
        if (workflowId === 'sysml-code-generation' && data.result.result?.sysmlCode) {
          // 这里可以调用编辑器的插入代码功能
          console.log('Generated code:', data.result.result.sysmlCode);
        }
      } else {
        showToast({
          type: 'error',
          title: '执行失败',
          message: data.error || '工作流执行失败'
        });
      }
    } catch (error) {
      console.error('Workflow execution failed:', error);
      showToast({
        type: 'error',
        title: '执行错误',
        message: '工作流执行过程中发生错误'
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // 执行工作流链
  const executeWorkflowChain = async (workflowIds: string[]) => {
    setIsExecuting(true);
    
    try {
      const requestBody = {
        action: 'execute-chain',
        workflowIds,
        input: {
          message: userInput,
          sysmlCode: getCurrentCode()
        }
      };

      const response = await fetch('/api/langgraph', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();
      
      if (data.success) {
        // 保存所有结果
        data.results.forEach((result: ExecutionResult, index: number) => {
          setExecutionResults(prev => ({
            ...prev,
            [workflowIds[index]]: result
          }));
        });

        showToast({
          type: 'success',
          title: '链式执行成功',
          message: `${workflowIds.length} 个工作流执行完成`,
          duration: 3000
        });
      } else {
        showToast({
          type: 'error',
          title: '链式执行失败',
          message: data.error || '工作流链执行失败'
        });
      }
    } catch (error) {
      console.error('Workflow chain execution failed:', error);
      showToast({
        type: 'error',
        title: '执行错误',
        message: '工作流链执行过程中发生错误'
      });
    } finally {
      setIsExecuting(false);
    }
  };

  // 获取工作流名称
  const getWorkflowName = (id: string) => {
    const workflow = workflows.find(wf => wf.id === id);
    return workflow?.name || id;
  };

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'generation': return 'bg-green-100 text-green-800';
      case 'analysis': return 'bg-blue-100 text-blue-800';
      case 'validation': return 'bg-yellow-100 text-yellow-800';
      case 'transformation': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'generation':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
        );
      case 'analysis':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
      case 'validation':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'transformation':
        return (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className="bg-white border rounded-lg shadow-sm">
      {/* 头部 */}
      <div className="px-4 py-3 border-b bg-gray-50">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <svg className="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          AI 工作流
        </h3>
        <p className="text-sm text-gray-500 mt-1">
          使用 LangGraph 驱动的智能工作流来生成、分析和优化 SysML v2 代码
        </p>
      </div>

      {/* 内容区域 */}
      <div className="p-4 space-y-4">
        {/* 用户输入 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            描述您的需求
          </label>
          <textarea
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            placeholder="例如：创建一个车辆系统模型，包含发动机、变速箱和车轮..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            rows={3}
          />
        </div>

        {/* 工作流选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            选择工作流
          </label>
          <div className="grid grid-cols-1 gap-2">
            {workflows.map(workflow => (
              <div
                key={workflow.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedWorkflow === workflow.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedWorkflow(workflow.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getCategoryIcon(workflow.category)}
                    <span className="font-medium">{workflow.name}</span>
                    <span className={`px-2 py-1 text-xs rounded-full ${getCategoryColor(workflow.category)}`}>
                      {workflow.category}
                    </span>
                  </div>
                  <input
                    type="radio"
                    checked={selectedWorkflow === workflow.id}
                    onChange={() => setSelectedWorkflow(workflow.id)}
                    className="text-blue-600"
                  />
                </div>
                <p className="text-sm text-gray-600 mt-1">{workflow.description}</p>
                {workflow.dependencies && workflow.dependencies.length > 0 && (
                  <div className="mt-2">
                    <span className="text-xs text-gray-500">
                      依赖: {workflow.dependencies.map(dep => getWorkflowName(dep)).join(', ')}
                    </span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* 执行按钮 */}
        <div className="flex space-x-2">
          <SysMLButton
            onClick={() => executeWorkflow(selectedWorkflow)}
            disabled={!selectedWorkflow || isExecuting || !userInput.trim()}
            className="flex-1"
          >
            {isExecuting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                执行中...
              </>
            ) : (
              <>
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                AI 生成
              </>
            )}
          </SysMLButton>

          <SysMLButton
            variant="outline"
            onClick={() => executeWorkflowChain(['sysml-code-generation', 'sysml-code-analysis'])}
            disabled={isExecuting || !userInput.trim()}
            title="生成代码并分析"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </SysMLButton>
        </div>

        {/* 执行结果 */}
        {Object.keys(executionResults).length > 0 && (
          <div className="mt-6">
            <h4 className="text-sm font-medium text-gray-700 mb-3">执行结果</h4>
            <div className="space-y-3">
              {Object.entries(executionResults).map(([workflowId, result]) => (
                <div key={workflowId} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">{getWorkflowName(workflowId)}</span>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {result.success ? '成功' : '失败'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {result.executionTime}ms
                      </span>
                    </div>
                  </div>
                  
                  {result.success && result.result && (
                    <div className="text-sm">
                      {result.result.sysmlCode && (
                        <div className="mb-2">
                          <span className="font-medium text-gray-700">生成的代码:</span>
                          <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                            {result.result.sysmlCode.substring(0, 200)}
                            {result.result.sysmlCode.length > 200 && '...'}
                          </pre>
                        </div>
                      )}
                      
                      {result.result.analysisResult && (
                        <div className="mb-2">
                          <span className="font-medium text-gray-700">分析结果:</span>
                          <p className="mt-1 text-gray-600 text-xs">
                            {typeof result.result.analysisResult === 'string' 
                              ? result.result.analysisResult.substring(0, 150) + '...'
                              : JSON.stringify(result.result.analysisResult)
                            }
                          </p>
                        </div>
                      )}
                    </div>
                  )}
                  
                  {!result.success && result.error && (
                    <p className="text-sm text-red-600">{result.error}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkflowPanel;
