//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
		File {from ="/src/DependencyMultipleMembership_Feature_FT.kerml"}
		File {from ="/src/DependencyMembership2_Feature_FT.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
				File {from ="/src/DependencyMultipleMembership_Feature_FT.kerml"}
				File {from ="/src/DependencyMembership2_Feature_FT.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	public import OuterPackage3::D;
	//XPECT linkedName at D --> OuterPackage3.D
	//* XPECT scope at D ---
	D, D.b, D.b.a1, D.b.a1.self, D.b.a1.that, D.b.a1.that.self, D.b.self, D.b.that,
	D.b.that.self, D.c, D.c.self, D.c.that, D.c.that.self, D.f, D.f.a1, D.f.a1.self, D.f.a1.that,
	D.f.a1.that.self, D.f.self, D.f.that, D.f.that.self, D.self, D.that, D.that.self, EE, EE.b,
	EE.b.a1, EE.b.a1.self, EE.b.a1.that, EE.b.a1.that.self, EE.b.self, EE.b.that,
	EE.b.that.self, EE.c, EE.c.self, EE.c.that, EE.c.that.self, EE.f, EE.f.a1, EE.f.a1.self,
	EE.f.a1.that, EE.f.a1.that.self, EE.f.self, EE.f.that, EE.f.that.self, EE.self, EE.that,
	EE.that.self, EE.try, EE.try.a1, EE.try.a1.self, EE.try.a1.that, EE.try.a1.that.self,
	EE.try.self, EE.try.that, EE.try.that.self, OuterPackage.A, OuterPackage.A.a1,
	OuterPackage.A.a1.self, OuterPackage.A.a1.that, OuterPackage.A.a1.that.self, OuterPackage.A.self,
	OuterPackage.A.that, OuterPackage.A.that.self, OuterPackage.B, OuterPackage.B.b,
	OuterPackage.B.b.a1, OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that,
	OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self, OuterPackage.B.b.that, OuterPackage.B.b.that.self,
	OuterPackage.B.self, OuterPackage.B.that, OuterPackage.B.that.self, OuterPackage2.B,
	OuterPackage2.B.b, OuterPackage2.B.b.a1, OuterPackage2.B.b.a1.self, OuterPackage2.B.b.a1.that,
	OuterPackage2.B.b.a1.that.self, OuterPackage2.B.b.self, OuterPackage2.B.b.that, OuterPackage2.B.b.that.self,
	OuterPackage2.B.self, OuterPackage2.B.that, OuterPackage2.B.that.self, OuterPackage2.C,
	OuterPackage2.C.b, OuterPackage2.C.b.a1, OuterPackage2.C.b.a1.self, OuterPackage2.C.b.a1.that,
	OuterPackage2.C.b.a1.that.self, OuterPackage2.C.b.self, OuterPackage2.C.b.that, OuterPackage2.C.b.that.self,
	OuterPackage2.C.c, OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self,
	OuterPackage2.C.self, OuterPackage2.C.that, OuterPackage2.C.that.self, OuterPackage3.C,
	OuterPackage3.C.b, OuterPackage3.C.b.a1, OuterPackage3.C.b.a1.self, OuterPackage3.C.b.a1.that,
	OuterPackage3.C.b.a1.that.self, OuterPackage3.C.b.self, OuterPackage3.C.b.that, OuterPackage3.C.b.that.self,
	OuterPackage3.C.c, OuterPackage3.C.c.self, OuterPackage3.C.c.that, OuterPackage3.C.c.that.self,
	OuterPackage3.C.self, OuterPackage3.C.that, OuterPackage3.C.that.self, OuterPackage3.D,
	OuterPackage3.D.b, OuterPackage3.D.b.a1, OuterPackage3.D.b.a1.self, OuterPackage3.D.b.a1.that,
	OuterPackage3.D.b.a1.that.self, OuterPackage3.D.b.self, OuterPackage3.D.b.that, OuterPackage3.D.b.that.self,
	OuterPackage3.D.c, OuterPackage3.D.c.self, OuterPackage3.D.c.that, OuterPackage3.D.c.that.self,
	OuterPackage3.D.f, OuterPackage3.D.f.a1, OuterPackage3.D.f.a1.self, OuterPackage3.D.f.a1.that,
	OuterPackage3.D.f.a1.that.self, OuterPackage3.D.f.self, OuterPackage3.D.f.that, OuterPackage3.D.f.that.self,
	OuterPackage3.D.self, OuterPackage3.D.that, OuterPackage3.D.that.self, test.D, test.D.b, test.D.b.a1,
	test.D.b.a1.self, test.D.b.a1.that, test.D.b.a1.that.self, test.D.b.self, test.D.b.that,
	test.D.b.that.self, test.D.c, test.D.c.self, test.D.c.that, test.D.c.that.self, test.D.f,
	test.D.f.a1, test.D.f.a1.self, test.D.f.a1.that, test.D.f.a1.that.self, test.D.f.self,
	test.D.f.that, test.D.f.that.self, test.D.self, test.D.that, test.D.that.self, test.EE,
	test.EE.b, test.EE.b.a1, test.EE.b.a1.self, test.EE.b.a1.that, test.EE.b.a1.that.self,
	test.EE.b.self, test.EE.b.that, test.EE.b.that.self, test.EE.c, test.EE.c.self, test.EE.c.that,
	test.EE.c.that.self, test.EE.f, test.EE.f.a1, test.EE.f.a1.self, test.EE.f.a1.that,
	test.EE.f.a1.that.self, test.EE.f.self, test.EE.f.that, test.EE.f.that.self, test.EE.self,
	test.EE.that, test.EE.that.self, test.EE.try, test.EE.try.a1, test.EE.try.a1.self,
	test.EE.try.a1.that, test.EE.try.a1.that.self, test.EE.try.self, test.EE.try.that,
	test.EE.try.that.self
	--- */
	feature EE : D {
		//XPECT linkedName at b --> OuterPackage.B.b
		//* XPECT scope at b ---
		D, D.b, D.b.a1, D.b.a1.self, D.b.a1.that, D.b.a1.that.self, D.b.self, D.b.that,
		D.b.that.self, D.c, D.c.self, D.c.that, D.c.that.self, D.f, D.f.a1, D.f.a1.self, D.f.a1.that,
		D.f.a1.that.self, D.f.self, D.f.that, D.f.that.self, D.self, D.that, D.that.self, EE, EE.b,
		EE.b.a1, EE.b.a1.self, EE.b.a1.that, EE.b.a1.that.self, EE.b.self, EE.b.that,
		EE.b.that.self, EE.c, EE.c.self, EE.c.that, EE.c.that.self, EE.f, EE.f.a1, EE.f.a1.self,
		EE.f.a1.that, EE.f.a1.that.self, EE.f.self, EE.f.that, EE.f.that.self, EE.self, EE.that,
		EE.that.self, EE.try, EE.try.a1, EE.try.a1.self, EE.try.a1.that, EE.try.a1.that.self,
		EE.try.self, EE.try.that, EE.try.that.self, OuterPackage.A, OuterPackage.A.a1,
		OuterPackage.A.a1.self, OuterPackage.A.a1.that, OuterPackage.A.a1.that.self, OuterPackage.A.self,
		OuterPackage.A.that, OuterPackage.A.that.self, OuterPackage.B, OuterPackage.B.b,
		OuterPackage.B.b.a1, OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that,
		OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self, OuterPackage.B.b.that, OuterPackage.B.b.that.self,
		OuterPackage.B.self, OuterPackage.B.that, OuterPackage.B.that.self, OuterPackage2.B,
		OuterPackage2.B.b, OuterPackage2.B.b.a1, OuterPackage2.B.b.a1.self, OuterPackage2.B.b.a1.that,
		OuterPackage2.B.b.a1.that.self, OuterPackage2.B.b.self, OuterPackage2.B.b.that, OuterPackage2.B.b.that.self,
		OuterPackage2.B.self, OuterPackage2.B.that, OuterPackage2.B.that.self, OuterPackage2.C,
		OuterPackage2.C.b, OuterPackage2.C.b.a1, OuterPackage2.C.b.a1.self, OuterPackage2.C.b.a1.that,
		OuterPackage2.C.b.a1.that.self, OuterPackage2.C.b.self, OuterPackage2.C.b.that, OuterPackage2.C.b.that.self,
		OuterPackage2.C.c, OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self,
		OuterPackage2.C.self, OuterPackage2.C.that, OuterPackage2.C.that.self, OuterPackage3.C,
		OuterPackage3.C.b, OuterPackage3.C.b.a1, OuterPackage3.C.b.a1.self, OuterPackage3.C.b.a1.that,
		OuterPackage3.C.b.a1.that.self, OuterPackage3.C.b.self, OuterPackage3.C.b.that, OuterPackage3.C.b.that.self,
		OuterPackage3.C.c, OuterPackage3.C.c.self, OuterPackage3.C.c.that, OuterPackage3.C.c.that.self,
		OuterPackage3.C.self, OuterPackage3.C.that, OuterPackage3.C.that.self, OuterPackage3.D,
		OuterPackage3.D.b, OuterPackage3.D.b.a1, OuterPackage3.D.b.a1.self, OuterPackage3.D.b.a1.that,
		OuterPackage3.D.b.a1.that.self, OuterPackage3.D.b.self, OuterPackage3.D.b.that, OuterPackage3.D.b.that.self,
		OuterPackage3.D.c, OuterPackage3.D.c.self, OuterPackage3.D.c.that, OuterPackage3.D.c.that.self,
		OuterPackage3.D.f, OuterPackage3.D.f.a1, OuterPackage3.D.f.a1.self, OuterPackage3.D.f.a1.that,
		OuterPackage3.D.f.a1.that.self, OuterPackage3.D.f.self, OuterPackage3.D.f.that, OuterPackage3.D.f.that.self,
		OuterPackage3.D.self, OuterPackage3.D.that, OuterPackage3.D.that.self, b, b.a1, b.a1.self, b.a1.that,
		b.a1.that.self, b.self, b.that, b.that.self, c, c.self, c.that, c.that.self, f, f.a1,
		f.a1.self, f.a1.that, f.a1.that.self, f.self, f.that, f.that.self, self, test.D, test.D.b,
		test.D.b.a1, test.D.b.a1.self, test.D.b.a1.that, test.D.b.a1.that.self, test.D.b.self,
		test.D.b.that, test.D.b.that.self, test.D.c, test.D.c.self, test.D.c.that, test.D.c.that.self,
		test.D.f, test.D.f.a1, test.D.f.a1.self, test.D.f.a1.that, test.D.f.a1.that.self,
		test.D.f.self, test.D.f.that, test.D.f.that.self, test.D.self, test.D.that, test.D.that.self,
		test.EE, test.EE.b, test.EE.b.a1, test.EE.b.a1.self, test.EE.b.a1.that,
		test.EE.b.a1.that.self, test.EE.b.self, test.EE.b.that, test.EE.b.that.self, test.EE.c, test.EE.c.self,
		test.EE.c.that, test.EE.c.that.self, test.EE.f, test.EE.f.a1, test.EE.f.a1.self,
		test.EE.f.a1.that, test.EE.f.a1.that.self, test.EE.f.self, test.EE.f.that, test.EE.f.that.self,
		test.EE.self, test.EE.that, test.EE.that.self, test.EE.try, test.EE.try.a1,
		test.EE.try.a1.self, test.EE.try.a1.that, test.EE.try.a1.that.self, test.EE.try.self,
		test.EE.try.that, test.EE.try.that.self, that, that.self, try, try.a1, try.a1.self, try.a1.that,
		try.a1.that.self, try.self, try.that, try.that.self
		--- */
		feature try : b;
	}
}
