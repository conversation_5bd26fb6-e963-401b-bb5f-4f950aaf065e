//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	classifier P{
		classifier PP {
			protected classifier PPP{
				classifier PPPP{}
			}
		}
	}
	classifier C specializes P::PP {
		feature CC: PPP;
	}
	classifier D specializes C {
		feature DD: PPP;
	}
	classifier E specializes D {
		feature EE: PPP;
		feature GG: PPP::PPPP;
	}
}
