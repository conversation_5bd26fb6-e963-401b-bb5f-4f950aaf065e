// SysMLv2 Language Server Web Worker
// Hybrid implementation: tries Langium first, falls back to simplified

// Worker 全局作用域类型
interface WorkerGlobalScope {
  postMessage(message: any): void;
  addEventListener(type: string, listener: (event: any) => void): void;
}

declare const self: WorkerGlobalScope;

// 语言服务器状态
let languageServerStarted = false;
let serverMode: 'langium' | 'simplified' = 'simplified';

// SysMLv2 关键字
const SYSML_KEYWORDS = [
  'package', 'part', 'def', 'attribute', 'connection', 'interface', 'port',
  'action', 'state', 'requirement', 'constraint', 'case', 'view', 'viewpoint',
  'analysis', 'verification', 'use', 'concern', 'item', 'allocation', 'flow',
  'binding', 'succession', 'metadata', 'abstract', 'individual', 'variation',
  'snapshot', 'timeslice', 'ordered', 'nonunique', 'derived', 'readonly',
  'composite', 'end', 'in', 'out', 'inout', 'ref', 'redefines', 'subsets',
  'specializes', 'conjugates', 'references', 'typed', 'by', 'featured',
  'chains', 'inverse', 'of', 'disjoint', 'from', 'import', 'private',
  'protected', 'public', 'all', 'filter', 'alias', 'for', 'about', 'doc',
  'comment', 'language', 'standard', 'library', 'then', 'if', 'else',
  'while', 'do', 'entry', 'exit', 'when', 'at', 'after', 'trigger',
  'guard', 'effect', 'assume', 'require', 'verify', 'satisfy', 'expose',
  'render', 'as', 'hastype', 'istype', 'meta', 'null', 'true', 'false',
  'and', 'or', 'xor', 'not', 'implies'
];

// 尝试初始化 Langium 服务器
async function tryInitializeLangiumServer(): Promise<boolean> {
  try {
    // 尝试使用用户提供的 Langium 实现
    const { BrowserMessageReader, BrowserMessageWriter, createConnection } = await import('vscode-languageserver/browser');
    const { createSysmlServices } = await import('../language/sysml-module');
    const { startLanguageServer } = await import('langium/lsp');

    const messageReader = new BrowserMessageReader(self);
    const messageWriter = new BrowserMessageWriter(self);
    const connection = createConnection(messageReader, messageWriter);

    // 使用正确的上下文格式
    const { shared } = createSysmlServices({
      connection,
      fileSystemProvider: () => ({
        readFile: async () => '',
        readDirectory: async () => [],
        stat: async () => ({ type: 1, ctime: 0, mtime: 0, size: 0 })
      })
    });
    startLanguageServer(shared);

    serverMode = 'langium';
    console.log('Full Langium SysML Language Server started');
    return true;
  } catch (error) {
    console.warn('Langium server initialization failed, using simplified mode:', error);
    return false;
  }
}

// 消息类型定义
export type WorkerMessageType = 
  | 'start' 
  | 'validate' 
  | 'complete' 
  | 'ping'
  | 'server-started' 
  | 'server-error' 
  | 'validation-result' 
  | 'validation-error'
  | 'completion-result' 
  | 'completion-error' 
  | 'pong' 
  | 'error' 
  | 'worker-error';

export interface WorkerMessage {
  type: WorkerMessageType;
  data?: any;
}

// 诊断信息接口
interface Diagnostic {
  severity: number; // 1=Error, 2=Warning, 3=Info
  message: string;
  range: {
    start: { line: number; character: number };
    end: { line: number; character: number };
  };
}

// 补全项接口
interface CompletionItem {
  label: string;
  kind: number;
  detail?: string;
  documentation?: string;
  insertText?: string;
  insertTextRules?: number;
}

// 简化的语法验证函数
function validateSysMLCode(code: string): Diagnostic[] {
  const lines = code.split('\n');
  const diagnostics: Diagnostic[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.length === 0 || line.startsWith('//') || line.startsWith('/*')) {
      continue; // 跳过空行和注释
    }
    
    // 检查基本语法错误
    if (line.includes('syntax error')) {
      diagnostics.push({
        severity: 1, // Error
        message: 'Syntax error detected',
        range: {
          start: { line: i, character: 0 },
          end: { line: i, character: line.length }
        }
      });
    }
    
    // 检查缺少分号的语句
    if ((line.includes('attribute') || line.includes('part ')) && 
        !line.includes(';') && 
        !line.includes('{') && 
        !line.includes('}') &&
        line.length > 0) {
      diagnostics.push({
        severity: 2, // Warning
        message: 'Missing semicolon after statement',
        range: {
          start: { line: i, character: line.length },
          end: { line: i, character: line.length }
        }
      });
    }
    
    // 检查未匹配的大括号
    const openBraces = (line.match(/\{/g) || []).length;
    const closeBraces = (line.match(/\}/g) || []).length;
    if (openBraces !== closeBraces && line.includes('{')) {
      diagnostics.push({
        severity: 2, // Warning
        message: 'Unmatched braces detected',
        range: {
          start: { line: i, character: 0 },
          end: { line: i, character: line.length }
        }
      });
    }
    
    // 检查未知关键字
    const words = line.split(/\s+/);
    for (const word of words) {
      const cleanWord = word.replace(/[^\w]/g, '');
      if (cleanWord.length > 2 && 
          !SYSML_KEYWORDS.includes(cleanWord) &&
          !/^[A-Z][a-zA-Z0-9]*$/.test(cleanWord) && // 可能是类型名
          !/^\d+$/.test(cleanWord) && // 不是数字
          !['true', 'false'].includes(cleanWord)) {
        // 这可能是拼写错误的关键字
        const suggestions = SYSML_KEYWORDS.filter(keyword => 
          keyword.toLowerCase().includes(cleanWord.toLowerCase()) ||
          cleanWord.toLowerCase().includes(keyword.toLowerCase())
        );
        
        if (suggestions.length > 0) {
          diagnostics.push({
            severity: 3, // Info
            message: `Unknown keyword '${cleanWord}'. Did you mean: ${suggestions.slice(0, 3).join(', ')}?`,
            range: {
              start: { line: i, character: line.indexOf(word) },
              end: { line: i, character: line.indexOf(word) + word.length }
            }
          });
        }
      }
    }
  }
  
  return diagnostics;
}

// 生成代码补全建议
function generateCompletions(): any[] {
  const completions: CompletionItem[] = [];
  
  // 基本关键字补全
  for (const keyword of SYSML_KEYWORDS) {
    completions.push({
      label: keyword,
      kind: 14, // Keyword
      detail: `SysML v2 keyword`,
      insertText: keyword,
      documentation: `SysML v2 keyword: ${keyword}`
    });
  }
  
  // 常用代码片段
  const snippets = [
    {
      label: 'package',
      kind: 15, // Snippet
      detail: 'Package definition',
      insertText: 'package ${1:PackageName} {\n\t$0\n}',
      insertTextRules: 4, // InsertAsSnippet
      documentation: 'Create a new package definition'
    },
    {
      label: 'part def',
      kind: 15,
      detail: 'Part definition',
      insertText: 'part def ${1:PartName} {\n\t$0\n}',
      insertTextRules: 4,
      documentation: 'Create a new part definition'
    },
    {
      label: 'attribute',
      kind: 15,
      detail: 'Attribute declaration',
      insertText: 'attribute ${1:name} : ${2:Type};',
      insertTextRules: 4,
      documentation: 'Declare an attribute'
    },
    {
      label: 'connection',
      kind: 15,
      detail: 'Connection definition',
      insertText: 'connection ${1:name} connect ${2:source} to ${3:target};',
      insertTextRules: 4,
      documentation: 'Define a connection between parts'
    },
    {
      label: 'requirement def',
      kind: 15,
      detail: 'Requirement definition',
      insertText: 'requirement def ${1:RequirementName} {\n\t$0\n}',
      insertTextRules: 4,
      documentation: 'Create a new requirement definition'
    },
    {
      label: 'constraint',
      kind: 15,
      detail: 'Constraint definition',
      insertText: 'constraint ${1:constraintName} {\n\t${2:expression}\n}',
      insertTextRules: 4,
      documentation: 'Define a constraint'
    },
    {
      label: 'state def',
      kind: 15,
      detail: 'State definition',
      insertText: 'state def ${1:StateName} {\n\tstate ${2:stateName};\n\t$0\n}',
      insertTextRules: 4,
      documentation: 'Create a new state definition'
    },
    {
      label: 'action def',
      kind: 15,
      detail: 'Action definition',
      insertText: 'action def ${1:ActionName} {\n\t$0\n}',
      insertTextRules: 4,
      documentation: 'Create a new action definition'
    }
  ];
  
  completions.push(...snippets);
  
  return completions;
}

// 启动语言服务器
async function startSysMLLanguageServer(): Promise<void> {
  if (languageServerStarted) {
    return;
  }

  try {
    // 首先尝试 Langium
    const langiumSuccess = await tryInitializeLangiumServer();

    if (!langiumSuccess) {
      // 回退到简化模式
      serverMode = 'simplified';
      console.log('Using simplified SysML Language Server');
    }

    languageServerStarted = true;

    self.postMessage({
      type: 'server-started',
      data: { success: true, mode: serverMode }
    });
  } catch (error) {
    console.error('Failed to start language server:', error);
    self.postMessage({
      type: 'server-error',
      data: { error: error instanceof Error ? error.message : String(error) }
    });
  }
}

// 处理代码验证
async function handleValidation(data: { code: string; uri: string }): Promise<void> {
  try {
    const diagnostics = validateSysMLCode(data.code);
    
    // 发送结果
    self.postMessage({
      type: 'validation-result',
      data: {
        uri: data.uri,
        diagnostics: diagnostics.map(d => ({
          severity: d.severity,
          message: d.message,
          range: d.range,
          source: 'SysML'
        }))
      }
    });
  } catch (error) {
    console.error('Validation error:', error);
    self.postMessage({
      type: 'validation-error',
      data: { 
        uri: data.uri, 
        error: error instanceof Error ? error.message : String(error) 
      }
    });
  }
}

// 处理代码补全
async function handleCompletion(data: { 
  code: string; 
  uri: string; 
  position: { line: number; character: number } 
}): Promise<void> {
  try {
    const completions = generateCompletions();
    
    // 发送结果
    self.postMessage({
      type: 'completion-result',
      data: {
        uri: data.uri,
        items: completions
      }
    });
  } catch (error) {
    console.error('Completion error:', error);
    self.postMessage({
      type: 'completion-error',
      data: { 
        uri: data.uri, 
        error: error instanceof Error ? error.message : String(error) 
      }
    });
  }
}

// 监听来自主线程的消息
self.addEventListener('message', (event: MessageEvent) => {
  const { type, data } = event.data;
  
  try {
    switch (type) {
      case 'start':
        startSysMLLanguageServer();
        break;
      case 'validate':
        handleValidation(data);
        break;
      case 'complete':
        handleCompletion(data);
        break;
      case 'ping':
        // 健康检查
        self.postMessage({
          type: 'pong',
          data: { 
            timestamp: Date.now(),
            serverRunning: languageServerStarted 
          }
        });
        break;
      default:
        console.warn('Unknown message type:', type);
        self.postMessage({
          type: 'error',
          data: { error: `Unknown message type: ${type}` }
        });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    self.postMessage({
      type: 'error',
      data: { 
        error: error instanceof Error ? error.message : String(error),
        originalType: type
      }
    });
  }
});

// 全局错误处理
self.addEventListener('error', (event: ErrorEvent) => {
  console.error('Worker error:', event.error);
  self.postMessage({
    type: 'worker-error',
    data: { 
      error: event.error?.message || 'Unknown worker error',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    }
  });
});

// 未处理的 Promise 拒绝
self.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
  console.error('Unhandled promise rejection in worker:', event.reason);
  self.postMessage({
    type: 'worker-error',
    data: { 
      error: event.reason instanceof Error ? event.reason.message : String(event.reason),
      type: 'unhandledrejection'
    }
  });
});

// 导出空对象以使其成为模块
export {};
