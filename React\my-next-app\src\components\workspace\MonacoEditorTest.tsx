'use client';

import React from 'react';
import Editor from '@monaco-editor/react';

const MonacoEditorTest: React.FC = () => {
  const handleEditorChange = (value: string | undefined) => {
    console.log('Editor content changed:', value);
  };

  const handleEditorDidMount = (editor: any, monaco: any) => {
    console.log('Monaco Editor mounted successfully!');
    console.log('Editor instance:', editor);
    console.log('Monaco instance:', monaco);

    // 注册 SysML 语言（简化版）
    if (!monaco.languages.getLanguages().find((lang: any) => lang.id === 'sysml')) {
      monaco.languages.register({
        id: 'sysml',
        extensions: ['.sysml', '.sysmlv2'],
        aliases: ['SysML', 'sysml']
      });

      // 简单的语法高亮
      monaco.languages.setMonarchTokensProvider('sysml', {
        keywords: [
          'package', 'part', 'attribute', 'port', 'connection', 'interface',
          'requirement', 'constraint', 'action', 'state', 'transition',
          'def', 'use', 'case', 'analysis', 'verification', 'doc',
          'subject', 'objective', 'method', 'entry', 'first', 'accept',
          'do', 'then', 'return'
        ],
        tokenizer: {
          root: [
            [/[a-z_$][\w$]*/, {
              cases: {
                '@keywords': 'keyword',
                '@default': 'identifier'
              }
            }],
            [/[A-Z][\w\$]*/, 'type.identifier'],
            [/\/\/.*$/, 'comment'],
            [/\/\*/, 'comment', '@comment'],
            [/".*?"/, 'string'],
            [/\d+/, 'number'],
            [/[{}()\[\]]/, '@brackets'],
            [/[<>](?!@symbols)/, '@brackets'],
            [/[;,.]/, 'delimiter'],
          ],
          comment: [
            [/[^\/*]+/, 'comment'],
            [/\/\*/, 'comment', '@push'],
            [/\*\//, 'comment', '@pop'],
            [/[\/*]/, 'comment']
          ],
        },
      });
    }
  };

  const sysmlSampleCode = `// SysML v2 示例代码
package VehicleSystem {

    // 定义车辆部件
    part def Vehicle {
        part engine : Engine;
        part transmission : Transmission;
        part wheels : Wheel[4];

        // 车辆属性
        attribute mass : Real;
        attribute maxSpeed : Real;

        // 端口定义
        port fuelInput : FuelPort;
        port powerOutput : PowerPort;
    }

    // 引擎定义
    part def Engine {
        attribute power : Real;
        attribute fuelConsumption : Real;

        port fuelIn : FuelPort;
        port powerOut : PowerPort;

        // 引擎约束
        constraint powerConstraint {
            power <= 500.0 // 最大功率限制
        }
    }

    // 需求定义
    requirement def PerformanceRequirement {
        doc /* 车辆性能需求 */

        requirement accelerationReq {
            doc /* 0-100km/h 加速时间不超过10秒 */
            subject vehicle : Vehicle;

            constraint {
                vehicle.maxSpeed >= 100.0
            }
        }
    }
}`;

  return (
    <div className="w-full h-96 border border-gray-300 rounded">
      <Editor
        height="100%"
        defaultLanguage="sysml"
        defaultValue={sysmlSampleCode}
        theme="vs-dark"
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        options={{
          fontSize: 14,
          lineNumbers: 'on',
          minimap: { enabled: true },
          scrollBeyondLastLine: false,
          wordWrap: 'on',
          automaticLayout: true,
          folding: true,
          contextmenu: true,
          mouseWheelZoom: true,
        }}
        loading={
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-500">加载 Monaco Editor...</div>
          </div>
        }
      />
    </div>
  );
};

export default MonacoEditorTest;
