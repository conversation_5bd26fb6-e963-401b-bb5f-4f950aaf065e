//* 
XPECT_SETUP org.omg.sysml.xpect.tests.validation.invalid.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/Links.kerml"}
       	File {from ="/library.kernel/Occurrences.kerml"}
       	File {from ="/library.kernel/Objects.kerml"}
       	File {from ="/library.kernel/Metaobjects.kerml"}
       	File {from ="/library.kernel/Performances.kerml"}
       	File {from ="/library.kernel/ScalarValues.kerml"}
       	File {from ="/library.kernel/BaseFunctions.kerml"}
       	File {from ="/library.kernel/DataFunctions.kerml"}
       	File {from ="/library.kernel/ScalarFunctions.kerml"}
       	File {from ="/library.kernel/ControlFunctions.kerml"}
       	File {from ="/library.kernel/KerML.kerml"}
       	File {from ="/library.systems/Attributes.sysml"}
       	File {from ="/library.systems/Items.sysml"}
       	File {from ="/library.systems/Metadata.sysml"}
       	File {from ="/library.systems/Actions.sysml"}
       	File {from ="/library.systems/Calculations.sysml"}
       	File {from ="/library.systems/SysML.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/Links.kerml"}
		       	File {from ="/library.kernel/Occurrences.kerml"}
		       	File {from ="/library.kernel/Objects.kerml"}
		       	File {from ="/library.kernel/Metaobjects.kerml"}
		       	File {from ="/library.kernel/Performances.kerml"}
		       	File {from ="/library.kernel/ScalarValues.kerml"}
		       	File {from ="/library.kernel/BaseFunctions.kerml"}
		       	File {from ="/library.kernel/DataFunctions.kerml"}
		       	File {from ="/library.kernel/ScalarFunctions.kerml"}
		       	File {from ="/library.kernel/ControlFunctions.kerml"}
		       	File {from ="/library.kernel/KerML.kerml"}
		       	File {from ="/library.systems/Attributes.sysml"}
		       	File {from ="/library.systems/Items.sysml"}
       			File {from ="/library.systems/Metadata.sysml"}
       			File {from ="/library.systems/Actions.sysml"}
       			File {from ="/library.systems/Calculations.sysml"}
		       	File {from ="/library.systems/SysML.sysml"}
			}
		}
	}
END_SETUP 
*/
package Test {
	
	metadata def A {
		ref :>> annotatedElement : SysML::AttributeUsage;
		attribute x : ScalarValues::Integer;
		attribute y : ScalarValues::String;
		attribute z : ScalarValues::Boolean;
		attribute u {
			attribute v : ScalarValues::Integer;
		}
	}
	
	calc def f { in x : ScalarValues::Boolean; }
	
	// XPECT errors --> "Must be model-level evaluable" at "filter f(A::z);"
	filter f(A::z);
	// XPECT errors --> "Must be model-level evaluable" at "filter ~A::z;"
	filter ~A::z;
	// XPECT errors --> "Must be model-level evaluable" at "filter A::y->ControlFunctions::collect {in ref x; x};"
	filter A::y->ControlFunctions::collect {in ref x; x};
	// XPECT errors --> "Must have a Boolean result" at "filter new A(null, 1, "", false);"
	filter new A(null, 1, "", false);
	
	enum def E :> ScalarValues::String {
		enum e = "e";
	}
	
	attribute bad;
	
	attribute a {
		@A {
			// XPECT errors --> "Must be model-level evaluable" at "= ~3"
			x = ~3;
			y = E::e;
			// XPECT errors --> "Must be model-level evaluable" at "= f(A::z)"
			z = f(A::z);
			u {
				v = 1;
				// XPECT errors --> "Must redefine an owning-type feature" at "bad;"
				bad;
			}		
		}
	}
	
	item p {
		// XPECT errors --> "Cannot annotate ItemUsage" at "@A;"
		@A;
		// XPECT errors --> "Must have a concrete type" at "@Metaobjects::Metaobject;"
		@Metaobjects::Metaobject;
	}
	
}
