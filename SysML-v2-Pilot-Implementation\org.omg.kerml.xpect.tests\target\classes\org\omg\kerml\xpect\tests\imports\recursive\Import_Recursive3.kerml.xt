//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.recursive.KerMLImportRecursiveTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package RecursiveImport {
  
  package P {
    package X {
    	classifier X1 {
    		classifier X2;
    	}
    }
    package Q {
      classifier Y;
      package R {
        classifier Z;
      }
    }
    package S {
      classifier S1 specializes X1;
      package T {
        classifier T1;
        package U {
        	classifier U1;
        }
      }
    }
    
  }
  
  public import P::**;
  	x1: X1;
	y: Y;
	z: Z;
	s: S1;
	t: T1;
	u: U1;
	
	
	
	//* XPECT scope at S::S1::X2 ---
	P.Q.R.Z, P.Q.R.Z.self, P.Q.R.Z.self.that, <PERSON>.Q.Y, <PERSON>.<PERSON>.<PERSON>.self, P.Q.Y.self.that,
	P.S.S1, P.S.S1.X2, P.S.S1.X2.self, P.S.S1.X2.self.that, P.S.S1.self, P.S.S1.self.that,
	P.S.T.T1, P.S.T.T1.self, P.S.T.T1.self.that, P.S.T.U.U1, P.S.T.U.U1.self,
	P.S.T.U.U1.self.that, P.X.X1, P.X.X1.X2, P.X.X1.X2.self, P.X.X1.X2.self.that, P.X.X1.self,
	P.X.X1.self.that, Q.R.Z, Q.Y, R.Z, RecursiveImport.P.Q.R.Z, RecursiveImport.P.Q.R.Z.self,
	RecursiveImport.P.Q.R.Z.self.that, RecursiveImport.P.Q.Y, RecursiveImport.P.Q.Y.self,
	RecursiveImport.P.Q.Y.self.that, RecursiveImport.P.S.S1, RecursiveImport.P.S.S1.X2,
	RecursiveImport.P.S.S1.X2.self, RecursiveImport.P.S.S1.X2.self.that, RecursiveImport.P.S.S1.self,
	RecursiveImport.P.S.S1.self.that, RecursiveImport.P.S.T.T1, RecursiveImport.P.S.T.T1.self,
	RecursiveImport.P.S.T.T1.self.that, RecursiveImport.P.S.T.U.U1, RecursiveImport.P.S.T.U.U1.self,
	RecursiveImport.P.S.T.U.U1.self.that, RecursiveImport.P.X.X1, RecursiveImport.P.X.X1.X2,
	RecursiveImport.P.X.X1.X2.self, RecursiveImport.P.X.X1.X2.self.that, RecursiveImport.P.X.X1.self,
	RecursiveImport.P.X.X1.self.that, RecursiveImport.Q.R.Z, RecursiveImport.Q.Y, RecursiveImport.R.Z,
	RecursiveImport.S.S1, RecursiveImport.S.S1.X2, RecursiveImport.S.T.T1, RecursiveImport.S.T.U.U1,
	RecursiveImport.S1, RecursiveImport.S1.X2, RecursiveImport.T.T1, RecursiveImport.T.U.U1,
	RecursiveImport.T1, RecursiveImport.U.U1, RecursiveImport.U1, RecursiveImport.X.X1,
	RecursiveImport.X.X1.X2, RecursiveImport.X1, RecursiveImport.X1.X2, RecursiveImport.X2,
	RecursiveImport.Y, RecursiveImport.Z, RecursiveImport.s, RecursiveImport.s.X2,
	RecursiveImport.s.X2.self, RecursiveImport.s.X2.self.that, RecursiveImport.s.self,
	RecursiveImport.s.self.that, RecursiveImport.s.that, RecursiveImport.s.that.self, RecursiveImport.t,
	RecursiveImport.t.self, RecursiveImport.t.self.that, RecursiveImport.t.that,
	RecursiveImport.t.that.self, RecursiveImport.u, RecursiveImport.u.self, RecursiveImport.u.self.that,
	RecursiveImport.u.that, RecursiveImport.u.that.self, RecursiveImport.x1, RecursiveImport.x1.X2,
	RecursiveImport.x1.X2.self, RecursiveImport.x1.X2.self.that, RecursiveImport.x1.self,
	RecursiveImport.x1.self.that, RecursiveImport.x1.that, RecursiveImport.x1.that.self, RecursiveImport.xx1,
	RecursiveImport.xx1.self, RecursiveImport.xx1.self.that, RecursiveImport.xx1.that,
	RecursiveImport.xx1.that.self, RecursiveImport.y, RecursiveImport.y.self, RecursiveImport.y.self.that,
	RecursiveImport.y.that, RecursiveImport.y.that.self, RecursiveImport.z, RecursiveImport.z.self,
	RecursiveImport.z.self.that, RecursiveImport.z.that, RecursiveImport.z.that.self, S.S1, S.S1.X2, S.T.T1,
	S.T.U.U1, S1, S1.X2, T.T1, T.U.U1, T1, U.U1, U1, X.X1, X.X1.X2, X1, X1.X2, X2, Y, Z, s,
	s.X2, s.X2.self, s.X2.self.that, s.self, s.self.that, s.that, s.that.self, t, t.self,
	t.self.that, t.that, t.that.self, u, u.self, u.self.that, u.that, u.that.self, x1, x1.X2,
	x1.X2.self, x1.X2.self.that, x1.self, x1.self.that, x1.that, x1.that.self, xx1, xx1.self,
	xx1.self.that, xx1.that, xx1.that.self, y, y.self, y.self.that, y.that, y.that.self, z,
	z.self, z.self.that, z.that, z.that.self
	--- */
	
	xx1: S::S1::X2;
}
