Manifest-Version: 1.0
Automatic-Module-Name: org.omg.sysml.xtext.ui
Bundle-ManifestVersion: 2
Bundle-Name: org.omg.sysml.xtext.ui
Bundle-Version: 0.51.0.202509041722
Bundle-SymbolicName: org.omg.sysml.xtext.ui; singleton:=true
Bundle-ActivationPolicy: lazy
Require-Bundle: org.omg.sysml;bundle-version="0.2.0",org.omg.sysml.xtext
 ,org.omg.sysml.xtext.ide,org.eclipse.compare,org.eclipse.ui,org.eclipse
 .ui.editors,org.eclipse.xtend.lib;bundle-version="2.14.0",org.eclipse.x
 text.builder,org.eclipse.xtext.common.types.ui,org.eclipse.xtext.ui,org
 .eclipse.xtext.ui.codetemplates.ui,org.eclipse.xtext.ui.shared,org.omg.
 kerml.xtext;bundle-version="0.2.0",org.omg.kerml.xtext.ui;bundle-versio
 n="0.2.0",org.omg.kerml.expressions.xtext,org.omg.kerml.expressions.xte
 xt.ide,org.omg.kerml.expressions.xtext.ui,com.google.gson,org.eclipse.e
 mf.ecore.editor,org.eclipse.core.expressions
Import-Package: org.apache.log4j
Export-Package: org.omg.sysml.xtext.ui,org.omg.sysml.xtext.ui.internal
Bundle-Activator: org.omg.sysml.xtext.ui.SysMLActivator
Bundle-Vendor: SysML v2 Submission Team
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

