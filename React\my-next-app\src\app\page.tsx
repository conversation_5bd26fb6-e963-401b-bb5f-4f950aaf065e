'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useApp } from '@/contexts/AppContext';

export default function Home() {
  const router = useRouter();
  const { state } = useApp();

  useEffect(() => {
    // 如果已经认证，跳转到工作区
    if (state.auth.isAuthenticated) {
      router.push('/workspace');
    } else if (!state.auth.isLoading) {
      // 如果未认证且不在加载中，跳转到登录页
      router.push('/login');
    }
  }, [state.auth.isAuthenticated, state.auth.isLoading, router]);

  // 显示加载界面
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-4 text-gray-600">正在加载...</p>
      </div>
    </div>
  );
}
