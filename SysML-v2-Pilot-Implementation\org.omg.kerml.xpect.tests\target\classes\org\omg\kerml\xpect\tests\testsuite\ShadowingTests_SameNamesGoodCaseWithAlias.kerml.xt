//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package test{
	alias A1 for A;
	classifier A{
		classifier a1{}
	}
	//A.a2, B.a2 should not be included.
	//XPECT linkedName at A --> test.A
	//* XPECT scope at A ---
		    A, A.a1, A1, A1.a1, B, B.a1, B.A, B.A.a2, B.b, 
		    test.A, test.A.a1, test.A1, test.A1.a1, test.B, test.B.a1, test.B.A, test.B.A.a2, test.B.b,
		--- */
	classifier B specializes A{
		classifier A{
			classifier a2{}
		}
		//XPECT linkedName at a1 --> test.A.a1
		//* XPECT scope at a1 ---
		    A, A.a2, A1, A1.a1, B, B.a1, B.A, B.A.a2, B.b, 
		    a1, b, test.A, test.A.a1, test.A1, test.A1.a1, test.B, test.B.a1, test.B.A, test.B.A.a2, test.B.b,
		--- */
		classifier b specializes a1{}
	}
}
