{"author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "bugs": "https://github.com/syntax-tree/hast-util-to-jsx-runtime/issues", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "dependencies": {"@types/estree": "^1.0.0", "@types/hast": "^3.0.0", "@types/unist": "^3.0.0", "comma-separated-tokens": "^2.0.0", "devlop": "^1.0.0", "estree-util-is-identifier-name": "^3.0.0", "hast-util-whitespace": "^3.0.0", "mdast-util-mdx-expression": "^2.0.0", "mdast-util-mdx-jsx": "^3.0.0", "mdast-util-mdxjs-esm": "^2.0.0", "property-information": "^7.0.0", "space-separated-tokens": "^2.0.0", "style-to-js": "^1.0.0", "unist-util-position": "^5.0.0", "vfile-message": "^4.0.0"}, "description": "hast utility to transform to preact, react, solid, svelte, vue, etc", "devDependencies": {"@types/node": "^22.0.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "c8": "^10.0.0", "esbuild": "^0.25.0", "estree-util-visit": "^2.0.0", "hastscript": "^9.0.0", "prettier": "^3.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "remark-cli": "^12.0.0", "remark-preset-wooorm": "^11.0.0", "sval": "^0.6.0", "type-coverage": "^2.0.0", "typescript": "^5.0.0", "xo": "^0.60.0"}, "exports": "./index.js", "files": ["lib/", "index.d.ts", "index.js"], "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "keywords": ["hast-util", "hast", "html", "preact", "react", "solid", "svelte", "unist", "utility", "util", "vue"], "license": "MIT", "name": "hast-util-to-jsx-runtime", "prettier": {"bracketSpacing": false, "semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "none", "useTabs": false}, "remarkConfig": {"plugins": ["remark-preset-wooorm"]}, "repository": "syntax-tree/hast-util-to-jsx-runtime", "scripts": {"build": "tsc --build --clean && tsc --build && type-coverage", "format": "remark --frail --output --quiet -- . && prettier --log-level warn --write -- . && xo --fix", "generate": "esbuild --bundle --format=esm --minify --outfile=example/hast-util-to-jsx-runtime.min.js --target=es2020 .", "prepack": "npm run build && npm run format", "test-api": "node --conditions development test/index.js", "test-coverage": "c8 --100 --reporter lcov -- npm run test-api", "test": "npm run generate && npm run build && npm run format && npm run test-coverage"}, "sideEffects": false, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "ignoreFiles": ["example/**/*.js"], "strict": true}, "type": "module", "version": "2.3.6", "xo": {"overrides": [{"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/array-type": ["error", {"default": "generic"}], "@typescript-eslint/ban-ts-comment": 0, "@typescript-eslint/ban-types": ["error", {"extendDefaults": true}], "@typescript-eslint/consistent-type-definitions": ["error", "interface"]}}], "prettier": true, "rules": {"logical-assignment-operators": "off", "unicorn/prefer-at": "off", "unicorn/prefer-string-replace-all": "off", "unicorn/prevent-abbreviations": "off"}}}