name: Verification Peer Builds

on:
  push:
    branches:
      - 'peer'
  workflow_dispatch:

jobs:
  peerVerify:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Vol<PERSON>
      uses: volta-cli/action@v4

    - name: Use pnpm
      uses: pnpm/action-setup@v3
      with:
        version: 9

    - name: Install
      shell: bash
      run: |
        npm ci

    - name: Versions Report
      shell: bash
      run: |
        npm run report:versions

    - name: Execute peer verification
      shell: bash
      run: |
        bash ./verify/buildPeers.sh
