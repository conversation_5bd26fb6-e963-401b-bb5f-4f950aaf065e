# SysML v2 建模平台 - 开发指南

## 快速开始

### 1. 安装依赖

确保您的系统已安装 Node.js (版本 18 或更高)。

```bash
cd React/my-next-app
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动。

### 3. 测试账号

在开发模式下，您可以使用以下测试账号：

- **用户名**: `admin`
- **密码**: `password123`

或者

- **用户名**: `developer`  
- **密码**: `password123`

您也可以注册新账号进行测试。

## 功能演示

### 1. 用户认证
- 访问 http://localhost:3000 会自动重定向到登录页面
- 使用测试账号登录或注册新账号
- 登录成功后会跳转到工作区

### 2. 项目管理
- 点击"新建项目"创建项目
- 在项目树中右键点击项目可以重命名或删除
- 右键点击项目可以创建命名空间

### 3. 代码编辑
- 在命名空间下创建 SysML v2 图
- 双击图文件在编辑器中打开
- 编辑器支持多标签页
- 代码会自动进行语法验证

### 4. 图形预览
- 在编辑器中输入 SysML v2 代码
- 点击"生成图"按钮生成 SVG 预览
- 预览区域支持缩放和平移

### 5. AI 功能
- 点击"AI 生成"按钮生成示例代码
- 系统会生成一个完整的 SysML v2 模型示例

## 开发模式特性

当前应用运行在开发模式下，使用模拟数据：

- **模拟 API**: 所有 API 调用都使用本地模拟数据
- **示例项目**: 预置了"智能汽车系统"和"航空航天系统"两个示例项目
- **示例代码**: 包含系统架构图、需求模型、状态机图等示例
- **模拟延迟**: API 调用包含真实的网络延迟模拟

## 项目结构说明

```
src/
├── app/                    # Next.js 页面路由
├── components/            # React 组件
│   ├── auth/              # 认证页面
│   ├── ui/                # 通用 UI 组件
│   └── workspace/         # 工作区组件
├── contexts/              # React Context 状态管理
├── types/                 # TypeScript 类型定义
└── utils/                 # 工具函数
    ├── api.ts             # API 调用（支持模拟数据）
    ├── mockData.ts        # 模拟数据
    ├── validation.ts      # 表单验证
    └── helpers.ts         # 通用工具
```

## 技术栈

- **Next.js 15**: React 框架，使用 App Router
- **React 19**: UI 库
- **TypeScript**: 类型安全
- **Tailwind CSS 4**: 样式框架
- **React Context**: 状态管理

## 开发工具

### 1. 代码检查
```bash
npm run lint
```

### 2. 类型检查
```bash
npx tsc --noEmit
```

### 3. 构建生产版本
```bash
npm run build
```

### 4. 启动生产服务器
```bash
npm start
```

## 自定义配置

### 环境变量

创建 `.env.local` 文件来配置环境变量：

```env
# API 基础 URL（如果不设置，将使用模拟数据）
NEXT_PUBLIC_API_URL=http://localhost:8000/api

# 其他配置...
```

### 模拟数据

模拟数据定义在 `src/utils/mockData.ts` 中，您可以：

- 添加更多示例项目
- 修改示例代码
- 调整 API 延迟时间
- 自定义用户数据

## 常见问题

### Q: 如何添加新的 SysML v2 语法支持？
A: 在 `mockValidateCode` 函数中添加语法检查规则，在 `mockAIGenerate` 中添加生成模板。

### Q: 如何自定义 SVG 图形样式？
A: 修改 `mockData.ts` 中的 `mockSVGContent` 变量。

### Q: 如何连接真实的后端 API？
A: 设置 `NEXT_PUBLIC_API_URL` 环境变量，系统会自动切换到真实 API 模式。

### Q: 如何添加新的 UI 组件？
A: 在 `src/components/ui/` 目录下创建新组件，遵循现有的设计模式。

## 下一步开发

1. **Monaco Editor 集成**: 替换简单文本框为完整的 Monaco Editor
2. **语法高亮**: 实现 SysML v2 语法高亮
3. **代码补全**: 实现智能代码补全
4. **实时协作**: 添加多用户协作编辑功能
5. **版本控制**: 集成 Git 版本控制
6. **插件系统**: 支持第三方插件扩展

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。
