'use client';

import React, { useState, useEffect } from 'react';

interface BuildTestResult {
  name: string;
  status: 'success' | 'error' | 'loading';
  message: string;
  details?: string;
}

const BuildTest: React.FC = () => {
  const [results, setResults] = useState<BuildTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runBuildTests = async () => {
    setIsRunning(true);
    const testResults: BuildTestResult[] = [];

    // Test 1: Monaco Editor import
    try {
      const monaco = await import('monaco-editor');
      testResults.push({
        name: 'Monaco Editor Import',
        status: 'success',
        message: 'Monaco Editor imported successfully',
        details: `Version: ${monaco.editor.VERSION || 'Unknown'}`
      });
    } catch (error) {
      testResults.push({
        name: 'Monaco Editor Import',
        status: 'error',
        message: 'Failed to import Monaco Editor',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // Test 2: Monaco Editor Wrapper import
    try {
      const { MonacoEditorLanguageClientWrapper } = await import('monaco-editor-wrapper');
      testResults.push({
        name: 'Monaco Editor Wrapper Import',
        status: 'success',
        message: 'Monaco Editor Wrapper imported successfully',
        details: 'MonacoEditorLanguageClientWrapper class available'
      });
    } catch (error) {
      testResults.push({
        name: 'Monaco Editor Wrapper Import',
        status: 'error',
        message: 'Failed to import Monaco Editor Wrapper',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // Test 3: VSCode Language Server Protocol (Browser) import
    try {
      const { BrowserMessageReader, BrowserMessageWriter } = await import('vscode-languageserver-protocol/browser');
      testResults.push({
        name: 'VSCode LSP Browser Import',
        status: 'success',
        message: 'VSCode Language Server Protocol (Browser) imported successfully',
        details: 'BrowserMessageReader and BrowserMessageWriter available'
      });
    } catch (error) {
      testResults.push({
        name: 'VSCode LSP Browser Import',
        status: 'error',
        message: 'Failed to import VSCode Language Server Protocol (Browser)',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // Test 4: Langium import
    try {
      const { EmptyFileSystem } = await import('langium');
      const { startLanguageServer } = await import('langium/lsp');
      testResults.push({
        name: 'Langium Import',
        status: 'success',
        message: 'Langium imported successfully',
        details: 'EmptyFileSystem and startLanguageServer available'
      });
    } catch (error) {
      testResults.push({
        name: 'Langium Import',
        status: 'error',
        message: 'Failed to import Langium',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // Test 5: Monaco VSCode API import
    try {
      const { LogLevel } = await import('@codingame/monaco-vscode-api');
      testResults.push({
        name: 'Monaco VSCode API Import',
        status: 'success',
        message: 'Monaco VSCode API imported successfully',
        details: 'LogLevel and other VSCode API components available'
      });
    } catch (error) {
      testResults.push({
        name: 'Monaco VSCode API Import',
        status: 'error',
        message: 'Failed to import Monaco VSCode API',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // Test 6: SysML Language Module import
    try {
      const { createSysmlServices } = await import('@/language/sysml-module');
      testResults.push({
        name: 'SysML Language Module Import',
        status: 'success',
        message: 'SysML Language Module imported successfully',
        details: 'createSysmlServices function available'
      });
    } catch (error) {
      testResults.push({
        name: 'SysML Language Module Import',
        status: 'error',
        message: 'Failed to import SysML Language Module',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    setResults(testResults);
    setIsRunning(false);
  };

  useEffect(() => {
    runBuildTests();
  }, []);

  const successCount = results.filter(r => r.status === 'success').length;
  const errorCount = results.filter(r => r.status === 'error').length;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Build Compatibility Test
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                Testing imports and module resolution for Next.js build compatibility
              </p>
            </div>
            <button
              onClick={runBuildTests}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isRunning ? 'Testing...' : 'Run Tests'}
            </button>
          </div>
        </div>

        {/* Summary */}
        <div className="px-6 py-4 bg-gray-50 border-b">
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-green-600">✅</span>
              <span>Passed: {successCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-red-600">❌</span>
              <span>Failed: {errorCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>Total: {results.length}</span>
            </div>
            {errorCount === 0 && results.length > 0 && (
              <div className="flex items-center space-x-2 text-green-600 font-medium">
                <span>🎉</span>
                <span>All tests passed! Build should work correctly.</span>
              </div>
            )}
          </div>
        </div>

        {/* Test Results */}
        <div className="divide-y divide-gray-200">
          {results.map((result, index) => (
            <div key={index} className="px-6 py-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 text-lg">
                  {result.status === 'success' ? '✅' : result.status === 'error' ? '❌' : '⏳'}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">
                      {result.name}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${
                      result.status === 'success' 
                        ? 'text-green-600 bg-green-50 border-green-200'
                        : result.status === 'error'
                        ? 'text-red-600 bg-red-50 border-red-200'
                        : 'text-blue-600 bg-blue-50 border-blue-200'
                    }`}>
                      {result.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {result.message}
                  </p>
                  {result.details && (
                    <p className="text-xs text-gray-500 mt-2 font-mono bg-gray-100 p-2 rounded">
                      {result.details}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {isRunning && (
          <div className="px-6 py-4 text-center">
            <div className="inline-flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>Running build compatibility tests...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BuildTest;
