Manifest-Version: 1.0
Automatic-Module-Name: org.omg.kerml.expressions.xtext.ui
Bundle-ManifestVersion: 2
Bundle-Name: org.omg.kerml.xtext.ui
Bundle-Vendor: SysML v2 Submission Team
Bundle-Version: 0.51.0.202509041722
Bundle-SymbolicName: org.omg.kerml.expressions.xtext.ui; singleton:=true
Bundle-ActivationPolicy: lazy
Require-Bundle: org.omg.kerml.expressions.xtext,org.omg.kerml.expression
 s.xtext.ide,org.eclipse.xtext.ui,org.eclipse.xtext.ui.shared,org.eclips
 e.xtext.ui.codetemplates.ui,org.eclipse.ui.editors;bundle-version="3.5.
 0",org.eclipse.ui.ide;bundle-version="3.5.0",org.eclipse.ui,org.eclipse
 .compare,org.eclipse.xtext.builder,org.eclipse.xtext.xbase.lib;bundle-v
 ersion="2.14.0",org.eclipse.xtend.lib;bundle-version="2.14.0";resolutio
 n:=optional,org.omg.sysml;bundle-version="0.2.0",org.eclipse.xtext.comm
 on.types.ui
Import-Package: org.apache.log4j
Export-Package: org.omg.kerml.expressions.xtext.ui.quickfix,org.omg.kerm
 l.expressions.xtext.ui.internal
Bundle-Activator: org.omg.kerml.expressions.xtext.ui.internal.XtextActiv
 ator
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

