# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@chevrotain/cst-dts-gen@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/cst-dts-gen@npm:11.0.3"
  dependencies:
    "@chevrotain/gast": "npm:11.0.3"
    "@chevrotain/types": "npm:11.0.3"
    lodash-es: "npm:4.17.21"
  checksum: 10c0/9e945a0611386e4e08af34c2d0b3af36c1af08f726b58145f11310f2aeafcb2d65264c06ec65a32df6b6a65771e6a55be70580c853afe3ceb51487e506967104
  languageName: node
  linkType: hard

"@chevrotain/gast@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/gast@npm:11.0.3"
  dependencies:
    "@chevrotain/types": "npm:11.0.3"
    lodash-es: "npm:4.17.21"
  checksum: 10c0/54fc44d7b4a7b0323f49d957dd88ad44504922d30cb226d93b430b0e09925efe44e0726068581d777f423fabfb878a2238ed2c87b690c0c0014ebd12b6968354
  languageName: node
  linkType: hard

"@chevrotain/regexp-to-ast@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/regexp-to-ast@npm:11.0.3"
  checksum: 10c0/6939c5c94fbfb8c559a4a37a283af5ded8e6147b184a7d7bcf5ad1404d9d663c78d81602bd8ea8458ec497358a9e1671541099c511835d0be2cad46f00c62b3f
  languageName: node
  linkType: hard

"@chevrotain/types@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/types@npm:11.0.3"
  checksum: 10c0/72fe8f0010ebef848e47faea14a88c6fdc3cdbafaef6b13df4a18c7d33249b1b675e37b05cb90a421700c7016dae7cd4187ab6b549e176a81cea434f69cd2503
  languageName: node
  linkType: hard

"@chevrotain/utils@npm:11.0.3":
  version: 11.0.3
  resolution: "@chevrotain/utils@npm:11.0.3"
  checksum: 10c0/b31972d1b2d444eef1499cf9b7576fc1793e8544910de33a3c18e07c270cfad88067f175d0ee63e7bc604713ebed647f8190db45cc8311852cd2d4fe2ef14068
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-039b5553-0838-562a-97c2-30d6e54a7b42-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-039b5553-0838-562a-97c2-30d6e54a7b42-common@npm:15.0.2"
  checksum: 10c0/1d0bd772403ac13b16d841efee04e111c3db77068e6fcd6ce2fa9a5e5a21ed810eff15154132af4bd0cacbf09c617decd522a469fa3eaa6693bb6490059b6b48
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/292e2ec5fafdc27fbad8122f1d80dee47676527b456393e6be89efd35f4b52a0cbd6b3fc4b0053586f896774f6f947f4a8bb3c9a360d79226fd353eb2b17a08b
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/144970ee7775b4982cff1b4e8e1f6d874f885179c0d83be27bc854148ba69dce036630ef18f0eb95a5c7d5e375d99b1bd40327e50d866a67bdebcf010ef493ff
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/579bddc5cc934be5e0d54b171111c4decc603aeac223ccddb1668384d037fb684295fe77c256c1c731590456f19a0e66d91c236f59e15e824d16ddf3d467382f
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/3003284b979a19fcacf58bd415052fa42bcf82e4ad0b820938561281a9c25b5dc3c568cd9938cac6fa4021a48887c0901c31560043320a10ca5a1a9437ec08a2
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-1021b67c-93e5-5c78-a270-cbdb2574d980-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-1021b67c-93e5-5c78-a270-cbdb2574d980-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/073ebe06ea2930585b9d2162bed2ad1c6d0ddf1c52e9b37487162e3b5c329bc0afb4a9aff65b83779025d9344f81f954a270bf0df1e5a88537aea0aec9e205fa
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/b8b1ffe7c5b8df1bacf79670597a5d6b51f506fac70294543449217bcb16575830256f0195dbb9b881e70ef6757d472a902e56d4b4bcdd8716b9f4177930ea0d
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/faa1dadb6d2d11919bac2d9e5038202efec0b4b9425d7993bf72208e03296ef62917acba407662b79916a7c5aaddb06bd97043311a4d093689104a6031ed1a46
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/883901c6e572236d8285b2f42431d5ceb0e8fca818ebdea10f069b4cb3438dbfa20526064551adac332d7537bafa8633b402e3c8c2dc7bcb34750ab8989ac9a5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/d642f3cf896030bd488d84ce11a9c615590a26fd582d905e573c05badde5ce124577b37c1bf7a64d9e566596b5ebef7354e0d1d0e418763a551e1d041f5a91fa
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/dff7d805f0fa25d61f7c921f7741a2468f6af36336c8dcf0f752dedce7aedd6be996990097ee99f63cbd4531eabda616b39ea2e64127a12000e592e459552967
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/6bd3e63c14da9189b00bebe084837e7402c0d406a4e952a0203ae0c15f8f62d6dddcfd893e5a7666fd3add00a25ee7950281027c57677d3ca1e4baaae4e6071c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/b412922a540be66fadc69e62b85c9d8fc181aa8a9c4c54e454a9a0ed509247bec6907c50abe5ffd956051a0cfff24f16bcbf8adc155566ef3d0deab3c4a28d8c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/618463fa2e40825d79c51830fc4a9495460d3ef4e81015766f23c1438ece234130304ef18dbfdd8f34bb0113a5429ee8025bdeb997a0691c2db861c8f61ccc0f
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/0bdee897de4e8cb318ff5df0948378dc1383c51e244abf885cba6e1758af7ec65014628f4bf928eeedc790a71951a1072c2ae79bc4e8b56feba180c5b330588f
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/30e1cfd27044753a3f731a6fdc9c1d237985b406ce6c2a4b67cdd2a94c3ca8e25d1bb158df09db3b9f6e00d911bc271e74a0674e082095ca9cdb3664f77bfe26
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-2a22c7b4-b906-5914-8cd1-3ed912fb738f-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-2a22c7b4-b906-5914-8cd1-3ed912fb738f-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/f2f0d851f617ca4dbe4809512560b0be2d944e7402d02d62a4c79e9ea8e783629c82a71aeef8f97c713b198394def9f72079991c8fce6ff34accf07dddbdbdfa
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@vscode/iconv-lite-umd": "npm:0.7.0"
    jschardet: "npm:3.1.4"
  checksum: 10c0/f696dc613154a96522bd7f45622927ebc68514b579d08aa3ae84ad9629a5f9a05929c86481a18fdefc1c2459df45c5c5d788366c5f718f81339d425118968f60
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/e44e683ba7ff72187cca90a8b9114c9066aaec7e79ae9fd7af4aea9e57b5b5fdea8f4dc22b1a275fed528703a31d00a34a4c43d4bb396a2f8191d079c24ef6de
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/39655f78f105e5a392744659d725b607f8026137ae6a59a2af6ff2009723bf6ffab916b722b1bd28793538524be652fa3a4c1f601b1949af7ead9c4909fb230c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-33833ac7-3af3-5e9d-8fb9-11838d852c59-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-33833ac7-3af3-5e9d-8fb9-11838d852c59-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/467bcef7ac0e6106330598a5a291399de65b56e77cf89e6d1a6e60b0d78dda97839bf7b80cdd587afbae126c6985351460149fd2a44166962e07a9cd900b49e2
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/9fe9859aca9415b0ed89c495ea182e7b42cd93005d7c95779df291d0779e61bda5f6c7606d7f5641f02a0b698fe30a314522b65a652fb8fc13e369c5c79e6688
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/f3fa973c1e7fd1ef30da070d78e63edac23176e95eba8edc43cba2c8614cb5e8e216f8bdc1ba6479abf70200ef1a6f64592260e9bf867f34168e84cc2a715cdc
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/7093563573aea48ca5195d778aaf52eabaf5d8538e5ca4c7d4118b91fc4582ae029c625e56484ebfeb67860204f66b595c5f2d517b78f054d0f601003f981a07
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/8620e57072c7621955edef2e2844a26ad4fc541fb774da8c31c2beb4e2e712b572afca9731ccfd3309daa9c9d649f9db30eb7929e594f616e13c520c8935ba99
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/f928123e9bc379f98480b374758c3e5c5ca6d9b194b482f75b865739d05df78c753db1844a434a7079331000bb52a0b755f20ca9649faabe732904101b932b3a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/a8f98401c17b4746a86dc7dd52b1c3232b3fda18a3394ec67161b0faee031fc5063b3e4b75f54cc32ed4e86d3f7f9bdc6e61131917d81ef69625cd519ac41b50
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/c5e3ee126f9e936224ebfb40e118259144a15ffb75eb9657c88318a2e679d8de6593fd31e7f9afbb5c3e569ac83393925109a10f39f024d4ccd3e97ef2d9d129
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/22a88ff301df96dc3f86959034cfcbe0c3355b546bdecfe00a5c964404338fd91e3884d7c959f717f10d56c9c80a0b01240a021f97e742a450b79a0619e17a36
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/a152e436135c9c09483601c9fc91ca36b03851cde936cfe986170b92bff9f54c479a1e9d05d9b7fa6133b1cb7f2f3653c8a6571c0f95193a2c574e42d37b0b6b
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/61a725898f5439b3ae56fe7e4d721ad2569a8745dd3a741d042237f95300b7967447857991bb496485dc31ca3b39e88529fad2cc552fcb7a7ccb4d97aab9b5a7
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/71edb0d25d1d18c3ea35e3f4cca7f24de834e8665801c9bb2d8a4eebd68a21ac73e1b30e648907995562b6f9f0525a41367c74afb057528cc40efc42490799ba
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/92522845afa29829caa89c5ca844b0b4a86c70334822acc2d13b833c78f79aeb14a25e11e871cd6efa46e8157aa4b01dd8ad6c54031f85fac3db3467f426caee
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/e8a7219fcb86b75c603eacf788d8fc4d030119eb4aabb00887c152c6181a95999d4d9b12c1820c018c00a5b611a39e0499d951ebea2f74565f60e87849aec4db
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-5452e2b7-9081-5f95-839b-4ab3544ce28f-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-5452e2b7-9081-5f95-839b-4ab3544ce28f-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/68b298896123472e2ed0d1d80bbe509f0692463d33d024a26c34b1147affa460b791753f289ca206ce7e43336299ba701c5d7b8d58647cabf075d537c1d6c7bf
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/7e3ccaa36a713d0219ecd7c17790bd2c96cf5282f539d9cb634cb872313b045c376735d0cd49195cba1bb97d2f60bcd9a7433a4bddaba28ac40842f71e724e8b
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/c20c5c0971dbad1951847c6fe9389ccfcf2dc5eeb704b48540d4b5e46f2bb27f0e554a6922921fec8a7329fedcbce3520b3d77820554c5d1cbbef8f261480b59
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/97cfd01fd33e7914e8ee148298870a549887524dd50c59beeb0423c532695ea853de155245d5136672c6de1e45d8ea812544a50ea6193cdb3f1c41431d32b0d1
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/35c5457747a9747d6dc83403d240f01166fcc12ee148bf19f5a0fb549a7d78c515c3637bdfa3c56989a2797b3359ca88bcfe4f7e10b9568c56a929fa9533079a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/3a72337002d2bf0a3401d0c447713f9d5391668c867c88f24f4e237e0e898f0b0c699d65112b618eef49285a3e9805cdbb035b1927e29288d93abe35a2d74148
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/1e16cfbf0305de88c9310071c0a1edefd56bf3d2c4224e5b330ea0511712ce98a24d9a91fcb1517fb63a1eceae8d3b01b5fa86b6eeef79f2505db7a3fd41dc9a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/254fdb26dc428dcb7adb044141aa2c152ad85a7fb103b6376f01c93493dcb7cbe5e0f49cf4d409a3dd5ff29ec6b97ea2fe1e9b579c4ae0d2b2edff5e30efb397
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/98091ddc42fb166dd5eb47177da0444b90bfffe22fc969cc61d813e3dcf5f997958a5a67e20403a7f84b449f0821ae505164fabba8fd35101726508580d004e5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/e7c01049d22207b9da4baea64802cb5e3396013d09ecfaa6884796635c66b10699dd2652b092120ef6f8426cdfecfe6bf6e94fc189bc0df5979bc4bdd660c2ac
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/04031c6ae241c26f21e525e7f103045659aeaea656de982b7609c766818822d184855710bbee947075c20f7e3662a9e40ebbe351dbf72875fe182e92f2df72d5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/6885871768ce0ebd69bbbe0558a43da25ccbbaa19f59c96b8e8e9bd551223b4d21685a07e71aefde3f09079f7750debe6bf9ecc122a0bb4161d5480f3a26fa8d
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/18453f5792a6ed780cf0195ceaf2b79a15cc855ba7b6123d002964f0924e9511d38856f6d4e898c657006c455a569cf83e7b0b4f5e8b511f28854159bfe274e3
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/fe89bc50f7b940d028a3b6761fa37e99e15f19b899778c2d7ba42b4de439ab251b7b4b85e1d41f7613a7e55d106b5a3ba7964af1696ee549ba5dc806ac70b2dd
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/cd16a2e94ca1d9bd7c437e7ebe42797a99675f43ce7b0869e2c540093929fa86286111942c64ec56856ab376091906a0980eb1ae33214935ed6f23d633ca36a9
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/6f95588e269e55dab011d617148fd14845a9e8fb2b3d24cd565c7f44aee18be975b662d22112ad5ec6db3054b5e7f9c911a8e5e5e63e3bb6a779ab64418cda81
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/52837ec1ee21cd01a4c4466037387a5735bf657f58127db656e429d68e05bb1eb692462d60cf7c12ecbce756745803610866136daee6f3b8b9476d3fd4dc9e6e
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/076c2c01b3bf353f88a1d267f50a05886b570afbb3f81301530ca05fd563246ffcf509944b26f37660753ebdec3eaf94332723f52aaab81a98b7be033eb45433
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/ea6997a57e2c8f22a80b3489a5cd18bfc6ed2164a1b1cc814f66c13a30e9bfb69c60cf4eb52364b79f73b5135ba9425021213694ddadd2accf6b3d2d030968f6
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/8d99b344e180d6964aa3790093576181b7b0fbc2bcb57c6bd520823780fc15392a7bacb877b492f45724f47880416fd8dde41d5dbbde0afe184c098c10ec2bc3
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    marked: "npm:14.0.0"
  checksum: 10c0/af6c381b50e54ff4a8c937997db6fda0a96ccddab9993a3d7166956045bf3a158ba6e4315adc860d7fbb0bf451157fb311241d5d6f7fd62da54bcf2b375a1a7f
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/8a265c2dac5f87d3ec3adaf3108f723238bd87c856d9af1ee631582cc24dde573fcbe5f6550d345d4aff9a1b4cf06fbfff6e0843c70d9f9495179c4bc3f1d61a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/3b61c1d782193352790f01bc797ced033670cd1e9dbf80ad1584bb9b97ba8835561d403735dd5b5f42bff7a0d44b5cd0e804e3a6b7b564cba7862955c1a696cb
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/f2d6a6dca17a950bfda8e56677b1e2702d313bd3d886d317755b4ee73d87faaa77f22993f7b6f3aaee8779e65d62b55e6e80e048dc251caf3fb20fc600c98e3c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/ea0cb1404a4d4d750bb0350a7ffc7404cb996d67e4eeaae41d3ed319536e969a050202f58c3f8ad2d7bca13207c0d57dfac089eec3c89c6931108f471753395e
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/fbcd90413147fb4b2f9d6b13392339175f29d05f2dc3081c7d2697e88b469784f9590e67e4b5c0513a34745591448c01a140b97b61b6d851cfda2f27bdc8c4be
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/b4b0e3d637e709faa1591b05cb4945e4822360a4f63748a2c253fc4d38c4a513b2dfe8dca0be43ee2d8a3b76d50950eca0ababd19aa417f0e1570c06080432b8
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/a9ea3dc0f2e2c30b0791828214af8a7b65f8ee0bd757d4132287d0d82d6a08ad4873510e63e3eb2aa4592832b3fe63551cd3d0235356bb7db49e3924c36aa69e
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/87b872c53c38c2fe92697c1b935c6c790f5cfb0db8f3752bdc51d344cf09bae7796e29025247f561711c6eb0da4f605921b43c0476489d14ddab98e3044aead2
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/1870a6aa0f5a1883ba802360eaa6d65287bd85de28ff7b9f3a307a6ab05dc08852294e47f656e1e0801a11a835216e5e73369219c9d2c32a83fd9ee3afd803cf
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/cfcebd02bbbf5808c067501b07be82be0e0e6edd3052bf429ed67a4efc37dc31b8df84886802a5b46182548e01c3bd37b4c7a7a97b70729ee1d558f63ced5f96
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/93173aa573905e50f1264e778e825293181c13d0c1c414abed0f41533dace022fb87289905dc9c5a78c056d0b41437a6203426295e21b21c588b06ba5292fc03
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-api@npm:15.0.2, @codingame/monaco-vscode-api@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-api@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-base-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-environment-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-extensions-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-host-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-layout-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-quickaccess-service-override": "npm:15.0.2"
    marked: "npm:14.0.0"
  checksum: 10c0/d11a2151bac468e8fecf2c47bb5c1b41994789666db087c6b07239848b24641a56fb9d9f3b712e3a19437bf1cdc02fef1f039feb5f4c2a07e389190bae75f392
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/2ac28d04f388538bfb754e2d30a2d2897e5060831b869acc17a6a2226f246401c2b91f581544e4f0ec2cdc61e603b481e6671c8699857464781ce9d8ab7d5563
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/85f88bf982477609e4c963847d33cb61b6faada17412a2361fc41172b3157f886e06516b193c8675ea6a1bb8a34297028885915a5ef41781448bcfe5bf4c100d
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/539fcd8c5adb87645708cf882496f861874b665b95e7693afe1b3eba553ee0be26d1c1d9e38d2cd7110e1d22706b28c743f71647174c46fc131bb9b29536563c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-base-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-base-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-039b5553-0838-562a-97c2-30d6e54a7b42-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/f7e9989c7b72b0d49960e9f18094e709ad1193b7980eedbcb0773bdac99c02af94f27585cee1f3fa97cf779cb65df470a4a6562b395aeb6e34c503dddc383e8f
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/8556cc19b36782b171dc0a9df054fb387f1691d1e14e56b88d9109c4cb93f151b67700602c73fd82103cfe1867e2fdff00bf364ee583053b8e26800f1006a165
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/c8619c4505229bd37b23048195b269260508ea76a2b7d4ba0dad760dd87b05e1e5fe46ad5ed62ccf44916479a5d4177593c85abd594decb1b81a6fbf795c7c4e
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/9b43017c909c6155ce9f4ca2fcab4eb2c2577a697988e527a5f0a75a7628ddeeb90f02d39fa7d90f9ff173b8ce0ab605a525e8fdad12844ed994c5aebd8996c5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/42d90107fccfd8dfb079243eafd3a8c569123c85b30075c43a05d5641101089e0f7f03eb253d17f66dd9049d63a46c0099d3a37fbebb806a5968a2e34462d919
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-bulk-edit-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-bulk-edit-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/f62231d5b2906d0c0925a3984c16bf6ef2738dc3342e8aa0dd3e67268f4f6cd5b6b1024234e63d1c3ba5ee8f089a90d10e5bc3b90c486a92c5519d4f9671d61d
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/141cbedfdb25b2c91ad06ade670b8a39875ad6d7b7fe9bdaf293227d52f33318a0d6cf0a50fe24ae0e49366876de8a3c29ca467eb5cc75510d23b4c4870a721e
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/e7d412cfc9435fc791f83fbaa63118556fc8cdae6dc3b35c8b81ce463e498cc8a870784095330fd1f7f5f629467dacefd0fd6ccae9b60013f12730e9c9ce97bb
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/7da99531ba6e8aeef31414b91ef2a7e6f83c944d3900e2f5e00adad89933ececfe75531371fa18e89eb9d14f4894837549f8dcb46f8d75a1c4bb83d830205254
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-configuration-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-configuration-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
  checksum: 10c0/cdeabaf13a6ed7892088b19749bf90b2fdee9562006f7ec3f8c58b2ae04c43df0af8242d32b638a174247aef793eba18fadad9bb310cad09a4a88ef451e5f65a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-cpp-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-cpp-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/e9581f91d59432b059bf2042f5738eeae3fee66d4cb9c0f8dce815f12eed8abec697dfdd769866167189eacbdc45758ca078c41fcd8d108d5b82306358edad58
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/ca9aad486934fa5d10d87187a472c142754698a12382e3cbeec743ce6ae665c77614ef05e3f03aa12d91bae95671172797703aeabf4fb37e94ad8c77c5d2bf5a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/883a2ed61fcdb34801170f10e23f6406371d7b84b5af0067e791c4dd5f70be9e362037c4dfdc1b997a5dc5d8fac7d406c198324084a5562c690a38361a3ab653
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/8171e5ff7b03e4fd36d9fd5bb4cffed75c3dcfbba1e2c52f7d16b28a8b3722b97aa899c76dfd0b8270a58309114d93022eb9bbfb9c0dcff02c3edbf748f83977
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/348d592f0f34ca957dc17c4536a38fbf894d91e7690405df3d7d9b2c78ec7333732e51491a4fac4cc85e006445b45c4efa9b543e52717f70e1c7dcb5d04140b8
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/d305740bb55beec2cb5f0a89f03fc4f1c821ab4e455f065afadd6c4b856fb9b13b2019c06a5b3ebe48a71afc927fbac938fe991f3ce0d9783b7d54047d47f399
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  checksum: 10c0/958cc8702511b7db0bc52876019b9cbb65de080dda92a9b498e7c1c22c90742dff4c2b7b81016c5427f41f062fb0b7858c26d18881b22ae25d4d322f224bae91
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-debug-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-debug-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/9f23a05f17380b8fea4b962f7388ade56e0626c5efa1fedf824551b58cddeb06fc92bf0e128b7a94971f01c2b613a8d986edd2b674f3bbb62c02104da455ddb7
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  checksum: 10c0/69cfb03dc03661a8196bac80950ccea466b35dd280a68a5737a307de3b75a5fae3dc30068f08176d7a31626f1dc69a38a745a4bd217d72cdb102696bdf9f5a78
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/3e34a391e46d6411b159c6b80473710a5bf9d80c0c02a0f8262d58bc1c64407a3064a2fff9b34dcb831851cf3759637c7cf25da6889b71e50b3c819f421a8b95
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/46d17dea21f878cb74456752d43b7f5c35fe188d01bb570f81c5fbb418d733b9e5a111b14dbb6ce9e8d96ba7e81af56168f2c1162e35d6f568ab5cc53473c261
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/0d4de6a1c6b37f6b078d1f353a60fac3144cc2fca92f17396436a41d248c5e3c69093eaf9058ee5a8ee50e8c09c4d3f2e904a60124072cdba4a0894e9af16db5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/a8811dc8cb6ca96d34cae6064dcb8e8726777042f79c48d29b378163b9c1d18f46f4712a384002c526647f37e6c9cbd2d82332501c7b02db7ee0f5b36c529b87
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-editor-api@npm:~15.0.2, monaco-editor@npm:@codingame/monaco-vscode-editor-api@15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-editor-api@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-5452e2b7-9081-5f95-839b-4ab3544ce28f-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/5aaa16918334c6465c7ae0947f2e15b5a821f88fea7267823c7b3a1e2995735ece85c786cd93ce45297e12489c890985e32ccc00e9667f229a501b44f229a125
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-editor-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-editor-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/68096520a7cac76be615952ca8a8dc9234be107dc23b0f433349c69dd6e6267fc96330b5e295838088bed5f95b641aef809869b7126dc6da61d90f27d3c54b16
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-environment-service-override@npm:15.0.2, @codingame/monaco-vscode-environment-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-environment-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/8dcee00a878110f218cde951d297185cbc3626aff83f69a08a2c6a2f11cae6fed80c896da6af9d61993db22fd6940a31af74f21c892a1d4632bce092502d4f4b
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-explorer-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-explorer-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/93de7b3d3a24d3737d864b192938b09be3da40979a25fa586a896f3463dce0bc24d8b02a1d9a3437c3983401c6bc164ecee5973fc5c86f8e8dc5b5da3a2e8b71
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-extension-api@npm:~15.0.2, vscode@npm:@codingame/monaco-vscode-extension-api@~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-extension-api@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-extensions-service-override": "npm:15.0.2"
  checksum: 10c0/634f8a99c12e348740e6b3c8354d832d694bbd14da30e463d9b83628c2eaa257a0250ef69f326a5af70ec1790746e8599c9fc2f1a3f349d357d45d9653b5103e
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-extensions-service-override@npm:15.0.2, @codingame/monaco-vscode-extensions-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-extensions-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
  checksum: 10c0/b125e14eb02c90911fc650385467074abc1922a03e748116f0e5a8a5eea8ce58e9d7cc4af5dfbe30e9ca828763668ebe75808f14c9f2ab402634dab61fd75281
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/30dc10da13bac6727c8141c458fb9a7c696fea5f9194720a65b75461cbb7398bd66dc2a9ebe06503671d29d30c7c231161d15ddc113d70afbbbfdd7b6b182e96
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@xterm/addon-clipboard": "npm:0.2.0-beta.81"
    "@xterm/addon-image": "npm:0.9.0-beta.98"
    "@xterm/addon-ligatures": "npm:0.10.0-beta.98"
    "@xterm/addon-progress": "npm:0.2.0-beta.4"
    "@xterm/addon-search": "npm:0.16.0-beta.98"
    "@xterm/addon-serialize": "npm:0.14.0-beta.98"
    "@xterm/addon-unicode11": "npm:0.9.0-beta.98"
    "@xterm/addon-webgl": "npm:0.19.0-beta.98"
  checksum: 10c0/30c2899971ad0eacc639f7b267597ae1b1d2bececfca5fd43f0e4af73c86f7aa00083f1bc19836e385e4230926990144bff2649b86c73aec9a6e480714683428
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  checksum: 10c0/dfce60ec48786e1c11297ffbba53fba617d3a577fa08c1c65dda110cb7a1df93c22d6b4074e48dc3c117efab84a11505d8451683fcff485807e681b1d3d8a1e5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/8cc615a3c1328f6eb7e5ef85431aaa04e83d7173c1eac563b209a9cee598b1d46da7cb82f49e750baf1547769ab98d4320f048d06b32df3972a6ca7154f6ad91
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-************************************-common@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-************************************-common@npm:15.0.2"
  checksum: 10c0/6cb42f2e72118eb04ab853d4dd028263686b37401f82a679cc3baf6bdf1f1cfd5703e42e2dbda20975920e4453a2b0df34a285d9c8dbe94c82b61b98d26efe26
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-files-service-override@npm:15.0.2, @codingame/monaco-vscode-files-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-files-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/eeeeb110cfbe79875f8b6e8643525b342895ab700010cc2d02d71cab1aceebc8ab9f793752e2dad6c4b97c0d8e636a352b068c416f9e965cc0558e426b58271b
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-groovy-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-groovy-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/ef211fae848cb57ab498762b0c1a9a317469b12d2c0fa71076446940e2ef49f676e2dc49350d5166ef69a2c75aba84d8d8b9d2ed37bb9cf6f5508d6f3196bca1
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-host-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-host-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/0f1f2fc16c1222ca32f9eccd2fdde8a83b06ed17770a709ae42960d45d61e5602de249b1ffb5e0b6c94da9a3b4b25cbd5879f6ef1263ff45d15b166f3aad0886
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-java-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-java-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/61364e537b83c4b411a142a5d21c01a202362a9893836a9e3ecb0de33fcc3b97fb94ea5f6e67df48b3d0010c84c7be97d9883a66d19598596e2f1debd7b7d8da
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-javascript-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-javascript-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/bbc552295416d13957a0feceecce2df74a682a6498927ca05b07e5da71f6f88180aaa8212b32832e9c2dd483e6668566782c23e44920e7e109f5f9537baf161a
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-json-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-json-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/fb60d704efddf3e60719ae340c516528ccbbcc64c8a8675fd6553d42d3d53a5c5107c5383f62bfc4aec38206374285ef1445c55913bfd5c54ba85a7fe3e3d6f5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-keybindings-service-override@npm:15.0.2, @codingame/monaco-vscode-keybindings-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-keybindings-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-2a22c7b4-b906-5914-8cd1-3ed912fb738f-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
  checksum: 10c0/1058f97f67872c1e9d810da21d12b9ae29ffc0d6c91c64ca232e98b3ed00e5c733768a8593d2943a80f8e14532511dd2d73609f326504fbb7b517963344ee8b5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-cs@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-cs@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/3c4cfef3c370876d67668f676a2d384dd37a19875f01d7bfe76f7c834a4394ea894d0c144b454c07e25f32d89430c7eafde370007bfad3f060827476113fcece
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-de@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-de@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/85eb681aa99d7945ddf60ccc337c993e1a4179c85f0584f51ddd63b3f66ca06bf64dfa5a712dbeb7e3d8389a26dd28b7cedf75e73a3970947b864cd9ff853f31
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-es@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-es@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/72d736ee5759ba4433279d56a46493ff2664c5aeaa940526e7d2d96d60c2673a6c3a95c074e6fc653cabf05d6fe0d998fb863031eff4dbbb8c1d18104aaf87c8
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-fr@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-fr@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/a6893eb12471ab936ba370b9180b8ee1dec920add933c3226802bccd6ea8ce026793d065d75f3274016b3617befb0513fe427ca364fe315199e27343f7de64a1
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-it@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-it@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/e13d0189c950153a655764f3b36da1ef23461159a49b505a944db4e70f05fc1a4e2d854d18b2654f822d83fe43910d8774f985ebd75a9861b80b68582a46c645
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-ja@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-ja@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/d329b03d66e7ec563ce790ccddfc8a8d0740674641d62c6ca4e0296eac05ec8cca4834a79cf28ce96e58c98d05428cd44d94aa960661574e5f96be3c140d50b0
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-ko@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-ko@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/d22db7ee867faafc0f098736dd772756063b05a0d5ecdccd5ff908da730eed32a7cbbdbcb76c8317846db38ce3dbd3573cd6297bc33e21e441f9087c12a47c50
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-pl@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-pl@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/5a460cce1f2fda47ebdc04bf05acfe1b98042f782b8cc7679a24cc765b9f86e7bf10be2690fc6b13fa93fd23a078e3e7b879d489d002aa507c96ab6bf20a340f
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-pt-br@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-pt-br@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/dc1237bf3ff9a492bc2d117a898171621110b3795bc32c7cffd9285b60abb1532294f896e820366e0783456887cfaaaebccc27c67fcbfea2f207eafee0ffbac4
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-qps-ploc@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-qps-ploc@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/59f22d015f1e7a4266214160b7f73128e8f71fa43ca6684e0e30552e231e78701df3e96952e22ce882ba48456720c8cff6cb4607594ea4502252d06fcc319fe9
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-ru@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-ru@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/4d98f7618e5838e4699776c7bf283326e924d135d96798193e8464d7d47782bfc10eeb820d0cd29f7703015b893345fe515ff86fc7071f190fa4d03b83f03596
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-tr@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-tr@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/862c4db7d9f926303a3754615f5db34d6be05db85b95b452fed399949de25ecfec557cecb0ac59234c618dada158a778c876be315616fecb9a8e69eb39dfb81c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-zh-hans@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-zh-hans@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/c4a5892709aea68c2042a401975a9edd21c55282b94b02bfae3d14e402cec614dfd47a9bc20cd1036933c9e5c3a5404278da34ded9a32baa5b8863138164c9ec
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-language-pack-zh-hant@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-language-pack-zh-hant@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/5f97a49cf48d93eded19bcde6f50bd6a8da37d09b25a717e8d1666d8710751364397553a5066db3f896c0df15b5dc6993b7f612ab341a4ceb85a013db8eaf2ce
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-languages-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-languages-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
  checksum: 10c0/3b91f2ffd6ebf14a2de1e3b820899a2a17d2be7559b8e1ba8928a60a97de348d04a3468d0e81ad5e003b5080a2987b1ffe2c847b54804fcd1bc61362f6240822
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-layout-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-layout-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/02c97b6fe69c7d8e236829ba8e42830c6fcd05390602c151a007b78ed8e21d4835424ad5f5bd5176e2579f27292acbab849ba4e3aee78a0ce04bb1800ba73906
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-lifecycle-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-lifecycle-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/7c08a1fc4ee8a85c1befeb1223241cc7fd2ab1b0ddae40f6663ed162c24155a2e4a1ab79e979f86f785c33277fd47c29f3bdac91ac7095ba6633664cc11f0f47
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-localization-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-localization-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/b1e623dbf109cd48c8b67313267c0ad16f6ac14ac01a32e90f0d9ccefa6a238093b7f787f618f351ce0e8cee37379216e3495ba210446b1c6e81d1a7b0bb5b8d
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-log-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-log-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-environment-service-override": "npm:15.0.2"
  checksum: 10c0/d31858b81a49cc21c31e63e288cd90b81b1778d65b2421aaad60587d4a96f8ce6bdee4bf154d84e0a66c6167492226c96124af475a10572e90ea359364ccba6d
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-model-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-model-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/9f1aa7875212065d85654295b3394affe90bae84eca6f7e360823aec41fce37086042b856d9918f2ffe3e270acc9569fef2672a5a7eec0b4c23bf67c486e5feb
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-monarch-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-monarch-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/09c0cc54959d39aa0f9ae06da57cd1873174d1c1cde94f7459d37a76ddff935fc5cc67707b6d260b0212458706e9ff0e35886853d68c4aaa739c5d5da97f57ba
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-preferences-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-preferences-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/f3fe9d5db7e340c0b3d5aec3fbe3633acbd62c8343d294e3e1be0f1ca5f81fa2014f91cfd2a7d0f0e13eecc06293610e26841a6eee478cde2937945de647c6f7
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-python-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-python-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/1cd6bec6781dcd99b4c8034d8128ada8d9dbd7b56ee9660f51f5cafa998aae4a6a25b935d9edc785a7c5b55fc7f60b31522b396e4a80979166f269f3e924fe3c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-quickaccess-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-quickaccess-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/2f05ad0bad1a7a859c0e7b7735482bab0f081e901de8b81e9beab3ee4f15a576f494d948bb82a940671028edce10bb78be925be6fbe3c4f169d5e2cebd60b0cc
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-remote-agent-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-remote-agent-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-environment-service-override": "npm:15.0.2"
  checksum: 10c0/96b8e3beb032cb72f89b96cf21fde55a01fe6600ab795989baa31a378a697d0bc2c657ed0228b5c3bc49a73058345ad8c154d67c0dcf33cc7fe6f24089d945a9
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-search-result-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-search-result-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/983423d50f52994452334f4c7c6448a54262c3bf1e42c2b58b8ed89bfce7dc363f853a07022e4c3dc4ec78114d92b9c79836af79f12a627d0ede5fc9d9caafa7
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-search-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-search-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/f1268f7f3a5a13e5d8b63a0321bcac244340a2fbe3759fd3dbe13799fd19b344a33585fd92c411be0fc1c092d9abf0b847d081cb8af4600de66ca86d7f904a21
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-secret-storage-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-secret-storage-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/b26e8e3f1f9c25cd53b61518d8268afbb8fcbf8d69a2cd7921b24449825a58edd068af4f827dd8d42142f22b7c485c7529ff84d2fe035f02276b408a2d920952
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-standalone-json-language-features@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-standalone-json-language-features@npm:15.0.2"
  dependencies:
    monaco-editor: "npm:@codingame/monaco-vscode-editor-api@15.0.2"
  checksum: 10c0/edfb80e3d4b43e0508bc8a73625a5698fc913ee917bf55052c7ad1e0bb90c0e8a9d93f04a65ebf504a095ab170fcb734fc63c797f006afac08da219fe13db671
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-standalone-languages@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-standalone-languages@npm:15.0.2"
  dependencies:
    monaco-editor: "npm:@codingame/monaco-vscode-editor-api@15.0.2"
  checksum: 10c0/98e0926d3d5629a6efcc4329f748dca1d1219651ef74e827424077af3804dc8806bf7e6e3a28c784e0aa032e052fc422cd1ce0b738b4f19138726a396ab473c1
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-standalone-typescript-language-features@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-standalone-typescript-language-features@npm:15.0.2"
  dependencies:
    monaco-editor: "npm:@codingame/monaco-vscode-editor-api@15.0.2"
  checksum: 10c0/0d83f5b674de96d6eff18684a184d3c1a49ca06fd1a17ae0f9edbdfaf57caa0373ef8ddf32c090f9359c66cde88193187b527f8e45ee089c286f29f559d2093b
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-storage-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-storage-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/27704a47dc02979a2c5ba70d0d808ea0159d28051a7ac6f2c0d49c1b1c85c243ee7fafea570f85c7b768fb546ec909f9eaf46505ab8a567b3bbbecf4a8d7b2c5
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-terminal-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-terminal-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@xterm/xterm": "npm:5.6.0-beta.98"
  checksum: 10c0/26dcac2ef611e3fcaa2ddb6e7844cc51ef911e88010d6c406bec175fd33cb133a7eccbaa001657ece30386aed494e5dcc21a6e3c824f5c67465920bb7b24b820
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-testing-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-testing-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-terminal-service-override": "npm:15.0.2"
  checksum: 10c0/789b9a5b36b35a63cf6e474a6156d4e72cc70df5d32fc409291057cad24d172807e917c2165257daa1a3866cf914e7a1dbc17ca72f07e00e8388ec4406f96d01
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-textmate-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-textmate-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-33833ac7-3af3-5e9d-8fb9-11838d852c59-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
    vscode-oniguruma: "npm:1.7.0"
    vscode-textmate: "npm:9.2.0"
  checksum: 10c0/c83df887a9c084395424c2bf0dd255ccbb3f825f34309a10609c837dcc29dabd969c87e7310a0a92d37142873e2c759e0636711406710ee752ed21dce274a884
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-theme-defaults-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-theme-defaults-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/071175ccae6de497333a70b98d4b6c6c1fdcbaf4e90cf803da6660f7bdea221ac47c2b8cfe539af5665caa8ad00fe5ca710333d4db41e5b0889fe5c9001bf85c
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-theme-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-theme-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:15.0.2"
  checksum: 10c0/0bd8849ad76fcdfa34ed678a21961f1c2d3e3fcd1cb9bfa05e42b10fa9531795271f75f03e0230d658d26bfe207562820795805bf283f796083b5262849e2584
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-typescript-basics-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-typescript-basics-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/b03490f75b0e11425eadca2576e4ace33d11082f1608d75b36f5f18a6cabe411c869ca36b07dd7c4c63c252110f2c02993c9fdffa82be98d7c9fb5e024f673fc
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-typescript-language-features-default-extension@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-typescript-language-features-default-extension@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:15.0.2"
  checksum: 10c0/2eae8d43c0fab0cb654fe909eca22afcf64046a07371a9c3fe2cd52540a4bfcce742057eb019710b439d740573ae3ead0fb2f82d0d439a2959e899a6abb389b0
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-view-banner-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-view-banner-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/77e5d0638e1ee4d028c2e3d41eef7747877666afb60e8bec97bd93d58253c21a4985ba0b1c6506d4cf834cc1883e93176c3a2e58f655fe9a61ba1631db508629
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-view-common-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-view-common-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-bulk-edit-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/6af20a77afc2b9ec2c449294c0fc1069f2cc143b53f05a0caa3322ed33f3d1d336bc8ea7660f0e14704e51548ea4466957f2c7505aaa8f56eeab9fe2b92e1102
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-view-status-bar-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-view-status-bar-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/2586461ee4668f39f94a4ceefad728a8e9b5dda6e3137c942cb446b098415b1f46d306c7db96964984b56b9aa2f9f129a7024126c65c9af31e8bd0199acdd620
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-view-title-bar-service-override@npm:15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-view-title-bar-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
  checksum: 10c0/d2748e045e7018e0927d11df436a522aae9571f9a7a9a7d929dfd9b85c24388c734345642c5e595d6cb276330a5ab0d94b85d27938df743cf60f6a5ae6f6be41
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-views-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-views-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-keybindings-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-layout-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-quickaccess-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-view-common-service-override": "npm:15.0.2"
  checksum: 10c0/47527c443f1fe4c21a27f55140962d75c3a3adba46a48fb039e1bdae093cdb9d11c0563e4745e8c4c513978f350275f4ae99dae20e5f5ea47a903055c8eca7a4
  languageName: node
  linkType: hard

"@codingame/monaco-vscode-workbench-service-override@npm:~15.0.2":
  version: 15.0.2
  resolution: "@codingame/monaco-vscode-workbench-service-override@npm:15.0.2"
  dependencies:
    "@codingame/monaco-vscode-1021b67c-93e5-5c78-a270-cbdb2574d980-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-api": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-************************************-common": "npm:15.0.2"
    "@codingame/monaco-vscode-keybindings-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-quickaccess-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-view-banner-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-view-common-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-view-status-bar-service-override": "npm:15.0.2"
    "@codingame/monaco-vscode-view-title-bar-service-override": "npm:15.0.2"
  checksum: 10c0/3eb70a7b402a1109e16d2219dd904db8ab1017de7f90166d5a3be1ae292ec411075be03b2cada628c57ff5952f92f8129947b512b505fad696e692b279563695
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@typefox/monaco-editor-react@npm:~6.6.0":
  version: 6.6.0
  resolution: "@typefox/monaco-editor-react@npm:6.6.0"
  dependencies:
    "@codingame/monaco-vscode-editor-api": "npm:~15.0.2"
    monaco-editor-wrapper: "npm:~6.6.0"
    react: "npm:>=18.0.0 || <20.0.0"
  checksum: 10c0/119e8e6e5bc31097c60a67163a99694b31f5ac370a018a3cd911baceceafd5f11f6810075143d7001c3673aa943ccc143724ae6dba0233aa9c50e78ac96d757f
  languageName: node
  linkType: hard

"@typefox/peer-check-yarn@workspace:.":
  version: 0.0.0-use.local
  resolution: "@typefox/peer-check-yarn@workspace:."
  dependencies:
    "@codingame/monaco-vscode-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-configuration-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-extension-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-extensions-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-languages-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-localization-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-log-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-model-service-override": "npm:~15.0.2"
    monaco-languageclient-examples: "npm:~2025.3.6"
    typescript: "npm:~5.8.2"
    vscode: "npm:@codingame/monaco-vscode-extension-api@~15.0.2"
    vscode-languageclient: "npm:~9.0.1"
  languageName: unknown
  linkType: soft

"@vscode/iconv-lite-umd@npm:0.7.0":
  version: 0.7.0
  resolution: "@vscode/iconv-lite-umd@npm:0.7.0"
  checksum: 10c0/3316664f65f2b2089f58d5f31d09ebc931da01d826c7f57d44740689bc862ba970224ebcf70da6b823b138c80c070f484c98a21ecfddf49c84c614a0b0bd3f6f
  languageName: node
  linkType: hard

"@vscode/l10n@npm:^0.0.18":
  version: 0.0.18
  resolution: "@vscode/l10n@npm:0.0.18"
  checksum: 10c0/d1fc797001f7d508ab3fa91175f7a50ea98516c4e47830ff2be79163cde9279279514a167a3bad15b7ab7fc243e7808d8f32d3eb41f4a7d6721d9dfdbb38d89e
  languageName: node
  linkType: hard

"@xterm/addon-clipboard@npm:0.2.0-beta.81":
  version: 0.2.0-beta.81
  resolution: "@xterm/addon-clipboard@npm:0.2.0-beta.81"
  dependencies:
    js-base64: "npm:^3.7.5"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/c65b80e184e87100de781c50e2f7e468812de5e7fabfeb70e6d54bae0b6d936527d89b9b0df28f6347c75fcd16ee2a20c825ce3b5032218933e81da3e2e98767
  languageName: node
  linkType: hard

"@xterm/addon-image@npm:0.9.0-beta.98":
  version: 0.9.0-beta.98
  resolution: "@xterm/addon-image@npm:0.9.0-beta.98"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/aa2068e2f6292bea84d71d5e0dd60e3d9daa5f8a574717c2998d22846bcae4914514355e16c76f2e11e132f4f68172dd0c2a3dc9f817a2bd7b992588cbbd74d2
  languageName: node
  linkType: hard

"@xterm/addon-ligatures@npm:0.10.0-beta.98":
  version: 0.10.0-beta.98
  resolution: "@xterm/addon-ligatures@npm:0.10.0-beta.98"
  dependencies:
    font-finder: "npm:^1.1.0"
    font-ligatures: "npm:^1.4.1"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/177c3877200869deea4a7670668a5b543e988dfe068f0d99f8ae2123a6827e8727e0d0f04bb357581940465b07cf73c8611cc46d4d082aefdfd8eab2e4199fcb
  languageName: node
  linkType: hard

"@xterm/addon-progress@npm:0.2.0-beta.4":
  version: 0.2.0-beta.4
  resolution: "@xterm/addon-progress@npm:0.2.0-beta.4"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/47515dd26369c0ba183eb6e9ee99e048d9a3e3f5aa17132273062d653fcafee44455dc87aa2a32338c8309fbb0b7a9a013baa87e308ba9096e6e9f717e926d75
  languageName: node
  linkType: hard

"@xterm/addon-search@npm:0.16.0-beta.98":
  version: 0.16.0-beta.98
  resolution: "@xterm/addon-search@npm:0.16.0-beta.98"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/12eeef483bccba1cf4e9ea355671a6930f70d7e8bf4d0190746fe122934b864c87661d0dd82f45b12bb8d33743503d6b619dc9533d60affaa4e207a4ff3edc6f
  languageName: node
  linkType: hard

"@xterm/addon-serialize@npm:0.14.0-beta.98":
  version: 0.14.0-beta.98
  resolution: "@xterm/addon-serialize@npm:0.14.0-beta.98"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/ee780c74e4a7baefc4a374bb60bced6fec0d09aca7f817506a6de8c37904d538c953536dcafb3aa5313844a467b8247780577782ad8b7f56738a24eddf85378d
  languageName: node
  linkType: hard

"@xterm/addon-unicode11@npm:0.9.0-beta.98":
  version: 0.9.0-beta.98
  resolution: "@xterm/addon-unicode11@npm:0.9.0-beta.98"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/babe568ed446f3676f6ca0359e205f182eb826e623c2b3ec5fbf99e589474453d94ab842a49fafef27d38593e20ce31a5dab93cc860091ec4373a348b778f2ed
  languageName: node
  linkType: hard

"@xterm/addon-webgl@npm:0.19.0-beta.98":
  version: 0.19.0-beta.98
  resolution: "@xterm/addon-webgl@npm:0.19.0-beta.98"
  peerDependencies:
    "@xterm/xterm": ^5.6.0-beta.98
  checksum: 10c0/88cd2ccf1169d6c1d02f77b7b9e36ab4a7dffc309d919eef452c45e31c0c2fbd4041aaee46633427f23dc181546bb5d8188b39b0746f4a9bd67b264af966b474
  languageName: node
  linkType: hard

"@xterm/xterm@npm:5.6.0-beta.98":
  version: 5.6.0-beta.98
  resolution: "@xterm/xterm@npm:5.6.0-beta.98"
  checksum: 10c0/d598507f168d90927d0e4d0158716643fee50111419bb143a1c8fc112b0a35f085ee6e383c4ea96cf8eaf2d78c69b0b85f0df55993ea397ef103245615bea582
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.0
  resolution: "abbrev@npm:3.0.0"
  checksum: 10c0/049704186396f571650eb7b22ed3627b77a5aedf98bb83caf2eac81ca2a3e25e795394b0464cfb2d6076df3db6a5312139eac5b6a126ca296ac53c5008069c28
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.1
  resolution: "brace-expansion@npm:2.0.1"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/b358f2fe060e2d7a87aa015979ecea07f3c37d4018f8d6deb5bd4c229ad3a0384fe6029bb76cd8be63c81e516ee52d1a0673edbe2023d53a5191732ae3c3e49f
  languageName: node
  linkType: hard

"bytes@npm:3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-bind-apply-helpers@npm:1.0.1"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/acb2ab68bf2718e68a3e895f0d0b73ccc9e45b9b6f210f163512ba76f91dab409eb8792f6dae188356f9095747512a3101646b3dea9d37fb8c7c6bf37796d18c
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.3
  resolution: "call-bound@npm:1.0.3"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    get-intrinsic: "npm:^1.2.6"
  checksum: 10c0/45257b8e7621067304b30dbd638e856cac913d31e8e00a80d6cf172911acd057846572d0b256b45e652d515db6601e2974a1b1a040e91b4fc36fb3dd86fa69cf
  languageName: node
  linkType: hard

"chevrotain-allstar@npm:~0.3.0":
  version: 0.3.1
  resolution: "chevrotain-allstar@npm:0.3.1"
  dependencies:
    lodash-es: "npm:^4.17.21"
  peerDependencies:
    chevrotain: ^11.0.0
  checksum: 10c0/5cadedffd3114eb06b15fd3939bb1aa6c75412dbd737fe302b52c5c24334f9cb01cee8edc1d1067d98ba80dddf971f1d0e94b387de51423fc6cf3c5d8b7ef27a
  languageName: node
  linkType: hard

"chevrotain@npm:~11.0.3":
  version: 11.0.3
  resolution: "chevrotain@npm:11.0.3"
  dependencies:
    "@chevrotain/cst-dts-gen": "npm:11.0.3"
    "@chevrotain/gast": "npm:11.0.3"
    "@chevrotain/regexp-to-ast": "npm:11.0.3"
    "@chevrotain/types": "npm:11.0.3"
    "@chevrotain/utils": "npm:11.0.3"
    lodash-es: "npm:4.17.21"
  checksum: 10c0/ffd425fa321e3f17e9833d7f44cd39f2743f066e92ca74b226176080ca5d455f853fe9091cdfd86354bd899d85c08b3bdc3f55b267e7d07124b048a88349765f
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10c0/5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 10c0/90a0e40abbddfd7618f8ccd63a74d88deea94e77d0e8dbbea059fa7ebebb8fbb4e2909667fe26f3a467073de1a542ebe6ae4c73a73745ac5833786759cd906c9
  languageName: node
  linkType: hard

"cors@npm:^2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: "npm:^4"
    vary: "npm:^1"
  checksum: 10c0/373702b7999409922da80de4a61938aabba6929aea5b6fd9096fefb9e8342f626c0ebd7507b0e8b0b311380744cc985f27edebc0a26e0ddb784b54e1085de761
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.0":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.3.4":
  version: 4.4.0
  resolution: "debug@npm:4.4.0"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/db94f1a182bf886f57b4755f85b3a74c39b5114b9377b7ab375dc2cfa3454f09490cc6c30f829df3fc8042bc8b8995f6567ce5cd96f3bc3688bd24027197d9de
  languageName: node
  linkType: hard

"depd@npm:2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express@npm:~4.21.2":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"font-finder@npm:^1.0.3, font-finder@npm:^1.1.0":
  version: 1.1.0
  resolution: "font-finder@npm:1.1.0"
  dependencies:
    get-system-fonts: "npm:^2.0.0"
    promise-stream-reader: "npm:^1.0.1"
  checksum: 10c0/2cd3842306abebd0d46b46c29649aecdae32a29d4e96a0ba23127669ca65d2196850324098ef4a3981da6f428dc264d7789c4e11979db1060cf3858b969aec38
  languageName: node
  linkType: hard

"font-ligatures@npm:^1.4.1":
  version: 1.4.1
  resolution: "font-ligatures@npm:1.4.1"
  dependencies:
    font-finder: "npm:^1.0.3"
    lru-cache: "npm:^6.0.0"
    opentype.js: "npm:^0.8.0"
  checksum: 10c0/350e445414d725b1c6e6735a6ef5ac2afffaf91ac4e30c8a954a7c6a7fbe7a334781b485f4a615d384ea06bf09e38a7bca575ba630a5fb56644d4f92ea6b9401
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.0
  resolution: "foreground-child@npm:3.3.0"
  dependencies:
    cross-spawn: "npm:^7.0.0"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/028f1d41000553fcfa6c4bb5c372963bf3d9bf0b1f25a87d1a6253014343fb69dfb1b42d9625d7cf44c8ba429940f3d0ff718b62105d4d4a4f6ef8ca0a53faa2
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6":
  version: 1.2.7
  resolution: "get-intrinsic@npm:1.2.7"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.0.0"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.0"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/b475dec9f8bff6f7422f51ff4b7b8d0b68e6776ee83a753c1d627e3008c3442090992788038b37eff72e93e43dceed8c1acbdf2d6751672687ec22127933080d
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.0":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-system-fonts@npm:^2.0.0":
  version: 2.0.2
  resolution: "get-system-fonts@npm:2.0.2"
  checksum: 10c0/5c2a1ae53df9e165a94efd10a746277ee55eacf6ba2bddf734b0032b451ad3c2136f6f07ebe0f4dadcea7637a504ec592d7e1dab2562c6b821f871c8f93224f6
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.10, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.2.6":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.1.1
  resolution: "http-cache-semantics@npm:4.1.1"
  checksum: 10c0/ce1319b8a382eb3cbb4a37c19f6bfe14e5bb5be3d09079e885e8c513ab2d3cd9214902f8a31c9dc4e37022633ceabfc2d697405deeaf1b8f3552bb4ed996fdfc
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"immediate@npm:~3.0.5":
  version: 3.0.6
  resolution: "immediate@npm:3.0.6"
  checksum: 10c0/f8ba7ede69bee9260241ad078d2d535848745ff5f6995c7c7cb41cfdc9ccc213f66e10fa5afb881f90298b24a3f7344b637b592beb4f54e582770cdce3f1f039
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inherits@npm:2.0.4, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: 10c0/18b5be6669be53425f0b84098732670ed4e727e3af33bc7f948aac01782110eb9a18b3b329c5323bcdd3acdaae547ee077d3951317e7f133bff7105264b3003d
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"js-base64@npm:^3.7.5":
  version: 3.7.7
  resolution: "js-base64@npm:3.7.7"
  checksum: 10c0/3c905a7e78b601e4751b5e710edd0d6d045ce2d23eb84c9df03515371e1b291edc72808dc91e081cb9855aef6758292a2407006f4608ec3705373dd8baf2f80f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jschardet@npm:3.1.4":
  version: 3.1.4
  resolution: "jschardet@npm:3.1.4"
  checksum: 10c0/d72c724ff60bc185d3962617ffda6849c6d632a935820841078c656a5247d73617a5df3b233e1fb1064de8683f7dae1b422b68186d1d6db22117b59edb5433dc
  languageName: node
  linkType: hard

"jsonc-parser@npm:^3.3.1":
  version: 3.3.1
  resolution: "jsonc-parser@npm:3.3.1"
  checksum: 10c0/269c3ae0a0e4f907a914bf334306c384aabb9929bd8c99f909275ebd5c2d3bc70b9bcd119ad794f339dec9f24b6a4ee9cd5a8ab2e6435e730ad4075388fc2ab6
  languageName: node
  linkType: hard

"jszip@npm:~3.10.1":
  version: 3.10.1
  resolution: "jszip@npm:3.10.1"
  dependencies:
    lie: "npm:~3.3.0"
    pako: "npm:~1.0.2"
    readable-stream: "npm:~2.3.6"
    setimmediate: "npm:^1.0.5"
  checksum: 10c0/58e01ec9c4960383fb8b38dd5f67b83ccc1ec215bf74c8a5b32f42b6e5fb79fada5176842a11409c4051b5b94275044851814a31076bf49e1be218d3ef57c863
  languageName: node
  linkType: hard

"langium@npm:~3.4.0":
  version: 3.4.0
  resolution: "langium@npm:3.4.0"
  dependencies:
    chevrotain: "npm:~11.0.3"
    chevrotain-allstar: "npm:~0.3.0"
    vscode-languageserver: "npm:~9.0.1"
    vscode-languageserver-textdocument: "npm:~1.0.11"
    vscode-uri: "npm:~3.0.8"
  checksum: 10c0/b11afc49953f4fea4fe5483a945bd7ddc2e4b38d35deb837d7911a6b27cc9f72f457718f8b7ccbebd5845522b88bb3c66a8bacaa66dc52e03b6967a6251395e9
  languageName: node
  linkType: hard

"lie@npm:~3.3.0":
  version: 3.3.0
  resolution: "lie@npm:3.3.0"
  dependencies:
    immediate: "npm:~3.0.5"
  checksum: 10c0/56dd113091978f82f9dc5081769c6f3b947852ecf9feccaf83e14a123bc630c2301439ce6182521e5fbafbde88e88ac38314327a4e0493a1bea7e0699a7af808
  languageName: node
  linkType: hard

"lodash-es@npm:4.17.21, lodash-es@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash-es@npm:4.17.21"
  checksum: 10c0/fb407355f7e6cd523a9383e76e6b455321f0f153a6c9625e21a8827d10c54c2a2341bd2ae8d034358b60e07325e1330c14c224ff582d04612a46a4f0479ff2f2
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/cb53e582785c48187d7a188d3379c181b5ca2a9c78d2bce3e7dee36f32761d1c42983da3fe12b55cb74e1779fa94cdc2e5367c028a9b35317184ede0c07a30a9
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"marked@npm:14.0.0":
  version: 14.0.0
  resolution: "marked@npm:14.0.0"
  bin:
    marked: bin/marked.js
  checksum: 10c0/57a47cb110f7b1a10f398b0a7236f9183aad2dcd5345ee73f2732b6387e585d04cef472bc655d2f84c542296be9728e179aebe3ed7f2f8666b8a0a9dae592876
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10c0/866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"minimatch@npm:^5.1.0":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.0
  resolution: "minipass-fetch@npm:4.0.0"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/7fa30ce7c373fb6f94c086b374fff1589fd7e78451855d2d06c2e2d9df936d131e73e952163063016592ed3081444bd8d1ea608533313b0149156ce23311da4b
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.1
  resolution: "minizlib@npm:3.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
    rimraf: "npm:^5.0.5"
  checksum: 10c0/82f8bf70da8af656909a8ee299d7ed3b3372636749d29e105f97f20e88971be31f5ed7642f2e898f00283b68b701cc01307401cdc209b0efc5dd3818220e5093
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"monaco-editor-wrapper@npm:~6.6.0":
  version: 6.6.0
  resolution: "monaco-editor-wrapper@npm:6.6.0"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-extension-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-cs": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-de": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-es": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-fr": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-it": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-ja": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-ko": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-pl": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-pt-br": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-qps-ploc": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-ru": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-tr": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-zh-hans": "npm:~15.0.2"
    "@codingame/monaco-vscode-language-pack-zh-hant": "npm:~15.0.2"
    "@codingame/monaco-vscode-monarch-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-textmate-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-theme-defaults-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-theme-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-views-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-workbench-service-override": "npm:~15.0.2"
    monaco-languageclient: "npm:~9.5.0"
    vscode: "npm:@codingame/monaco-vscode-extension-api@~15.0.2"
    vscode-languageclient: "npm:~9.0.1"
    vscode-languageserver-protocol: "npm:~3.17.5"
    vscode-ws-jsonrpc: "npm:~3.4.0"
  checksum: 10c0/017a6c540be35f1c9712c090496ffe024f1ee48f556586e86a038ef0c690b69ba19994400c9b45888ea55049f86154b64413d9f8fff63bca8ddaef5afac44beb
  languageName: node
  linkType: hard

"monaco-languageclient-examples@npm:~2025.3.6":
  version: 2025.3.6
  resolution: "monaco-languageclient-examples@npm:2025.3.6"
  dependencies:
    "@codingame/monaco-vscode-configuration-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-cpp-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-debug-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-environment-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-explorer-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-files-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-groovy-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-java-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-javascript-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-json-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-keybindings-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-lifecycle-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-localization-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-preferences-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-python-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-remote-agent-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-search-result-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-search-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-secret-storage-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-standalone-json-language-features": "npm:~15.0.2"
    "@codingame/monaco-vscode-standalone-languages": "npm:~15.0.2"
    "@codingame/monaco-vscode-standalone-typescript-language-features": "npm:~15.0.2"
    "@codingame/monaco-vscode-storage-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-testing-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-textmate-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-theme-defaults-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-theme-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-typescript-basics-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-typescript-language-features-default-extension": "npm:~15.0.2"
    "@codingame/monaco-vscode-views-service-override": "npm:~15.0.2"
    "@typefox/monaco-editor-react": "npm:~6.6.0"
    cors: "npm:^2.8.5"
    express: "npm:~4.21.2"
    jszip: "npm:~3.10.1"
    langium: "npm:~3.4.0"
    monaco-editor-wrapper: "npm:~6.6.0"
    monaco-languageclient: "npm:~9.5.0"
    pyright: "npm:~1.1.396"
    react: "npm:~19.0.0"
    react-dom: "npm:~19.0.0"
    request-light: "npm:~0.8.0"
    vscode: "npm:@codingame/monaco-vscode-extension-api@~15.0.2"
    vscode-json-languageservice: "npm:~5.4.2"
    vscode-languageclient: "npm:~9.0.1"
    vscode-languageserver: "npm:~9.0.1"
    vscode-uri: "npm:~3.1.0"
    vscode-ws-jsonrpc: "npm:~3.4.0"
    ws: "npm:~8.18.0"
    wtd-core: "npm:~4.0.1"
  checksum: 10c0/e0138b01c4fb2b2844d64feacb1cae836e7f043d888ef1e720e0a9e6acb75bd4f1698be40f6e1aa094720be0765f1fbd7dffa8a0bd3bdab2ef731a5d01884b99
  languageName: node
  linkType: hard

"monaco-languageclient@npm:~9.5.0":
  version: 9.5.0
  resolution: "monaco-languageclient@npm:9.5.0"
  dependencies:
    "@codingame/monaco-vscode-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-configuration-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-editor-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-extension-api": "npm:~15.0.2"
    "@codingame/monaco-vscode-extensions-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-languages-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-localization-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-log-service-override": "npm:~15.0.2"
    "@codingame/monaco-vscode-model-service-override": "npm:~15.0.2"
    vscode: "npm:@codingame/monaco-vscode-extension-api@~15.0.2"
    vscode-languageclient: "npm:~9.0.1"
  checksum: 10c0/8b3c8808fc000a2f078d00d80e130d8e9b5715d26f2df363383380c39ed9af082fcc13132a734485ffcd437f7312c8fe48946cdd175ae740562b9aad2af23cb9
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.1.0
  resolution: "node-gyp@npm:11.1.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    glob: "npm:^10.3.10"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/c38977ce502f1ea41ba2b8721bd5b49bc3d5b3f813eabfac8414082faf0620ccb5211e15c4daecc23ed9f5e3e9cc4da00e575a0bcfc2a95a069294f2afa1e0cd
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"object-assign@npm:^4":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"opentype.js@npm:^0.8.0":
  version: 0.8.0
  resolution: "opentype.js@npm:0.8.0"
  dependencies:
    tiny-inflate: "npm:^1.0.2"
  bin:
    ot: ./bin/ot
  checksum: 10c0/461e1ea942b7d5f66542b62535c5605c2f056d229609b9a8254531696d12830c044c896747c88d05923bf4acf7d4efb845362b46712b06bf7d3ca25596a9967f
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"pako@npm:~1.0.2":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 10c0/86dd99d8b34c3930345b8bbeb5e1cd8a05f608eeb40967b293f72fe469d0e9c88b783a8777e4cc7dc7c91ce54c5e93d88ff4b4f060e6ff18408fd21030d9ffbe
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10c0/1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 10c0/bec089239487833d46b59d80327a1605e1c5287eaad770a291add7f45fda1bb5e28b38e0e061add0a1d0ee0984788ce74fa394d345eed1c420cacf392c554367
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"promise-stream-reader@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-stream-reader@npm:1.0.1"
  checksum: 10c0/c0ad303d579462e82298a8da2afadd990f0fe082c48f13d97c7e8e9b92b8a9c7fb80a8b6b87a19ef8cf5480379aac84644879d6ccbf52993294f43097cd902e1
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"pyright@npm:~1.1.396":
  version: 1.1.396
  resolution: "pyright@npm:1.1.396"
  dependencies:
    fsevents: "npm:~2.3.3"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    pyright: index.js
    pyright-langserver: langserver.index.js
  checksum: 10c0/ed2b8b2bf2a89cb0aeb71bfec183113497a3555070a202b70d5f93357a29b9b2ed0cc7f70f0aba82d230dbf68074eb367b2c3143fb259c5c82274cb40d74e2ff
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"react-dom@npm:~19.0.0":
  version: 19.0.0
  resolution: "react-dom@npm:19.0.0"
  dependencies:
    scheduler: "npm:^0.25.0"
  peerDependencies:
    react: ^19.0.0
  checksum: 10c0/a36ce7ab507b237ae2759c984cdaad4af4096d8199fb65b3815c16825e5cfeb7293da790a3fc2184b52bfba7ba3ff31c058c01947aff6fd1a3701632aabaa6a9
  languageName: node
  linkType: hard

"react@npm:>=18.0.0 || <20.0.0, react@npm:~19.0.0":
  version: 19.0.0
  resolution: "react@npm:19.0.0"
  checksum: 10c0/9cad8f103e8e3a16d15cb18a0d8115d8bd9f9e1ce3420310aea381eb42aa0a4f812cf047bb5441349257a05fba8a291515691e3cb51267279b2d2c3253f38471
  languageName: node
  linkType: hard

"readable-stream@npm:~2.3.6":
  version: 2.3.8
  resolution: "readable-stream@npm:2.3.8"
  dependencies:
    core-util-is: "npm:~1.0.0"
    inherits: "npm:~2.0.3"
    isarray: "npm:~1.0.0"
    process-nextick-args: "npm:~2.0.0"
    safe-buffer: "npm:~5.1.1"
    string_decoder: "npm:~1.1.1"
    util-deprecate: "npm:~1.0.1"
  checksum: 10c0/7efdb01f3853bc35ac62ea25493567bf588773213f5f4a79f9c365e1ad13bab845ac0dae7bc946270dc40c3929483228415e92a3fc600cc7e4548992f41ee3fa
  languageName: node
  linkType: hard

"request-light@npm:~0.8.0":
  version: 0.8.0
  resolution: "request-light@npm:0.8.0"
  checksum: 10c0/5b6c2ddde5d2060fa0495df4bf322335c08b08e5c558a4de9360205cdd79032ceffbe33da7e946c16c4cb5075600900610d785e0e3e00e8d297acb304e40f3e1
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10c0/7da4fd0e15118ee05b918359462cfa1e7fe4b1228c7765195a45b55576e8c15b95db513b8466ec89129666f4af45ad978a3057a02139afba1a63512a2d9644cc
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: 10c0/780ba6b5d99cc9a40f7b951d47152297d0e260f0df01472a1b99d4889679a4b94a13d644f7dbc4f022572f09ae9005fa2fbb93bbbd83643316f365a3e9a45b21
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"scheduler@npm:^0.25.0":
  version: 0.25.0
  resolution: "scheduler@npm:0.25.0"
  checksum: 10c0/a4bb1da406b613ce72c1299db43759526058fdcc413999c3c3e0db8956df7633acf395cb20eb2303b6a65d658d66b6585d344460abaee8080b4aa931f10eaafe
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.7.1
  resolution: "semver@npm:7.7.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/fd603a6fb9c399c6054015433051bdbe7b99a940a8fb44b85c2b524c4004b023d7928d47cb22154f8d054ea7ee8597f586605e05b52047f048278e4ac56ae958
  languageName: node
  linkType: hard

"semver@npm:^7.3.7":
  version: 7.6.3
  resolution: "semver@npm:7.6.3"
  bin:
    semver: bin/semver.js
  checksum: 10c0/88f33e148b210c153873cb08cfe1e281d518aaa9a666d4d148add6560db5cd3c582f3a08ccb91f38d5f379ead256da9931234ed122057f40bb5766e65e58adaf
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.5":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: 10c0/5bae81bfdbfbd0ce992893286d49c9693c82b1bcc00dcaaf3a09c8f428fdeacf4190c013598b81875dfac2b08a572422db7df779a99332d0fce186d15a3e4d49
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.6":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.4
  resolution: "socks@npm:2.8.4"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/00c3271e233ccf1fb83a3dd2060b94cc37817e0f797a93c560b9a7a86c4a0ec2961fb31263bdd24a3c28945e24868b5f063cd98744171d9e942c513454b50ae5
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: "npm:~5.1.0"
  checksum: 10c0/b4f89f3a92fd101b5653ca3c99550e07bdf9e13b35037e9e2a1c7b47cec4e55e06ff3fc468e314a0b5e80bfbaf65c1ca5a84978764884ae9413bec1fc6ca924e
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"tiny-inflate@npm:^1.0.2":
  version: 1.0.3
  resolution: "tiny-inflate@npm:1.0.3"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"typescript@npm:~5.8.2":
  version: 5.8.2
  resolution: "typescript@npm:5.8.2"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5c4f6fbf1c6389b6928fe7b8fcd5dc73bb2d58cd4e3883f1d774ed5bd83b151cbac6b7ecf11723de56d4676daeba8713894b1e9af56174f2f9780ae7848ec3c6
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A~5.8.2#optional!builtin<compat/typescript>":
  version: 5.8.2
  resolution: "typescript@patch:typescript@npm%3A5.8.2#optional!builtin<compat/typescript>::version=5.8.2&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5448a08e595cc558ab321e49d4cac64fb43d1fa106584f6ff9a8d8e592111b373a995a1d5c7f3046211c8a37201eb6d0f1566f15cdb7a62a5e3be01d087848e2
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vscode-json-languageservice@npm:~5.4.2":
  version: 5.4.3
  resolution: "vscode-json-languageservice@npm:5.4.3"
  dependencies:
    "@vscode/l10n": "npm:^0.0.18"
    jsonc-parser: "npm:^3.3.1"
    vscode-languageserver-textdocument: "npm:^1.0.12"
    vscode-languageserver-types: "npm:^3.17.5"
    vscode-uri: "npm:^3.0.8"
  checksum: 10c0/8706b431828e12562db3655074b0358800bf8695fb442ac4e764bea8c0421892e0cd3ef5dec19e5a16f19d10f97f186ba28f4611899421670f1419c71532262e
  languageName: node
  linkType: hard

"vscode-jsonrpc@npm:8.2.0":
  version: 8.2.0
  resolution: "vscode-jsonrpc@npm:8.2.0"
  checksum: 10c0/0789c227057a844f5ead55c84679206227a639b9fb76e881185053abc4e9848aa487245966cc2393fcb342c4541241b015a1a2559fddd20ac1e68945c95344e6
  languageName: node
  linkType: hard

"vscode-jsonrpc@npm:~8.2.1":
  version: 8.2.1
  resolution: "vscode-jsonrpc@npm:8.2.1"
  checksum: 10c0/595e07f779112d979d9cd37a00e4b973c39da09dd7c35b149f9fb7857c87db00d0ece7772c5a6f65cc19e38afc4fc64f8130d2f0e6b5f7fc160a0fc6b94e8c07
  languageName: node
  linkType: hard

"vscode-languageclient@npm:~9.0.1":
  version: 9.0.1
  resolution: "vscode-languageclient@npm:9.0.1"
  dependencies:
    minimatch: "npm:^5.1.0"
    semver: "npm:^7.3.7"
    vscode-languageserver-protocol: "npm:3.17.5"
  checksum: 10c0/fe6638dabb0ef9915937cae2cecb74978a3e84c01f9c3f6a6bd61d0bbbb601fb694aae6253689bde30d40ce941c02068b0448268cf3e5ef08e6f9167cc7f6242
  languageName: node
  linkType: hard

"vscode-languageserver-protocol@npm:3.17.5, vscode-languageserver-protocol@npm:~3.17.5":
  version: 3.17.5
  resolution: "vscode-languageserver-protocol@npm:3.17.5"
  dependencies:
    vscode-jsonrpc: "npm:8.2.0"
    vscode-languageserver-types: "npm:3.17.5"
  checksum: 10c0/5f38fd80da9868d706eaa4a025f4aff9c3faad34646bcde1426f915cbd8d7e8b6c3755ce3fef6eebd256ba3145426af1085305f8a76e34276d2e95aaf339a90b
  languageName: node
  linkType: hard

"vscode-languageserver-textdocument@npm:^1.0.12, vscode-languageserver-textdocument@npm:~1.0.11":
  version: 1.0.12
  resolution: "vscode-languageserver-textdocument@npm:1.0.12"
  checksum: 10c0/534349894b059602c4d97615a1147b6c4c031141c2093e59657f54e38570f5989c21b376836f13b9375419869242e9efb4066643208b21ab1e1dee111a0f00fb
  languageName: node
  linkType: hard

"vscode-languageserver-types@npm:3.17.5, vscode-languageserver-types@npm:^3.17.5":
  version: 3.17.5
  resolution: "vscode-languageserver-types@npm:3.17.5"
  checksum: 10c0/1e1260de79a2cc8de3e46f2e0182cdc94a7eddab487db5a3bd4ee716f67728e685852707d72c059721ce500447be9a46764a04f0611e94e4321ffa088eef36f8
  languageName: node
  linkType: hard

"vscode-languageserver@npm:~9.0.1":
  version: 9.0.1
  resolution: "vscode-languageserver@npm:9.0.1"
  dependencies:
    vscode-languageserver-protocol: "npm:3.17.5"
  bin:
    installServerIntoExtension: bin/installServerIntoExtension
  checksum: 10c0/8a0838d77c98a211c76e54bd3a6249fc877e4e1a73322673fb0e921168d8e91de4f170f1d4ff7e8b6289d0698207afc6aba6662d4c1cd8e4bd7cae96afd6b0c2
  languageName: node
  linkType: hard

"vscode-oniguruma@npm:1.7.0":
  version: 1.7.0
  resolution: "vscode-oniguruma@npm:1.7.0"
  checksum: 10c0/bef0073c665ddf8c86e51da94529c905856559e9aba97a9882f951acd572da560384775941ab6e7e8db94d9c578b25fefb951e4b73c37e8712e16b0231de2689
  languageName: node
  linkType: hard

"vscode-textmate@npm:9.2.0":
  version: 9.2.0
  resolution: "vscode-textmate@npm:9.2.0"
  checksum: 10c0/b518438a2355dfc16852d4079c7cf47acf1c0f38b21fbf5c22f6008fa7461d08be32610133632be78e533ab68a8387624daa04c69242e0bea2029c32015ffeea
  languageName: node
  linkType: hard

"vscode-uri@npm:^3.0.8, vscode-uri@npm:~3.1.0":
  version: 3.1.0
  resolution: "vscode-uri@npm:3.1.0"
  checksum: 10c0/5f6c9c10fd9b1664d71fab4e9fbbae6be93c7f75bb3a1d9d74399a88ab8649e99691223fd7cef4644376cac6e94fa2c086d802521b9a8e31c5af3e60f0f35624
  languageName: node
  linkType: hard

"vscode-uri@npm:~3.0.8":
  version: 3.0.8
  resolution: "vscode-uri@npm:3.0.8"
  checksum: 10c0/f7f217f526bf109589969fe6e66b71e70b937de1385a1d7bb577ca3ee7c5e820d3856a86e9ff2fa9b7a0bc56a3dd8c3a9a557d3fedd7df414bc618d5e6b567f9
  languageName: node
  linkType: hard

"vscode-ws-jsonrpc@npm:~3.4.0":
  version: 3.4.0
  resolution: "vscode-ws-jsonrpc@npm:3.4.0"
  dependencies:
    vscode-jsonrpc: "npm:~8.2.1"
  checksum: 10c0/04c47b26ea3c826d34ebbcd6a6be4f82a0d300e4db3d38c46a990ba0328158272cbe6b3a563cabda840eb7f2b974f637f05d68c0f14896a0d45ffba0ad27792b
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"ws@npm:~8.18.0":
  version: 8.18.0
  resolution: "ws@npm:8.18.0"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/25eb33aff17edcb90721ed6b0eb250976328533ad3cd1a28a274bd263682e7296a6591ff1436d6cbc50fa67463158b062f9d1122013b361cec99a05f84680e06
  languageName: node
  linkType: hard

"wtd-core@npm:~4.0.1":
  version: 4.0.1
  resolution: "wtd-core@npm:4.0.1"
  checksum: 10c0/18b515eefb34d5c5e137af35e74c2d0f44164530b48b6f5c2bedc4a39427c026e986721d0b6abf33ea8cae1aa54ae217e54f00d1100b39cb44fef13cae4cd388
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard
