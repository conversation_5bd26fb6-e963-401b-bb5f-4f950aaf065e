<!DOCTYPE html>
<html lang="en">

<head>
    <title>Application Playground</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="./resources/styles/views.css">
</head>

<!-- body style of vscode theme are not applied when embedded in react -->
<body style="background-color: #1f1f1f; color: #cccccc;">
    <div class="exampleHeadelineDiv">
        <b class="exampleHeadeline">Application Playground</b> - [<a href="../../index.html">Back to Index</a>] <b>Heads up:</b> This is a prototype and still evolving.
    </div>
    <div id="react-root"></div>
    <script type="module" rel="modulepreload" src="./src/appPlayground/reactLauncher"></script>
</body>

</html>
