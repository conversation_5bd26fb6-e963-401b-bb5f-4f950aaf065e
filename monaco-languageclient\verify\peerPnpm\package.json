{"name": "@typefox/peer-check-pnpm", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.10.0", "pnpm": ">=9.15.0"}, "volta": {"node": "22.16.0", "pnpm": "10.3.0"}, "dependencies": {"monaco-languageclient-examples": "~2025.6.2", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0"}, "devDependencies": {"typescript": "~5.8.2"}, "scripts": {"build": "tsc --build tsconfig.json", "verify:ci": "pnpm install && pnpm run build"}}