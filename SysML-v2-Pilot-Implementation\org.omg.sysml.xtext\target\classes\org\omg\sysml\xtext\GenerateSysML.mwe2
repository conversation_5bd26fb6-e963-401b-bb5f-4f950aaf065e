module org.omg.sysml.xtext.GenerateSysML

import org.eclipse.xtext.xtext.generator.*
import org.eclipse.xtext.xtext.generator.model.project.*
import org.eclipse.emf.mwe.utils.StandaloneSetup
import org.omg.kerml.expressions.xtext.NoContentAssistAntlrGeneratorFragment

var rootPath = ".."
var projectName = "org.omg.sysml"
var genModel = "platform:/resource/${projectName}/model/SysML_.genmodel"
var ePackage = "${projectName}.lang.sysml.SysMLPackage"

Workflow {
	
	component = XtextGenerator {
		configuration = {
			project = StandardProjectConfig {
				baseName = "${projectName}.xtext"
				rootPath = rootPath
				runtimeTest = {
					enabled = false
				}
				eclipsePlugin = {
					enabled = true
				}
				eclipsePluginTest = {
					enabled = false
				}
				createEclipseMetaData = true
			}
			code = {
				encoding = "UTF-8"
				lineDelimiter = "\n"
				fileHeader = "/*\n * generated by Xtext \${version}\n */"
			}
		}
		
	    language = XtextGeneratorLanguage {
			name = "${projectName}.xtext.SysML"
			fileExtensions = "sysml"
		    referencedResource = "${genModel}"

	        fragment = grammarAccess.GrammarAccessFragment2 {}
	        fragment = ecore.EMFGeneratorFragment2 {}
	        fragment = serializer.SerializerFragment2 {}
	        fragment = resourceFactory.ResourceFactoryFragment2 {}
	        fragment = NoContentAssistAntlrGeneratorFragment {}
	        fragment = InternalParserSplitterFragment {
	        	originalClassName = "InternalSysMLParser"
	        	baseName = "${projectName}.xtext"
	        	count = 18
	        }
	        fragment = validation.ValidatorFragment2 {}
	        fragment = scoping.ImportNamespacesScopingFragment2 {}
	        fragment = exporting.QualifiedNamesFragment2 {}
	        fragment = builder.BuilderIntegrationFragment2 {}
	        fragment = generator.GeneratorFragment2 {}
//	        fragment = formatting.Formatter2Fragment2 {}
	        fragment = ui.labeling.LabelProviderFragment2 {}
	        fragment = ui.outline.QuickOutlineFragment2 {}
	        fragment = ui.outline.OutlineTreeProviderFragment2 {}
//	        fragment = ui.quickfix.QuickfixProviderFragment2 {}
//	        fragment = ui.contentAssist.ContentAssistFragment2 {}
	        fragment = junit.JUnitFragment {}
//	        fragment = ui.refactoring.RefactorElementNameFragment2 {}
	        fragment = types.TypesGeneratorFragment2 {}
	        fragment = xbase.XtypeGeneratorFragment2 {}
	        fragment = xbase.XbaseGeneratorFragment2 {}
//	        fragment = ui.templates.CodetemplatesGeneratorFragment2 {}
	        fragment = ui.compare.CompareFragment2 {}
//	        fragment = web.WebIntegrationFragment {
//	            framework = "Ace"
//	        }
	        fragment = ui.projectWizard.TemplateProjectWizardFragment {}
	        fragment = ui.fileWizard.TemplateFileWizardFragment {}
	    }	
    }
	
	bean = StandaloneSetup {
        platformUri = "${rootPath}"
        scanClassPath = true
        registerGeneratedEPackage = "${ePackage}"
        registerGenModelFile = "${genModel}"
    }	
    
}
