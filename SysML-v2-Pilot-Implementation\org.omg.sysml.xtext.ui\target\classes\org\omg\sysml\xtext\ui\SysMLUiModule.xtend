/*
 * generated by Xtext 2.18.0.M3
 */
package org.omg.sysml.xtext.ui

import org.eclipse.xtend.lib.annotations.FinalFieldsConstructor
import org.eclipse.xtext.ui.shared.Access
import org.eclipse.xtext.ui.editor.quickfix.IssueResolutionProvider
import org.omg.sysml.xtext.ui.quickfix.SysMLQuickfixProvider
import org.eclipse.xtext.ide.editor.syntaxcoloring.ISemanticHighlightingCalculator
import org.omg.kerml.xtext.ui.KerMLUserKeywordHighlightingCalculator
import org.omg.kerml.xtext.ui.KerMLAntlrTokenToAttributeIdMapper
import org.eclipse.xtext.ui.editor.syntaxcoloring.AbstractAntlrTokenToAttributeIdMapper
import org.eclipse.xtext.ui.editor.syntaxcoloring.IHighlightingConfiguration
import org.omg.kerml.xtext.ui.KerMLHighlightingConfiguration
import org.omg.kerml.xtext.library.ILibraryIndexProvider
import org.omg.kerml.xtext.ui.library.DynamicLibraryIndexProvider
import com.google.inject.Provides
import org.eclipse.xtext.ui.resource.ProjectByResourceProvider

/**
 * Use this class to register components to be used within the Eclipse IDE.
 */
@FinalFieldsConstructor
class SysMLUiModule extends AbstractSysMLUiModule {
	
	override Class<? extends IssueResolutionProvider> bindIssueResolutionProvider() {
		return SysMLQuickfixProvider
	}
	
	def Class<? extends ISemanticHighlightingCalculator> bindISemanticHighlightingCalculator() {
		return KerMLUserKeywordHighlightingCalculator
	}
	
	def Class<? extends IHighlightingConfiguration> bindIHighlightingConfiguration() {
		KerMLHighlightingConfiguration
	}
	
	def Class<? extends AbstractAntlrTokenToAttributeIdMapper> bindAbstractAntlrTokenToAttributeIdMapper() {
		KerMLAntlrTokenToAttributeIdMapper
	}
	
	override provideIAllContainersState() {
		// Replaces the JDT-aware project state implementation with one that handles dependencies via project references
		Access.workspaceProjectsState
	}
	
    @Provides
    def ILibraryIndexProvider getILibraryIndexProvider(ProjectByResourceProvider provider) {
        DynamicLibraryIndexProvider.getInstance(provider)
    }  
}
