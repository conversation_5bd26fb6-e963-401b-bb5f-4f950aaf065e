//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/
//XPECT noErrors ---> ""
package test{
	feature A{
		feature a {}
		alias aa for a;
	}
//* XPECT scope at A ---
	A, A.a, A.a.self, A.a.that, A.a.that.self, A.aa, A.aa.self, A.aa.that,
	A.aa.that.self, A.self, A.that, A.that.self, B, B.a, B.a.self, B.a.that, B.a.that.self, B.aa,
	B.aa.self, B.aa.that, B.aa.that.self, B.b, B.b.self, B.b.that, B.b.that.self, B.b_alias,
	B.b_alias.self, B.b_alias.that, B.b_alias.that.self, B.self, B.that, B.that.self, test.A,
	test.A.a, test.A.a.self, test.A.a.that, test.A.a.that.self, test.A.aa, test.A.aa.self,
	test.A.aa.that, test.A.aa.that.self, test.A.self, test.A.that, test.A.that.self, test.B,
	test.B.a, test.B.a.self, test.B.a.that, test.B.a.that.self, test.B.aa, test.B.aa.self,
	test.B.aa.that, test.B.aa.that.self, test.B.b, test.B.b.self, test.B.b.that,
	test.B.b.that.self, test.B.b_alias, test.B.b_alias.self, test.B.b_alias.that,
	test.B.b_alias.that.self, test.B.self, test.B.that, test.B.that.self
--- */
	feature B : A{
		feature b: aa;
		alias b_alias for b;
	}
}
