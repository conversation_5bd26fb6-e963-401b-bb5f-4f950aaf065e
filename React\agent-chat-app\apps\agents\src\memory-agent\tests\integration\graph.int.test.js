"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var langgraph_1 = require("@langchain/langgraph");
var graph_js_1 = require("../../graph.js");
(0, globals_1.describe)("Memory Graph", function () {
    var conversations = [
        ["My name is Alice and I love pizza. Remember this."],
        [
            "Hi, I'm Bob and I enjoy playing tennis. Remember this.",
            "Yes, I also have a pet dog named Max.",
            "Max is a golden retriever and he's 5 years old. Please remember this too.",
        ],
        [
            "Hello, I'm Charlie. I work as a software engineer and I'm passionate about AI. Remember this.",
            "I specialize in machine learning algorithms and I'm currently working on a project involving natural language processing.",
            "My main goal is to improve sentiment analysis accuracy in multi-lingual texts. It's challenging but exciting.",
            "We've made some progress using transformer models, but we're still working on handling context and idioms across languages.",
            "Chinese and English have been the most challenging pair so far due to their vast differences in structure and cultural contexts.",
        ],
    ];
    globals_1.it.each(conversations.map(function (conversation, index) { return [
        ["short", "medium", "long"][index],
        conversation,
    ]; }))("should store memories for %s conversation", function (_, conversation) { return __awaiter(void 0, void 0, void 0, function () {
        var memStore, graph, userId, _i, conversation_1, content, namespace, memories, badNamespace, badMemories;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    memStore = new langgraph_1.InMemoryStore();
                    graph = graph_js_1.builder.compile({
                        store: memStore,
                        checkpointer: new langgraph_1.MemorySaver(),
                    });
                    userId = "test-user";
                    _i = 0, conversation_1 = conversation;
                    _a.label = 1;
                case 1:
                    if (!(_i < conversation_1.length)) return [3 /*break*/, 4];
                    content = conversation_1[_i];
                    return [4 /*yield*/, graph.invoke({
                            messages: [
                                { role: "user", content: [{ type: "text", text: content }] },
                            ],
                        }, {
                            configurable: {
                                userId: userId,
                                thread_id: "thread",
                                model: "gpt-4o-mini",
                                systemPrompt: "You are a helpful assistant.",
                            },
                        })];
                case 2:
                    _a.sent();
                    _a.label = 3;
                case 3:
                    _i++;
                    return [3 /*break*/, 1];
                case 4:
                    namespace = ["memories", userId];
                    return [4 /*yield*/, memStore.search(namespace)];
                case 5:
                    memories = _a.sent();
                    (0, globals_1.expect)(memories.length).toBeGreaterThan(0);
                    badNamespace = ["memories", "wrong-user"];
                    return [4 /*yield*/, memStore.search(badNamespace)];
                case 6:
                    badMemories = _a.sent();
                    (0, globals_1.expect)(badMemories.length).toBe(0);
                    return [2 /*return*/];
            }
        });
    }); }, 30000);
});
