{"name": "micromark-util-normalize-identifier", "version": "2.0.1", "description": "micromark utility normalize identifiers (as found in references, definitions)", "license": "MIT", "keywords": ["micromark", "util", "utility", "normalize", "id", "identifier"], "repository": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-normalize-identifier", "bugs": "https://github.com/micromark/micromark/issues", "funding": [{"type": "GitHub Sponsors", "url": "https://github.com/sponsors/unifiedjs"}, {"type": "OpenCollective", "url": "https://opencollective.com/unified"}], "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "type": "module", "files": ["dev/", "index.d.ts.map", "index.d.ts", "index.js"], "exports": {"development": "./dev/index.js", "default": "./index.js"}, "dependencies": {"micromark-util-symbol": "^2.0.0"}, "scripts": {"build": "micromark-build"}, "xo": {"envs": ["shared-node-browser"], "prettier": true, "rules": {"unicorn/prefer-code-point": "off", "unicorn/prefer-string-replace-all": "off"}}}