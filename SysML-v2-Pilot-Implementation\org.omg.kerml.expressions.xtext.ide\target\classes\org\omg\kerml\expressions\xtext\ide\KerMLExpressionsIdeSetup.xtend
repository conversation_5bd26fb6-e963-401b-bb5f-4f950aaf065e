/*
 * generated by Xtext 2.24.0
 */
package org.omg.kerml.expressions.xtext.ide

import com.google.inject.Guice
import org.eclipse.xtext.util.Modules2
import org.omg.kerml.expressions.xtext.KerMLExpressionsRuntimeModule
import org.omg.kerml.expressions.xtext.KerMLExpressionsStandaloneSetup

/**
 * Initialization support for running Xtext languages as language servers.
 */
class KerMLExpressionsIdeSetup extends KerMLExpressionsStandaloneSetup {

	override createInjector() {
		Guice.createInjector(Modules2.mixin(new KerMLExpressionsRuntimeModule, new KerMLExpressionsIdeModule))
	}
	
}
