//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	feature A{
		feature a2{}
	}
	feature inner{
		public import OuterPackage::*;
		feature B : A {
			//* XPECT errors --- 
			"Couldn't resolve reference to Type 'a2'." at "a2"
			--- */
			feature b : a2{}
		}
	}
}
