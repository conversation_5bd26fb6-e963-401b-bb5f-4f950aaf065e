/*
 * generated by Xtext 2.25.0
 */
package org.omg.kerml.xtext.ide

import com.google.inject.Guice
import org.eclipse.xtext.util.Modules2
import org.omg.kerml.xtext.KerMLRuntimeModule
import org.omg.kerml.xtext.KerMLStandaloneSetup
import com.google.inject.Module;

/**
 * Initialization support for running Xtext languages as language servers.
 */
class KerMLIdeSetup extends KerMLStandaloneSetup {

	override createInjector() {
		Guice.createInjector(Modules2.mixin(new KerMLRuntimeModule as Module, new KerMLIdeModule))
	}
	
}
