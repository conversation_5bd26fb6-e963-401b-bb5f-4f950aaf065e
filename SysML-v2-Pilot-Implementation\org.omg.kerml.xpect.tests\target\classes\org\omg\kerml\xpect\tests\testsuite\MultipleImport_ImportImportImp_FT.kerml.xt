//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
		File {from ="/src/DependencyMultipleImport_Feature_FT.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
				File {from ="/src/DependencyMultipleImport_Feature_FT.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	public import OuterPackage2::*;
	//XPECT linkedName at C --> OuterPackage2.C
	//* XPECT scope at C ---
	A, A.a1, A.a1.self, A.a1.that, A.a1.that.self, A.self, A.that, A.that.self, B,
	B.b, B.b.a1, B.b.a1.self, B.b.a1.that, B.b.a1.that.self, B.b.self, B.b.that,
	B.b.that.self, B.self, B.that, B.that.self, C, C.b, C.b.a1, C.b.a1.self, C.b.a1.that,
	C.b.a1.that.self, C.b.self, C.b.that, C.b.that.self, C.c, C.c.self, C.c.that, C.c.that.self,
	C.self, C.that, C.that.self, OuterPackage.A, OuterPackage.A.a1, OuterPackage.A.a1.self,
	OuterPackage.A.a1.that, OuterPackage.A.a1.that.self, OuterPackage.A.self, OuterPackage.A.that,
	OuterPackage.A.that.self, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1,
	OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that, OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self,
	OuterPackage.B.b.that, OuterPackage.B.b.that.self, OuterPackage.B.self, OuterPackage.B.that,
	OuterPackage.B.that.self, OuterPackage2.A, OuterPackage2.A.a1, OuterPackage2.A.a1.self,
	OuterPackage2.A.a1.that, OuterPackage2.A.a1.that.self, OuterPackage2.A.self, OuterPackage2.A.that,
	OuterPackage2.A.that.self, OuterPackage2.B, OuterPackage2.B.b, OuterPackage2.B.b.a1,
	OuterPackage2.B.b.a1.self, OuterPackage2.B.b.a1.that, OuterPackage2.B.b.a1.that.self,
	OuterPackage2.B.b.self, OuterPackage2.B.b.that, OuterPackage2.B.b.that.self, OuterPackage2.B.self,
	OuterPackage2.B.that, OuterPackage2.B.that.self, OuterPackage2.C, OuterPackage2.C.b,
	OuterPackage2.C.b.a1, OuterPackage2.C.b.a1.self, OuterPackage2.C.b.a1.that,
	OuterPackage2.C.b.a1.that.self, OuterPackage2.C.b.self, OuterPackage2.C.b.that, OuterPackage2.C.b.that.self,
	OuterPackage2.C.c, OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self,
	OuterPackage2.C.self, OuterPackage2.C.that, OuterPackage2.C.that.self, Test, Test.b, Test.b.a1,
	Test.b.a1.self, Test.b.a1.that, Test.b.a1.that.self, Test.b.self, Test.b.that,
	Test.b.that.self, Test.c, Test.c.self, Test.c.that, Test.c.that.self, Test.self, Test.that,
	Test.that.self, Test.try, Test.try.a1, Test.try.a1.self, Test.try.a1.that,
	Test.try.a1.that.self, Test.try.self, Test.try.that, Test.try.that.self, test.A, test.A.a1,
	test.A.a1.self, test.A.a1.that, test.A.a1.that.self, test.A.self, test.A.that,
	test.A.that.self, test.B, test.B.b, test.B.b.a1, test.B.b.a1.self, test.B.b.a1.that,
	test.B.b.a1.that.self, test.B.b.self, test.B.b.that, test.B.b.that.self, test.B.self, test.B.that,
	test.B.that.self, test.C, test.C.b, test.C.b.a1, test.C.b.a1.self, test.C.b.a1.that,
	test.C.b.a1.that.self, test.C.b.self, test.C.b.that, test.C.b.that.self, test.C.c, test.C.c.self,
	test.C.c.that, test.C.c.that.self, test.C.self, test.C.that, test.C.that.self, test.Test,
	test.Test.b, test.Test.b.a1, test.Test.b.a1.self, test.Test.b.a1.that,
	test.Test.b.a1.that.self, test.Test.b.self, test.Test.b.that, test.Test.b.that.self, test.Test.c,
	test.Test.c.self, test.Test.c.that, test.Test.c.that.self, test.Test.self, test.Test.that,
	test.Test.that.self, test.Test.try, test.Test.try.a1, test.Test.try.a1.self, test.Test.try.a1.that,
	test.Test.try.a1.that.self, test.Test.try.self, test.Test.try.that, test.Test.try.that.self
	--- */
	feature Test : C {
		//XPECT linkedName at b --> OuterPackage.B.b
		//* XPECT scope at b ---
		A, A.a1, A.a1.self, A.a1.that, A.a1.that.self, A.self, A.that, A.that.self, B,
		B.b, B.b.a1, B.b.a1.self, B.b.a1.that, B.b.a1.that.self, B.b.self, B.b.that,
		B.b.that.self, B.self, B.that, B.that.self, C, C.b, C.b.a1, C.b.a1.self, C.b.a1.that,
		C.b.a1.that.self, C.b.self, C.b.that, C.b.that.self, C.c, C.c.self, C.c.that, C.c.that.self,
		C.self, C.that, C.that.self, OuterPackage.A, OuterPackage.A.a1, OuterPackage.A.a1.self,
		OuterPackage.A.a1.that, OuterPackage.A.a1.that.self, OuterPackage.A.self, OuterPackage.A.that,
		OuterPackage.A.that.self, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1,
		OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that, OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self,
		OuterPackage.B.b.that, OuterPackage.B.b.that.self, OuterPackage.B.self, OuterPackage.B.that,
		OuterPackage.B.that.self, OuterPackage2.A, OuterPackage2.A.a1, OuterPackage2.A.a1.self,
		OuterPackage2.A.a1.that, OuterPackage2.A.a1.that.self, OuterPackage2.A.self, OuterPackage2.A.that,
		OuterPackage2.A.that.self, OuterPackage2.B, OuterPackage2.B.b, OuterPackage2.B.b.a1,
		OuterPackage2.B.b.a1.self, OuterPackage2.B.b.a1.that, OuterPackage2.B.b.a1.that.self,
		OuterPackage2.B.b.self, OuterPackage2.B.b.that, OuterPackage2.B.b.that.self, OuterPackage2.B.self,
		OuterPackage2.B.that, OuterPackage2.B.that.self, OuterPackage2.C, OuterPackage2.C.b,
		OuterPackage2.C.b.a1, OuterPackage2.C.b.a1.self, OuterPackage2.C.b.a1.that,
		OuterPackage2.C.b.a1.that.self, OuterPackage2.C.b.self, OuterPackage2.C.b.that, OuterPackage2.C.b.that.self,
		OuterPackage2.C.c, OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self,
		OuterPackage2.C.self, OuterPackage2.C.that, OuterPackage2.C.that.self, Test, Test.b, Test.b.a1,
		Test.b.a1.self, Test.b.a1.that, Test.b.a1.that.self, Test.b.self, Test.b.that,
		Test.b.that.self, Test.c, Test.c.self, Test.c.that, Test.c.that.self, Test.self, Test.that,
		Test.that.self, Test.try, Test.try.a1, Test.try.a1.self, Test.try.a1.that,
		Test.try.a1.that.self, Test.try.self, Test.try.that, Test.try.that.self, b, b.a1, b.a1.self,
		b.a1.that, b.a1.that.self, b.self, b.that, b.that.self, c, c.self, c.that, c.that.self,
		self, test.A, test.A.a1, test.A.a1.self, test.A.a1.that, test.A.a1.that.self,
		test.A.self, test.A.that, test.A.that.self, test.B, test.B.b, test.B.b.a1, test.B.b.a1.self,
		test.B.b.a1.that, test.B.b.a1.that.self, test.B.b.self, test.B.b.that, test.B.b.that.self,
		test.B.self, test.B.that, test.B.that.self, test.C, test.C.b, test.C.b.a1, test.C.b.a1.self,
		test.C.b.a1.that, test.C.b.a1.that.self, test.C.b.self, test.C.b.that, test.C.b.that.self,
		test.C.c, test.C.c.self, test.C.c.that, test.C.c.that.self, test.C.self, test.C.that,
		test.C.that.self, test.Test, test.Test.b, test.Test.b.a1, test.Test.b.a1.self,
		test.Test.b.a1.that, test.Test.b.a1.that.self, test.Test.b.self, test.Test.b.that,
		test.Test.b.that.self, test.Test.c, test.Test.c.self, test.Test.c.that, test.Test.c.that.self,
		test.Test.self, test.Test.that, test.Test.that.self, test.Test.try, test.Test.try.a1,
		test.Test.try.a1.self, test.Test.try.a1.that, test.Test.try.a1.that.self, test.Test.try.self,
		test.Test.try.that, test.Test.try.that.self, that, that.self, try, try.a1, try.a1.self,
		try.a1.that, try.a1.that.self, try.self, try.that, try.that.self
		--- */
		feature try : b;
	}
}
