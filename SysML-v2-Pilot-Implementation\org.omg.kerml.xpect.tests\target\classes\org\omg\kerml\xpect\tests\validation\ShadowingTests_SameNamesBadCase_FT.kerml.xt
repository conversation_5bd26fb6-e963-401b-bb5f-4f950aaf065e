//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	feature A{
		feature a1{}
	}
	feature B : A{
		feature A{
			feature a2{}
		}
		// XPECT errors --> "Couldn't resolve reference to Type 'A::a1'." at "A::a1"
		feature b : A::a1{}
	}
}
