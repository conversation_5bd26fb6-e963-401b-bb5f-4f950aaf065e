# Monaco Editor 集成说明

本项目已成功集成 `@monaco-editor/react` 来实现专业的代码编辑器功能。

## 🚀 功能特性

### ✅ 已实现的功能

1. **Monaco Editor 集成**
   - 使用 `@monaco-editor/react` v4.7.0
   - 支持 TypeScript 类型定义
   - 完整的编辑器功能（语法高亮、代码补全、错误检测等）

2. **SysML v2 语言支持**
   - 自定义 SysML v2 语言定义
   - 完整的关键字高亮
   - 语法着色配置
   - 自定义深色主题

3. **编辑器功能**
   - 代码折叠
   - 行号显示
   - 小地图（minimap）
   - 智能缩进
   - 多光标编辑
   - 查找替换
   - 快捷键支持（Ctrl+S 保存）

4. **集成功能**
   - 实时内容变化检测
   - 防抖处理避免频繁更新
   - 与应用状态管理集成
   - 文件保存功能
   - AI 代码生成集成
   - SVG 图表生成集成

## 📁 文件结构

```
src/components/workspace/
├── CodeEditor.tsx          # 主要的代码编辑器组件
├── MonacoEditorTest.tsx    # 测试组件
└── ...

src/app/
├── test-editor/
│   └── page.tsx           # 编辑器测试页面
└── ...
```

## 🔧 技术实现

### 核心组件：CodeEditor.tsx

```typescript
import Editor, { Monaco } from '@monaco-editor/react';

// 主要功能：
// 1. SysML v2 语言配置
// 2. 自定义主题定义
// 3. 编辑器事件处理
// 4. 与应用状态集成
```

### SysML v2 语言支持

- **关键字**: package, part, attribute, port, connection, interface, requirement, constraint, action, state, transition 等
- **语法高亮**: 支持字符串、数字、注释、操作符等
- **自动补全**: 基于 SysML v2 语法的智能提示

### 自定义主题

- **sysml-dark**: 专为 SysML v2 优化的深色主题
- 支持语法元素的差异化着色
- 优化的背景色和前景色搭配

## 🚀 使用方法

### 1. 基本使用

```tsx
import CodeEditor from '@/components/workspace/CodeEditor';

// 在组件中使用
<CodeEditor activeTab={activeTab} />
```

### 2. 测试页面

访问 `/test-editor` 页面来测试 Monaco Editor 的基本功能。

### 3. 开发环境运行

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问测试页面
http://localhost:3000/test-editor
```

## 📋 配置选项

### Editor 配置

```typescript
options={{
  fontSize: 14,
  lineNumbers: 'on',
  minimap: { enabled: true },
  scrollBeyondLastLine: false,
  wordWrap: 'on',
  folding: true,
  automaticLayout: true,
  // ... 更多配置
}}
```

### 主题配置

- `theme="sysml-dark"` - SysML 深色主题
- `theme="vs-dark"` - VS Code 深色主题
- `theme="light"` - 浅色主题

## 🔍 调试和测试

### 1. 控制台输出

编辑器会在控制台输出以下信息：
- Monaco Editor 挂载成功信息
- 编辑器实例和 Monaco 实例
- 内容变化事件

### 2. 功能测试

- ✅ 语法高亮
- ✅ 代码补全
- ✅ 错误检测
- ✅ 快捷键（Ctrl+S）
- ✅ 代码折叠
- ✅ 查找替换

## 🛠️ 故障排除

### 常见问题

1. **编辑器不显示**
   - 检查 `@monaco-editor/react` 是否正确安装
   - 确认网络连接（Monaco 从 CDN 加载）

2. **语法高亮不工作**
   - 确认语言设置为 `sysml`
   - 检查语言配置是否正确注册

3. **主题不生效**
   - 确认主题名称正确
   - 检查主题定义是否在 `beforeMount` 中注册

### 开发建议

1. 使用 `MonacoEditorTest` 组件进行功能测试
2. 在浏览器开发者工具中查看控制台输出
3. 参考 Monaco Editor 官方文档进行高级配置

## 📚 参考资源

- [Monaco Editor 官方文档](https://microsoft.github.io/monaco-editor/)
- [@monaco-editor/react 文档](https://www.npmjs.com/package/@monaco-editor/react)
- [SysML v2 语言规范](https://www.omg.org/spec/SysML/)

## 🎯 下一步计划

- [ ] 添加更多 SysML v2 语法支持
- [ ] 实现语言服务器协议 (LSP) 集成
- [ ] 添加代码片段 (snippets)
- [ ] 优化性能和加载速度
- [ ] 添加更多主题选项
