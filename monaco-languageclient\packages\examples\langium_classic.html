<!DOCTYPE html>
<html lang="en">

<head>
    <title>Langium Grammar DSL (Classic Mode)</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="exampleHeadelineDiv">
        <b class="exampleHeadeline">Langium Grammar DSL (Classic Mode)</b> - [<a href="../../index.html">Back to Index</a>]
        <br>
        <button type="button" id="button-start">Start</button>
        <button type="button" id="button-dispose">Dispose</button>
    </div>
    <div id="monaco-editor-root" style="height: 80vh; border: 1px solid grey"></div>
    <script type="module">
        import { runLangiumDslWrapper } from './src/langium/langium-dsl/wrapperLangium.ts';

        runLangiumDslWrapper(false);
    </script>;
</body>

</html>
