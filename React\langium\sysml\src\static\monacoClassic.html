<!DOCTYPE html>
<html>
    <head>
        <meta charset='utf-8'>
        <!-- Page & Monaco styling -->
        <link href="styles.css" rel="stylesheet"/>
        <title>sysml in Langium</title>
    </head>
    <body>
        <h1>sysml in Langium</h1>
        <div class="wrapper">
            <b>Monaco-Editor + Monarch</b>
            <!-- Monaco Root -->
            <div id="monaco-editor-root"></div>
        </div>
        <footer>
            <p style="font-style:italic">Powered by</p>
            <img width="125" src="https://langium.org/assets/langium_logo_w_nib.svg" alt="Langium">
        </footer>
        <!-- Monaco Configuration -->
        <script type="module">
            // vite is used to resolve the included TypeScript files
            import { configureMonacoWorkers } from '../src/setupCommon';
            import { executeClassic } from '../src/setupClassic';

            configureMonacoWorkers();
            // keep a reference to a promise for when the editor is finished starting, we'll use this to setup the canvas on load
            const startingPromise = executeClassic(document.getElementById('monaco-editor-root'));
        </script>
    </body>
</html>
