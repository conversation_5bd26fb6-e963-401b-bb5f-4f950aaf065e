{"name": "html-url-attributes", "version": "3.0.1", "description": "Map of URL attributes in HTML", "license": "MIT", "keywords": ["attribute", "attribute", "html", "property", "url"], "repository": "https://github.com/rehypejs/rehype-minify/tree/main/packages/html-url-attributes", "bugs": "https://github.com/rehypejs/rehype-minify/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/unified"}, "author": "<PERSON> <<EMAIL>> (https://wooorm.com)", "contributors": ["<PERSON> <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": "./index.js", "files": ["index.d.ts", "index.d.ts.map", "index.js", "lib/"], "scripts": {}, "typeCoverage": {"atLeast": 100, "detail": true, "ignoreCatch": true, "strict": true}, "xo": {"prettier": true, "rules": {"capitalized-comments": "off"}}}