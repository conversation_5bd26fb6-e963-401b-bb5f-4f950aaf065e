/*
 * generated by Xtext 2.34.0
 */
grammar InternalKerMLExpressions;

options {
	superClass=AbstractInternalAntlrParser;
}

@lexer::header {
package org.omg.kerml.expressions.xtext.parser.antlr.internal;

// Hack: Use our own Lexer superclass by means of import. 
// Currently there is no other way to specify the superclass for the lexer.
import org.eclipse.xtext.parser.antlr.Lexer;
}

@parser::header {
package org.omg.kerml.expressions.xtext.parser.antlr.internal;

import org.eclipse.xtext.*;
import org.eclipse.xtext.parser.*;
import org.eclipse.xtext.parser.impl.*;
import org.eclipse.emf.ecore.util.EcoreUtil;
import org.eclipse.emf.ecore.EObject;
import org.eclipse.xtext.parser.antlr.AbstractInternalAntlrParser;
import org.eclipse.xtext.parser.antlr.XtextTokenStream;
import org.eclipse.xtext.parser.antlr.XtextTokenStream.HiddenTokens;
import org.eclipse.xtext.parser.antlr.AntlrDatatypeRuleToken;
import org.omg.kerml.expressions.xtext.services.KerMLExpressionsGrammarAccess;

}

@parser::members {

 	private KerMLExpressionsGrammarAccess grammarAccess;

    public InternalKerMLExpressionsParser(TokenStream input, KerMLExpressionsGrammarAccess grammarAccess) {
        this(input);
        this.grammarAccess = grammarAccess;
        registerRules(grammarAccess.getGrammar());
    }

    @Override
    protected String getFirstRuleName() {
    	return "OwnedExpressionMember";
   	}

   	@Override
   	protected KerMLExpressionsGrammarAccess getGrammarAccess() {
   		return grammarAccess;
   	}

}

@rulecatch {
    catch (RecognitionException re) {
        recover(input,re);
        appendSkippedTokens();
    }
}

// Entry rule entryRuleOwnedExpressionMember
entryRuleOwnedExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedExpressionMemberRule()); }
	iv_ruleOwnedExpressionMember=ruleOwnedExpressionMember
	{ $current=$iv_ruleOwnedExpressionMember.current; }
	EOF;

// Rule OwnedExpressionMember
ruleOwnedExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedExpressionMemberAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOwnedExpression
entryRuleOwnedExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedExpressionRule()); }
	iv_ruleOwnedExpression=ruleOwnedExpression
	{ $current=$iv_ruleOwnedExpression.current; }
	EOF;

// Rule OwnedExpression
ruleOwnedExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		newCompositeNode(grammarAccess.getOwnedExpressionAccess().getConditionalExpressionParserRuleCall());
	}
	this_ConditionalExpression_0=ruleConditionalExpression
	{
		$current = $this_ConditionalExpression_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleOwnedExpressionReference
entryRuleOwnedExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedExpressionReferenceRule()); }
	iv_ruleOwnedExpressionReference=ruleOwnedExpressionReference
	{ $current=$iv_ruleOwnedExpressionReference.current; }
	EOF;

// Rule OwnedExpressionReference
ruleOwnedExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedExpressionReferenceAccess().getOwnedRelationshipOwnedExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleOwnedExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleConditionalExpression
entryRuleConditionalExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConditionalExpressionRule()); }
	iv_ruleConditionalExpression=ruleConditionalExpression
	{ $current=$iv_ruleConditionalExpression.current; }
	EOF;

// Rule ConditionalExpression
ruleConditionalExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getConditionalExpressionAccess().getNullCoalescingExpressionParserRuleCall_0());
		}
		this_NullCoalescingExpression_0=ruleNullCoalescingExpression
		{
			$current = $this_NullCoalescingExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getConditionalExpressionAccess().getOperatorExpressionAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperatorConditionalOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleConditionalOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ConditionalOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperandNullCoalescingExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleNullCoalescingExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.NullCoalescingExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_4='?'
			{
				newLeafNode(otherlv_4, grammarAccess.getConditionalExpressionAccess().getQuestionMarkKeyword_1_3());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperandOwnedExpressionReferenceParserRuleCall_1_4_0());
					}
					lv_operand_5_0=ruleOwnedExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_5_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_6='else'
			{
				newLeafNode(otherlv_6, grammarAccess.getConditionalExpressionAccess().getElseKeyword_1_5());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperandOwnedExpressionReferenceParserRuleCall_1_6_0());
					}
					lv_operand_7_0=ruleOwnedExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_7_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRuleConditionalOperator
entryRuleConditionalOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getConditionalOperatorRule()); }
	iv_ruleConditionalOperator=ruleConditionalOperator
	{ $current=$iv_ruleConditionalOperator.current.getText(); }
	EOF;

// Rule ConditionalOperator
ruleConditionalOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='if'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getConditionalOperatorAccess().getIfKeyword());
	}
;

// Entry rule entryRuleNullCoalescingExpression
entryRuleNullCoalescingExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNullCoalescingExpressionRule()); }
	iv_ruleNullCoalescingExpression=ruleNullCoalescingExpression
	{ $current=$iv_ruleNullCoalescingExpression.current; }
	EOF;

// Rule NullCoalescingExpression
ruleNullCoalescingExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getNullCoalescingExpressionAccess().getImpliesExpressionParserRuleCall_0());
		}
		this_ImpliesExpression_0=ruleImpliesExpression
		{
			$current = $this_ImpliesExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getNullCoalescingExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getNullCoalescingExpressionAccess().getOperatorNullCoalescingOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleNullCoalescingOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNullCoalescingExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.NullCoalescingOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getNullCoalescingExpressionAccess().getOperandImpliesExpressionReferenceParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleImpliesExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNullCoalescingExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleNullCoalescingOperator
entryRuleNullCoalescingOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getNullCoalescingOperatorRule()); }
	iv_ruleNullCoalescingOperator=ruleNullCoalescingOperator
	{ $current=$iv_ruleNullCoalescingOperator.current.getText(); }
	EOF;

// Rule NullCoalescingOperator
ruleNullCoalescingOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='??'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getNullCoalescingOperatorAccess().getQuestionMarkQuestionMarkKeyword());
	}
;

// Entry rule entryRuleImpliesExpressionReference
entryRuleImpliesExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImpliesExpressionReferenceRule()); }
	iv_ruleImpliesExpressionReference=ruleImpliesExpressionReference
	{ $current=$iv_ruleImpliesExpressionReference.current; }
	EOF;

// Rule ImpliesExpressionReference
ruleImpliesExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getImpliesExpressionReferenceAccess().getOwnedRelationshipImpliesExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleImpliesExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getImpliesExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleImpliesExpressionMember
entryRuleImpliesExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImpliesExpressionMemberRule()); }
	iv_ruleImpliesExpressionMember=ruleImpliesExpressionMember
	{ $current=$iv_ruleImpliesExpressionMember.current; }
	EOF;

// Rule ImpliesExpressionMember
ruleImpliesExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getImpliesExpressionMemberAccess().getOwnedRelatedElementImpliesExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleImpliesExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getImpliesExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleImpliesExpression
entryRuleImpliesExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImpliesExpressionRule()); }
	iv_ruleImpliesExpression=ruleImpliesExpression
	{ $current=$iv_ruleImpliesExpression.current; }
	EOF;

// Rule ImpliesExpression
ruleImpliesExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getImpliesExpressionAccess().getOrExpressionParserRuleCall_0());
		}
		this_OrExpression_0=ruleOrExpression
		{
			$current = $this_OrExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getImpliesExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getImpliesExpressionAccess().getOperatorImpliesOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleImpliesOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getImpliesExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getImpliesExpressionAccess().getOperandOrExpressionReferenceParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleOrExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getImpliesExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OrExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleImpliesOperator
entryRuleImpliesOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getImpliesOperatorRule()); }
	iv_ruleImpliesOperator=ruleImpliesOperator
	{ $current=$iv_ruleImpliesOperator.current.getText(); }
	EOF;

// Rule ImpliesOperator
ruleImpliesOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='implies'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getImpliesOperatorAccess().getImpliesKeyword());
	}
;

// Entry rule entryRuleOrExpressionReference
entryRuleOrExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOrExpressionReferenceRule()); }
	iv_ruleOrExpressionReference=ruleOrExpressionReference
	{ $current=$iv_ruleOrExpressionReference.current; }
	EOF;

// Rule OrExpressionReference
ruleOrExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOrExpressionReferenceAccess().getOwnedRelationshipOrExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleOrExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOrExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OrExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOrExpressionMember
entryRuleOrExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOrExpressionMemberRule()); }
	iv_ruleOrExpressionMember=ruleOrExpressionMember
	{ $current=$iv_ruleOrExpressionMember.current; }
	EOF;

// Rule OrExpressionMember
ruleOrExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOrExpressionMemberAccess().getOwnedRelatedElementOrExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOrExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOrExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OrExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOrExpression
entryRuleOrExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOrExpressionRule()); }
	iv_ruleOrExpression=ruleOrExpression
	{ $current=$iv_ruleOrExpression.current; }
	EOF;

// Rule OrExpression
ruleOrExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getOrExpressionAccess().getXorExpressionParserRuleCall_0());
		}
		this_XorExpression_0=ruleXorExpression
		{
			$current = $this_XorExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getOrExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperatorOrOperatorParserRuleCall_1_1_0_0_0());
							}
							lv_operator_2_0=ruleOrOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_2_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.OrOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperandXorExpressionParserRuleCall_1_1_0_1_0());
							}
							lv_operand_3_0=ruleXorExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_3_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperatorConditionalOrOperatorParserRuleCall_1_1_1_0_0());
							}
							lv_operator_4_0=ruleConditionalOrOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_4_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.ConditionalOrOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperandXorExpressionReferenceParserRuleCall_1_1_1_1_0());
							}
							lv_operand_5_0=ruleXorExpressionReference
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_5_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpressionReference");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)
		)*
	)
;

// Entry rule entryRuleOrOperator
entryRuleOrOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getOrOperatorRule()); }
	iv_ruleOrOperator=ruleOrOperator
	{ $current=$iv_ruleOrOperator.current.getText(); }
	EOF;

// Rule OrOperator
ruleOrOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='|'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getOrOperatorAccess().getVerticalLineKeyword());
	}
;

// Entry rule entryRuleConditionalOrOperator
entryRuleConditionalOrOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getConditionalOrOperatorRule()); }
	iv_ruleConditionalOrOperator=ruleConditionalOrOperator
	{ $current=$iv_ruleConditionalOrOperator.current.getText(); }
	EOF;

// Rule ConditionalOrOperator
ruleConditionalOrOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='or'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getConditionalOrOperatorAccess().getOrKeyword());
	}
;

// Entry rule entryRuleXorExpressionReference
entryRuleXorExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getXorExpressionReferenceRule()); }
	iv_ruleXorExpressionReference=ruleXorExpressionReference
	{ $current=$iv_ruleXorExpressionReference.current; }
	EOF;

// Rule XorExpressionReference
ruleXorExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getXorExpressionReferenceAccess().getOwnedRelationshipXorExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleXorExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getXorExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleXorExpressionMember
entryRuleXorExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getXorExpressionMemberRule()); }
	iv_ruleXorExpressionMember=ruleXorExpressionMember
	{ $current=$iv_ruleXorExpressionMember.current; }
	EOF;

// Rule XorExpressionMember
ruleXorExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getXorExpressionMemberAccess().getOwnedRelatedElementXorExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleXorExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getXorExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleXorExpression
entryRuleXorExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getXorExpressionRule()); }
	iv_ruleXorExpression=ruleXorExpression
	{ $current=$iv_ruleXorExpression.current; }
	EOF;

// Rule XorExpression
ruleXorExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getXorExpressionAccess().getAndExpressionParserRuleCall_0());
		}
		this_AndExpression_0=ruleAndExpression
		{
			$current = $this_AndExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getXorExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getXorExpressionAccess().getOperatorXorOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleXorOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getXorExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.XorOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getXorExpressionAccess().getOperandAndExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleAndExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getXorExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.AndExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleXorOperator
entryRuleXorOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getXorOperatorRule()); }
	iv_ruleXorOperator=ruleXorOperator
	{ $current=$iv_ruleXorOperator.current.getText(); }
	EOF;

// Rule XorOperator
ruleXorOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='xor'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getXorOperatorAccess().getXorKeyword());
	}
;

// Entry rule entryRuleAndExpression
entryRuleAndExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAndExpressionRule()); }
	iv_ruleAndExpression=ruleAndExpression
	{ $current=$iv_ruleAndExpression.current; }
	EOF;

// Rule AndExpression
ruleAndExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getAndExpressionAccess().getEqualityExpressionParserRuleCall_0());
		}
		this_EqualityExpression_0=ruleEqualityExpression
		{
			$current = $this_EqualityExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getAndExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperatorAndOperatorParserRuleCall_1_1_0_0_0());
							}
							lv_operator_2_0=ruleAndOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_2_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.AndOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperandEqualityExpressionParserRuleCall_1_1_0_1_0());
							}
							lv_operand_3_0=ruleEqualityExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_3_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperatorConditionalAndOperatorParserRuleCall_1_1_1_0_0());
							}
							lv_operator_4_0=ruleConditionalAndOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_4_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.ConditionalAndOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperandEqualityExpressionReferenceParserRuleCall_1_1_1_1_0());
							}
							lv_operand_5_0=ruleEqualityExpressionReference
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_5_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpressionReference");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)
		)*
	)
;

// Entry rule entryRuleAndOperator
entryRuleAndOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getAndOperatorRule()); }
	iv_ruleAndOperator=ruleAndOperator
	{ $current=$iv_ruleAndOperator.current.getText(); }
	EOF;

// Rule AndOperator
ruleAndOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='&'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getAndOperatorAccess().getAmpersandKeyword());
	}
;

// Entry rule entryRuleConditionalAndOperator
entryRuleConditionalAndOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getConditionalAndOperatorRule()); }
	iv_ruleConditionalAndOperator=ruleConditionalAndOperator
	{ $current=$iv_ruleConditionalAndOperator.current.getText(); }
	EOF;

// Rule ConditionalAndOperator
ruleConditionalAndOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='and'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getConditionalAndOperatorAccess().getAndKeyword());
	}
;

// Entry rule entryRuleEqualityExpressionReference
entryRuleEqualityExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEqualityExpressionReferenceRule()); }
	iv_ruleEqualityExpressionReference=ruleEqualityExpressionReference
	{ $current=$iv_ruleEqualityExpressionReference.current; }
	EOF;

// Rule EqualityExpressionReference
ruleEqualityExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getEqualityExpressionReferenceAccess().getOwnedRelationshipEqualityExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleEqualityExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getEqualityExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleEqualityExpressionMember
entryRuleEqualityExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEqualityExpressionMemberRule()); }
	iv_ruleEqualityExpressionMember=ruleEqualityExpressionMember
	{ $current=$iv_ruleEqualityExpressionMember.current; }
	EOF;

// Rule EqualityExpressionMember
ruleEqualityExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getEqualityExpressionMemberAccess().getOwnedRelatedElementEqualityExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleEqualityExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getEqualityExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleEqualityExpression
entryRuleEqualityExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEqualityExpressionRule()); }
	iv_ruleEqualityExpression=ruleEqualityExpression
	{ $current=$iv_ruleEqualityExpression.current; }
	EOF;

// Rule EqualityExpression
ruleEqualityExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getEqualityExpressionAccess().getClassificationExpressionParserRuleCall_0());
		}
		this_ClassificationExpression_0=ruleClassificationExpression
		{
			$current = $this_ClassificationExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getEqualityExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getEqualityExpressionAccess().getOperatorEqualityOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleEqualityOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getEqualityExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getEqualityExpressionAccess().getOperandClassificationExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleClassificationExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getEqualityExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ClassificationExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleEqualityOperator
entryRuleEqualityOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getEqualityOperatorRule()); }
	iv_ruleEqualityOperator=ruleEqualityOperator
	{ $current=$iv_ruleEqualityOperator.current.getText(); }
	EOF;

// Rule EqualityOperator
ruleEqualityOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='=='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getEqualsSignEqualsSignKeyword_0());
		}
		    |
		kw='!='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getExclamationMarkEqualsSignKeyword_1());
		}
		    |
		kw='==='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getEqualsSignEqualsSignEqualsSignKeyword_2());
		}
		    |
		kw='!=='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getExclamationMarkEqualsSignEqualsSignKeyword_3());
		}
	)
;

// Entry rule entryRuleClassificationExpression
entryRuleClassificationExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getClassificationExpressionRule()); }
	iv_ruleClassificationExpression=ruleClassificationExpression
	{ $current=$iv_ruleClassificationExpression.current; }
	EOF;

// Rule ClassificationExpression
ruleClassificationExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getClassificationExpressionAccess().getRelationalExpressionParserRuleCall_0_0());
			}
			this_RelationalExpression_0=ruleRelationalExpression
			{
				$current = $this_RelationalExpression_0.current;
				afterParserOrEnumRuleCall();
			}
			(
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getClassificationExpressionAccess().getOperatorExpressionOperandAction_0_1_0_0(),
								$current);
						}
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorClassificationTestOperatorParserRuleCall_0_1_0_1_0());
							}
							lv_operator_2_0=ruleClassificationTestOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_2_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.ClassificationTestOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeReferenceMemberParserRuleCall_0_1_0_2_0());
							}
							lv_ownedRelationship_3_0=ruleTypeReferenceMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_3_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReferenceMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getClassificationExpressionAccess().getOperatorExpressionOperandAction_0_1_1_0(),
								$current);
						}
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorCastOperatorParserRuleCall_0_1_1_1_0());
							}
							lv_operator_5_0=ruleCastOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_5_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.CastOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_0_1_1_2_0());
							}
							lv_ownedRelationship_6_0=ruleTypeResultMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_6_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)?
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandSelfReferenceExpressionParserRuleCall_1_1_0());
					}
					lv_operand_8_0=ruleSelfReferenceExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_8_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.SelfReferenceExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorClassificationTestOperatorParserRuleCall_1_2_0());
					}
					lv_operator_9_0=ruleClassificationTestOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_9_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ClassificationTestOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeReferenceMemberParserRuleCall_1_3_0());
					}
					lv_ownedRelationship_10_0=ruleTypeReferenceMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_10_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReferenceMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_2_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandMetadataReferenceParserRuleCall_2_1_0());
					}
					lv_operand_12_0=ruleMetadataReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_12_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetadataReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorMetaClassificationTestOperatorParserRuleCall_2_2_0());
					}
					lv_operator_13_0=ruleMetaClassificationTestOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_13_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetaClassificationTestOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeReferenceMemberParserRuleCall_2_3_0());
					}
					lv_ownedRelationship_14_0=ruleTypeReferenceMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_14_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReferenceMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_3_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandSelfReferenceExpressionParserRuleCall_3_1_0());
					}
					lv_operand_16_0=ruleSelfReferenceExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_16_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.SelfReferenceExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorCastOperatorParserRuleCall_3_2_0());
					}
					lv_operator_17_0=ruleCastOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_17_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.CastOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_3_3_0());
					}
					lv_ownedRelationship_18_0=ruleTypeResultMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_18_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_4_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandMetadataReferenceParserRuleCall_4_1_0());
					}
					lv_operand_20_0=ruleMetadataReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_20_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetadataReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorMetaCastOperatorParserRuleCall_4_2_0());
					}
					lv_operator_21_0=ruleMetaCastOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_21_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetaCastOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_4_3_0());
					}
					lv_ownedRelationship_22_0=ruleTypeResultMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_22_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRuleClassificationTestOperator
entryRuleClassificationTestOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getClassificationTestOperatorRule()); }
	iv_ruleClassificationTestOperator=ruleClassificationTestOperator
	{ $current=$iv_ruleClassificationTestOperator.current.getText(); }
	EOF;

// Rule ClassificationTestOperator
ruleClassificationTestOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='hastype'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getClassificationTestOperatorAccess().getHastypeKeyword_0());
		}
		    |
		kw='istype'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getClassificationTestOperatorAccess().getIstypeKeyword_1());
		}
		    |
		kw='@'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getClassificationTestOperatorAccess().getCommercialAtKeyword_2());
		}
	)
;

// Entry rule entryRuleMetaClassificationTestOperator
entryRuleMetaClassificationTestOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getMetaClassificationTestOperatorRule()); }
	iv_ruleMetaClassificationTestOperator=ruleMetaClassificationTestOperator
	{ $current=$iv_ruleMetaClassificationTestOperator.current.getText(); }
	EOF;

// Rule MetaClassificationTestOperator
ruleMetaClassificationTestOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='@@'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getMetaClassificationTestOperatorAccess().getCommercialAtCommercialAtKeyword());
	}
;

// Entry rule entryRuleCastOperator
entryRuleCastOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getCastOperatorRule()); }
	iv_ruleCastOperator=ruleCastOperator
	{ $current=$iv_ruleCastOperator.current.getText(); }
	EOF;

// Rule CastOperator
ruleCastOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='as'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getCastOperatorAccess().getAsKeyword());
	}
;

// Entry rule entryRuleMetaCastOperator
entryRuleMetaCastOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getMetaCastOperatorRule()); }
	iv_ruleMetaCastOperator=ruleMetaCastOperator
	{ $current=$iv_ruleMetaCastOperator.current.getText(); }
	EOF;

// Rule MetaCastOperator
ruleMetaCastOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='meta'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getMetaCastOperatorAccess().getMetaKeyword());
	}
;

// Entry rule entryRuleMetadataReference
entryRuleMetadataReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataReferenceRule()); }
	iv_ruleMetadataReference=ruleMetadataReference
	{ $current=$iv_ruleMetadataReference.current; }
	EOF;

// Rule MetadataReference
ruleMetadataReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getMetadataReferenceAccess().getOwnedRelationshipElementReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleElementReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getMetadataReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ElementReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleTypeReferenceMember
entryRuleTypeReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeReferenceMemberRule()); }
	iv_ruleTypeReferenceMember=ruleTypeReferenceMember
	{ $current=$iv_ruleTypeReferenceMember.current; }
	EOF;

// Rule TypeReferenceMember
ruleTypeReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getTypeReferenceMemberAccess().getOwnedRelatedElementTypeReferenceParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleTypeReference
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getTypeReferenceMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReference");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleTypeResultMember
entryRuleTypeResultMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeResultMemberRule()); }
	iv_ruleTypeResultMember=ruleTypeResultMember
	{ $current=$iv_ruleTypeResultMember.current; }
	EOF;

// Rule TypeResultMember
ruleTypeResultMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getTypeResultMemberAccess().getOwnedRelatedElementTypeReferenceParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleTypeReference
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getTypeResultMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReference");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleTypeReference
entryRuleTypeReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeReferenceRule()); }
	iv_ruleTypeReference=ruleTypeReference
	{ $current=$iv_ruleTypeReference.current; }
	EOF;

// Rule TypeReference
ruleTypeReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getTypeReferenceAccess().getOwnedRelationshipReferenceTypingParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleReferenceTyping
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getTypeReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ReferenceTyping");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleReferenceTyping
entryRuleReferenceTyping returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getReferenceTypingRule()); }
	iv_ruleReferenceTyping=ruleReferenceTyping
	{ $current=$iv_ruleReferenceTyping.current; }
	EOF;

// Rule ReferenceTyping
ruleReferenceTyping returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getReferenceTypingRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getReferenceTypingAccess().getTypeTypeCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleSelfReferenceExpression
entryRuleSelfReferenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSelfReferenceExpressionRule()); }
	iv_ruleSelfReferenceExpression=ruleSelfReferenceExpression
	{ $current=$iv_ruleSelfReferenceExpression.current; }
	EOF;

// Rule SelfReferenceExpression
ruleSelfReferenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getSelfReferenceExpressionAccess().getOwnedRelationshipSelfReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleSelfReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getSelfReferenceExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.SelfReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleSelfReferenceMember
entryRuleSelfReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSelfReferenceMemberRule()); }
	iv_ruleSelfReferenceMember=ruleSelfReferenceMember
	{ $current=$iv_ruleSelfReferenceMember.current; }
	EOF;

// Rule SelfReferenceMember
ruleSelfReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getSelfReferenceMemberAccess().getOwnedRelatedElementEmptyFeatureParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleEmptyFeature
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getSelfReferenceMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.EmptyFeature");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleEmptyFeature
entryRuleEmptyFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEmptyFeatureRule()); }
	iv_ruleEmptyFeature=ruleEmptyFeature
	{ $current=$iv_ruleEmptyFeature.current; }
	EOF;

// Rule EmptyFeature
ruleEmptyFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			$current = forceCreateModelElement(
				grammarAccess.getEmptyFeatureAccess().getFeatureAction(),
				$current);
		}
	)
;

// Entry rule entryRuleRelationalExpression
entryRuleRelationalExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getRelationalExpressionRule()); }
	iv_ruleRelationalExpression=ruleRelationalExpression
	{ $current=$iv_ruleRelationalExpression.current; }
	EOF;

// Rule RelationalExpression
ruleRelationalExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getRelationalExpressionAccess().getRangeExpressionParserRuleCall_0());
		}
		this_RangeExpression_0=ruleRangeExpression
		{
			$current = $this_RangeExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getRelationalExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getRelationalExpressionAccess().getOperatorRelationalOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleRelationalOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRelationalExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.RelationalOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getRelationalExpressionAccess().getOperandRangeExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleRangeExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRelationalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.RangeExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleRelationalOperator
entryRuleRelationalOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getRelationalOperatorRule()); }
	iv_ruleRelationalOperator=ruleRelationalOperator
	{ $current=$iv_ruleRelationalOperator.current.getText(); }
	EOF;

// Rule RelationalOperator
ruleRelationalOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='<'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getLessThanSignKeyword_0());
		}
		    |
		kw='>'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getGreaterThanSignKeyword_1());
		}
		    |
		kw='<='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getLessThanSignEqualsSignKeyword_2());
		}
		    |
		kw='>='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getGreaterThanSignEqualsSignKeyword_3());
		}
	)
;

// Entry rule entryRuleRangeExpression
entryRuleRangeExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getRangeExpressionRule()); }
	iv_ruleRangeExpression=ruleRangeExpression
	{ $current=$iv_ruleRangeExpression.current; }
	EOF;

// Rule RangeExpression
ruleRangeExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getRangeExpressionAccess().getAdditiveExpressionParserRuleCall_0());
		}
		this_AdditiveExpression_0=ruleAdditiveExpression
		{
			$current = $this_AdditiveExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getRangeExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					lv_operator_2_0='..'
					{
						newLeafNode(lv_operator_2_0, grammarAccess.getRangeExpressionAccess().getOperatorFullStopFullStopKeyword_1_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getRangeExpressionRule());
						}
						setWithLastConsumed($current, "operator", lv_operator_2_0, "..");
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getRangeExpressionAccess().getOperandAdditiveExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleAdditiveExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRangeExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.AdditiveExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
	)
;

// Entry rule entryRuleAdditiveExpression
entryRuleAdditiveExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAdditiveExpressionRule()); }
	iv_ruleAdditiveExpression=ruleAdditiveExpression
	{ $current=$iv_ruleAdditiveExpression.current; }
	EOF;

// Rule AdditiveExpression
ruleAdditiveExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getAdditiveExpressionAccess().getMultiplicativeExpressionParserRuleCall_0());
		}
		this_MultiplicativeExpression_0=ruleMultiplicativeExpression
		{
			$current = $this_MultiplicativeExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getAdditiveExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getAdditiveExpressionAccess().getOperatorAdditiveOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleAdditiveOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getAdditiveExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.AdditiveOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getAdditiveExpressionAccess().getOperandMultiplicativeExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleMultiplicativeExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getAdditiveExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MultiplicativeExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleAdditiveOperator
entryRuleAdditiveOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getAdditiveOperatorRule()); }
	iv_ruleAdditiveOperator=ruleAdditiveOperator
	{ $current=$iv_ruleAdditiveOperator.current.getText(); }
	EOF;

// Rule AdditiveOperator
ruleAdditiveOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='+'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getAdditiveOperatorAccess().getPlusSignKeyword_0());
		}
		    |
		kw='-'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getAdditiveOperatorAccess().getHyphenMinusKeyword_1());
		}
	)
;

// Entry rule entryRuleMultiplicativeExpression
entryRuleMultiplicativeExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicativeExpressionRule()); }
	iv_ruleMultiplicativeExpression=ruleMultiplicativeExpression
	{ $current=$iv_ruleMultiplicativeExpression.current; }
	EOF;

// Rule MultiplicativeExpression
ruleMultiplicativeExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getMultiplicativeExpressionAccess().getExponentiationExpressionParserRuleCall_0());
		}
		this_ExponentiationExpression_0=ruleExponentiationExpression
		{
			$current = $this_ExponentiationExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getMultiplicativeExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getMultiplicativeExpressionAccess().getOperatorMultiplicativeOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleMultiplicativeOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMultiplicativeExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MultiplicativeOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getMultiplicativeExpressionAccess().getOperandExponentiationExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleExponentiationExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMultiplicativeExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExponentiationExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleMultiplicativeOperator
entryRuleMultiplicativeOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicativeOperatorRule()); }
	iv_ruleMultiplicativeOperator=ruleMultiplicativeOperator
	{ $current=$iv_ruleMultiplicativeOperator.current.getText(); }
	EOF;

// Rule MultiplicativeOperator
ruleMultiplicativeOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='*'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getMultiplicativeOperatorAccess().getAsteriskKeyword_0());
		}
		    |
		kw='/'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getMultiplicativeOperatorAccess().getSolidusKeyword_1());
		}
		    |
		kw='%'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getMultiplicativeOperatorAccess().getPercentSignKeyword_2());
		}
	)
;

// Entry rule entryRuleExponentiationExpression
entryRuleExponentiationExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExponentiationExpressionRule()); }
	iv_ruleExponentiationExpression=ruleExponentiationExpression
	{ $current=$iv_ruleExponentiationExpression.current; }
	EOF;

// Rule ExponentiationExpression
ruleExponentiationExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getExponentiationExpressionAccess().getUnaryExpressionParserRuleCall_0());
		}
		this_UnaryExpression_0=ruleUnaryExpression
		{
			$current = $this_UnaryExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getExponentiationExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getExponentiationExpressionAccess().getOperatorExponentiationOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleExponentiationOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExponentiationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExponentiationOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getExponentiationExpressionAccess().getOperandExponentiationExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleExponentiationExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExponentiationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExponentiationExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
	)
;

// Entry rule entryRuleExponentiationOperator
entryRuleExponentiationOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getExponentiationOperatorRule()); }
	iv_ruleExponentiationOperator=ruleExponentiationOperator
	{ $current=$iv_ruleExponentiationOperator.current.getText(); }
	EOF;

// Rule ExponentiationOperator
ruleExponentiationOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='**'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getExponentiationOperatorAccess().getAsteriskAsteriskKeyword_0());
		}
		    |
		kw='^'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getExponentiationOperatorAccess().getCircumflexAccentKeyword_1());
		}
	)
;

// Entry rule entryRuleUnaryExpression
entryRuleUnaryExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getUnaryExpressionRule()); }
	iv_ruleUnaryExpression=ruleUnaryExpression
	{ $current=$iv_ruleUnaryExpression.current; }
	EOF;

// Rule UnaryExpression
ruleUnaryExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getUnaryExpressionAccess().getOperatorExpressionAction_0_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getUnaryExpressionAccess().getOperatorUnaryOperatorParserRuleCall_0_1_0());
					}
					lv_operator_1_0=ruleUnaryOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getUnaryExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_1_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.UnaryOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getUnaryExpressionAccess().getOperandExtentExpressionParserRuleCall_0_2_0());
					}
					lv_operand_2_0=ruleExtentExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getUnaryExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExtentExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		{
			newCompositeNode(grammarAccess.getUnaryExpressionAccess().getExtentExpressionParserRuleCall_1());
		}
		this_ExtentExpression_3=ruleExtentExpression
		{
			$current = $this_ExtentExpression_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleUnaryOperator
entryRuleUnaryOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getUnaryOperatorRule()); }
	iv_ruleUnaryOperator=ruleUnaryOperator
	{ $current=$iv_ruleUnaryOperator.current.getText(); }
	EOF;

// Rule UnaryOperator
ruleUnaryOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='+'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getPlusSignKeyword_0());
		}
		    |
		kw='-'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getHyphenMinusKeyword_1());
		}
		    |
		kw='~'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getTildeKeyword_2());
		}
		    |
		kw='not'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getNotKeyword_3());
		}
	)
;

// Entry rule entryRuleExtentExpression
entryRuleExtentExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExtentExpressionRule()); }
	iv_ruleExtentExpression=ruleExtentExpression
	{ $current=$iv_ruleExtentExpression.current; }
	EOF;

// Rule ExtentExpression
ruleExtentExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getExtentExpressionAccess().getOperatorExpressionAction_0_0(),
						$current);
				}
			)
			(
				(
					lv_operator_1_0='all'
					{
						newLeafNode(lv_operator_1_0, grammarAccess.getExtentExpressionAccess().getOperatorAllKeyword_0_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getExtentExpressionRule());
						}
						setWithLastConsumed($current, "operator", lv_operator_1_0, "all");
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getExtentExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_0_2_0());
					}
					lv_ownedRelationship_2_0=ruleTypeResultMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExtentExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		{
			newCompositeNode(grammarAccess.getExtentExpressionAccess().getPrimaryExpressionParserRuleCall_1());
		}
		this_PrimaryExpression_3=rulePrimaryExpression
		{
			$current = $this_PrimaryExpression_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRulePrimaryExpression
entryRulePrimaryExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPrimaryExpressionRule()); }
	iv_rulePrimaryExpression=rulePrimaryExpression
	{ $current=$iv_rulePrimaryExpression.current; }
	EOF;

// Rule PrimaryExpression
rulePrimaryExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getBaseExpressionParserRuleCall_0());
		}
		this_BaseExpression_0=ruleBaseExpression
		{
			$current = $this_BaseExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getPrimaryExpressionAccess().getFeatureChainExpressionOperandAction_1_0(),
						$current);
				}
			)
			otherlv_2='.'
			{
				newLeafNode(otherlv_2, grammarAccess.getPrimaryExpressionAccess().getFullStopKeyword_1_1());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOwnedRelationshipFeatureChainMemberParserRuleCall_1_2_0());
					}
					lv_ownedRelationship_3_0=ruleFeatureChainMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureChainMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
		(
			(
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getIndexExpressionOperandAction_2_0_0_0(),
								$current);
						}
					)
					otherlv_5='#'
					{
						newLeafNode(otherlv_5, grammarAccess.getPrimaryExpressionAccess().getNumberSignKeyword_2_0_0_1());
					}
					otherlv_6='('
					{
						newLeafNode(otherlv_6, grammarAccess.getPrimaryExpressionAccess().getLeftParenthesisKeyword_2_0_0_2());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandSequenceExpressionParserRuleCall_2_0_0_3_0());
							}
							lv_operand_7_0=ruleSequenceExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_7_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.SequenceExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
					otherlv_8=')'
					{
						newLeafNode(otherlv_8, grammarAccess.getPrimaryExpressionAccess().getRightParenthesisKeyword_2_0_0_4());
					}
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getOperatorExpressionOperandAction_2_0_1_0(),
								$current);
						}
					)
					(
						(
							lv_operator_10_0='['
							{
								newLeafNode(lv_operator_10_0, grammarAccess.getPrimaryExpressionAccess().getOperatorLeftSquareBracketKeyword_2_0_1_1_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getPrimaryExpressionRule());
								}
								setWithLastConsumed($current, "operator", lv_operator_10_0, "[");
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandSequenceExpressionParserRuleCall_2_0_1_2_0());
							}
							lv_operand_11_0=ruleSequenceExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_11_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.SequenceExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
					otherlv_12=']'
					{
						newLeafNode(otherlv_12, grammarAccess.getPrimaryExpressionAccess().getRightSquareBracketKeyword_2_0_1_3());
					}
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getInvocationExpressionOperandAction_2_0_2_0(),
								$current);
						}
					)
					otherlv_14='->'
					{
						newLeafNode(otherlv_14, grammarAccess.getPrimaryExpressionAccess().getHyphenMinusGreaterThanSignKeyword_2_0_2_1());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOwnedRelationshipInstantiatedTypeMemberParserRuleCall_2_0_2_2_0());
							}
							lv_ownedRelationship_15_0=ruleInstantiatedTypeMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_15_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.InstantiatedTypeMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							(
								{
									newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandBodyExpressionParserRuleCall_2_0_2_3_0_0());
								}
								lv_operand_16_0=ruleBodyExpression
								{
									if ($current==null) {
										$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
									}
									add(
										$current,
										"operand",
										lv_operand_16_0,
										"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyExpression");
									afterParserOrEnumRuleCall();
								}
							)
						)
						    |
						(
							(
								{
									newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandFunctionReferenceExpressionParserRuleCall_2_0_2_3_1_0());
								}
								lv_operand_17_0=ruleFunctionReferenceExpression
								{
									if ($current==null) {
										$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
									}
									add(
										$current,
										"operand",
										lv_operand_17_0,
										"org.omg.kerml.expressions.xtext.KerMLExpressions.FunctionReferenceExpression");
									afterParserOrEnumRuleCall();
								}
							)
						)
						    |
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getPrimaryExpressionRule());
							}
							newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getArgumentListParserRuleCall_2_0_2_3_2());
						}
						this_ArgumentList_18=ruleArgumentList[$current]
						{
							$current = $this_ArgumentList_18.current;
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getCollectExpressionOperandAction_2_0_3_0(),
								$current);
						}
					)
					otherlv_20='.'
					{
						newLeafNode(otherlv_20, grammarAccess.getPrimaryExpressionAccess().getFullStopKeyword_2_0_3_1());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandBodyExpressionParserRuleCall_2_0_3_2_0());
							}
							lv_operand_21_0=ruleBodyExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_21_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getSelectExpressionOperandAction_2_0_4_0(),
								$current);
						}
					)
					otherlv_23='.?'
					{
						newLeafNode(otherlv_23, grammarAccess.getPrimaryExpressionAccess().getFullStopQuestionMarkKeyword_2_0_4_1());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandBodyExpressionParserRuleCall_2_0_4_2_0());
							}
							lv_operand_24_0=ruleBodyExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_24_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)
			(
				(
					{
						$current = forceCreateModelElementAndAdd(
							grammarAccess.getPrimaryExpressionAccess().getFeatureChainExpressionOperandAction_2_1_0(),
							$current);
					}
				)
				otherlv_26='.'
				{
					newLeafNode(otherlv_26, grammarAccess.getPrimaryExpressionAccess().getFullStopKeyword_2_1_1());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOwnedRelationshipFeatureChainMemberParserRuleCall_2_1_2_0());
						}
						lv_ownedRelationship_27_0=ruleFeatureChainMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_27_0,
								"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureChainMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)*
	)
;

// Entry rule entryRuleFunctionReferenceExpression
entryRuleFunctionReferenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionReferenceExpressionRule()); }
	iv_ruleFunctionReferenceExpression=ruleFunctionReferenceExpression
	{ $current=$iv_ruleFunctionReferenceExpression.current; }
	EOF;

// Rule FunctionReferenceExpression
ruleFunctionReferenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFunctionReferenceExpressionAccess().getOwnedRelationshipFunctionReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleFunctionReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFunctionReferenceExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.FunctionReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFunctionReferenceMember
entryRuleFunctionReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionReferenceMemberRule()); }
	iv_ruleFunctionReferenceMember=ruleFunctionReferenceMember
	{ $current=$iv_ruleFunctionReferenceMember.current; }
	EOF;

// Rule FunctionReferenceMember
ruleFunctionReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFunctionReferenceMemberAccess().getOwnedRelatedElementFunctionReferenceParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleFunctionReference
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFunctionReferenceMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.FunctionReference");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFunctionReference
entryRuleFunctionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionReferenceRule()); }
	iv_ruleFunctionReference=ruleFunctionReference
	{ $current=$iv_ruleFunctionReference.current; }
	EOF;

// Rule FunctionReference
ruleFunctionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFunctionReferenceAccess().getOwnedRelationshipReferenceTypingParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleReferenceTyping
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFunctionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ReferenceTyping");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFeatureChainMember
entryRuleFeatureChainMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureChainMemberRule()); }
	iv_ruleFeatureChainMember=ruleFeatureChainMember
	{ $current=$iv_ruleFeatureChainMember.current; }
	EOF;

// Rule FeatureChainMember
ruleFeatureChainMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureChainMemberRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getFeatureChainMemberAccess().getMemberElementFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getFeatureChainMemberAccess().getOwningMembershipAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureChainMemberAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_1_0());
					}
					lv_ownedRelatedElement_2_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureChainMemberRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRuleOwnedFeatureChain
entryRuleOwnedFeatureChain returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureChainRule()); }
	iv_ruleOwnedFeatureChain=ruleOwnedFeatureChain
	{ $current=$iv_ruleOwnedFeatureChain.current; }
	EOF;

// Rule OwnedFeatureChain
ruleOwnedFeatureChain returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getOwnedFeatureChainRule());
		}
		newCompositeNode(grammarAccess.getOwnedFeatureChainAccess().getFeatureChainParserRuleCall());
	}
	this_FeatureChain_0=ruleFeatureChain[$current]
	{
		$current = $this_FeatureChain_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleBaseExpression
entryRuleBaseExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBaseExpressionRule()); }
	iv_ruleBaseExpression=ruleBaseExpression
	{ $current=$iv_ruleBaseExpression.current; }
	EOF;

// Rule BaseExpression
ruleBaseExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getNullExpressionParserRuleCall_0());
		}
		this_NullExpression_0=ruleNullExpression
		{
			$current = $this_NullExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getLiteralExpressionParserRuleCall_1());
		}
		this_LiteralExpression_1=ruleLiteralExpression
		{
			$current = $this_LiteralExpression_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getFeatureReferenceExpressionParserRuleCall_2());
		}
		this_FeatureReferenceExpression_2=ruleFeatureReferenceExpression
		{
			$current = $this_FeatureReferenceExpression_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getMetadataAccessExpressionParserRuleCall_3());
		}
		this_MetadataAccessExpression_3=ruleMetadataAccessExpression
		{
			$current = $this_MetadataAccessExpression_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getInvocationExpressionParserRuleCall_4());
		}
		this_InvocationExpression_4=ruleInvocationExpression
		{
			$current = $this_InvocationExpression_4.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getConstructorExpressionParserRuleCall_5());
		}
		this_ConstructorExpression_5=ruleConstructorExpression
		{
			$current = $this_ConstructorExpression_5.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getBodyExpressionParserRuleCall_6());
		}
		this_BodyExpression_6=ruleBodyExpression
		{
			$current = $this_BodyExpression_6.current;
			afterParserOrEnumRuleCall();
		}
		    |
		(
			otherlv_7='('
			{
				newLeafNode(otherlv_7, grammarAccess.getBaseExpressionAccess().getLeftParenthesisKeyword_7_0());
			}
			{
				newCompositeNode(grammarAccess.getBaseExpressionAccess().getSequenceExpressionParserRuleCall_7_1());
			}
			this_SequenceExpression_8=ruleSequenceExpression
			{
				$current = $this_SequenceExpression_8.current;
				afterParserOrEnumRuleCall();
			}
			otherlv_9=')'
			{
				newLeafNode(otherlv_9, grammarAccess.getBaseExpressionAccess().getRightParenthesisKeyword_7_2());
			}
		)
	)
;

// Entry rule entryRuleBodyExpression
entryRuleBodyExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBodyExpressionRule()); }
	iv_ruleBodyExpression=ruleBodyExpression
	{ $current=$iv_ruleBodyExpression.current; }
	EOF;

// Rule BodyExpression
ruleBodyExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getBodyExpressionAccess().getOwnedRelationshipExpressionBodyMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleExpressionBodyMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getBodyExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ExpressionBodyMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleExpressionBodyMember
entryRuleExpressionBodyMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExpressionBodyMemberRule()); }
	iv_ruleExpressionBodyMember=ruleExpressionBodyMember
	{ $current=$iv_ruleExpressionBodyMember.current; }
	EOF;

// Rule ExpressionBodyMember
ruleExpressionBodyMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getExpressionBodyMemberAccess().getOwnedRelatedElementExpressionBodyParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleExpressionBody
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getExpressionBodyMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ExpressionBody");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleExpressionBody
entryRuleExpressionBody returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExpressionBodyRule()); }
	iv_ruleExpressionBody=ruleExpressionBody
	{ $current=$iv_ruleExpressionBody.current; }
	EOF;

// Rule ExpressionBody
ruleExpressionBody returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='{'
		{
			newLeafNode(otherlv_0, grammarAccess.getExpressionBodyAccess().getLeftCurlyBracketKeyword_0());
		}
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getExpressionBodyAccess().getOwnedRelationshipBodyParameterMemberParserRuleCall_1_0_0());
					}
					lv_ownedRelationship_1_0=ruleBodyParameterMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExpressionBodyRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_1_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyParameterMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_2=';'
			{
				newLeafNode(otherlv_2, grammarAccess.getExpressionBodyAccess().getSemicolonKeyword_1_1());
			}
		)*
		(
			(
				{
					newCompositeNode(grammarAccess.getExpressionBodyAccess().getOwnedRelationshipResultExpressionMemberParserRuleCall_2_0());
				}
				lv_ownedRelationship_3_0=ruleResultExpressionMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getExpressionBodyRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_3_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ResultExpressionMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_4='}'
		{
			newLeafNode(otherlv_4, grammarAccess.getExpressionBodyAccess().getRightCurlyBracketKeyword_3());
		}
	)
;

// Entry rule entryRuleResultExpressionMember
entryRuleResultExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getResultExpressionMemberRule()); }
	iv_ruleResultExpressionMember=ruleResultExpressionMember
	{ $current=$iv_ruleResultExpressionMember.current; }
	EOF;

// Rule ResultExpressionMember
ruleResultExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getResultExpressionMemberAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getResultExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleBodyParameterMember
entryRuleBodyParameterMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBodyParameterMemberRule()); }
	iv_ruleBodyParameterMember=ruleBodyParameterMember
	{ $current=$iv_ruleBodyParameterMember.current; }
	EOF;

// Rule BodyParameterMember
ruleBodyParameterMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='in'
		{
			newLeafNode(otherlv_0, grammarAccess.getBodyParameterMemberAccess().getInKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getBodyParameterMemberAccess().getOwnedRelatedElementBodyParameterParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleBodyParameter
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getBodyParameterMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyParameter");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleBodyParameter
entryRuleBodyParameter returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBodyParameterRule()); }
	iv_ruleBodyParameter=ruleBodyParameter
	{ $current=$iv_ruleBodyParameter.current; }
	EOF;

// Rule BodyParameter
ruleBodyParameter returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getBodyParameterAccess().getDeclaredNameNameParserRuleCall_0());
			}
			lv_declaredName_0_0=ruleName
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getBodyParameterRule());
				}
				set(
					$current,
					"declaredName",
					lv_declaredName_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleSequenceExpression
entryRuleSequenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSequenceExpressionRule()); }
	iv_ruleSequenceExpression=ruleSequenceExpression
	{ $current=$iv_ruleSequenceExpression.current; }
	EOF;

// Rule SequenceExpression
ruleSequenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getSequenceExpressionAccess().getOwnedExpressionParserRuleCall_0());
		}
		this_OwnedExpression_0=ruleOwnedExpression
		{
			$current = $this_OwnedExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getSequenceExpressionAccess().getCommaKeyword_1_0());
			}
			    |
			(
				(
					{
						$current = forceCreateModelElementAndAdd(
							grammarAccess.getSequenceExpressionAccess().getOperatorExpressionOperandAction_1_1_0(),
							$current);
					}
				)
				(
					(
						lv_operator_3_0=','
						{
							newLeafNode(lv_operator_3_0, grammarAccess.getSequenceExpressionAccess().getOperatorCommaKeyword_1_1_1_0());
						}
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getSequenceExpressionRule());
							}
							setWithLastConsumed($current, "operator", lv_operator_3_0, ",");
						}
					)
				)
				(
					(
						{
							newCompositeNode(grammarAccess.getSequenceExpressionAccess().getOperandSequenceExpressionParserRuleCall_1_1_2_0());
						}
						lv_operand_4_0=ruleSequenceExpression
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getSequenceExpressionRule());
							}
							add(
								$current,
								"operand",
								lv_operand_4_0,
								"org.omg.kerml.expressions.xtext.KerMLExpressions.SequenceExpression");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)
		)?
	)
;

// Entry rule entryRuleFeatureReferenceExpression
entryRuleFeatureReferenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureReferenceExpressionRule()); }
	iv_ruleFeatureReferenceExpression=ruleFeatureReferenceExpression
	{ $current=$iv_ruleFeatureReferenceExpression.current; }
	EOF;

// Rule FeatureReferenceExpression
ruleFeatureReferenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFeatureReferenceExpressionAccess().getOwnedRelationshipFeatureReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleFeatureReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFeatureReferenceExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFeatureReferenceMember
entryRuleFeatureReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureReferenceMemberRule()); }
	iv_ruleFeatureReferenceMember=ruleFeatureReferenceMember
	{ $current=$iv_ruleFeatureReferenceMember.current; }
	EOF;

// Rule FeatureReferenceMember
ruleFeatureReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureReferenceMemberRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getFeatureReferenceMemberAccess().getMemberElementFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleMetadataAccessExpression
entryRuleMetadataAccessExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataAccessExpressionRule()); }
	iv_ruleMetadataAccessExpression=ruleMetadataAccessExpression
	{ $current=$iv_ruleMetadataAccessExpression.current; }
	EOF;

// Rule MetadataAccessExpression
ruleMetadataAccessExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getMetadataAccessExpressionAccess().getOwnedRelationshipElementReferenceMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleElementReferenceMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMetadataAccessExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ElementReferenceMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_1='.'
		{
			newLeafNode(otherlv_1, grammarAccess.getMetadataAccessExpressionAccess().getFullStopKeyword_1());
		}
		otherlv_2='metadata'
		{
			newLeafNode(otherlv_2, grammarAccess.getMetadataAccessExpressionAccess().getMetadataKeyword_2());
		}
	)
;

// Entry rule entryRuleElementReferenceMember
entryRuleElementReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getElementReferenceMemberRule()); }
	iv_ruleElementReferenceMember=ruleElementReferenceMember
	{ $current=$iv_ruleElementReferenceMember.current; }
	EOF;

// Rule ElementReferenceMember
ruleElementReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getElementReferenceMemberRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getElementReferenceMemberAccess().getMemberElementElementCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleInvocationExpression
entryRuleInvocationExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getInvocationExpressionRule()); }
	iv_ruleInvocationExpression=ruleInvocationExpression
	{ $current=$iv_ruleInvocationExpression.current; }
	EOF;

// Rule InvocationExpression
ruleInvocationExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getInvocationExpressionAccess().getOwnedRelationshipInstantiatedTypeMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleInstantiatedTypeMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getInvocationExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.InstantiatedTypeMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInvocationExpressionRule());
			}
			newCompositeNode(grammarAccess.getInvocationExpressionAccess().getArgumentListParserRuleCall_1());
		}
		this_ArgumentList_1=ruleArgumentList[$current]
		{
			$current = $this_ArgumentList_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleConstructorExpression
entryRuleConstructorExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConstructorExpressionRule()); }
	iv_ruleConstructorExpression=ruleConstructorExpression
	{ $current=$iv_ruleConstructorExpression.current; }
	EOF;

// Rule ConstructorExpression
ruleConstructorExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='new'
		{
			newLeafNode(otherlv_0, grammarAccess.getConstructorExpressionAccess().getNewKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getConstructorExpressionAccess().getOwnedRelationshipInstantiatedTypeMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleInstantiatedTypeMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConstructorExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.InstantiatedTypeMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getConstructorExpressionAccess().getOwnedRelationshipConstructorResultMemberParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleConstructorResultMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConstructorExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ConstructorResultMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleConstructorResultMember
entryRuleConstructorResultMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConstructorResultMemberRule()); }
	iv_ruleConstructorResultMember=ruleConstructorResultMember
	{ $current=$iv_ruleConstructorResultMember.current; }
	EOF;

// Rule ConstructorResultMember
ruleConstructorResultMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getConstructorResultMemberAccess().getOwnedRelatedElementConstructorResultParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleConstructorResult
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getConstructorResultMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ConstructorResult");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleConstructorResult
entryRuleConstructorResult returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConstructorResultRule()); }
	iv_ruleConstructorResult=ruleConstructorResult
	{ $current=$iv_ruleConstructorResult.current; }
	EOF;

// Rule ConstructorResult
ruleConstructorResult returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getConstructorResultRule());
		}
		newCompositeNode(grammarAccess.getConstructorResultAccess().getArgumentListParserRuleCall());
	}
	this_ArgumentList_0=ruleArgumentList[$current]
	{
		$current = $this_ArgumentList_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleInstantiatedTypeMember
entryRuleInstantiatedTypeMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getInstantiatedTypeMemberRule()); }
	iv_ruleInstantiatedTypeMember=ruleInstantiatedTypeMember
	{ $current=$iv_ruleInstantiatedTypeMember.current; }
	EOF;

// Rule InstantiatedTypeMember
ruleInstantiatedTypeMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getInstantiatedTypeMemberRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getInstantiatedTypeMemberAccess().getMemberElementTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getInstantiatedTypeMemberAccess().getOwningMembershipAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getInstantiatedTypeMemberAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_1_0());
					}
					lv_ownedRelatedElement_2_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getInstantiatedTypeMemberRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;


// Rule FeatureChain
ruleFeatureChain[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getFeatureChainAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleOwnedFeatureChaining
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeatureChainRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1='.'
			{
				newLeafNode(otherlv_1, grammarAccess.getFeatureChainAccess().getFullStopKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureChainAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleOwnedFeatureChaining
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureChainRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)+
	)
;

// Entry rule entryRuleOwnedFeatureChaining
entryRuleOwnedFeatureChaining returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureChainingRule()); }
	iv_ruleOwnedFeatureChaining=ruleOwnedFeatureChaining
	{ $current=$iv_ruleOwnedFeatureChaining.current; }
	EOF;

// Rule OwnedFeatureChaining
ruleOwnedFeatureChaining returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getOwnedFeatureChainingRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getOwnedFeatureChainingAccess().getChainingFeatureFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule ArgumentList
ruleArgumentList[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='('
		{
			newLeafNode(otherlv_0, grammarAccess.getArgumentListAccess().getLeftParenthesisKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getArgumentListRule());
				}
				newCompositeNode(grammarAccess.getArgumentListAccess().getPositionalArgumentListParserRuleCall_1_0());
			}
			this_PositionalArgumentList_1=rulePositionalArgumentList[$current]
			{
				$current = $this_PositionalArgumentList_1.current;
				afterParserOrEnumRuleCall();
			}
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getArgumentListRule());
				}
				newCompositeNode(grammarAccess.getArgumentListAccess().getNamedArgumentListParserRuleCall_1_1());
			}
			this_NamedArgumentList_2=ruleNamedArgumentList[$current]
			{
				$current = $this_NamedArgumentList_2.current;
				afterParserOrEnumRuleCall();
			}
		)?
		otherlv_3=')'
		{
			newLeafNode(otherlv_3, grammarAccess.getArgumentListAccess().getRightParenthesisKeyword_2());
		}
	)
;


// Rule PositionalArgumentList
rulePositionalArgumentList[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getPositionalArgumentListAccess().getOwnedRelationshipArgumentMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleArgumentMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getPositionalArgumentListRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getPositionalArgumentListAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getPositionalArgumentListAccess().getOwnedRelationshipArgumentMemberParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleArgumentMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPositionalArgumentListRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleArgumentMember
entryRuleArgumentMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getArgumentMemberRule()); }
	iv_ruleArgumentMember=ruleArgumentMember
	{ $current=$iv_ruleArgumentMember.current; }
	EOF;

// Rule ArgumentMember
ruleArgumentMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getArgumentMemberAccess().getOwnedRelatedElementArgumentParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleArgument
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getArgumentMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.Argument");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleArgument
entryRuleArgument returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getArgumentRule()); }
	iv_ruleArgument=ruleArgument
	{ $current=$iv_ruleArgument.current; }
	EOF;

// Rule Argument
ruleArgument returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getArgumentAccess().getOwnedRelationshipArgumentValueParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleArgumentValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getArgumentRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule NamedArgumentList
ruleNamedArgumentList[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getNamedArgumentListAccess().getOwnedRelationshipNamedArgumentMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleNamedArgumentMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamedArgumentListRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.NamedArgumentMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getNamedArgumentListAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getNamedArgumentListAccess().getOwnedRelationshipNamedArgumentMemberParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleNamedArgumentMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNamedArgumentListRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.NamedArgumentMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleNamedArgumentMember
entryRuleNamedArgumentMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamedArgumentMemberRule()); }
	iv_ruleNamedArgumentMember=ruleNamedArgumentMember
	{ $current=$iv_ruleNamedArgumentMember.current; }
	EOF;

// Rule NamedArgumentMember
ruleNamedArgumentMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getNamedArgumentMemberAccess().getOwnedRelatedElementNamedArgumentParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleNamedArgument
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getNamedArgumentMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.NamedArgument");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleNamedArgument
entryRuleNamedArgument returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamedArgumentRule()); }
	iv_ruleNamedArgument=ruleNamedArgument
	{ $current=$iv_ruleNamedArgument.current; }
	EOF;

// Rule NamedArgument
ruleNamedArgument returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getNamedArgumentAccess().getOwnedRelationshipParameterRedefinitionParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleParameterRedefinition
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamedArgumentRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ParameterRedefinition");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_1='='
		{
			newLeafNode(otherlv_1, grammarAccess.getNamedArgumentAccess().getEqualsSignKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getNamedArgumentAccess().getOwnedRelationshipArgumentValueParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleArgumentValue
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamedArgumentRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentValue");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleParameterRedefinition
entryRuleParameterRedefinition returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getParameterRedefinitionRule()); }
	iv_ruleParameterRedefinition=ruleParameterRedefinition
	{ $current=$iv_ruleParameterRedefinition.current; }
	EOF;

// Rule ParameterRedefinition
ruleParameterRedefinition returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getParameterRedefinitionRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getParameterRedefinitionAccess().getRedefinedFeatureFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleArgumentValue
entryRuleArgumentValue returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getArgumentValueRule()); }
	iv_ruleArgumentValue=ruleArgumentValue
	{ $current=$iv_ruleArgumentValue.current; }
	EOF;

// Rule ArgumentValue
ruleArgumentValue returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getArgumentValueAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getArgumentValueRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleNullExpression
entryRuleNullExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNullExpressionRule()); }
	iv_ruleNullExpression=ruleNullExpression
	{ $current=$iv_ruleNullExpression.current; }
	EOF;

// Rule NullExpression
ruleNullExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				$current = forceCreateModelElement(
					grammarAccess.getNullExpressionAccess().getNullExpressionAction_0(),
					$current);
			}
		)
		(
			otherlv_1='null'
			{
				newLeafNode(otherlv_1, grammarAccess.getNullExpressionAccess().getNullKeyword_1_0());
			}
			    |
			(
				otherlv_2='('
				{
					newLeafNode(otherlv_2, grammarAccess.getNullExpressionAccess().getLeftParenthesisKeyword_1_1_0());
				}
				otherlv_3=')'
				{
					newLeafNode(otherlv_3, grammarAccess.getNullExpressionAccess().getRightParenthesisKeyword_1_1_1());
				}
			)
		)
	)
;

// Entry rule entryRuleLiteralExpression
entryRuleLiteralExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralExpressionRule()); }
	iv_ruleLiteralExpression=ruleLiteralExpression
	{ $current=$iv_ruleLiteralExpression.current; }
	EOF;

// Rule LiteralExpression
ruleLiteralExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralBooleanParserRuleCall_0());
		}
		this_LiteralBoolean_0=ruleLiteralBoolean
		{
			$current = $this_LiteralBoolean_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralStringParserRuleCall_1());
		}
		this_LiteralString_1=ruleLiteralString
		{
			$current = $this_LiteralString_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralIntegerParserRuleCall_2());
		}
		this_LiteralInteger_2=ruleLiteralInteger
		{
			$current = $this_LiteralInteger_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralRealParserRuleCall_3());
		}
		this_LiteralReal_3=ruleLiteralReal
		{
			$current = $this_LiteralReal_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralInfinityParserRuleCall_4());
		}
		this_LiteralInfinity_4=ruleLiteralInfinity
		{
			$current = $this_LiteralInfinity_4.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleLiteralBoolean
entryRuleLiteralBoolean returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralBooleanRule()); }
	iv_ruleLiteralBoolean=ruleLiteralBoolean
	{ $current=$iv_ruleLiteralBoolean.current; }
	EOF;

// Rule LiteralBoolean
ruleLiteralBoolean returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getLiteralBooleanAccess().getValueBooleanValueParserRuleCall_0());
			}
			lv_value_0_0=ruleBooleanValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getLiteralBooleanRule());
				}
				set(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.BooleanValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleBooleanValue
entryRuleBooleanValue returns [String current=null]:
	{ newCompositeNode(grammarAccess.getBooleanValueRule()); }
	iv_ruleBooleanValue=ruleBooleanValue
	{ $current=$iv_ruleBooleanValue.current.getText(); }
	EOF;

// Rule BooleanValue
ruleBooleanValue returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='true'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getBooleanValueAccess().getTrueKeyword_0());
		}
		    |
		kw='false'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getBooleanValueAccess().getFalseKeyword_1());
		}
	)
;

// Entry rule entryRuleLiteralString
entryRuleLiteralString returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralStringRule()); }
	iv_ruleLiteralString=ruleLiteralString
	{ $current=$iv_ruleLiteralString.current; }
	EOF;

// Rule LiteralString
ruleLiteralString returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			lv_value_0_0=RULE_STRING_VALUE
			{
				newLeafNode(lv_value_0_0, grammarAccess.getLiteralStringAccess().getValueSTRING_VALUETerminalRuleCall_0());
			}
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getLiteralStringRule());
				}
				setWithLastConsumed(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.STRING_VALUE");
			}
		)
	)
;

// Entry rule entryRuleLiteralInteger
entryRuleLiteralInteger returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralIntegerRule()); }
	iv_ruleLiteralInteger=ruleLiteralInteger
	{ $current=$iv_ruleLiteralInteger.current; }
	EOF;

// Rule LiteralInteger
ruleLiteralInteger returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			lv_value_0_0=RULE_DECIMAL_VALUE
			{
				newLeafNode(lv_value_0_0, grammarAccess.getLiteralIntegerAccess().getValueDECIMAL_VALUETerminalRuleCall_0());
			}
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getLiteralIntegerRule());
				}
				setWithLastConsumed(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.DECIMAL_VALUE");
			}
		)
	)
;

// Entry rule entryRuleLiteralReal
entryRuleLiteralReal returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralRealRule()); }
	iv_ruleLiteralReal=ruleLiteralReal
	{ $current=$iv_ruleLiteralReal.current; }
	EOF;

// Rule LiteralReal
ruleLiteralReal returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getLiteralRealAccess().getValueRealValueParserRuleCall_0());
			}
			lv_value_0_0=ruleRealValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getLiteralRealRule());
				}
				set(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.RealValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleRealValue
entryRuleRealValue returns [String current=null]:
	{ newCompositeNode(grammarAccess.getRealValueRule()); }
	iv_ruleRealValue=ruleRealValue
	{ $current=$iv_ruleRealValue.current.getText(); }
	EOF;

// Rule RealValue
ruleRealValue returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				this_DECIMAL_VALUE_0=RULE_DECIMAL_VALUE
				{
					$current.merge(this_DECIMAL_VALUE_0);
				}
				{
					newLeafNode(this_DECIMAL_VALUE_0, grammarAccess.getRealValueAccess().getDECIMAL_VALUETerminalRuleCall_0_0());
				}
			)?
			kw='.'
			{
				$current.merge(kw);
				newLeafNode(kw, grammarAccess.getRealValueAccess().getFullStopKeyword_0_1());
			}
			(
				this_DECIMAL_VALUE_2=RULE_DECIMAL_VALUE
				{
					$current.merge(this_DECIMAL_VALUE_2);
				}
				{
					newLeafNode(this_DECIMAL_VALUE_2, grammarAccess.getRealValueAccess().getDECIMAL_VALUETerminalRuleCall_0_2_0());
				}
				    |
				this_EXP_VALUE_3=RULE_EXP_VALUE
				{
					$current.merge(this_EXP_VALUE_3);
				}
				{
					newLeafNode(this_EXP_VALUE_3, grammarAccess.getRealValueAccess().getEXP_VALUETerminalRuleCall_0_2_1());
				}
			)
		)
		    |
		this_EXP_VALUE_4=RULE_EXP_VALUE
		{
			$current.merge(this_EXP_VALUE_4);
		}
		{
			newLeafNode(this_EXP_VALUE_4, grammarAccess.getRealValueAccess().getEXP_VALUETerminalRuleCall_1());
		}
	)
;

// Entry rule entryRuleLiteralInfinity
entryRuleLiteralInfinity returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralInfinityRule()); }
	iv_ruleLiteralInfinity=ruleLiteralInfinity
	{ $current=$iv_ruleLiteralInfinity.current; }
	EOF;

// Rule LiteralInfinity
ruleLiteralInfinity returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				$current = forceCreateModelElement(
					grammarAccess.getLiteralInfinityAccess().getLiteralInfinityAction_0(),
					$current);
			}
		)
		otherlv_1='*'
		{
			newLeafNode(otherlv_1, grammarAccess.getLiteralInfinityAccess().getAsteriskKeyword_1());
		}
	)
;

// Entry rule entryRuleName
entryRuleName returns [String current=null]:
	{ newCompositeNode(grammarAccess.getNameRule()); }
	iv_ruleName=ruleName
	{ $current=$iv_ruleName.current.getText(); }
	EOF;

// Rule Name
ruleName returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		this_ID_0=RULE_ID
		{
			$current.merge(this_ID_0);
		}
		{
			newLeafNode(this_ID_0, grammarAccess.getNameAccess().getIDTerminalRuleCall_0());
		}
		    |
		this_UNRESTRICTED_NAME_1=RULE_UNRESTRICTED_NAME
		{
			$current.merge(this_UNRESTRICTED_NAME_1);
		}
		{
			newLeafNode(this_UNRESTRICTED_NAME_1, grammarAccess.getNameAccess().getUNRESTRICTED_NAMETerminalRuleCall_1());
		}
	)
;

// Entry rule entryRuleGlobalQualification
entryRuleGlobalQualification returns [String current=null]:
	{ newCompositeNode(grammarAccess.getGlobalQualificationRule()); }
	iv_ruleGlobalQualification=ruleGlobalQualification
	{ $current=$iv_ruleGlobalQualification.current.getText(); }
	EOF;

// Rule GlobalQualification
ruleGlobalQualification returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='$'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getGlobalQualificationAccess().getDollarSignKeyword_0());
		}
		kw='::'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getGlobalQualificationAccess().getColonColonKeyword_1());
		}
	)
;

// Entry rule entryRuleQualification
entryRuleQualification returns [String current=null]:
	{ newCompositeNode(grammarAccess.getQualificationRule()); }
	iv_ruleQualification=ruleQualification
	{ $current=$iv_ruleQualification.current.getText(); }
	EOF;

// Rule Qualification
ruleQualification returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getQualificationAccess().getNameParserRuleCall_0());
		}
		this_Name_0=ruleName
		{
			$current.merge(this_Name_0);
		}
		{
			afterParserOrEnumRuleCall();
		}
		kw='::'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getQualificationAccess().getColonColonKeyword_1());
		}
	)+
;

// Entry rule entryRuleQualifiedName
entryRuleQualifiedName returns [String current=null]:
	{ newCompositeNode(grammarAccess.getQualifiedNameRule()); }
	iv_ruleQualifiedName=ruleQualifiedName
	{ $current=$iv_ruleQualifiedName.current.getText(); }
	EOF;

// Rule QualifiedName
ruleQualifiedName returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getQualifiedNameAccess().getGlobalQualificationParserRuleCall_0());
			}
			this_GlobalQualification_0=ruleGlobalQualification
			{
				$current.merge(this_GlobalQualification_0);
			}
			{
				afterParserOrEnumRuleCall();
			}
		)?
		(
			{
				newCompositeNode(grammarAccess.getQualifiedNameAccess().getQualificationParserRuleCall_1());
			}
			this_Qualification_1=ruleQualification
			{
				$current.merge(this_Qualification_1);
			}
			{
				afterParserOrEnumRuleCall();
			}
		)?
		{
			newCompositeNode(grammarAccess.getQualifiedNameAccess().getNameParserRuleCall_2());
		}
		this_Name_2=ruleName
		{
			$current.merge(this_Name_2);
		}
		{
			afterParserOrEnumRuleCall();
		}
	)
;

RULE_DECIMAL_VALUE : '0'..'9' ('0'..'9')*;

RULE_EXP_VALUE : RULE_DECIMAL_VALUE ('e'|'E') ('+'|'-')? RULE_DECIMAL_VALUE;

RULE_ID : ('a'..'z'|'A'..'Z'|'_') ('a'..'z'|'A'..'Z'|'_'|'0'..'9')*;

RULE_UNRESTRICTED_NAME : '\'' ('\\' ('b'|'t'|'n'|'f'|'r'|'"'|'\''|'\\')|~(('\\'|'\'')))* '\'';

RULE_STRING_VALUE : '"' ('\\' ('b'|'t'|'n'|'f'|'r'|'"'|'\''|'\\')|~(('\\'|'"')))* '"';

RULE_REGULAR_COMMENT : '/*' ( options {greedy=false;} : . )*'*/';

RULE_ML_NOTE : '//*' ( options {greedy=false;} : . )*'*/';

RULE_SL_NOTE : '//' (~(('\n'|'\r')) ~(('\n'|'\r'))*)? ('\r'? '\n')?;

RULE_WS : (' '|'\t'|'\r'|'\n')+;
