//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	public import VisibilityPackage::*;
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Protect::c_publicc'." at "c_clazz::c_Protect::c_publicc"
	classifier try specializes c_clazz::c_Protect::c_publicc{}
	
	classifier A specializes c_clazz{
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Public::c_protect'." at "c_Public::c_protect"
		classifier AA specializes c_Public::c_protect{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Public::c_packagee'." at "c_Public::c_packagee"
		classifier BB specializes c_Public::c_packagee{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Public::c_Package::c_publicc'." at "c_Public::c_Package::c_publicc"
		classifier CC specializes c_Public::c_Package::c_publicc{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Protected::c_protect'." at "c_Protected::c_protect"
		classifier DD specializes c_Protected::c_protect{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Package'." at "c_Package"
		classifier EE specializes c_Package{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Package::c_publicc'." at "c_Package::c_publicc"
		classifier FF specializes c_Package::c_publicc{}
	}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Public::c_protect'." at "c_clazz::c_Public::c_protect"
	classifier AAA specializes c_clazz::c_Public::c_protect{}
	//XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Public::c_packagee'." at "c_clazz::c_Public::c_packagee"
	classifier ABB specializes c_clazz::c_Public::c_packagee{}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Public::c_Package::c_publicc'." at "c_clazz::c_Public::c_Package::c_publicc"
	classifier ACC specializes c_clazz::c_Public::c_Package::c_publicc{}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Protected::c_protect'." at "c_clazz::c_Protected::c_protect"
	classifier ADD specializes c_clazz::c_Protected::c_protect{}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Package'." at "c_clazz::c_Package"
	classifier AEE specializes c_clazz::c_Package{}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_clazz::c_Package::c_publicc'." at "c_clazz::c_Package::c_publicc"
	classifier AFF specializes c_clazz::c_Package::c_publicc{}
	
	
	
	
	classifier B specializes c_Public {
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_private'." at "c_private"
		classifier AA specializes c_private{}
	}
	//XPECT errors --> "Couldn't resolve reference to Classifier 'c_Public::c_private'." at "c_Public::c_private"
	classifier BAA specializes c_Public::c_private{}
	
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Private'." at "c_Private"
	classifier C specializes c_Private {
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_private'." at "c_private"
		classifier AA specializes c_private{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_public'." at "c_public"
		classifier BB specializes c_public{}
	}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Private::c_private'." at "c_Private::c_private"
	classifier CC specializes c_Private::c_private{}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Private::c_public'." at "c_Private::c_public"
	classifier CCC specializes c_Private::c_public{}
	
	
	classifier D specializes c_Public_alias{
		// XPECT errors --> "Couldn't resolve reference to Classifier 'alias_private'." at "alias_private"
  		classifier AA specializes alias_private{}
  	}
  	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Private_alias'." at "c_Private_alias"
  	classifier E specializes c_Private_alias{
		// XPECT errors --> "Couldn't resolve reference to Classifier 'c_public'." at "c_public"
  		classifier AA specializes c_public{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'alias_private'." at "alias_private"
  		classifier BB specializes alias_private{}
		// XPECT errors --> "Couldn't resolve reference to Classifier 'alias_public'." at "alias_public"
  		classifier CC specializes alias_public{}
  	}
  	
}

 //Only public members are available via qualification
