'use client';

import React, { useState, useCallback } from 'react';
import { useApp } from '@/contexts/AppContext';
import { useToast } from '@/components/ui/Toast';
import { TreeNode, ContextMenuItem } from '@/types';
import { projectsToTreeNodes, updateTreeNode } from '@/utils/helpers';

interface ProjectTreeProps {
  onNodeSelect?: (node: TreeNode) => void;
  onNodeDoubleClick?: (node: TreeNode) => void;
}

const ProjectTree: React.FC<ProjectTreeProps> = ({
  onNodeSelect,
  onNodeDoubleClick,
}) => {
  const { state, dispatch } = useApp();
  const { showToast } = useToast();
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  // 将项目数据转换为树形结构
  const treeNodes = projectsToTreeNodes(state.projects);

  // 切换节点展开状态
  const toggleNodeExpansion = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  // 处理节点点击
  const handleNodeClick = useCallback((node: TreeNode, event: React.MouseEvent) => {
    event.stopPropagation();
    
    setSelectedNodeId(node.id);
    
    if (node.type === 'project' || node.type === 'namespace') {
      toggleNodeExpansion(node.id);
    }
    
    onNodeSelect?.(node);
  }, [toggleNodeExpansion, onNodeSelect]);

  // 处理节点双击
  const handleNodeDoubleClick = useCallback((node: TreeNode, event: React.MouseEvent) => {
    event.stopPropagation();
    onNodeDoubleClick?.(node);
  }, [onNodeDoubleClick]);

  // 处理右键菜单
  const handleContextMenu = useCallback((node: TreeNode, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    const contextMenuItems: ContextMenuItem[] = [];

    if (node.type === 'project') {
      contextMenuItems.push(
        {
          id: 'new-namespace',
          label: '新建命名空间',
          action: () => {
            dispatch({
              type: 'SET_MODAL',
              payload: {
                isOpen: true,
                type: 'create-namespace',
                data: { projectId: node.id },
              },
            });
          },
        },
        {
          id: 'separator-1',
          label: '',
          separator: true,
          action: () => {},
        },
        {
          id: 'rename-project',
          label: '重命名',
          action: () => {
            dispatch({
              type: 'SET_MODAL',
              payload: {
                isOpen: true,
                type: 'rename-project',
                data: { projectId: node.id, currentName: node.name },
              },
            });
          },
        },
        {
          id: 'delete-project',
          label: '删除',
          action: () => {
            dispatch({
              type: 'SET_MODAL',
              payload: {
                isOpen: true,
                type: 'delete-confirm',
                data: { 
                  type: 'project',
                  id: node.id,
                  name: node.name,
                },
              },
            });
          },
        }
      );
    } else if (node.type === 'namespace') {
      contextMenuItems.push(
        {
          id: 'new-diagram',
          label: '新建 SysMLv2 图',
          action: () => {
            dispatch({
              type: 'SET_MODAL',
              payload: {
                isOpen: true,
                type: 'create-diagram',
                data: { namespaceId: node.id },
              },
            });
          },
        }
      );
    }

    dispatch({
      type: 'SET_CONTEXT_MENU',
      payload: {
        isVisible: true,
        x: event.clientX,
        y: event.clientY,
        items: contextMenuItems,
        targetId: node.id,
      },
    });
  }, [dispatch]);

  // 获取节点图标
  const getNodeIcon = (node: TreeNode) => {
    const isExpanded = expandedNodes.has(node.id);
    
    switch (node.type) {
      case 'project':
        return isExpanded ? '📂' : '📁';
      case 'namespace':
        return isExpanded ? '📂' : '📁';
      case 'diagram':
        return '📄';
      default:
        return '📄';
    }
  };

  // 渲染树节点
  const renderTreeNode = (node: TreeNode, level: number = 0) => {
    const isSelected = selectedNodeId === node.id;
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;

    return (
      <div key={node.id}>
        <div
          className={`tree-node ${isSelected ? 'selected' : ''}`}
          style={{ paddingLeft: `${level * 20 + 8}px` }}
          onClick={(e) => handleNodeClick(node, e)}
          onDoubleClick={(e) => handleNodeDoubleClick(node, e)}
          onContextMenu={(e) => handleContextMenu(node, e)}
        >
          <div className="tree-node-content">
            {hasChildren && (
              <span className="tree-node-icon">
                {isExpanded ? '▼' : '▶'}
              </span>
            )}
            {!hasChildren && <span className="tree-node-icon"></span>}
            <span className="tree-node-icon">{getNodeIcon(node)}</span>
            <span className="tree-node-label">{node.name}</span>
          </div>
        </div>
        
        {hasChildren && isExpanded && (
          <div>
            {node.children!.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        <h3 className="text-sm font-semibold text-gray-700 mb-3">项目资源管理器</h3>
        
        {treeNodes.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <p className="text-sm text-gray-500 mb-4">还没有项目</p>
            <button
              onClick={() => {
                dispatch({
                  type: 'SET_MODAL',
                  payload: {
                    isOpen: true,
                    type: 'create-project',
                  },
                });
              }}
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              创建第一个项目
            </button>
          </div>
        ) : (
          <div className="space-y-1">
            {treeNodes.map(node => renderTreeNode(node))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectTree;
