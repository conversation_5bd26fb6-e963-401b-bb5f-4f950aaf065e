/* --------------------------------------------------------------------------------------------
 * Copyright (c) 2024 TypeFox and others.
 * Licensed under the MIT License. See LICENSE in the package root for license information.
 * ------------------------------------------------------------------------------------------ */

import { type Module, inject } from 'langium';
import type { LangiumServices, LangiumSharedServices, PartialLangiumServices } from 'langium/lsp';
import { createDefaultModule, createDefaultSharedModule, type DefaultSharedModuleContext } from 'langium/lsp';
import { StatemachineGeneratedModule, StatemachineGeneratedSharedModule } from './generated/module.js';
import { StatemachineValidator, registerValidationChecks } from './statemachine-validator.js';

/**
 * Declaration of custom services - add your own service classes here.
 */
export type StatemachineAddedServices = {
    validation: {
        StatemachineValidator: StatemachineValidator
    }
}

/**
 * Union of Langium default services and your custom services - use this as constructor parameter
 * of custom service classes.
 */
export type StatemachineServices = LangiumServices & StatemachineAddedServices

/**
 * Dependency injection module that overrides Langium default services and contributes the
 * declared custom services. The Langium defaults can be partially specified to override only
 * selected services, while the custom services must be fully specified.
 */
export const StatemachineModule: Module<StatemachineServices, PartialLangiumServices & StatemachineAddedServices> = {
    validation: {
        StatemachineValidator: () => new StatemachineValidator()
    }
};

/**
 * Create the full set of services required by Langium.
 *
 * First inject the shared services by merging two modules:
 *  - Langium default shared services
 *  - Services generated by langium-cli
 *
 * Then inject the language-specific services by merging three modules:
 *  - Langium default language-specific services
 *  - Services generated by langium-cli
 *  - Services specified in this file
 *
 * @param context Optional module context with the LSP connection
 * @returns An object wrapping the shared services and the language-specific services
 */
export function createStatemachineServices(context: DefaultSharedModuleContext): {
    shared: LangiumSharedServices,
    statemachine: StatemachineServices
} {
    const shared = inject(
        createDefaultSharedModule(context),
        StatemachineGeneratedSharedModule
    );
    const statemachine = inject(
        createDefaultModule({ shared }),
        StatemachineGeneratedModule,
        StatemachineModule
    );
    shared.ServiceRegistry.register(statemachine);
    registerValidationChecks(statemachine);
    if (!context.connection) {
        // We don't run inside a language server
        // Therefore, initialize the configuration provider instantly
        shared.workspace.ConfigurationProvider.initialized({});
    }
    return { shared, statemachine };
}
