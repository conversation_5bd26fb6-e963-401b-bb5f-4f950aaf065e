# Version information and project History

## Monaco-editor / @codingame/monaco-vscode-api compatibility table

The following table describes which version of **monaco-languageclient** and **@codingame/monaco-vscode-api** are compatible with a specific version of **monaco-editor** and **vscode**. The listing starts with version 2.0.0 because **@codingame/monaco-vscode-api** was introduced for the first time.

| monaco-languageclient | monaco-editor-wrapper | monaco-editor-react | monaco-vscode-api / editor-api | vscode | monaco-editor | release date | comment |
| :---- | :---- | :--- | :--- | :--- | :--- | :--- | :--- |
| 9.8.0 | 6.9.0 | 6.9.0 | 18.1.0 | 1.101.1 | 0.52.2 | 2025-06-24 | |
| 9.7.1 | 6.8.1 | 6.8.1 | 17.2.1 | 1.100.3 | 0.52.2 | 2025-06-17 | |
| 9.7.0 | 6.8.0 | 6.8.0 | 17.1.2 | 1.100.2 | 0.52.2 | 2025-05-28 | |
| 9.6.0 | 6.7.0 | 6.7.0 | 16.1.1 | 1.99.3 | 0.52.2 | 2025-05-06 | |
| 9.5.0 | 6.6.0 | 6.6.0 | 15.0.2 | 1.97.2 | 0.52.2 | 2025-03-13 | |
| 9.4.0 | 6.4.0 | 6.4.0 | 14.0.4 | 1.97.2 | 0.52.2 | 2025-02-18 | |
| 9.3.0 | 6.3.0 | 6.3.0 | 14.0.2 | 1.97.0 | 0.52.2 | 2025-02-12 | |
| 9.2.5 | 6.2.5 | 6.2.5 | 13.1.6 | 1.96.4 | 0.52.2 | 2025-02-08 | |
| 9.2.4 | 6.2.4 | 6.2.4 | 13.1.4 | 1.96.4 | 0.52.2 | 2025-02-06 | |
| 9.2.3 | 6.2.3 | 6.2.3 | 13.1.3 | 1.96.4 | 0.52.2 | 2025-02-04 | |
| 9.2.2 | 6.2.2 | 6.2.2 | 13.1.2 | 1.96.4 | 0.52.2 | 2025-02-03 | |
| 9.2.1 | 6.2.1 | 6.2.1 | 13.1.1 | 1.96.4 | 0.52.2 | 2025-01-31 | |
| 9.2.0 | 6.2.0 | 6.2.0 | 13.1.1 | 1.96.4 | 0.52.2 | 2025-01-31 | |
| 9.1.1 | 6.1.1 | 6.1.1 | 11.1.2 | 1.95.3 | 0.52.0 | 2025-01-20 | |
| 9.1.0 | 6.1.0 | 6.1.0 | 11.1.2 | 1.95.3 | 0.52.0 | 2025-01-10 | |
| 9.0.0 | 6.0.0 | 6.0.0 | 11.1.2 | 1.95.3 | 0.52.0 | 2024-12-18 | |
| 8.8.3         | 5.5.3 | 4.5.3 | 8.0.4  | 1.92.2 | 0.51.0 | 2024-08-26 | |
| 8.8.2         | 5.5.2 | 4.5.2 | 8.0.2  | 1.92.2 | 0.50.0 | 2024-08-21 | |
| 8.8.1         | 5.5.1 | 4.5.1 | 8.0.1  | 1.92.1 | 0.50.0 | 2024-08-12 | |
| 8.8.0         | 5.5.0 | 4.5.0 | 8.0.0  | 1.92.0 | 0.50.0 | 2024-08-08 | |
| 8.7.0         | 5.4.0 | 4.4.0 | 7.0.7  | 1.91.1 | 0.50.0 | 2024-07-16 | |
| 8.6.0         | 5.3.1 | 4.3.2 | 6.0.3  | 1.90.0 | 0.50.0 | 2024-07-02 | |
| 8.6.0         | 5.3.0 | 4.3.0 | 6.0.3  | 1.90.0 | 0.50.0 | 2024-06-29 | |
| 8.5.0         | 5.2.0 | 4.2.0 | 5.2.0  | 1.89.1 | 0.48.0 | 2024-06-04 | |
| 8.4.0         | 5.1.2 | 4.1.2 | 5.1.1  | 1.89.1 | 0.48.0 | 2024-05-17 | |
| 8.4.0         | 5.1.1 | 4.1.1 | 5.1.1  | 1.89.1 | 0.48.0 | 2024-05-15 | |
| 8.4.0         | 5.1.0 | 4.1.0 | 5.1.1  | 1.89.1 | 0.48.0 | 2024-05-15 | |
| 8.4.0         | 5.0.0 | 4.0.0 | 5.1.1  | 1.89.1 | 0.48.0 | 2024-05-15 | |
| 8.3.1         | 4.2.1 | 3.2.1 | 4.3.2  | 1.88.1 | 0.47.0 | 2024-04-17 | |
| 8.3.0         | 4.2.0 | 3.2.0 | 4.2.1  | 1.88.1 | 0.47.0 | 2024-04-12 | |
| 8.2.0         | 4.1.0 | 3.1.0 | 4.1.2  | 1.88.0 | 0.47.0 | 2024-04-10 | |
| 8.1.1         | 4.0.2 | 3.0.2 | 3.2.3  | 1.87.2 | 0.47.0 | 2024-04-03 | |
| 8.1.0         | 4.0.1 | 3.0.1 | 3.2.1  | 1.87.2 | 0.47.0 | 2024-03-22 | |
| 8.0.0         | 4.0.0 | 3.0.0 | 3.1.2  | 1.87.2 | 0.47.0 | 2024-03-18 | `@codingame/monaco-vscode-api` implements its own versioning scheme<br>`@codingame/monaco-vscode-editor-api` replaces<br>`@codingame/monaco-editor-treemended`<br>Added `monaco-editor-wrapper` and `@typefox/monaco-editor-react` to the table |
| 7.3.0         | | | 1.85.0  | 1.85.0 | 0.45.0 | 2024-01-04 | |
| 7.2.0         | | | 1.83.16  | 1.83.16 | 0.44.0 | 2023-12-07 | |
| 7.1.0         | | | 1.83.12  | 1.83.12 | 0.44.0 | 2023-11-27 | |
| 7.0.2         | | | 1.83.7  | 1.83.7 | 0.44.0 | 2023-11-02 | |
| 7.0.1         | | | 1.83.5  | 1.83.5 | 0.44.0 | 2023-11-10 | |
| 7.0.0         | | | 1.83.5  | 1.83.5 | 0.44.0 | 2023-11-02 | Introduction of `@codingame/monaco-editor-treemended` |
| 6.6.1         | | | 1.83.3  | | 0.44.0 | 2023-10-20 | |
| 6.6.0         | | | 1.83.2  | | 0.44.0 | 2023-10-16 | |
| 6.5.3         | | | 1.82.5  | | 0.43.0 | 2023-10-11 | |
| 6.5.2         | | | 1.82.4  | | 0.43.0 | 2023-10-07 | |
| 6.5.1         | | | 1.82.3  | | 0.43.0 | 2023-10-04 | |
| 6.5.0         | | | 1.82.2  | | 0.43.0 | 2023-09-29 | |
| 6.4.6         | | | 1.81.7  | | 0.41.0 | 2023-09-05 | |
| 6.4.5         | | | 1.81.5  | | 0.41.0 | 2023-08-30 | |
| 6.4.4         | | | 1.81.5  | | 0.41.0 | 2023-08-24 | |
| 6.4.3         | | | 1.81.3  | | 0.41.0 | 2023-08-22 | |
| 6.4.2         | | | 1.81.2  | | 0.41.0 | 2023-08-19 | |
| 6.4.1         | | | 1.81.1  | | 0.41.0 | 2023-08-18 | |
| 6.4.0         | | | 1.81.0  | | 0.41.0 | 2023-08-10 | |
| 6.3.0         | | | 1.80.2  | | 0.40.0 | 2023-08-04 | |
| 6.2.0         | | | 1.79.3  | | 0.39.0 | 2023-06-16 | |
| 6.1.0         | | | 1.79.1  | | 0.38.0 | 2023-06-12 | |
| 6.0.3         | | | 1.78.8  | | 0.37.1 | 2023-05-31 | |
| 6.0.2         | | | 1.78.6  | | 0.37.1 | 2023-05-24 | |
| 6.0.1         | | | 1.78.6  | | 0.37.1 | 2023-05-12 | |
| 6.0.0         | | | 1.78.5  | | 0.37.1 | 2023-05-04 | |
| 5.0.1         | | | 1.76.6  | | 0.36.1 | 2023-04-05 | |
| 5.0.0         | | | 1.76.6  | | 0.36.1 | 2023-04-04 | |
| 4.0.3         | | | 1.69.13 | | 0.34.1 | | |
| 4.0.1         | | | 1.69.12 | | 0.34.1 | | |
| 4.0.0         | | | 1.69.10 | | 0.34.0 | | |
| 3.0.1         | | | 1.69.9  | | 0.34.0 | | |
| 3.0.0         | | | 1.69.0  | | 0.34.0 | | |
| 2.1.0         | | | 1.67.20 | | 0.33.0 | | monaco-editor and vscode compatible again |
| 2.0.0 - 2.0.2 | | | 1.68.4  | | 0.33.0 | | monaco-editor and vscode incompatible |

## Important Project Changes

### March 2024 (v8.0.0)

`@codingame/monaco-editor-treemended` is replaced by self-standing `@codingame/monaco-vscode-editor-api`. `monaco-editor-wrapper` and `@typefox/monaco-editor-react` have been moved into this repository.

### October 2023 (v7.0.0)

Revised the `treemending` approach. The postinstall step is removed. `monaco-languageclient` no longer patches and existing `monaco-editor` instead the package `@codingame/monaco-editor-treemended` is used. This requires that projects using this lib have to enforce the correct editor by overrides (npm/pnpm) or resolutions (yarn) in the `package.json`.

### September 2023 (v6.5.0)

Updated to `monaco-editor` `0.43.0` and `@codingame/monaco-vscode-api` `1.82.2`. `initServices` does no longer use dynamic imports. All services available from [@codingame/monaco-vscode-api](https://github.com/CodinGame/monaco-vscode-api#monaco-standalone-services) or the own service must be passed to `userServices`. All examples have been adapted accordingly.

### May 2023 (v6.0.0)

Updated to `@codingame/monaco-vscode-api` `1.78.5` and therefore retired `MonacoServices`. It is replaced by `initServices` that makes configuration of services exposed by `@codingame/monaco-vscode-api` handy and still allows the definition of own services as [outlined here](https://github.com/CodinGame/monaco-vscode-api#monaco-standalone-services) and these can be passed as `userServices` in `initServices`.

### April 2023 (v5.0.0)

Both libraries no longer export code from other libraries (`vscode-jsonrpc`, `vscode-languageclient` and `vscode-languageserver-protocol`).

### September 2022 (v4.0.0)

All code has been transformed to esm and npm packages are now of type module. cjs bundles are no longer available.
The `monaco-converter` has been removed.

### June 2022 (v2.0.0)

[@codingame/monaco-vscode-api](https://github.com/CodinGame/monaco-vscode-api) was created by [CGNonofr](https://github.com/CGNonofr) and this library is now based on it and the old [implementation was removed](https://github.com/CodinGame/monaco-vscode-api#history).

We added the independent **[vscode-ws-jsonrpc](./packages/vscode-ws-jsonrpc)** as sub-package into this repository.

### May 2022 (v1.0.0)

From release 1.0.0 onward the project switched to npm workspaces. We no longer require yarn, lerna and webpack. Mostly therefore the list of `devDependencies` is substantially shorter. All code has been moved to [./packages](./packages) directory.

As before the library code is just compiled with the TypeScript compiler and the library is now packaged with npm. The need for bundling does no longer exist for the example. The compiled code is either executed by node or the web/client related code/pages are served with [vite.js](https://vitejs.dev/). We added a [verification examples](../README.md#verification-examples) for the web client example using webpack.

The default and protected branch is now `main`.
