//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors --> ""
package Test1{
	classifier A {
		public import B::*;
		//XPECT linkedName at A --> Test1.A
		//* XPECT scope at A ---
   		A, A.B, A.B.B, B, B.B, Test1.A, Test1.A.B, Test1.A.B.B
 	--- */
		classifier B specializes A{}
	}
}
