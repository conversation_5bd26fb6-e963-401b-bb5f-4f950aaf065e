/* --------------------------------------------------------------------------------------------
 * SysML v2 Monaco Editor Wrapper Configuration
 * Based on monaco-languageclient examples
 * ------------------------------------------------------------------------------------------ */

import { LogLevel } from '@codingame/monaco-vscode-api';
import getKeybindingsServiceOverride from '@codingame/monaco-vscode-keybindings-service-override';
import type { MessageReader, MessageWriter } from 'vscode-languageserver-protocol';

// Define MessageTransports type locally
type MessageTransports = {
  reader: MessageReader;
  writer: MessageWriter;
};
import type { Logger } from 'monaco-languageclient/tools';
import { useWorkerFactory } from 'monaco-languageclient/workerFactory';
import type { WrapperConfig } from 'monaco-editor-wrapper';
import { defineDefaultWorkerLoaders } from 'monaco-editor-wrapper/workers/workerLoaders';

// SysML v2 Monarch 语法高亮配置
export const SysMLMonarchContent = {
  tokenizer: {
    root: [
      // 关键字
      [/\b(package|part|def|attribute|connection|interface|port|action|state|requirement|constraint|doc|comment|metadata|import|alias|abstract|variation|individual|ref|in|out|inout|derived|readonly|ordered|nonunique|end|composite|portion|snapshot|timeslice|binding|succession|flow|allocation|view|viewpoint|rendering|case|analysis|verification|use|concern|actor|stakeholder|subject|frame|satisfy|assume|require|verify|expose|render|include|exhibit|perform|send|accept|assign|if|then|else|while|for|loop|merge|decision|join|fork|transition|trigger|guard|effect|entry|do|exit|when|at|after|first|to|from|about|locale|language|standard|library|public|private|protected|redefines|subsets|specializes|conjugates|crosses|references|typed|by|defined|hastype|istype|as|meta|all|null|true|false|and|or|xor|not|implies)\b/, 'keyword'],

      // 操作符
      [/(:>|::>|:>>|=>|==|!=|===|!==|<=|>=|<|>|\+|-|\*|\/|%|\*\*|\^|~|&|\||!|\?|\?\?|\.\.|\.|,|;|:|\(|\)|\[|\]|\{|\}|=|:=)/, 'operator'],

      // 字符串
      [/"([^"\\]|\\.)*$/, 'string.invalid'],
      [/"/, 'string', '@string'],
      [/'([^'\\]|\\.)*$/, 'string.invalid'],
      [/'/, 'string', '@string_single'],

      // 数字
      [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
      [/\d+[eE][\-+]?\d+/, 'number.float'],
      [/\d+/, 'number'],

      // 注释
      [/\/\*/, 'comment', '@comment'],
      [/\/\/.*$/, 'comment'],

      // 标识符
      [/[a-zA-Z_]\w*/, 'identifier'],

      // 空白字符
      [/[ \t\r\n]+/, 'white']
    ],

    string: [
      [/[^\\"]+/, 'string'],
      [/\\./, 'string.escape'],
      [/"/, 'string', '@pop']
    ],

    string_single: [
      [/[^\\']+/, 'string'],
      [/\\./, 'string.escape'],
      [/'/, 'string', '@pop']
    ],

    comment: [
      [/[^\/*]+/, 'comment'],
      [/\/\*/, 'comment', '@push'],
      [/\*\//, 'comment', '@pop'],
      [/[\/*]/, 'comment']
    ]
  }
};

// SysML v2 语言配置
export const SysMLLanguageConfig = {
  comments: {
    lineComment: '//',
    blockComment: ['/*', '*/']
  },
  brackets: [
    ['{', '}'],
    ['[', ']'],
    ['(', ')']
  ],
  autoClosingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '"', close: '"' },
    { open: "'", close: "'" }
  ],
  surroundingPairs: [
    { open: '{', close: '}' },
    { open: '[', close: ']' },
    { open: '(', close: ')' },
    { open: '"', close: '"' },
    { open: "'", close: "'" }
  ],
  folding: {
    markers: {
      start: new RegExp('^\\s*//\\s*#?region\\b'),
      end: new RegExp('^\\s*//\\s*#?endregion\\b')
    }
  }
};

// 示例 SysML v2 代码
export const sysmlExampleCode = `package VehicleExample {
  // 车辆部件定义
  part def Vehicle {
    attribute mass : Real;
    attribute length : Real;
    attribute width : Real;
    
    part engine : Engine;
    part wheels : Wheel[4];
    part body : Body;
    
    // 连接定义
    connection def PowerConnection {
      end source : Engine;
      end target : Wheel;
    }
    
    // 动力传输连接
    connection powerTrain : PowerConnection 
      connect engine to wheels;
  }
  
  // 引擎定义
  part def Engine {
    attribute power : Real;
    attribute fuelType : String;
    
    action def Start {
      in signal : StartSignal;
      out status : EngineStatus;
    }
  }
  
  // 车轮定义
  part def Wheel {
    attribute diameter : Real;
    attribute material : String;
  }
  
  // 车身定义
  part def Body {
    attribute color : String;
    attribute material : String;
  }
  
  // 需求定义
  requirement def SafetyRequirement {
    doc /* 车辆必须满足安全标准 */
    
    require constraint {
      vehicle.mass <= 2000.0
    }
  }
  
  // 具体车辆实例
  part myVehicle : Vehicle {
    :>> mass = 1500.0;
    :>> length = 4.5;
    :>> width = 1.8;
    
    part :>> engine {
      :>> power = 150.0;
      :>> fuelType = "gasoline";
    }
  }
}`;

// SysML v2 深色主题配置
export const SysMLDarkTheme = {
  base: 'vs-dark' as const,
  inherit: true,
  rules: [
    { token: 'keyword', foreground: '569cd6' },
    { token: 'operator', foreground: 'd4d4d4' },
    { token: 'string', foreground: 'ce9178' },
    { token: 'number', foreground: 'b5cea8' },
    { token: 'comment', foreground: '6a9955', fontStyle: 'italic' },
    { token: 'identifier', foreground: '9cdcfe' },
    { token: 'type', foreground: '4ec9b0' },
    { token: 'delimiter', foreground: 'd4d4d4' }
  ],
  colors: {
    'editor.background': '#1e1e1e',
    'editor.foreground': '#d4d4d4',
    'editor.lineHighlightBackground': '#2d2d30',
    'editor.selectionBackground': '#264f78',
    'editor.inactiveSelectionBackground': '#3a3d41'
  }
};

export const setupSysMLWrapperConfig = async (params: {
  worker: Worker;
  messageTransports?: MessageTransports;
  htmlContainer: HTMLElement;
  initialCode?: string;
}): Promise<WrapperConfig> => {
  const workerLoaders = defineDefaultWorkerLoaders();
  workerLoaders.TextMateWorker = undefined;

  return {
    $type: 'classic',
    htmlContainer: params.htmlContainer,
    logLevel: LogLevel.Debug,
    vscodeApiConfig: {
      serviceOverrides: {
        ...getKeybindingsServiceOverride()
      }
    },
    editorAppConfig: {
      codeResources: {
        modified: {
          text: params.initialCode || sysmlExampleCode,
          uri: '/workspace/example.sysml',
          enforceLanguageId: 'sysml'
        }
      },
      editorOptions: {
        'semanticHighlighting.enabled': true,
        wordBasedSuggestions: 'off',
        theme: 'sysml-dark',
        fontSize: 14,
        lineNumbers: 'on',
        minimap: { enabled: true },
        scrollBeyondLastLine: false,
        wordWrap: 'on',
        folding: true,
        automaticLayout: true,
        contextmenu: true,
        mouseWheelZoom: true,
        smoothScrolling: true,
        cursorBlinking: 'blink',
        renderLineHighlight: 'line',
        selectOnLineNumbers: true,
        colorDecorators: true,
        codeLens: true
      },
      languageDef: {
        monarchLanguage: SysMLMonarchContent as any,
        languageExtensionConfig: {
          id: 'sysml',
          extensions: ['.sysml', '.sysmlv2'],
          aliases: ['SysML', 'sysml']
        }
      },
      monacoWorkerFactory: (logger?: Logger) => {
        useWorkerFactory({
          workerLoaders,
          logger
        });
      }
    },
    languageClientConfigs: {
      configs: {
        sysml: {
          clientOptions: {
            documentSelector: ['sysml'],
            workspaceFolder: {
              uri: '/workspace',
              name: 'SysML Workspace'
            }
          },
          connection: {
            options: {
              $type: 'WorkerDirect',
              worker: params.worker
            },
            messageTransports: params.messageTransports
          }
        }
      }
    }
  };
};
