//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage_Feature_Rdef.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage_Feature_Rdef.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{				
	feature A{
		feature a2{}
	}
	feature inner{
		public import OuterPackage::*;
	}
	feature inner1 subsets inner {
		//XPECT warnings --> "Duplicate of inherited member name 'B' from OuterPackage" at "B"
		feature B redefines A {
			//XPECT linkedName at a1 --> OuterPackage.A.a1
			//* XPECT scope at a1 ---
			A, A.a1, A.a1.self, A.a1.that, A.a1.that.self, <PERSON>.self, A.that, A.that.self, B,
			<PERSON>.b, <PERSON>.b.self, B.b.that, B.b.that.self, B.self, B.that, B.that.self, OuterPackage.A,
			OuterPackage.A.a1, OuterPackage.A.a1.self, OuterPackage.A.a1.that, OuterPackage.A.a1.that.self,
			OuterPackage.A.self, OuterPackage.A.that, OuterPackage.A.that.self, OuterPackage.B,
			OuterPackage.B.b, OuterPackage.B.b.a1, OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that,
			OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self, OuterPackage.B.b.that, OuterPackage.B.b.that.self,
			OuterPackage.B.self, OuterPackage.B.that, OuterPackage.B.that.self, a1, a1.self, a1.that,
			a1.that.self, inner, inner.A, inner.A.a1, inner.A.a1.self, inner.A.a1.that,
			inner.A.a1.that.self, inner.A.self, inner.A.that, inner.A.that.self, inner.B, inner.B.b,
			inner.B.b.a1, inner.B.b.a1.self, inner.B.b.a1.that, inner.B.b.a1.that.self, inner.B.b.self,
			inner.B.b.that, inner.B.b.that.self, inner.B.self, inner.B.that, inner.B.that.self, inner.self,
			inner.that, inner.that.self, inner1, inner1.A, inner1.A.a1, inner1.A.a1.self,
			inner1.A.a1.that, inner1.A.a1.that.self, inner1.A.self, inner1.A.that, inner1.A.that.self,
			inner1.B, inner1.B.b, inner1.B.b.self, inner1.B.b.that, inner1.B.b.that.self,
			inner1.B.self, inner1.B.that, inner1.B.that.self, inner1.self, inner1.that, inner1.that.self,
			self, test.A, test.A.a2, test.A.a2.self, test.A.a2.that, test.A.a2.that.self,
			test.A.self, test.A.that, test.A.that.self, test.inner, test.inner.A, test.inner.A.a1,
			test.inner.A.a1.self, test.inner.A.a1.that, test.inner.A.a1.that.self, test.inner.A.self,
			test.inner.A.that, test.inner.A.that.self, test.inner.B, test.inner.B.b, test.inner.B.b.a1,
			test.inner.B.b.a1.self, test.inner.B.b.a1.that, test.inner.B.b.a1.that.self, test.inner.B.b.self,
			test.inner.B.b.that, test.inner.B.b.that.self, test.inner.B.self, test.inner.B.that,
			test.inner.B.that.self, test.inner.self, test.inner.that, test.inner.that.self, test.inner1,
			test.inner1.A, test.inner1.A.a1, test.inner1.A.a1.self, test.inner1.A.a1.that,
			test.inner1.A.a1.that.self, test.inner1.A.self, test.inner1.A.that, test.inner1.A.that.self, test.inner1.B,
			test.inner1.B.b, test.inner1.B.b.self, test.inner1.B.b.that, test.inner1.B.b.that.self,
			test.inner1.B.self, test.inner1.B.that, test.inner1.B.that.self, test.inner1.self,
			test.inner1.that, test.inner1.that.self, that, that.self
			--- */
			feature b redefines a1{}
		}
	}
}
