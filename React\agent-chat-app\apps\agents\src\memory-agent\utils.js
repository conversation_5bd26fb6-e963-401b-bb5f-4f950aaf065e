"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStoreFromConfigOrThrow = getStoreFromConfigOrThrow;
exports.splitModelAndProvider = splitModelAndProvider;
/**
 * Get the store from the configuration or throw an error.
 */
function getStoreFromConfigOrThrow(config) {
    if (!config.store) {
        throw new Error("Store not found in configuration");
    }
    return config.store;
}
/**
 * Split the fully specified model name into model and provider.
 */
function splitModelAndProvider(fullySpecifiedName) {
    var _a;
    var provider;
    var model;
    if (fullySpecifiedName.includes("/")) {
        _a = fullySpecifiedName.split("/", 2), provider = _a[0], model = _a[1];
    }
    else {
        model = fullySpecifiedName;
    }
    return { model: model, provider: provider };
}
