{
  "compileOnSave": false,
  "compilerOptions": {
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "Bundler",
    "lib": [
      "ES2022",
      "DOM",
    ],
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true,
    "inlineSources": false,
    "stripInternal": true,
    "strict": true,
    "strictPropertyInitialization": false,
    "importHelpers": true,
    "noImplicitReturns": true,
    "noUnusedParameters": true,
    "noUnusedLocals": true,
    // Disallow inconsistently-cased references to the same file
    "forceConsistentCasingInFileNames": true,
    "noImplicitOverride": true,
    "experimentalDecorators": true,
    "noPropertyAccessFromIndexSignature": true,
    "types": [
      "vscode"
    ],
    "skipLibCheck": true
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  },
  "include": [
    "src/**/*.ts"
  ],
  "exclude": [
    "dist",
    "node_modules",
    "src/server/**/*.ts"
  ]
}
