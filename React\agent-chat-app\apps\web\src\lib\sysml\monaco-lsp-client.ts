// Monaco Editor LSP Client for SysML v2
import { 
  MonacoLanguageClient, 
  CloseAction, 
  ErrorAction,
  MonacoServices,
  MessageTransports
} from 'monaco-languageclient';
import { 
  BrowserMessageReader, 
  BrowserMessageWriter 
} from 'vscode-languageserver-protocol/browser';
import { toSocket, WebSocketMessageReader, WebSocketMessageWriter } from 'vscode-ws-jsonrpc';

export class SysMLMonacoLSPClient {
  private client: MonacoLanguageClient | null = null;
  private worker: Worker | null = null;

  constructor() {
    this.initializeServices();
  }

  private initializeServices() {
    // 安装 Monaco 语言客户端服务
    MonacoServices.install();
  }

  async startLanguageClient(): Promise<void> {
    if (this.client) {
      return; // 已经启动
    }

    try {
      // 创建 Web Worker
      this.worker = new Worker(
        new URL('./langium-worker.ts', import.meta.url),
        { type: 'module' }
      );

      // 创建消息传输
      const messageReader = new BrowserMessageReader(this.worker);
      const messageWriter = new BrowserMessageWriter(this.worker);

      const messageTransports: MessageTransports = {
        reader: message<PERSON>eader,
        writer: messageWriter
      };

      // 创建语言客户端
      this.client = new MonacoLanguageClient({
        name: 'SysML v2 Language Client',
        clientOptions: {
          documentSelector: [
            { scheme: 'file', language: 'sysml' },
            { scheme: 'inmemory', language: 'sysml' }
          ],
          errorHandler: {
            error: () => ({ action: ErrorAction.Continue }),
            closed: () => ({ action: CloseAction.DoNotRestart })
          },
          workspaceFolder: {
            uri: 'file:///workspace',
            name: 'SysML Workspace'
          }
        },
        messageTransports
      });

      // 启动客户端
      await this.client.start();
      
      // 启动 Worker 中的语言服务器
      this.worker.postMessage({ type: 'start' });

      console.log('SysML Language Client started successfully');
    } catch (error) {
      console.error('Failed to start language client:', error);
      throw error;
    }
  }

  async stopLanguageClient(): Promise<void> {
    if (this.client) {
      await this.client.stop();
      this.client = null;
    }

    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
  }

  isRunning(): boolean {
    return this.client !== null;
  }

  getClient(): MonacoLanguageClient | null {
    return this.client;
  }
}

// 单例实例
let lspClient: SysMLMonacoLSPClient | null = null;

export function getSysMLLSPClient(): SysMLMonacoLSPClient {
  if (!lspClient) {
    lspClient = new SysMLMonacoLSPClient();
  }
  return lspClient;
}

export async function startSysMLLSP(): Promise<void> {
  const client = getSysMLLSPClient();
  if (!client.isRunning()) {
    await client.startLanguageClient();
  }
}

export async function stopSysMLLSP(): Promise<void> {
  if (lspClient) {
    await lspClient.stopLanguageClient();
    lspClient = null;
  }
}
