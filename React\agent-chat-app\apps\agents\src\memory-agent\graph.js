"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.graph = exports.builder = void 0;
// Main graph
var langgraph_1 = require("@langchain/langgraph");
var universal_1 = require("langchain/chat_models/universal");
var tools_js_1 = require("./tools.js");
var configuration_js_1 = require("./configuration.js");
var state_js_1 = require("./state.js");
var utils_js_1 = require("./utils.js");
function callModel(state, config) {
    return __awaiter(this, void 0, void 0, function () {
        var llm, store, configurable, memories, formatted, sys, tools, boundLLM, result;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0: return [4 /*yield*/, (0, universal_1.initChatModel)()];
                case 1:
                    llm = _b.sent();
                    store = (0, utils_js_1.getStoreFromConfigOrThrow)(config);
                    configurable = (0, configuration_js_1.ensureConfiguration)(config);
                    return [4 /*yield*/, store.search(["memories", configurable.userId], {
                            limit: 10,
                        })];
                case 2:
                    memories = _b.sent();
                    formatted = ((_a = memories === null || memories === void 0 ? void 0 : memories.map(function (mem) { return "[".concat(mem.key, "]: ").concat(JSON.stringify(mem.value)); })) === null || _a === void 0 ? void 0 : _a.join("\n")) || "";
                    if (formatted) {
                        formatted = "\n<memories>\n".concat(formatted, "\n</memories>");
                    }
                    sys = configurable.systemPrompt
                        .replace("{user_info}", formatted)
                        .replace("{time}", new Date().toISOString());
                    tools = (0, tools_js_1.initializeTools)(config);
                    boundLLM = llm.bind({
                        tools: tools,
                        tool_choice: "auto",
                    });
                    return [4 /*yield*/, boundLLM.invoke(__spreadArray([{ role: "system", content: sys }], state.messages, true), {
                            configurable: (0, utils_js_1.splitModelAndProvider)(configurable.model),
                        })];
                case 3:
                    result = _b.sent();
                    return [2 /*return*/, { messages: [result] }];
            }
        });
    });
}
function storeMemory(state, config) {
    return __awaiter(this, void 0, void 0, function () {
        var lastMessage, toolCalls, tools, upsertMemoryTool, savedMemories;
        var _this = this;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    lastMessage = state.messages[state.messages.length - 1];
                    toolCalls = lastMessage.tool_calls || [];
                    tools = (0, tools_js_1.initializeTools)(config);
                    upsertMemoryTool = tools[0];
                    return [4 /*yield*/, Promise.all(toolCalls.map(function (tc) { return __awaiter(_this, void 0, void 0, function () {
                            return __generator(this, function (_a) {
                                switch (_a.label) {
                                    case 0: return [4 /*yield*/, upsertMemoryTool.invoke(tc)];
                                    case 1: return [2 /*return*/, _a.sent()];
                                }
                            });
                        }); }))];
                case 1:
                    savedMemories = _a.sent();
                    return [2 /*return*/, { messages: savedMemories }];
            }
        });
    });
}
function routeMessage(state) {
    var _a;
    var lastMessage = state.messages[state.messages.length - 1];
    if ((_a = lastMessage.tool_calls) === null || _a === void 0 ? void 0 : _a.length) {
        return "store_memory";
    }
    return langgraph_1.END;
}
// Create the graph + all nodes
exports.builder = new langgraph_1.StateGraph({
    stateSchema: state_js_1.GraphAnnotation,
}, configuration_js_1.ConfigurationAnnotation)
    .addNode("call_model", callModel)
    .addNode("store_memory", storeMemory)
    .addEdge(langgraph_1.START, "call_model")
    .addConditionalEdges("call_model", routeMessage, (_a = {
        store_memory: "store_memory"
    },
    _a[langgraph_1.END] = langgraph_1.END,
    _a))
    .addEdge("store_memory", "call_model");
exports.graph = exports.builder.compile();
exports.graph.name = "MemoryAgent";
