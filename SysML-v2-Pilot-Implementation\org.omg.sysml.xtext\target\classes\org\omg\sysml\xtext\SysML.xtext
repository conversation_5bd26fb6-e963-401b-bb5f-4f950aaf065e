/*****************************************************************************
 * SysML 2 Pilot Implementation
 * Copyright (c) 2018-2025 Model Driven Solutions, Inc.
 * Copyright (c) 2018 IncQuery Labs Ltd.
 * Copyright (c) 2019 Maplesoft (Waterloo Maple, Inc.)
 * Copyright (c) 2019 Mgnite Inc.
 *    
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this program.  If not, see <https://www.gnu.org/licenses/>.
 *
 * @license LGPL-3.0-or-later <http://spdx.org/licenses/LGPL-3.0-or-later>
 * 
 * Contributors:
 *  <PERSON>, MDS
 *  Zoltan Kiss, IncQuery
 *  Balazs Grill, IncQuery
 *  Hisashi Miyashita, Maplesoft/Mgnite
 * 
 *****************************************************************************/

grammar org.omg.sysml.xtext.SysML with org.omg.kerml.expressions.xtext.KerMLExpressions

import "http://www.eclipse.org/emf/2002/Ecore" as Ecore
import "https://www.omg.org/spec/SysML/20250201" as SysML

/* ROOT NAMESPACE */

RootNamespace returns SysML::Namespace :
	{SysML::Namespace} PackageBodyElement*
;

/* BASIC ELEMENTS */

fragment Identification returns SysML::Element :
	  '<' declaredShortName = Name '>' ( declaredName = Name )?
	| declaredName = Name
;

fragment RelationshipBody returns SysML::Relationship :
	';' | '{' ( ownedRelationship += OwnedAnnotation )* '}'
;

/* DEPENDENCIES */

Dependency returns SysML::Dependency :
	( ownedRelationship += PrefixMetadataAnnotation )*
	'dependency' ( Identification? 'from' )?
    client += [SysML::Element|QualifiedName] ( ',' client += [SysML::Element|QualifiedName] )* 'to'
    supplier += [SysML::Element|QualifiedName] ( ',' supplier += [SysML::Element|QualifiedName] )*
    RelationshipBody
;

/* ANNOTATIONS */

Annotation returns SysML::Annotation :
	annotatedElement = [SysML::Element|QualifiedName]
;

OwnedAnnotation returns SysML::Annotation :
	ownedRelatedElement += AnnotatingElement
;

AnnotatingMember returns SysML::OwningMembership :
	ownedRelatedElement += AnnotatingElement 
;

AnnotatingElement returns SysML::AnnotatingElement :
	  Comment 
	| Documentation
	| TextualRepresentation
	| MetadataUsage
;

/* Comments */

Comment returns SysML::Comment :
	( 'comment' Identification? 
	  ('about' ownedRelationship += Annotation
	     ( ',' ownedRelationship += Annotation )* )? 
	)?
	( 'locale' locale = STRING_VALUE )?
	body = REGULAR_COMMENT
;

Documentation returns SysML::Documentation :
	'doc' Identification? 
	( 'locale' locale = STRING_VALUE )?
	body = REGULAR_COMMENT
;

/* Textual Representation */

TextualRepresentation returns SysML::TextualRepresentation :
    ( 'rep' Identification? )?
    'language' language = STRING_VALUE 
    body = REGULAR_COMMENT
;

/* Metadata */

MetadataKeyword :
	'metadata'
;

MetadataDefKeyword :
	MetadataKeyword 'def'
;

MetadataUsageKeyword :
	MetadataKeyword | '@'
;

MetadataDefinition returns SysML::MetadataDefinition :
	( isAbstract ?= 'abstract')? 
	DefinitionExtensionKeyword* MetadataDefKeyword 
	Definition
;

PrefixMetadataAnnotation returns SysML::Annotation :
	'#' ownedRelatedElement += PrefixMetadataUsage
;

PrefixMetadataMember returns SysML::OwningMembership :
	'#' ownedRelatedElement += PrefixMetadataUsage 
;

PrefixMetadataUsage returns SysML::MetadataUsage :
	ownedRelationship += MetadataTyping
//	( ownedRelationship += MetadataBodyUsageMember )*
;

MetadataUsage returns SysML::MetadataUsage :
	UsageExtensionKeyword*
	MetadataUsageKeyword MetadataUsageDeclaration 
	( 'about' ownedRelationship += Annotation 
		( ',' ownedRelationship += Annotation )*
	)?
	MetadataBody
;

fragment MetadataUsageDeclaration returns SysML::MetadataUsage :
	( Identification? DefinedByKeyword )? ownedRelationship += MetadataTyping
;

MetadataTyping returns SysML::FeatureTyping :
	type = [SysML::Metaclass | QualifiedName]
;

fragment MetadataBody returns SysML::Usage :
	  ';' 
	| '{' ( ownedRelationship += DefinitionMember 
		  | ownedRelationship += MetadataBodyUsageMember 
		  | ownedRelationship += AliasMember
		  | ownedRelationship += Import
	      )* 
	  '}'
;

MetadataBodyUsageMember returns SysML::FeatureMembership :
	ownedRelatedElement += MetadataBodyUsage
;

MetadataBodyUsage returns SysML::ReferenceUsage :
	'ref'? ( ':>>' | 'redefines' )? ownedRelationship += OwnedRedefinition 
	FeatureSpecializationPart? ValuePart?
	MetadataBody
;

/* PACKAGES */

/* Packages */

Package returns SysML::Package :
	( ownedRelationship += PrefixMetadataMember )*
	PackageDeclaration PackageBody 
;

LibraryPackage returns SysML::LibraryPackage :
	( isStandard ?= 'standard' )? 'library'
	( ownedRelationship += PrefixMetadataMember )*
	PackageDeclaration PackageBody
;

fragment PackageDeclaration returns SysML::Package :
	'package' Identification?
;

fragment PackageBody returns SysML::Package :
	 ';' 
	| '{' ( // Note: PackageBodyElement is expanded here to avoid
			// infinite loops in the incremental parser.
		    ownedRelationship += PackageMember
		  | ownedRelationship += ElementFilterMember
		  | ownedRelationship += AliasMember
		  | ownedRelationship += Import )* 
	  '}'
;

/* Package Bodies */

fragment PackageBodyElement returns SysML::Namespace :
	  ownedRelationship += PackageMember
	| ownedRelationship += ElementFilterMember
	| ownedRelationship += AliasMember
	| ownedRelationship += Import
;

fragment MemberPrefix returns SysML::Membership :
    ( visibility = VisibilityIndicator )?
;

PackageMember returns SysML::OwningMembership : 
	MemberPrefix		
	( ownedRelatedElement += DefinitionElement
	| ownedRelatedElement += UsageElement 
	)
;

ElementFilterMember returns SysML::ElementFilterMembership :
	MemberPrefix
	'filter' ownedRelatedElement += OwnedExpression ';'
;

AliasMember returns SysML::Membership :
	MemberPrefix
	'alias' ( '<' memberShortName = Name '>' )? ( memberName = Name )?
	'for' memberElement = [SysML::Element|QualifiedName]
	RelationshipBody
;

fragment ImportPrefix returns SysML::Import :
	visibility = VisibilityIndicator  
	'import' ( isImportAll ?= 'all' )?
;

Import returns SysML::Import :
	( MembershipImport | NamespaceImport )
	RelationshipBody
;

MembershipImport returns SysML::MembershipImport :
	ImportPrefix ImportedMembership
;

fragment ImportedMembership returns SysML::MembershipImport :
	importedMembership = [SysML::Membership|QualifiedName]
	( '::' isRecursive ?= '**' )?	
;

NamespaceImport returns SysML::NamespaceImport :
	ImportPrefix 
	( ImportedNamespace
	| ownedRelatedElement += FilterPackage
	)
;

fragment ImportedNamespace returns SysML::NamespaceImport :
	importedNamespace = [SysML::Namespace|QualifiedName] '::' '*'
	( '::' isRecursive ?= '**' )?
;

FilterPackage returns SysML::Package :
	ownedRelationship += FilterPackageImport
	( ownedRelationship += FilterPackageMember )+
;

FilterPackageImport returns SysML::Import :
	 FilterPackageMembershipImport | FilterPackageNamespaceImport
;

FilterPackageMembershipImport returns SysML::MembershipImport :
	ImportedMembership
;

FilterPackageNamespaceImport returns SysML::NamespaceImport :
	ImportedNamespace
;

FilterPackageMember returns SysML::ElementFilterMembership :
	visibility = FilterPackageMemberVisibility ownedRelatedElement += OwnedExpression ']'
;

enum FilterPackageMemberVisibility returns SysML::VisibilityKind :
	private = '['
;

enum VisibilityIndicator returns SysML::VisibilityKind:
	public = 'public' | private = 'private'| protected = 'protected'
;

/* Package Elements */

DefinitionElement returns SysML::Element :
	  Package
	| LibraryPackage
	| AnnotatingElement
	| Dependency
	| AttributeDefinition
	| EnumerationDefinition
	| OccurrenceDefinition
	| IndividualDefinition
	| ItemDefinition
	| MetadataDefinition
	| PartDefinition
	| ConnectionDefinition
	| FlowDefinition
	| InterfaceDefinition
	| AllocationDefinition
	| PortDefinition
	| ActionDefinition
	| CalculationDefinition
	| StateDefinition
	| ConstraintDefinition
	| RequirementDefinition
	| ConcernDefinition
	| CaseDefinition
	| AnalysisCaseDefinition
	| VerificationCaseDefinition
	| UseCaseDefinition
	| ViewDefinition
	| ViewpointDefinition
	| RenderingDefinition
	| ExtendedDefinition
;
	
UsageElement returns SysML::Usage :
	  NonOccurrenceUsageElement 
	| OccurrenceUsageElement
;

/* CLASSIFIERS */

/* Subclassification */

fragment SubclassificationPart returns SysML::Classifier :
	SpecializesKeyword ownedRelationship += OwnedSubclassification 
	( ',' ownedRelationship += OwnedSubclassification )*
;

SpecializesKeyword :
	':>' | 'specializes'
;

OwnedSubclassification returns SysML::Subclassification:
	superclassifier = [SysML::Classifier | QualifiedName]
;

/* FEATURES */

/* Features */

fragment FeatureDeclaration returns SysML::Feature :
	  Identification FeatureSpecializationPart?
	| FeatureSpecializationPart
;

fragment FeatureSpecializationPart returns SysML::Feature :
	  ( -> FeatureSpecialization )+ MultiplicityPart? FeatureSpecialization*
	| MultiplicityPart FeatureSpecialization*
;

fragment MultiplicityPart returns SysML::Feature :
	  ownedRelationship += OwnedMultiplicity 
	| ( ownedRelationship += OwnedMultiplicity )?
	  ( isOrdered ?= 'ordered' isNonunique ?= 'nonunique'?
	  | isNonunique ?= 'nonunique' isOrdered ?= 'ordered'?
	  )
;

fragment FeatureSpecialization returns SysML::Feature :
    Typings | Subsettings | References | Crosses | Redefinitions
;

fragment Typings returns SysML::Feature :
	TypedBy ( ',' ownedRelationship += FeatureTyping )*
;

fragment TypedBy returns SysML::Feature :
	DefinedByKeyword ownedRelationship += FeatureTyping
;

DefinedByKeyword :
	':' | 'defined' 'by'
;

fragment Subsettings returns SysML::Feature :
	Subsets ( ',' ownedRelationship += OwnedSubsetting )*
;

fragment Subsets returns SysML::Feature :
	SubsetsKeyword ownedRelationship += OwnedSubsetting 
;

SubsetsKeyword :
	':>' | 'subsets'
;

fragment References returns SysML::Feature :
	ReferencesKeyword ownedRelationship += OwnedReferenceSubsetting
;

ReferencesKeyword :
	'::>' | 'references'
;

fragment Crosses returns SysML::Feature :
    CrossesKeyword ownedRelationship += OwnedCrossSubsetting
;

CrossesKeyword :
	'=>' | 'crosses'
;

fragment Redefines returns SysML::Feature :
	RedefinesKeyword ownedRelationship += OwnedRedefinition
;

RedefinesKeyword :
	':>>' | 'redefines'
;

/* Feature Typing, Subsetting and Redefinition */

FeatureTyping returns SysML::FeatureTyping :
	OwnedFeatureTyping | ConjugatedPortTyping
;

OwnedFeatureTyping returns SysML::FeatureTyping :
	  type = [SysML::Type | QualifiedName]
	| ownedRelatedElement += OwnedFeatureChain
;

OwnedSubsetting returns SysML::Subsetting :
	  subsettedFeature = [SysML::Feature|QualifiedName]
	| ownedRelatedElement += OwnedFeatureChain
;

OwnedReferenceSubsetting returns SysML::ReferenceSubsetting :
	  referencedFeature = [SysML::Feature|QualifiedName]
	| ownedRelatedElement += OwnedFeatureChain
;

OwnedCrossSubsetting returns SysML::CrossSubsetting :
      crossedFeature = [SysML::Feature|QualifiedName]
    | ownedRelatedElement += OwnedFeatureChain
;

fragment Redefinitions returns SysML::Feature :
	Redefines ( ',' ownedRelationship += OwnedRedefinition )*
;

OwnedRedefinition returns SysML::Redefinition:
	  redefinedFeature = [SysML::Feature | QualifiedName]
	| ownedRelatedElement += OwnedFeatureChain
;

/* Multiplicity */

OwnedMultiplicity returns SysML::OwningMembership :
	ownedRelatedElement += MultiplicityRange 
;

MultiplicityRange returns SysML::MultiplicityRange :
	// TODO: Allow general expressions for bounds. (Causes LL parsing issues.)
	'[' ownedRelationship += MultiplicityExpressionMember
	      ( '..' ownedRelationship += MultiplicityExpressionMember )? ']'
;

MultiplicityExpressionMember returns SysML::OwningMembership :
	ownedRelatedElement += ( LiteralExpression | FeatureReferenceExpression ) 
;

/* DEFINITION AND USAGE */

/* Definitions */

fragment BasicDefinitionPrefix returns SysML::Definition :
	isAbstract ?= 'abstract' | isVariation ?= 'variation'
;

fragment DefinitionExtensionKeyword returns SysML::Definition :
	ownedRelationship += PrefixMetadataMember
;

fragment DefinitionPrefix returns SysML::Definition :
	BasicDefinitionPrefix? DefinitionExtensionKeyword*
;

fragment Definition returns SysML::Definition :
	DefinitionDeclaration DefinitionBody
;

fragment DefinitionDeclaration returns SysML::Definition :	
	Identification? SubclassificationPart?
;

fragment DefinitionBody returns SysML::Type :
	';' | '{' DefinitionBodyItem* '}'
;

fragment DefinitionBodyItem returns SysML::Type :
	  ownedRelationship += DefinitionMember 
	| ownedRelationship += VariantUsageMember
	| ownedRelationship += NonOccurrenceUsageMember
	| ( ownedRelationship += EmptySuccessionMember )?
	  ownedRelationship += OccurrenceUsageMember	
	| ownedRelationship += AliasMember
	| => ownedRelationship += Import
;

DefinitionMember returns SysML::OwningMembership : 
	MemberPrefix 
	ownedRelatedElement += DefinitionElement 
;

VariantUsageMember returns SysML::VariantMembership :
	MemberPrefix 'variant' 
	ownedRelatedElement += VariantUsageElement 
;

NonOccurrenceUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += NonOccurrenceUsageElement 
;

OccurrenceUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += OccurrenceUsageElement 
;

StructureUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += StructureUsageElement 
;

BehaviorUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += BehaviorUsageElement 
;

/* Usages */

enum FeatureDirection returns SysML::FeatureDirectionKind:
	in = 'in' | out = 'out' | inout = 'inout'
;

fragment RefPrefix returns SysML::Usage :
    ( direction = FeatureDirection )?
    ( isDerived ?= 'derived' )?
    ( isAbstract ?= 'abstract' | isVariation ?= 'variation')?
    ( isConstant ?= 'constant' )?
;

fragment BasicUsagePrefix returns SysML::Usage :
	RefPrefix
	( isReference ?= 'ref' )?
;

fragment EndUsagePrefix returns SysML::Usage :
	isEnd ?= 'end' ( ownedRelationship += OwnedCrossFeatureMember )?
;

fragment UnextendedUsagePrefix returns SysML::Usage :
    EndUsagePrefix | BasicUsagePrefix
;

fragment UsageExtensionKeyword returns SysML::Usage :
	ownedRelationship += PrefixMetadataMember
;

fragment UsagePrefix returns SysML::Usage :
	UnextendedUsagePrefix UsageExtensionKeyword*
;

OwnedCrossFeatureMember returns SysML::OwningMembership :
    ownedRelatedElement += OwnedCrossFeature
;

OwnedCrossFeature returns SysML::ReferenceUsage :
    BasicUsagePrefix UsageDeclaration
;

fragment Usage returns SysML::Usage :
	UsageDeclaration? UsageCompletion
;

fragment UsageDeclaration returns SysML::Usage :
	  FeatureDeclaration
;

fragment UsageCompletion returns SysML::Usage :
	ValuePart? UsageBody
;

fragment UsageBody returns SysML::Usage :
	DefinitionBody
;

fragment ValuePart returns SysML::Feature :
	  ownedRelationship += FeatureValue
;

FeatureValue returns SysML::FeatureValue :
	( '=' 
	| isInitial ?= ':='
	| isDefault ?= 'default' ( '=' | isInitial ?= ':=' )?
	)
	ownedRelatedElement += OwnedExpression
;

/* Reference Usages */

ReferenceKeyword :
	'ref'
;

ReferenceUsageKeyword :
	ReferenceKeyword
;

DefaultReferenceUsage returns SysML::ReferenceUsage :
	( isEnd ?= 'end' )? RefPrefix 
	UsageDeclaration ValuePart? UsageBody
;

ReferenceUsage returns SysML::ReferenceUsage :
	( EndUsagePrefix | RefPrefix) 
	ReferenceUsageKeyword Usage
;

VariantReference returns SysML::ReferenceUsage :
	ownedRelationship += OwnedReferenceSubsetting 
	FeatureSpecialization* UsageBody
;

/* Body Elements */

NonOccurrenceUsageElement returns SysML::Usage :
	  DefaultReferenceUsage
	| ReferenceUsage
	| AttributeUsage
	| EnumerationUsage
	| BindingConnectorAsUsage
	| SuccessionAsUsage
	| ExtendedUsage
;

OccurrenceUsageElement returns SysML::Usage :
	StructureUsageElement | BehaviorUsageElement
;

StructureUsageElement returns SysML::Usage :
	  OccurrenceUsage
	| IndividualUsage
	| PortionUsage
	| EventOccurrenceUsage
	| ItemUsage
	| PartUsage
	| ViewUsage
	| RenderingUsage
	| PortUsage
	| ConnectionUsage
	| InterfaceUsage
	| AllocationUsage
	| Message
	| FlowUsage
	| SuccessionFlowUsage
;

BehaviorUsageElement returns SysML::Usage :
	  ActionUsage
	| CalculationUsage
	| StateUsage
	| ConstraintUsage
	| RequirementUsage
	| ConcernUsage
	| CaseUsage
	| AnalysisCaseUsage
	| VerificationCaseUsage
	| UseCaseUsage
	| ViewpointUsage
	| PerformActionUsage
	| ExhibitStateUsage
	| IncludeUseCaseUsage
	| AssertConstraintUsage
	| SatisfyRequirementUsage
;

VariantUsageElement returns SysML::Usage :
	  VariantReference
	| ReferenceUsage
	| AttributeUsage
	| BindingConnectorAsUsage
	| SuccessionAsUsage
	| OccurrenceUsage
	| IndividualUsage
	| PortionUsage
	| EventOccurrenceUsage
	| ItemUsage
	| PartUsage
	| ViewUsage
	| RenderingUsage
	| PortUsage
	| ConnectionUsage
	| InterfaceUsage
	| AllocationUsage
	| Message
	| FlowUsage
	| SuccessionFlowUsage
	| BehaviorUsageElement
;

/* EXTENSION */

ExtendedDefinition returns SysML::Definition :
	BasicDefinitionPrefix? DefinitionExtensionKeyword+ 'def' Definition
;

ExtendedUsage returns SysML::Usage :
	UnextendedUsagePrefix UsageExtensionKeyword+ Usage
;

/* ATTRIBUTES */

AttributeKeyword :
	'attribute'
;

AttributeDefKeyword :
	AttributeKeyword 'def'
;

AttributeUsageKeyword :
	AttributeKeyword
;

AttributeDefinition returns SysML::AttributeDefinition :
	DefinitionPrefix AttributeDefKeyword Definition
;

AttributeUsage returns SysML::AttributeUsage :
	UsagePrefix AttributeUsageKeyword Usage
;

/* ENUMERATIONS */

EnumerationKeyword :
	'enum'
;
EnumerationDefKeyword :
	EnumerationKeyword 'def'
;

EnumerationUsageKeyword :
	EnumerationKeyword
;

EnumerationDefinition returns SysML::EnumerationDefinition :
	DefinitionExtensionKeyword*
	EnumerationDefKeyword DefinitionDeclaration EnumerationBody
;

fragment EnumerationBody returns SysML::EnumerationDefinition :
	  ';' 
	| '{' ( ownedRelationship += AnnotatingMember
		  | => ownedRelationship += EnumerationUsageMember 
		  )* 
	  '}'
;

EnumerationUsageMember returns SysML::VariantMembership :
	MemberPrefix ownedRelatedElement += EnumeratedValue 
;

EnumeratedValue returns SysML::EnumerationUsage :
	UsageExtensionKeyword* EnumerationUsageKeyword? Usage
;

EnumerationUsage returns SysML::EnumerationUsage :
	UsagePrefix EnumerationUsageKeyword Usage
;

/* OCCURRENCES */

/* Occurrence Definitions */

OccurrenceKeyword :
	'occurrence'
;

OccurrenceDefKeyword :
	OccurrenceKeyword 'def'
;

fragment OccurrenceDefinitionPrefix returns SysML::OccurrenceDefinition :
	BasicDefinitionPrefix?
	( isIndividual ?= 'individual' 
	  ownedRelationship += EmptyMultiplicityMember
	)?
	DefinitionExtensionKeyword*
;

OccurrenceDefinition returns SysML::OccurrenceDefinition :
	OccurrenceDefinitionPrefix OccurrenceDefKeyword Definition
;

IndividualDefinition returns SysML::OccurrenceDefinition :
	BasicDefinitionPrefix? isIndividual ?= 'individual' 
	ownedRelationship += EmptyMultiplicityMember
	DefinitionExtensionKeyword* 'def' Definition
;

EmptyMultiplicityMember returns SysML::OwningMembership :
        ownedRelatedElement += EmptyMultiplicity
;

EmptyMultiplicity returns SysML::Multiplicity :
	{SysML::Multiplicity}
;

/* Occurrence Usages */

OccurrenceUsageKeyword :
	OccurrenceKeyword
;

fragment OccurrenceUsagePrefix returns SysML::OccurrenceUsage :
	( EndUsagePrefix
	| BasicUsagePrefix
	  ( isIndividual ?= 'individual' )?
	  ( portionKind = PortionKind )?
	)
	UsageExtensionKeyword*
;

OccurrenceUsage returns SysML::OccurrenceUsage :
	OccurrenceUsagePrefix OccurrenceUsageKeyword Usage
;

IndividualUsage returns SysML::OccurrenceUsage :
	BasicUsagePrefix isIndividual ?= 'individual' 
	UsageExtensionKeyword* Usage
;

PortionUsage returns SysML::OccurrenceUsage :
	BasicUsagePrefix ( isIndividual ?= 'individual' )?
	portionKind = PortionKind
	UsageExtensionKeyword* Usage
;

enum PortionKind returns SysML::PortionKind :
	// Literal keywords identified explicitly so they can be found by syntax highlighting script
	snapshot = 'snapshot' | timeslice = 'timeslice'
;

EventOccurrenceUsage returns SysML::EventOccurrenceUsage :
	 OccurrenceUsagePrefix 'event'
	( ownedRelationship += OwnedReferenceSubsetting FeatureSpecializationPart?
   	| OccurrenceUsageKeyword UsageDeclaration? )
    UsageCompletion
;

/* Occurrence Successions */

EmptySuccessionMember returns SysML::FeatureMembership :
	ownedRelatedElement += EmptySuccession 
;

EmptySuccession returns SysML::SuccessionAsUsage :
	'then' ownedRelationship += MultiplicitySourceEndMember 
	ownedRelationship += EmptyTargetEndMember
;

MultiplicitySourceEndMember returns SysML::EndFeatureMembership :
	ownedRelatedElement += MultiplicitySourceEnd 
;

MultiplicitySourceEnd returns SysML::ReferenceUsage :
	{SysML::ReferenceUsage} ( ownedRelationship += OwnedMultiplicity )?
;

EmptyTargetEndMember returns SysML::EndFeatureMembership :
	ownedRelatedElement += EmptyTargetEnd 
;

EmptyTargetEnd returns SysML::ReferenceUsage :
	{SysML::ReferenceUsage}
;

/* ITEMS */

ItemKeyword :
	'item'
;

ItemDefKeyword :
	ItemKeyword 'def'
;

ItemUsageKeyword :
	ItemKeyword
;

ItemDefinition returns SysML::ItemDefinition :
	OccurrenceDefinitionPrefix ItemDefKeyword Definition
;

ItemUsage returns SysML::ItemUsage :
	OccurrenceUsagePrefix ItemUsageKeyword Usage
;

/* PARTS */

PartKeyword :
	'part'
;

PartDefKeyword :
	PartKeyword 'def'
;

PartUsageKeyword :
	PartKeyword
;

PartDefinition returns SysML::PartDefinition :
	OccurrenceDefinitionPrefix PartDefKeyword Definition
;

PartUsage returns SysML::PartUsage :
	OccurrenceUsagePrefix PartUsageKeyword Usage
;

/* PORTS */

/* Port Definitions */

PortKeyword :
	'port'
;

PortDefKeyword :
	PortKeyword 'def'
;

PortDefinition returns SysML::PortDefinition :
	DefinitionPrefix PortDefKeyword Definition
	ownedRelationship += ConjugatedPortDefinitionMember
;

ConjugatedPortDefinitionMember returns SysML::OwningMembership :
	ownedRelatedElement += ConjugatedPortDefinition 
;

ConjugatedPortDefinition returns SysML::ConjugatedPortDefinition :
	ownedRelationship += PortConjugation
;

PortConjugation returns SysML::PortConjugation :
	{SysML::PortConjugation}
;

ConjugatedPortTyping returns SysML::ConjugatedPortTyping :
	conjugatedPortDefinition = [SysML::ConjugatedPortDefinition | ConjugatedQualifiedName]
;

ConjugatedQualifiedName :
	'~' QualifiedName
;

/* Port Usages */

PortUsageKeyword :
	PortKeyword
;

PortUsage returns SysML::PortUsage :
	OccurrenceUsagePrefix PortUsageKeyword Usage
;

/* CONNECTIONS */

/* Connectors */

ConnectorEndMember returns SysML::EndFeatureMembership :
	ownedRelatedElement += ConnectorEnd 
;

ConnectorEnd returns SysML::ReferenceUsage :
	( ownedRelationship += OwnedCrossMultiplicityMember )?
	( declaredName = Name ReferencesKeyword )? 
	ownedRelationship += OwnedReferenceSubsetting
;

OwnedCrossMultiplicityMember returns SysML::OwningMembership :
	ownedRelatedElement += OwnedCrossMultiplicity
;

OwnedCrossMultiplicity returns SysML::Feature :
	ownedRelationship += OwnedMultiplicity
;

/* Binding Connectors */

BindingKeyword :
	'binding'
;

BindingConnectorAsUsage returns SysML::BindingConnectorAsUsage :
	UsagePrefix ( BindingKeyword UsageDeclaration? )?
	'bind' ownedRelationship += ConnectorEndMember 
	'=' ownedRelationship += ConnectorEndMember 
	DefinitionBody	
;

/* Successions */

SuccessionKeyword:
	'succession'
;

SuccessionAsUsage returns SysML::SuccessionAsUsage :
	UsagePrefix ( SuccessionKeyword UsageDeclaration? )? 
	'first' ownedRelationship += ConnectorEndMember 
	'then' ownedRelationship += ConnectorEndMember 
	DefinitionBody
;

/* Connection Definitions */

ConnectionKeyword :
	'connection'
;

ConnectionDefKeyword :
	ConnectionKeyword 'def' 
;

ConnectionDefinition returns SysML::ConnectionDefinition :
	OccurrenceDefinitionPrefix ConnectionDefKeyword Definition
;

/* Connection Usages */

ConnectorKeyword :
	'connect'
;

ConnectionUsageKeyword :
	ConnectionKeyword
;

ConnectionUsage returns SysML::ConnectionUsage :	 
	OccurrenceUsagePrefix 
	( ConnectionUsageKeyword UsageDeclaration? ValuePart?
	  ( ConnectorKeyword ConnectorPart )? 
	| ConnectorKeyword ConnectorPart
	) UsageBody
;

fragment ConnectorPart returns SysML::ConnectionUsage :
	BinaryConnectorPart | NaryConnectorPart
;

fragment BinaryConnectorPart returns SysML::Connector :
	ownedRelationship += ConnectorEndMember 'to' 
	ownedRelationship += ConnectorEndMember
;

fragment NaryConnectorPart returns SysML::Connector :
	'(' ownedRelationship += ConnectorEndMember ',' 
	    ownedRelationship += ConnectorEndMember
	    ( ',' ownedRelationship += ConnectorEndMember )* ')'
;

EmptySourceEndMember returns SysML::EndFeatureMembership :
	ownedRelatedElement += EmptySourceEnd 
;

EmptySourceEnd returns SysML::ReferenceUsage :
	{SysML::ReferenceUsage}
;

/* INTERFACES */

/* Interface Definitions */

InterfaceKeyword :
	'interface'
;

InterfaceDefKeyword :
	InterfaceKeyword 'def'
;

InterfaceDefinition returns SysML::InterfaceDefinition :
	OccurrenceDefinitionPrefix InterfaceDefKeyword DefinitionDeclaration InterfaceBody
;

fragment InterfaceBody returns SysML::Type :
	';' | '{' InterfaceBodyItem* '}'
;

fragment InterfaceBodyItem returns SysML::Type :
	  ownedRelationship += DefinitionMember 
	| ownedRelationship += VariantUsageMember
	| ownedRelationship += InterfaceNonOccurrenceUsageMember
	| ( ownedRelationship += EmptySuccessionMember )?
	  ownedRelationship += InterfaceOccurrenceUsageMember
	| ownedRelationship += AliasMember
	| => ownedRelationship += Import
;

InterfaceNonOccurrenceUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += InterfaceNonOccurrenceUsageElement 
;

InterfaceNonOccurrenceUsageElement returns SysML::Usage :
	  ReferenceUsage
	| AttributeUsage
	| EnumerationUsage
	| BindingConnectorAsUsage
	| SuccessionAsUsage
;

InterfaceOccurrenceUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += InterfaceOccurrenceUsageElement 
;

InterfaceOccurrenceUsageElement returns SysML::Usage :
	DefaultInterfaceEnd | StructureUsageElement | BehaviorUsageElement
;

DefaultInterfaceEnd returns SysML::PortUsage :
    isEnd ?= 'end' Usage
;

/* Interface Usages */

InterfaceUsageKeyword :
	InterfaceKeyword
;

InterfaceUsage returns SysML::InterfaceUsage :	 
	OccurrenceUsagePrefix InterfaceUsageKeyword InterfaceUsageDeclaration InterfaceBody
;

fragment InterfaceUsageDeclaration returns SysML::InterfaceUsage :
	UsageDeclaration? ( ConnectorKeyword InterfacePart )? | InterfacePart
;

fragment InterfacePart returns SysML::ConnectionUsage :
	BinaryInterfacePart | NaryInterfacePart
;

fragment BinaryInterfacePart returns SysML::Connector :
	ownedRelationship += InterfaceEndMember 'to' 
	ownedRelationship += InterfaceEndMember
;

fragment NaryInterfacePart returns SysML::Connector :
	'(' ownedRelationship += InterfaceEndMember ',' 
	    ownedRelationship += InterfaceEndMember
	    ( ',' ownedRelationship += InterfaceEndMember )* ')'
;

InterfaceEndMember returns SysML::EndFeatureMembership :
	ownedRelatedElement += InterfaceEnd 
;

InterfaceEnd returns SysML::PortUsage :
	( ownedRelationship += OwnedCrossMultiplicityMember )?
	( declaredName = Name ReferencesKeyword )? 
	ownedRelationship += OwnedReferenceSubsetting
;

/* ALLOCATIONS */

/* Allocation Definitions */

AllocationKeyword :
	'allocation'
;

AllocationDefKeyword :
	AllocationKeyword 'def'
;

AllocationDefinition returns SysML::AllocationDefinition :
	OccurrenceDefinitionPrefix AllocationDefKeyword Definition
;

/* AllocationUsage Usages */

AllocationUsageKeyword :
	AllocationKeyword
;

AllocateKeyword :
	'allocate'
;

AllocationUsage returns SysML::AllocationUsage :	 
	OccurrenceUsagePrefix AllocationUsageDeclaration UsageBody
;

fragment AllocationUsageDeclaration returns SysML::AllocationUsage :
	    AllocationUsageKeyword UsageDeclaration? ( AllocateKeyword ConnectorPart )?
	  | AllocateKeyword ConnectorPart
;

/* FLOWS */

/* Flow Definitions */

FlowKeyword :
	'flow'
;

FlowDefKeyword :
	FlowKeyword 'def'
;

FlowDefinition returns SysML::FlowDefinition :
	OccurrenceDefinitionPrefix FlowDefKeyword Definition
;

/* Messages */

MessageKeyword :
	'message'
;

Message returns SysML::FlowUsage :
	OccurrenceUsagePrefix MessageKeyword MessageDeclaration DefinitionBody
;

fragment MessageDeclaration returns SysML::FlowUsage :
	  UsageDeclaration? ValuePart?
      ( 'of' ownedRelationship += PayloadFeatureMember )?
      ( 'from' ownedRelationship += MessageEventMember 
        'to' ownedRelationship += MessageEventMember
      )?
    | ownedRelationship += MessageEventMember 'to' 
	  ownedRelationship += MessageEventMember
;

MessageEventMember returns SysML::ParameterMembership :
	ownedRelatedElement += MessageEvent 
;

MessageEvent returns SysML::EventOccurrenceUsage :
	ownedRelationship += OwnedReferenceSubsetting
;

/* Flow Usages */

FlowUsage returns SysML::FlowUsage :
	OccurrenceUsagePrefix FlowKeyword 
	FlowDeclaration DefinitionBody
;

SuccessionFlowKeyword :
	SuccessionKeyword FlowKeyword
;

SuccessionFlowUsage returns SysML::SuccessionFlowUsage :
	OccurrenceUsagePrefix SuccessionFlowKeyword 
	FlowDeclaration DefinitionBody
;

fragment FlowDeclaration returns SysML::FlowUsage :
	  UsageDeclaration? ValuePart?
      ( 'of'  ownedRelationship += PayloadFeatureMember )?
      ( 'from' ownedRelationship += FlowEndMember 
        'to' ownedRelationship += FlowEndMember )?
    | ownedRelationship += FlowEndMember 'to' 
	  ownedRelationship += FlowEndMember
;

/* Flow Members */

PayloadFeatureMember returns SysML::FeatureMembership :
	ownedRelatedElement += PayloadFeature 
;

PayloadFeature returns SysML::PayloadFeature :
	  Payload
;

fragment Payload returns SysML::Feature :
	  Identification? PayloadFeatureSpecializationPart ValuePart?
	| Identification? ValuePart
    | ownedRelationship += OwnedFeatureTyping ( ownedRelationship += OwnedMultiplicity )?
    | ownedRelationship += OwnedMultiplicity ownedRelationship += OwnedFeatureTyping
;

fragment PayloadFeatureSpecializationPart returns SysML::Feature :
	  ( -> FeatureSpecialization )+ MultiplicityPart? FeatureSpecialization*
	| MultiplicityPart FeatureSpecialization+
;

FlowEndMember returns SysML::EndFeatureMembership :
	ownedRelatedElement += FlowEnd 
;

FlowEnd returns SysML::FlowEnd :
	( ownedRelationship += FlowEndSubsetting )?
	ownedRelationship += FlowFeatureMember
;

FlowEndSubsetting returns SysML::ReferenceSubsetting :
	  referencedFeature = [SysML::Feature | QualifiedName] '.'
	| ownedRelatedElement += FeatureChainPrefix
;

FeatureChainPrefix returns SysML::Feature :
	( ownedRelationship += OwnedFeatureChaining '.' )+
	ownedRelationship += OwnedFeatureChaining '.'
;

FlowFeatureMember returns SysML::FeatureMembership :
	ownedRelatedElement += FlowFeature 
;

FlowFeature returns SysML::ReferenceUsage :
	ownedRelationship += FlowRedefinition
;

FlowRedefinition returns SysML::Redefinition :
	redefinedFeature = [SysML::Feature|QualifiedName]
;

/* ACTION */

/* Action Definitions */

ActionKeyword :
	'action'
;

ActionDefKeyword :
	ActionKeyword 'def'
;

ActionDefinition returns SysML::ActionDefinition :
	OccurrenceDefinitionPrefix ActionDefKeyword DefinitionDeclaration ActionBody
;

fragment ActionBody returns SysML::Type :
	  ';' 
	  // Note: Using a syntactic predicate here seems to avoid a possible infinite loop
	  // while incrementally parsing.
	| '{' => ActionBodyItem* '}' 
;

fragment ActionBodyItem returns SysML::Type :
	  ownedRelationship += Import
	| ownedRelationship += AliasMember
	| ownedRelationship += DefinitionMember 
	| ownedRelationship += VariantUsageMember
	| ownedRelationship += NonOccurrenceUsageMember
	| ( ownedRelationship += EmptySuccessionMember )?
	  ownedRelationship += StructureUsageMember
	| ownedRelationship += InitialNodeMember
	  ( => ownedRelationship += TargetSuccessionMember )*
	| ( ownedRelationship += EmptySuccessionMember )? 
	  ownedRelationship += ( BehaviorUsageMember | ActionNodeMember )
	  ( => ownedRelationship += TargetSuccessionMember )*
	| ownedRelationship += GuardedSuccessionMember
;

InitialNodeMember returns SysML::Membership :
	MemberPrefix 'first' memberElement = [SysML::Feature|QualifiedName]
	RelationshipBody	
;

ActionNodeMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += ActionNode 
;

TargetSuccessionMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += ActionTargetSuccession 
;

GuardedSuccessionMember returns SysML::FeatureMembership :
	ownedRelatedElement += GuardedSuccession 
;

/* Action Usages */

ActionUsageKeyword :
	ActionKeyword
;

ActionUsage returns SysML::ActionUsage :
	OccurrenceUsagePrefix ActionUsageKeyword ActionUsageDeclaration ActionBody
;

PerformActionUsage returns SysML::PerformActionUsage :
	OccurrenceUsagePrefix 'perform' PerformActionUsageDeclaration ActionBody
;

fragment PerformActionUsageDeclaration returns SysML::ActionUsage :
   	( ownedRelationship += OwnedReferenceSubsetting FeatureSpecializationPart?
   	| ActionUsageKeyword UsageDeclaration? )
    ValuePart?
;

fragment ActionUsageDeclaration returns SysML::ActionUsage :
	  UsageDeclaration? ValuePart?
;

/* Action Nodes */

ActionNode returns SysML::ActionUsage :
	  SendNode | AcceptNode | AssignmentNode 
	| IfNode | WhileLoopNode | ForLoopNode
	| TerminateNode
	| ControlNode
;

fragment ActionNodeUsageDeclaration returns SysML::ActionUsage :
	ActionUsageKeyword UsageDeclaration?
;

fragment ActionNodePrefix returns SysML::ActionUsage :
	OccurrenceUsagePrefix ActionNodeUsageDeclaration?
;

AcceptNode returns SysML::AcceptActionUsage :
	OccurrenceUsagePrefix AcceptNodeDeclaration ActionBody
;

fragment AcceptNodeDeclaration returns SysML::ActionUsage :
	ActionNodeUsageDeclaration? 'accept' AcceptParameterPart
;

fragment AcceptParameterPart returns SysML::ActionUsage :
	ownedRelationship += PayloadParameterMember
	( 'via' ownedRelationship += NodeParameterMember )?
;

PayloadParameterMember returns SysML::ParameterMembership :
	ownedRelatedElement += PayloadParameter 
;

PayloadParameter returns SysML::ReferenceUsage :
	  Payload
	| Identification? PayloadFeatureSpecializationPart? TriggerValuePart
;

fragment TriggerValuePart returns SysML::Feature :
	ownedRelationship += TriggerFeatureValue
;

TriggerFeatureValue returns SysML::FeatureValue :
	ownedRelatedElement += TriggerExpression
;

TriggerExpression returns SysML::TriggerInvocationExpression :
	  kind = TimeTriggerKind
	  ownedRelationship += ArgumentMember
	| kind = ChangeTriggerKind
	  ownedRelationship += ArgumentExpressionMember
;

TimeTriggerKind returns SysML::TriggerKind :
	'at' | 'after'
;

ChangeTriggerKind returns SysML::TriggerKind :
	'when'
;

ArgumentExpressionMember returns SysML::ParameterMembership :
    ownedRelatedElement += ArgumentExpression
;

ArgumentExpression returns SysML::Feature :
    ownedRelationship += ArgumentExpressionValue
;

ArgumentExpressionValue returns SysML::FeatureValue :
    ownedRelatedElement += OwnedExpressionReference
;

SendNode returns SysML::SendActionUsage :
	OccurrenceUsagePrefix ActionNodeUsageDeclaration? 'send' 
	( ActionBody
	| ( => 
		( ownedRelationship += NodeParameterMember SenderReceiverPart?
	    | ownedRelationship += EmptyParameterMember SenderReceiverPart
	    )
	    ActionBody
	  )
	)
;

fragment SendNodeDeclaration returns SysML::ActionUsage :
	ActionNodeUsageDeclaration? 'send' 
	ownedRelationship += NodeParameterMember SenderReceiverPart?
;

fragment SenderReceiverPart returns SysML::ActionUsage :
	'via' ownedRelationship += NodeParameterMember 
	  ( 'to' ownedRelationship += NodeParameterMember )?
	| ownedRelationship += EmptyParameterMember
	  'to' ownedRelationship += NodeParameterMember
;

NodeParameterMember returns SysML::ParameterMembership :
	ownedRelatedElement += NodeParameter 
;

NodeParameter returns SysML::ReferenceUsage :
	ownedRelationship += FeatureBinding
;

FeatureBinding returns SysML::FeatureValue :
	ownedRelatedElement += OwnedExpression
;

AssignmentNode returns SysML::AssignmentActionUsage :
	OccurrenceUsagePrefix AssignmentNodeDeclaration ActionBody
;

fragment AssignmentNodeDeclaration returns SysML::ActionUsage :
	ActionNodeUsageDeclaration? 'assign' 
	ownedRelationship += AssignmentTargetMember
	ownedRelationship += FeatureChainMember ':=' 
	ownedRelationship += NodeParameterMember
;

AssignmentTargetMember returns SysML::ParameterMembership :
	ownedRelatedElement += TargetParameter 
;

TargetParameter returns SysML::ReferenceUsage :
	( => ownedRelationship += TargetBinding '.' )?
	ownedRelationship += TargetFeatureMember
;

TargetFeatureMember returns SysML::FeatureMembership :
	ownedRelatedElement += TargetFeature 
;

TargetFeature returns SysML::ReferenceUsage :
	ownedRelationship += TargetAccessedFeatureMember
;

TargetAccessedFeatureMember returns SysML::FeatureMembership :
	ownedRelatedElement += EmptyUsage 
;

TargetBinding returns SysML::FeatureValue :
	ownedRelatedElement += TargetExpression 
;

TargetExpression returns SysML::Expression :
	BaseExpression
	( ( {SysML::FeatureChainExpression.operand += current} '.' 
	  ownedRelationship += FeatureChainMember
	  )?
	  ( {SysML::OperatorExpression.operand += current} 
	    operator = '[' operand += SequenceExpression ']'
	  | {SysML::OperatorExpression.operand += current} '->' 
	    ownedRelationship += ReferenceTyping 
	    ( ownedRelationship += ExpressionBodyMember 
	    | ownedRelationship += FunctionReferenceMember 
	    | ArgumentList
	    ) 
	  | {SysML::CollectExpression.operand += current} '.'
	    ownedRelationship += ExpressionBodyMember 
	  | {SysML::SelectExpression.operand += current} '.?'
	    ownedRelationship += ExpressionBodyMember 
	  )
	)*
;

ExpressionParameterMember returns SysML::ParameterMembership :
	ownedRelatedElement += OwnedExpression 
;

IfNode returns SysML::IfActionUsage :
	ActionNodePrefix 
	'if' ownedRelationship += ExpressionParameterMember
	ownedRelationship += ActionBodyParameterMember
	( 'else' ownedRelationship += ( ActionBodyParameterMember | IfNodeParameterMember ) )?
;

ActionBodyParameterMember returns SysML::ParameterMembership :
	ownedRelatedElement += ActionBodyParameter 
;

ActionBodyParameter returns SysML::ActionUsage :
	{SysML::ActionUsage} ( ActionUsageKeyword UsageDeclaration? )? '{' => ActionBodyItem* '}'
;

IfNodeParameterMember returns SysML::ParameterMembership :
	ownedRelatedElement += IfNode 
;

WhileLoopNode returns SysML::WhileLoopActionUsage :
	ActionNodePrefix
	( 'while' ownedRelationship += ExpressionParameterMember
	| 'loop' ownedRelationship += EmptyParameterMember
	)
	ownedRelationship += ActionBodyParameterMember
	( 'until' ownedRelationship += ExpressionParameterMember ';' )?
;

ForLoopNode returns SysML::ForLoopActionUsage :
	ActionNodePrefix
	'for' ownedRelationship += ForVariableDeclarationMember
	'in' ownedRelationship += NodeParameterMember
	ownedRelationship += ActionBodyParameterMember
;


ForVariableDeclarationMember returns SysML::FeatureMembership :
	ownedRelatedElement += ForVariableDeclaration 
;


ForVariableDeclaration returns SysML::ReferenceUsage :
	UsageDeclaration
;

TerminateNode returns SysML::TerminateActionUsage :
	OccurrenceUsagePrefix ActionNodeUsageDeclaration? 
	'terminate' 
	( ActionBody
	| -> ownedRelationship += NodeParameterMember
	  ActionBody
	)
;

ControlNode returns SysML::ControlNode :
	  MergeNode
	| DecisionNode
	| JoinNode
	| ForkNode
;

fragment ControlNodePrefix returns SysML::OccurrenceUsage :
	  RefPrefix
	  ( isIndividual ?= 'individual' )?
	  ( portionKind = PortionKind )?
	  UsageExtensionKeyword*
;

MergeNode returns SysML::MergeNode :
	ControlNodePrefix 
	isComposite ?= 'merge' UsageDeclaration?
	ActionBody
;

DecisionNode returns SysML::DecisionNode :
	ControlNodePrefix 
	isComposite ?= 'decide' UsageDeclaration?
	ActionBody
;

JoinNode returns SysML::JoinNode :
	ControlNodePrefix 
	isComposite ?= 'join' UsageDeclaration?
	ActionBody
;

ForkNode returns SysML::ForkNode :
	ControlNodePrefix 
	isComposite ?= 'fork' UsageDeclaration?
	ActionBody
;

EmptyParameterMember returns SysML::ParameterMembership :
	ownedRelatedElement += EmptyUsage 
;

EmptyUsage returns SysML::ReferenceUsage :
	{SysML::ReferenceUsage}
;

/* Action Successions */

ActionTargetSuccession returns SysML::Usage :
	( TargetSuccession | GuardedTargetSuccession | DefaultTargetSuccession )
	UsageBody
;

TargetSuccession returns SysML::SuccessionAsUsage :
	ownedRelationship += MultiplicitySourceEndMember
	'then' ownedRelationship += ConnectorEndMember
;

GuardedTargetSuccession returns SysML::TransitionUsage :
	ownedRelationship += EmptyParameterMember
	ownedRelationship += GuardExpressionMember
	'then' ownedRelationship += TransitionSuccessionMember
;

DefaultTargetSuccession returns SysML::TransitionUsage :
	ownedRelationship += EmptyParameterMember
	'else' ownedRelationship += TransitionSuccessionMember
;

GuardedSuccession returns SysML::TransitionUsage :
	( SuccessionKeyword UsageDeclaration )? 'first'
	ownedRelationship += TransitionSourceMember 
	ownedRelationship += EmptyParameterMember
	ownedRelationship += GuardExpressionMember
	'then' ownedRelationship += TransitionSuccessionMember
	UsageBody
;

/* STATES */

/* State Definitions */

StateKeyword :
	'state'
;

StateDefKeyword :
	StateKeyword 'def'
;

StateDefinition returns SysML::StateDefinition :
	OccurrenceDefinitionPrefix StateDefKeyword DefinitionDeclaration StateDefBody
;

fragment StateDefBody returns SysML::StateDefinition :
	';' | ( isParallel ?= 'parallel' )? '{' StateBodyPart '}'
;

fragment StateBodyPart returns SysML::Type :
	// Note: Using a syntactic predicate here seems to avoid a possible infinite loop
	// while incrementally parsing.
	=> StateBodyItem*
;

fragment StateBodyItem returns SysML::Type :
	  ownedRelationship += Import
	| ownedRelationship += AliasMember
	| ownedRelationship += DefinitionMember 
	| ownedRelationship += VariantUsageMember
	| ownedRelationship += NonOccurrenceUsageMember
	| ( ownedRelationship += EmptySuccessionMember )?
	  ownedRelationship += StructureUsageMember
	| ( ownedRelationship += EmptySuccessionMember )?
	  ownedRelationship += BehaviorUsageMember
	  ( ownedRelationship += TargetTransitionUsageMember )*
	| ownedRelationship += TransitionUsageMember
	| ownedRelationship += EntryActionMember
	  ( ownedRelationship += EntryTransitionMember )*
	| ownedRelationship += DoActionMember
	| ownedRelationship += ExitActionMember	
;

EntryActionMember returns SysML::StateSubactionMembership :
	MemberPrefix kind = EntryActionKind ownedRelatedElement += StateActionUsage 
;

EntryActionKind returns SysML::StateSubactionKind :
	'entry'
;

DoActionMember returns SysML::StateSubactionMembership :
	MemberPrefix kind = DoActionKind ownedRelatedElement += StateActionUsage 
;

DoActionKind returns SysML::StateSubactionKind :
	'do'
;

ExitActionMember returns SysML::StateSubactionMembership :
	MemberPrefix kind = ExitActionKind ownedRelatedElement += StateActionUsage 
;

ExitActionKind returns SysML::StateSubactionKind :
	'exit'
;

EntryTransitionMember returns SysML::FeatureMembership :
	MemberPrefix 
	( ownedRelatedElement += GuardedTargetSuccession 
	| 'then' ownedRelatedElement += TransitionSuccession 
	) ';'
;

StateActionUsage returns SysML::ActionUsage :
	EmptyActionUsage ';' | PerformedActionUsage ActionBody
;

EmptyActionUsage returns SysML::ActionUsage :
	{SysML::ActionUsage}
;

PerformedActionUsage returns SysML::ActionUsage :
	  {SysML::PerformActionUsage} PerformActionUsageDeclaration 
	| {SysML::AcceptActionUsage} AcceptNodeDeclaration 
	| {SysML::SendActionUsage} SendNodeDeclaration
	| {SysML::AssignmentActionUsage} AssignmentNodeDeclaration
;

TransitionUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += TransitionUsage 
;

TargetTransitionUsageMember returns SysML::FeatureMembership :
	MemberPrefix ownedRelatedElement += TargetTransitionUsage 
;

/* State Usages */

StateUsageKeyword :
	StateKeyword
;

StateUsage returns SysML::StateUsage :
	OccurrenceUsagePrefix StateUsageKeyword ActionUsageDeclaration StateUsageBody
;

fragment StateUsageBody returns SysML::StateUsage :
	';' | ( isParallel ?= 'parallel' )? '{' StateBodyPart '}'
;

ExhibitStateUsage returns SysML::ExhibitStateUsage :
    OccurrenceUsagePrefix 'exhibit' 
    ( ownedRelationship += OwnedReferenceSubsetting FeatureSpecializationPart?
    | StateUsageKeyword UsageDeclaration?
    )
    ValuePart? StateUsageBody
;

/* Transition Usages */

TransitionUsageKeyword :
	'transition'
;

TransitionUsage returns SysML::TransitionUsage :
	TransitionUsageKeyword ( UsageDeclaration? 'first' )? 
	ownedRelationship += TransitionSourceMember
	ownedRelationship += EmptyParameterMember
	( ownedRelationship += EmptyParameterMember 
	  ownedRelationship += TriggerActionMember )?
	( ownedRelationship += GuardExpressionMember )?
	( ownedRelationship += EffectBehaviorMember )?
	'then' ownedRelationship += TransitionSuccessionMember
	ActionBody
;

TargetTransitionUsage returns SysML::TransitionUsage :
	ownedRelationship += EmptyParameterMember
	( TransitionUsageKeyword
	  ( ownedRelationship += EmptyParameterMember 
	    ownedRelationship += TriggerActionMember )?
	  ( ownedRelationship += GuardExpressionMember )?
	  ( ownedRelationship += EffectBehaviorMember )?
	| ownedRelationship += EmptyParameterMember 
	  ownedRelationship += TriggerActionMember
	  ( ownedRelationship += GuardExpressionMember )?
	  ( ownedRelationship += EffectBehaviorMember )?
	| ownedRelationship += GuardExpressionMember
	  ( ownedRelationship += EffectBehaviorMember )?
	)?
	'then' ownedRelationship += TransitionSuccessionMember
	ActionBody
;

TransitionSourceMember returns SysML::Membership :
	  memberElement = [SysML::Feature|QualifiedName]
	| {SysML::OwningMembership} ownedRelatedElement += OwnedFeatureChain 
;

TriggerActionMember returns SysML::TransitionFeatureMembership :
	kind = TriggerFeatureKind ownedRelatedElement += TriggerAction 
;

enum TriggerFeatureKind returns SysML::TransitionFeatureKind :
	trigger = 'accept'
;

TriggerAction returns SysML::AcceptActionUsage :
	AcceptParameterPart
;

GuardExpressionMember returns SysML::TransitionFeatureMembership :
	kind = GuardFeatureKind ownedRelatedElement += OwnedExpression 
;

enum GuardFeatureKind returns SysML::TransitionFeatureKind :
	guard = 'if'
;

EffectBehaviorMember returns SysML::TransitionFeatureMembership :
	kind = EffectFeatureKind ownedRelatedElement += EffectBehaviorUsage 
;

enum EffectFeatureKind returns SysML::TransitionFeatureKind :
	effect = 'do'
;

EffectBehaviorUsage returns SysML::ActionUsage :
	  EmptyActionUsage | PerformedActionUsage ( '{' ActionBodyItem* '}' )?
;

TransitionSuccessionMember returns SysML::OwningMembership :
	ownedRelatedElement += TransitionSuccession 
;

TransitionSuccession returns SysML::SuccessionAsUsage :
	ownedRelationship += EmptySourceEndMember 
	ownedRelationship += ConnectorEndMember
;

/* CALCULATIONS */

/* Calculation Definitions */

CalculationKeyword :
	'calc'
;

CalculationDefKeyword :
	CalculationKeyword 'def'
;

CalculationDefinition returns SysML::CalculationDefinition :
	OccurrenceDefinitionPrefix CalculationDefKeyword DefinitionDeclaration 
	CalculationBody
;

fragment CalculationBody returns SysML::Type :
	';' | '{' CalculationBodyPart '}' 
;

fragment CalculationBodyPart returns SysML::Type :
	=> CalculationBodyItem*
	( ownedRelationship += ResultExpressionMember )?
;

fragment CalculationBodyItem returns SysML::Type :
	  ActionBodyItem
	| ownedRelationship += ReturnParameterMember
;

ReturnParameterMember returns SysML::ReturnParameterMembership :
	MemberPrefix 'return' 
	ownedRelatedElement += UsageElement 
;

@Override
ResultExpressionMember returns SysML::ResultExpressionMembership :
	MemberPrefix ownedRelatedElement += OwnedExpression 
;

/* Calculation Usages */

CalculationUsageKeyword :
	CalculationKeyword
;

CalculationUsage returns SysML::CalculationUsage :
	OccurrenceUsagePrefix CalculationUsageKeyword ActionUsageDeclaration CalculationBody
;

/* CONSTRAINTS */

/* Constraint Definitions */

ConstraintKeyword :
	'constraint'
;

ConstraintDefKeyword :
	ConstraintKeyword 'def'
;

ConstraintDefinition returns SysML::ConstraintDefinition :
	OccurrenceDefinitionPrefix ConstraintDefKeyword DefinitionDeclaration CalculationBody  
;

/* Constraint Usages */

ConstraintUsageKeyword :
	ConstraintKeyword
;

ConstraintUsage returns SysML::ConstraintUsage :
	OccurrenceUsagePrefix ConstraintUsageKeyword ConstraintUsageDeclaration CalculationBody
;

AssertConstraintUsage returns SysML::AssertConstraintUsage :
	OccurrenceUsagePrefix 'assert' ( isNegated ?= 'not' )?
    ( ownedRelationship += OwnedReferenceSubsetting FeatureSpecializationPart?
    | ConstraintUsageKeyword ConstraintUsageDeclaration
    )
    CalculationBody
;

fragment ConstraintUsageDeclaration returns SysML::ConstraintUsage :
	  UsageDeclaration? ValuePart?
;

/* REQUIREMENTS */

/* Requirement Definitions */

RequirementKeyword :
	'requirement'
;

RequirementDefKeyword :
	RequirementKeyword 'def'
;

RequirementDefinition returns SysML::RequirementDefinition :
	OccurrenceDefinitionPrefix RequirementDefKeyword DefinitionDeclaration RequirementBody  
;

fragment RequirementBody returns SysML::Type :
	  ';' | '{' => RequirementBodyItem* '}'
;

fragment RequirementBodyItem returns SysML::Type :
	  DefinitionBodyItem
	| ownedRelationship += SubjectMember
	| ownedRelationship += RequirementConstraintMember
	| ownedRelationship += FramedConcernMember
	| ownedRelationship += RequirementVerificationMember
	| ownedRelationship += ActorMember
	| ownedRelationship += StakeholderMember
;

SubjectMember returns SysML::SubjectMembership :
	MemberPrefix ownedRelatedElement += SubjectUsage 
;

SubjectUsage returns SysML::ReferenceUsage :
	'subject' UsageExtensionKeyword* Usage
;

RequirementConstraintMember returns SysML::RequirementConstraintMembership :
	MemberPrefix kind = RequirementConstraintKind 
	ownedRelatedElement += RequirementConstraintUsage 
;

enum RequirementConstraintKind returns SysML::RequirementConstraintKind :
	assumption = 'assume' | requirement = 'require'
;

RequirementConstraintUsage returns SysML::ConstraintUsage :
      ownedRelationship += OwnedReferenceSubsetting FeatureSpecialization* 
      CalculationBody
    | ( UsageExtensionKeyword* ConstraintUsageKeyword | UsageExtensionKeyword+ )
      ConstraintUsageDeclaration CalculationBody
;

FramedConcernMember returns SysML::FramedConcernMembership :
	MemberPrefix kind = FramedConcernKind 
	ownedRelatedElement += FramedConcernUsage 
;

enum FramedConcernKind returns SysML::RequirementConstraintKind :
	requirement = 'frame'
;

FramedConcernUsage returns SysML::ConcernUsage :
      ownedRelationship += OwnedReferenceSubsetting FeatureSpecialization* 
      RequirementBody
    | ( UsageExtensionKeyword* ConcernUsageKeyword | UsageExtensionKeyword+ ) 
      ConstraintUsageDeclaration CalculationBody
;

ActorMember returns SysML::ActorMembership :
	MemberPrefix
	ownedRelatedElement += ActorUsage 
;

ActorUsage returns SysML::PartUsage :
      'actor' UsageExtensionKeyword* Usage
;

StakeholderMember returns SysML::StakeholderMembership :
	MemberPrefix
	ownedRelatedElement += StakeholderUsage 
;

StakeholderUsage returns SysML::PartUsage :
      'stakeholder' UsageExtensionKeyword* Usage
;

/* Requirement Usages */

RequirementUsageKeyword :
	RequirementKeyword
;

RequirementUsage returns SysML::RequirementUsage :
	OccurrenceUsagePrefix RequirementUsageKeyword ConstraintUsageDeclaration RequirementBody
;

SatisfyRequirementUsage returns SysML::SatisfyRequirementUsage :
	OccurrenceUsagePrefix 'assert'? ( isNegated ?= 'not' )? 'satisfy' 
	( ownedRelationship += OwnedReferenceSubsetting FeatureSpecializationPart?
    | RequirementUsageKeyword UsageDeclaration? 
    )
    ValuePart?
    ( 'by' ownedRelationship += SatisfactionSubjectMember )?
     RequirementBody
;

SatisfactionSubjectMember returns SysML::SubjectMembership :
	ownedRelatedElement += SatisfactionParameter 
;

SatisfactionParameter returns SysML::ReferenceUsage :
	ownedRelationship += SatisfactionFeatureValue
;

SatisfactionFeatureValue returns SysML::FeatureValue :
	ownedRelatedElement += SatisfactionReferenceExpression
;

SatisfactionReferenceExpression returns SysML::FeatureReferenceExpression :
	ownedRelationship += FeatureChainMember
;

/* CONCERNS */

/* Concern Definitions */

ConcernKeyword :
	'concern'
;

ConcernDefKeyword :
	ConcernKeyword 'def'
;

ConcernDefinition returns SysML::ConcernDefinition :
	OccurrenceDefinitionPrefix ConcernDefKeyword DefinitionDeclaration RequirementBody  
;

ConcernUsageKeyword :
	ConcernKeyword
;

ConcernUsage returns SysML::ConcernUsage :
	OccurrenceUsagePrefix ConcernUsageKeyword ConstraintUsageDeclaration RequirementBody
;

/* CASES */

/* Case Definitions */

CaseKeyword :
	'case'
;

CaseDefKeyword :
	CaseKeyword 'def'
;

CaseDefinition returns SysML::CaseDefinition :
	OccurrenceDefinitionPrefix CaseDefKeyword DefinitionDeclaration CaseBody
;

fragment CaseBody returns SysML::Type :
	    ';' 
	  | '{' => CaseBodyItem*
	        ( ownedRelationship += ResultExpressionMember )?
	    '}' 
;

fragment CaseBodyItem returns SysML::Type :
	  CalculationBodyItem
	| ownedRelationship += SubjectMember 
	| ownedRelationship += ActorMember
	| ownedRelationship += ObjectiveMember
	
;

ObjectiveMember returns SysML::ObjectiveMembership :
	MemberPrefix 'objective' ownedRelatedElement += ObjectiveRequirementUsage 
;

ObjectiveRequirementUsage returns SysML::RequirementUsage :
	UsageExtensionKeyword* ConstraintUsageDeclaration RequirementBody
;

/* Case Usages */

CaseUsageKeyword :
	CaseKeyword
;

// TODO: Correct erroneous use of ConstraintUsageDeclaration for CaseUsage from resolution of SYSML2-783.

CaseUsage returns SysML::CaseUsage :
	OccurrenceUsagePrefix CaseUsageKeyword ActionUsageDeclaration CaseBody
;

/* ANALYSIS CASES */

AnalysisCaseKeyword :
	'analysis'
;

AnalysisCaseDefKeyword :
	AnalysisCaseKeyword 'def'
;

AnalysisCaseUsageKeyword :
	AnalysisCaseKeyword
;

AnalysisCaseDefinition returns SysML::AnalysisCaseDefinition :
	OccurrenceDefinitionPrefix AnalysisCaseDefKeyword DefinitionDeclaration CaseBody
;

AnalysisCaseUsage returns SysML::AnalysisCaseUsage :
	OccurrenceUsagePrefix AnalysisCaseUsageKeyword ActionUsageDeclaration CaseBody
;

/* VERIFICATION CASES */

VerificationCaseKeyword :
	'verification'
;

VerificationCaseDefKeyword :
	VerificationCaseKeyword 'def'
;

VerificationCaseUsageKeyword :
	VerificationCaseKeyword
;

VerificationCaseDefinition returns SysML::VerificationCaseDefinition :
	OccurrenceDefinitionPrefix VerificationCaseDefKeyword DefinitionDeclaration CaseBody
;

VerificationCaseUsage returns SysML::VerificationCaseUsage :
	OccurrenceUsagePrefix VerificationCaseUsageKeyword ActionUsageDeclaration CaseBody
;

RequirementVerificationMember returns SysML::RequirementVerificationMembership :
	MemberPrefix kind = RequirementVerificationKind 
	ownedRelatedElement += RequirementVerificationUsage 
;

enum RequirementVerificationKind returns SysML::RequirementConstraintKind :
	requirement = 'verify'
;

RequirementVerificationUsage returns SysML::RequirementUsage :
      ownedRelationship += OwnedReferenceSubsetting FeatureSpecialization* 
      RequirementBody
    | ( UsageExtensionKeyword* RequirementUsageKeyword | UsageExtensionKeyword+ )
      ConstraintUsageDeclaration RequirementBody
;

/* USE CASES */

UseCaseKeyword :
	'use' 'case'
;

UseCaseDefKeyword :
	UseCaseKeyword 'def'
;

UseCaseUsageKeyword :
	UseCaseKeyword
;

UseCaseDefinition returns SysML::UseCaseDefinition :
	OccurrenceDefinitionPrefix UseCaseDefKeyword DefinitionDeclaration CaseBody
;

UseCaseUsage returns SysML::UseCaseUsage :
	OccurrenceUsagePrefix UseCaseUsageKeyword ActionUsageDeclaration CaseBody
;

IncludeUseCaseUsage returns SysML::IncludeUseCaseUsage :
	OccurrenceUsagePrefix 'include' 
   	( ownedRelationship += OwnedReferenceSubsetting FeatureSpecializationPart?
   	| UseCaseUsageKeyword UsageDeclaration? )
    ValuePart?
	CaseBody
;

/* VIEWS */

/* View Definitions */

ViewKeyword :
	'view'
;

ViewDefKeyword :
	ViewKeyword 'def'
;

ViewDefinition returns SysML::ViewDefinition :
	OccurrenceDefinitionPrefix ViewDefKeyword DefinitionDeclaration ViewDefinitionBody
;

fragment ViewDefinitionBody returns SysML::ViewDefinition :
	  ';' 
	| '{' ViewDefinitionBodyItem* '}'
;

fragment ViewDefinitionBodyItem returns SysML::ViewDefinition :
	  DefinitionBodyItem
	| ownedRelationship += ElementFilterMember
	| ownedRelationship += ViewRenderingMember
;

ViewRenderingMember returns SysML::ViewRenderingMembership :
	MemberPrefix 'render' 
	ownedRelatedElement += ViewRenderingUsage 
;

ViewRenderingUsage returns SysML::RenderingUsage :
	  ownedRelationship += OwnedReferenceSubsetting FeatureSpecialization* UsageBody
	| ( UsageExtensionKeyword* 'rendering' | UsageExtensionKeyword+ ) Usage
;

/* View Usages */

ViewUsageKeyword :
	ViewKeyword
;

ViewUsage returns SysML::ViewUsage :
	OccurrenceUsagePrefix ViewUsageKeyword UsageDeclaration? ValuePart? ViewBody
;

fragment ViewBody returns SysML::ViewUsage :
	';' | '{' ViewBodyItem* '}'
;

fragment ViewBodyItem returns SysML::ViewUsage :
	  DefinitionBodyItem
	| ownedRelationship += ElementFilterMember
	| ownedRelationship += Expose
	| ownedRelationship += ViewRenderingMember
;

fragment ExposePrefix returns SysML::Expose :
	visibility = ExposeVisibilityKind
;

enum ExposeVisibilityKind returns SysML::VisibilityKind :
	protected = 'expose'
;

Expose returns SysML::Expose :
	( MembershipExpose | NamespaceExpose )
	RelationshipBody
;

MembershipExpose returns SysML::MembershipExpose :
	ExposePrefix ImportedMembership
;

NamespaceExpose returns SysML::NamespaceExpose :
	ExposePrefix 
	( ImportedNamespace
	| ownedRelatedElement += FilterPackage
	)
;

/* VIEWPOINTS */

ViewpointKeyword :
	'viewpoint'
;

ViewpointDefKeyword :
	ViewpointKeyword 'def'
;

ViewpointUsageKeyword :
	ViewpointKeyword
;

ViewpointDefinition returns SysML::ViewpointDefinition :
	OccurrenceDefinitionPrefix ViewpointDefKeyword DefinitionDeclaration RequirementBody  
;

ViewpointUsage returns SysML::ViewpointUsage :
	OccurrenceUsagePrefix ViewpointUsageKeyword ConstraintUsageDeclaration RequirementBody
;

/* RENDERINGS */

RenderingKeyword :
	'rendering'
;

RenderingDefKeyword :
	RenderingKeyword 'def'
;

RenderingDefinition returns SysML::RenderingDefinition :
	OccurrenceDefinitionPrefix RenderingDefKeyword Definition
;

RenderingUsageKeyword :
	RenderingKeyword
;

RenderingUsage returns SysML::RenderingUsage :
	OccurrenceUsagePrefix RenderingUsageKeyword Usage
;

/* EXPRESSIONS */

@Override
ExpressionBody returns SysML::Expression :
	CalculationBody
;