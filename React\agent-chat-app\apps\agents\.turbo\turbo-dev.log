
> agents@0.0.1 dev
> npx @langchain/langgraph-cli dev --port 2024 --config ../../langgraph.json


          Welcome to

╦  ┌─┐┌┐┌┌─┐╔═╗┬─┐┌─┐┌─┐┬ ┬
║  ├─┤││││ ┬║ ╦├┬┘├─┤├─┘├─┤
╩═╝┴ ┴┘└┘└─┘╚═╝┴└─┴ ┴┴  ┴ ┴.js

- 🚀 API: [36mhttp://localhost:2024[0m
- 🎨 Studio UI: [36mhttps://smith.langchain.com/studio?baseUrl=http://localhost:2024[0m

This in-memory server is designed for development and testing.
For production use, please use LangGraph Cloud.


[32minfo[39m:    [32m[2m▪[22m[0m [0m[32mStarting server...[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32mInitializing storage...[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32mRegistering graphs from C:\Users\<USER>\Desktop\压缩包\20375230-李成宇\React\agent-chat-app[39m
[32minfo[39m:    [32m[2m┏[22m[0m [0m[32mRegistering graph with id 'agent'[39m
[2m[32minfo[39m[22m:    [32m[2m┗[22m[0m [0m[2m[1][22m { graph_id: [32m'agent'[39m }
[32minfo[39m:    [32m[2m┏[22m[0m [0m[32mRegistering graph with id 'memory_agent'[39m
[2m[32minfo[39m[22m:    [32m[2m┗[22m[0m [0m[2m[1][22m { graph_id: [32m'memory_agent'[39m }
[32minfo[39m:    [32m[2m┏[22m[0m [0m[32mRegistering graph with id 'research_agent'[39m
[2m[32minfo[39m[22m:    [32m[2m┗[22m[0m [0m[2m[1][22m { graph_id: [32m'research_agent'[39m }
[32minfo[39m:    [32m[2m┏[22m[0m [0m[32mRegistering graph with id 'research_index_graph'[39m
[2m[32minfo[39m[22m:    [32m[2m┗[22m[0m [0m[2m[1][22m { graph_id: [32m'research_index_graph'[39m }
[32minfo[39m:    [32m[2m┏[22m[0m [0m[32mRegistering graph with id 'retrieval_agent'[39m
[2m[32minfo[39m[22m:    [32m[2m┗[22m[0m [0m[2m[1][22m { graph_id: [32m'retrieval_agent'[39m }
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32mStarting 10 workers[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32mServer running at ::1:2024[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 2ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/b12faf45-1d31-5322-bb04-0c6c49ace86b/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /info[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /info [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/367c073f-e631-5af5-825b-25bd4d6567b9/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas [32m200[0m 4s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/9dc0ca3b-1aa6-547d-93f0-e21597d2011c/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/fe096781-5601-53d2-b2f6-0d3403f7e9ca/schemas [32m200[0m 4s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/b12faf45-1d31-5322-bb04-0c6c49ace86b/schemas [32m200[0m 6s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/graph?xray=true[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/graph?xray=true [32m200[0m 3ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/367c073f-e631-5af5-825b-25bd4d6567b9/schemas [32m200[0m 6s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/subgraphs?recurse=true[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas [32m200[0m 4s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /info[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /info [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /threads/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /threads/search [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /threads/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /threads/search [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /info[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /info [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/subgraphs?recurse=true [32m200[0m 3s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/9dc0ca3b-1aa6-547d-93f0-e21597d2011c/schemas [32m200[0m 6s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/graph?xray=true[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/graph?xray=true [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/subgraphs?recurse=true[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 1ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/graph?xray=true[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/graph?xray=true [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- POST /assistants/search[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> POST /assistants/search [32m200[0m 0ms[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas [32m200[0m 3s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/subgraphs?recurse=true [32m200[0m 3s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m<-- GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/subgraphs?recurse=true[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/schemas [32m200[0m 3s[39m
[32minfo[39m:    [32m[2m▪[22m[0m [0m[32m--> GET /assistants/56b507ad-d20a-514f-b167-bc26d0cfda1f/subgraphs?recurse=true [32m200[0m 3s[39m
