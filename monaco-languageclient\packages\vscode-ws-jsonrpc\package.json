{"name": "vscode-ws-jsonrpc", "version": "3.4.0", "description": "VSCode JSON RPC over WebSocket", "author": {"name": "TypeFox GmbH", "url": "http://www.typefox.io"}, "homepage": "https://github.com/TypeFox/monaco-languageclient/blob/main/packages/vscode-ws-jsonrpc/README.md", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TypeFox/monaco-languageclient.git", "directory": "packages/vscode-ws-jsonrpc"}, "bugs": {"url": "https://github.com/TypeFox/monaco-languageclient/issues"}, "type": "module", "main": "./lib/index.js", "module": "./lib/index.js", "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./server": {"types": "./lib/server/index.d.ts", "default": "./lib/server/index.js"}, "./socket": {"types": "./lib/socket/index.d.ts", "default": "./lib/socket/index.js"}}, "typesVersions": {"*": {".": ["lib/index"], "server": ["lib/server"], "socket": ["lib/socket"]}}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "files": ["lib", "src", "README.md", "CHANGELOG.md", "LICENSE"], "dependencies": {"vscode-jsonrpc": "~8.2.1"}, "scripts": {"clean": "shx rm -fr ./lib *.tsbuildinfo", "compile": "tsc --build tsconfig.src.json", "build:msg": "echo Building vscode-ws-jsonrpc:", "build": "npm run build:msg && npm run clean && npm run compile"}}