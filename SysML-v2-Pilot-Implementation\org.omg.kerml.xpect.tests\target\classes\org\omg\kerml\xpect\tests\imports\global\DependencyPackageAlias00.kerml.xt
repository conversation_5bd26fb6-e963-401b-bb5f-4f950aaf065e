//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.global.KerMLImportGlobalTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyPackageAlias1.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyPackageAlias1.kerml"}
			}
		}
	}
END_SETUP 
*/
//
// XPECT noErrors ---> ""
package test{
 	public import PackageAlias1;
	//* XPECT scope at PackageAlias1::A::a ---
	   	PackageAlias1, PackageAlias1.A, PackageAlias1.A.a, PackageAlias1.A.a_alias,
	   	PackageAlias1.AA, PackageAlias1.AA.aa, PackageAlias1.AA.aa_alias,
	   	PackageAlias1.A_alias, PackageAlias1.A_alias.a, PackageAlias1.A_alias.a_alias,
    	PackageAlias1.AA_alias, PackageAlias1.AA_alias.aa, PackageAlias1.AA_alias.aa_alias,
    	
	    test, test.PackageAlias1,
    	test.PackageAlias1.A, test.PackageAlias1.A.a, test.PackageAlias1.A.a_alias,
	   	test.PackageAlias1.AA, test.PackageAlias1.AA.aa, test.PackageAlias1.AA.aa_alias,
	   	test.PackageAlias1.A_alias, test.PackageAlias1.A_alias.a, test.PackageAlias1.A_alias.a_alias,
    	test.PackageAlias1.AA_alias, test.PackageAlias1.AA_alias.aa, test.PackageAlias1.AA_alias.aa_alias,
 --- */
 	public import PackageAlias1::A::a::*;
}
//*
package PackageAlias1{
	alias A_alias for A;
	class A{
		class a{}
			alias a_alias for a;
	}
	alias AA_alias for AA;
	class AA{
		class aa{}
		alias aa_alias for aa;
	}
}*/

