standard library package ISQInformation {
    doc
    /*
     * International System of Quantities and Units
     * Generated on 2025-03-13T15:00:05Z from standard IEC-80000-13:2008 "Information science and technology"
     * see also https://www.iso.org/obp/ui/#iso:std:iec:80000:-13:ed-1:v1:en
     * 
     * Note 1: In documentation comments, AsciiMath notation (see http://asciimath.org/) is used for mathematical concepts,
     * with Greek letters in Unicode encoding. In running text, AsciiMath is placed between backticks.
     * Note 2: For vector and tensor quantities currently the unit and quantity value type for their (scalar) magnitude is 
     * defined, as well as their typical Cartesian 3d VectorMeasurementReference (i.e. coordinate system) 
     * or TensorMeasurementReference.
     */

    private import ScalarValues::Real;
    private import Quantities::*;
    private import MeasurementReferences::*;
    private import ISQBase::*;

    /* Quantity definitions referenced from other ISQ packages */
    private import ISQMechanics::PowerValue;
    private import ISQSpaceTime::FrequencyValue;
    private import ISQThermodynamics::EnergyValue;

    /* IEC-80000-13 item 13-1 traffic intensity */
    attribute def TrafficIntensityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-1 traffic intensity
         * symbol(s): `A`
         * application domain: generic
         * name: TrafficIntensity
         * quantity dimension: 1
         * measurement unit(s): E
         * tensor order: 0
         * definition: number of simultaneously busy resources in a particular pool of resources
         * remarks: See IEC 60050-715, item 715-05-02. The name "erlang" was given to the traffic intensity unit in 1946 by the CCIF, in honour of the Danish mathematician, A. K. Erlang (1878-1929), who was the founder of traffic theory in telephony.
         */
        attribute :>> num: Real;
        attribute :>> mRef: TrafficIntensityUnit[1];
    }

    attribute trafficIntensity: TrafficIntensityValue[*] nonunique :> scalarQuantities;

    attribute def TrafficIntensityUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-2 traffic offered intensity */
    attribute def TrafficOfferedIntensityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-2 traffic offered intensity
         * symbol(s): `A_o`
         * application domain: generic
         * name: TrafficOfferedIntensity
         * quantity dimension: 1
         * measurement unit(s): E
         * tensor order: 0
         * definition: traffic intensity (item 13-1) of the traffic that would have been generated by the users of a pool of resources if their use had not been limited by the size of the pool
         * remarks: See IEC 60050-715, item 715-05-05. See 13-1 for unit E.
         */
        attribute :>> num: Real;
        attribute :>> mRef: TrafficOfferedIntensityUnit[1];
    }

    attribute trafficOfferedIntensity: TrafficOfferedIntensityValue[*] nonunique :> scalarQuantities;

    attribute def TrafficOfferedIntensityUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-3 traffic carried intensity, traffic load */
    attribute def TrafficCarriedIntensityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-3 traffic carried intensity, traffic load
         * symbol(s): `Y`
         * application domain: generic
         * name: TrafficCarriedIntensity
         * quantity dimension: 1
         * measurement unit(s): E
         * tensor order: 0
         * definition: traffic intensity (item 13-1) of the traffic served by a particular pool of resources
         * remarks: General practice is to estimate the traffic intensity as an average over a specified time interval, e.g. the busy hour. See IEC 60050-715, item 715-05-04. See 13-1 for unit E.
         */
        attribute :>> num: Real;
        attribute :>> mRef: TrafficCarriedIntensityUnit[1];
    }

    attribute trafficCarriedIntensity: TrafficCarriedIntensityValue[*] nonunique :> scalarQuantities;

    attribute def TrafficCarriedIntensityUnit :> DimensionOneUnit {
    }

    alias TrafficLoadUnit for TrafficCarriedIntensityUnit;
    alias TrafficLoadValue for TrafficCarriedIntensityValue;
    alias trafficLoad for trafficCarriedIntensity;

    /* IEC-80000-13 item 13-4 mean queue length */
    attribute def MeanQueueLengthValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-4 mean queue length
         * symbol(s): `L`, `(Ω)`
         * application domain: generic
         * name: MeanQueueLength (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: time average of queue length
         * remarks: None.
         */
    }
    attribute meanQueueLength: MeanQueueLengthValue :> scalarQuantities;

    /* IEC-80000-13 item 13-5 loss probability */
    attribute def LossProbabilityValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-5 loss probability
         * symbol(s): `B`
         * application domain: generic
         * name: LossProbability (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: probability for losing a call attempt
         * remarks: None.
         */
    }
    attribute lossProbability: LossProbabilityValue :> scalarQuantities;

    /* IEC-80000-13 item 13-6 waiting probability */
    attribute def WaitingProbabilityValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-6 waiting probability
         * symbol(s): `W`
         * application domain: generic
         * name: WaitingProbability (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: probability for waiting for a resource
         * remarks: None.
         */
    }
    attribute waitingProbability: WaitingProbabilityValue :> scalarQuantities;

    /* IEC-80000-13 item 13-7 call intensity, calling rate */
    attribute def CallIntensityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-7 call intensity, calling rate
         * symbol(s): `λ`
         * application domain: generic
         * name: CallIntensity
         * quantity dimension: T^-1
         * measurement unit(s): s^-1
         * tensor order: 0
         * definition: number of call attempts over a specified time interval divided by the duration (ISO 80000-3, item 3-7) of this interval
         * remarks: See IEC 60050-715, item 715-03-13.
         */
        attribute :>> num: Real;
        attribute :>> mRef: CallIntensityUnit[1];
    }

    attribute callIntensity: CallIntensityValue[*] nonunique :> scalarQuantities;

    attribute def CallIntensityUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    alias CallingRateUnit for CallIntensityUnit;
    alias CallingRateValue for CallIntensityValue;
    alias callingRate for callIntensity;

    /* IEC-80000-13 item 13-8 completed call intensity */
    attribute def CompletedCallIntensityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-8 completed call intensity
         * symbol(s): `μ`
         * application domain: generic
         * name: CompletedCallIntensity
         * quantity dimension: T^-1
         * measurement unit(s): s^-1
         * tensor order: 0
         * definition: call intensity (item 13-7) for the call attempts that result in the transmission of an answer signal
         * remarks: For a definition of the complete call attempt, see IEC 60050-715, item 715-03-11.
         */
        attribute :>> num: Real;
        attribute :>> mRef: CompletedCallIntensityUnit[1];
    }

    attribute completedCallIntensity: CompletedCallIntensityValue[*] nonunique :> scalarQuantities;

    attribute def CompletedCallIntensityUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    /* IEC-80000-13 item 13-9 storage capacity, storage size */
    attribute def StorageCapacityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-9 storage capacity, storage size
         * symbol(s): `M`
         * application domain: generic
         * name: StorageCapacity
         * quantity dimension: 1
         * measurement unit(s): bit, o, B, 1
         * tensor order: 0
         * definition: amount of data that can be contained in a storage device, expressed as a number of specified data elements
         * remarks: None.
         */
        attribute :>> num: Real;
        attribute :>> mRef: StorageCapacityUnit[1];
    }

    attribute storageCapacity: StorageCapacityValue[*] nonunique :> scalarQuantities;

    attribute def StorageCapacityUnit :> DimensionOneUnit {
    }

    alias StorageSizeUnit for StorageCapacityUnit;
    alias StorageSizeValue for StorageCapacityValue;
    alias storageSize for storageCapacity;

    /* IEC-80000-13 item 13-10 equivalent binary storage capacity */
    attribute def EquivalentBinaryStorageCapacityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-10 equivalent binary storage capacity
         * symbol(s): `M_e`
         * application domain: generic
         * name: EquivalentBinaryStorageCapacity
         * quantity dimension: 1
         * measurement unit(s): bit, 1
         * tensor order: 0
         * definition: `M_e = log_2 n` where `n` is the number of possible states of the given device
         * remarks: The minimum storage capacity of a bit-organized storage device which would contain the amount of data in the given storage device is equal to the smallest integer greater than or equal to the equivalent binary storage capacity.
         */
        attribute :>> num: Real;
        attribute :>> mRef: EquivalentBinaryStorageCapacityUnit[1];
    }

    attribute equivalentBinaryStorageCapacity: EquivalentBinaryStorageCapacityValue[*] nonunique :> scalarQuantities;

    attribute def EquivalentBinaryStorageCapacityUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-11 transfer rate */
    attribute def TransferRateValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-11 transfer rate
         * symbol(s): `r`, `(ν)`
         * application domain: generic
         * name: TransferRate
         * quantity dimension: T^-1
         * measurement unit(s): o/s, B/s, s^-1
         * tensor order: 0
         * definition: quotient of the number of specified data elements transferred in a time interval by the duration of this interval
         * remarks: The symbol `ν` is the Greek letter nu. A subscript referring to a specified data element can be added to the symbol. EXAMPLES: digit rate, `r_d` or `ν_d` (see IEC 60050-702 and 60050-704, items 702-05-23 and 704-16-06); transfer rate for octets (or bytes), `r_o`, `r_B`, `ν_o`, or `ν_B`; binary digit rate or bit rate (item 13-13).
         */
        attribute :>> num: Real;
        attribute :>> mRef: TransferRateUnit[1];
    }

    attribute transferRate: TransferRateValue[*] nonunique :> scalarQuantities;

    attribute def TransferRateUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    /* IEC-80000-13 item 13-12 period of data elements */
    attribute periodOfDataElements: DurationValue :> scalarQuantities {
        doc
        /*
         * source: item 13-12 period of data elements
         * symbol(s): `T`
         * application domain: generic
         * name: PeriodOfDataElements (specializes Duration)
         * quantity dimension: T^1
         * measurement unit(s): s
         * tensor order: 0
         * definition: `T = 1/r`, where `r` is transfer rate (item 13-11) when the data elements are transmitted in series
         * remarks: A subscript referring to a specified data element can be added to the symbol. EXAMPLES: period of digits, `T_d`; period of octets (or bytes), `T_o` or `T_B`.
         */
    }

    /* IEC-80000-13 item 13-13 binary digit rate, bit rate */
    attribute def BinaryDigitRateValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-13 binary digit rate, bit rate
         * symbol(s): `r_b`, `r_"bit"`, `(ν_b)`, `(ν_"bit")`
         * application domain: generic
         * name: BinaryDigitRate
         * quantity dimension: T^-1
         * measurement unit(s): bit/s, s^-1
         * tensor order: 0
         * definition: transfer rate (item 13-11) where the data elements are binary digits
         * remarks: In English, the systematic name would be "transfer rate for binary digits". See IEC 60050-704, item 704-16-07. The bit per second may be combined with prefixes, for example megabit per second, symbol Mbit/s.
         */
        attribute :>> num: Real;
        attribute :>> mRef: BinaryDigitRateUnit[1];
    }

    attribute binaryDigitRate: BinaryDigitRateValue[*] nonunique :> scalarQuantities;

    attribute def BinaryDigitRateUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    alias BitRateUnit for BinaryDigitRateUnit;
    alias BitRateValue for BinaryDigitRateValue;
    alias bitRate for binaryDigitRate;

    /* IEC-80000-13 item 13-14 period of binary digits, bit period */
    attribute periodOfBinaryDigits: DurationValue :> scalarQuantities {
        doc
        /*
         * source: item 13-14 period of binary digits, bit period
         * symbol(s): `T_b`, `T_"bit"`
         * application domain: generic
         * name: PeriodOfBinaryDigits (specializes Duration)
         * quantity dimension: T^1
         * measurement unit(s): s
         * tensor order: 0
         * definition: `T_b = 1/r_b`, where `r_b` is the binary digit rate (item 13-13) when the binary digits are transmitted in series
         * remarks: None.
         */
    }

    alias bitPeriod for periodOfBinaryDigits;

    /* IEC-80000-13 item 13-15 equivalent binary digit rate, equivalent bit rate */
    attribute def EquivalentBinaryDigitRateValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-15 equivalent binary digit rate, equivalent bit rate
         * symbol(s): `r_e`, `(ν_e)`
         * application domain: generic
         * name: EquivalentBinaryDigitRate
         * quantity dimension: T^-1
         * measurement unit(s): bit/s, s^-1
         * tensor order: 0
         * definition: binary digit rate (item 13-13) equivalent to a transfer rate (item 13-11) for specified data elements
         * remarks: In English, the systematic name would be "equivalent binary transfer rate". See IEC 60050-704, item 704-17-05.
         */
        attribute :>> num: Real;
        attribute :>> mRef: EquivalentBinaryDigitRateUnit[1];
    }

    attribute equivalentBinaryDigitRate: EquivalentBinaryDigitRateValue[*] nonunique :> scalarQuantities;

    attribute def EquivalentBinaryDigitRateUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    alias EquivalentBitRateUnit for EquivalentBinaryDigitRateUnit;
    alias EquivalentBitRateValue for EquivalentBinaryDigitRateValue;
    alias equivalentBitRate for equivalentBinaryDigitRate;

    /* IEC-80000-13 item 13-16 modulation rate, line digit rate */
    attribute def ModulationRateValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-16 modulation rate, line digit rate
         * symbol(s): `r_m`, `u`
         * application domain: generic
         * name: ModulationRate
         * quantity dimension: T^-1
         * measurement unit(s): Bd, s^-1
         * tensor order: 0
         * definition: inverse of the shortest duration of a signal element
         * remarks: The term “modulation rate” is used in conventional telegraphy and data transmission. In isochronous digital transmission, the term "line digit rate" is generally used. See IEC 60050-704, item 704-17-03. Baud is a special name for the second to the power minus one for this quantity. The baud may be combined with prefixes, for example kilobaud, symbol kBd, megabaud, symbol MBd.
         */
        attribute :>> num: Real;
        attribute :>> mRef: ModulationRateUnit[1];
    }

    attribute modulationRate: ModulationRateValue[*] nonunique :> scalarQuantities;

    attribute def ModulationRateUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    alias LineDigitRateUnit for ModulationRateUnit;
    alias LineDigitRateValue for ModulationRateValue;
    alias lineDigitRate for modulationRate;

    /* IEC-80000-13 item 13-17 quantizing distortion rate */
    attribute quantizingDistortionRate: PowerValue :> scalarQuantities {
        doc
        /*
         * source: item 13-17 quantizing distortion rate
         * symbol(s): `T_Q`
         * application domain: generic
         * name: QuantizingDistortionRate (specializes Power)
         * quantity dimension: L^2*M^1*T^-3
         * measurement unit(s): W
         * tensor order: 0
         * definition: distortion of a signal resulting from the process of quantizing an original signal when the values to be quantized are within the working range of the quantizer
         * remarks: See IEC 60050-704, item 704-24-13.
         */
    }

    /* IEC-80000-13 item 13-18 carrier power */
    attribute carrierPower: PowerValue :> scalarQuantities {
        doc
        /*
         * source: item 13-18 carrier power
         * symbol(s): `P_c`, `C`
         * application domain: generic
         * name: CarrierPower (specializes Power)
         * quantity dimension: L^2*M^1*T^-3
         * measurement unit(s): W
         * tensor order: 0
         * definition: power supplied to the antenna feed line by a radio transmitter taken under the condition of no modulation
         * remarks: See IEC 60050-713, item 713-09-20.
         */
    }

    /* IEC-80000-13 item 13-19 signal energy per binary digit */
    attribute signalEnergyPerBinaryDigit: EnergyValue :> scalarQuantities {
        doc
        /*
         * source: item 13-19 signal energy per binary digit
         * symbol(s): `E_b`, `E_"bit"`
         * application domain: generic
         * name: SignalEnergyPerBinaryDigit (specializes Energy)
         * quantity dimension: L^2*M^1*T^-2
         * measurement unit(s): J
         * tensor order: 0
         * definition: `E_b = P_c*T_b`, where `P_c` is carrier power (item 13-18) and `T_b` is period of binary digits (item 13-14)
         * remarks: None.
         */
    }

    /* IEC-80000-13 item 13-20 error probability */
    attribute def ErrorProbabilityValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-20 error probability
         * symbol(s): `P`
         * application domain: generic
         * name: ErrorProbability (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: probability that a data element be incorrectly received
         * remarks: A subscript referring to a specified data element can be added to the symbol. EXAMPLES: error probability for binary digits or bit error probability, `P_b` or `P_bit`; block error probability, `P_bl`. The measured value is designated as "error ratio", whereas "error rate" is deprecated, for example, bit error ratio (BER), block error ratio. See IEC 60050-704 and IEC 60050-721.
         */
    }
    attribute errorProbability: ErrorProbabilityValue :> scalarQuantities;

    /* IEC-80000-13 item 13-21 Hamming distance */
    attribute hammingDistance: CountValue :> scalarQuantities {
        doc
        /*
         * source: item 13-21 Hamming distance
         * symbol(s): `d_n`
         * application domain: generic
         * name: HammingDistance (specializes Count)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: number of digit positions in which the corresponding digits of two words of the same length are different
         * remarks: See IEC 60050-721, item 721-08-25.
         */
    }

    /* IEC-80000-13 item 13-22 clock frequency, clock rate */
    attribute clockFrequency: FrequencyValue :> scalarQuantities {
        doc
        /*
         * source: item 13-22 clock frequency, clock rate
         * symbol(s): `f_"cl"`
         * application domain: generic
         * name: ClockFrequency (specializes Frequency)
         * quantity dimension: T^-1
         * measurement unit(s): Hz
         * tensor order: 0
         * definition: frequency at which a clock oscillates
         * remarks: None.
         */
    }

    alias clockRate for clockFrequency;

    /* IEC-80000-13 item 13-23 decision content */
    attribute def DecisionContentValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-23 decision content
         * symbol(s): `D_a`
         * application domain: generic
         * name: DecisionContent (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: `D_a` = `log_a n`, where `a` is the number of possibilities at each decision and `n` the number of events
         * remarks: See ISO/IEC 2382-16, item 16.03.01. See also IEC 60027-3. When the same base is used for the same number of events then `D_a = H_0` , where `H_0` is maximum entropy (item 13-28).
         */
    }
    attribute decisionContent: DecisionContentValue :> scalarQuantities;

    /* IEC-80000-13 item 13-24 information content */
    attribute def InformationContentValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-24 information content
         * symbol(s): `I(x)`
         * application domain: generic
         * name: InformationContent
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `I(x) = log_2(1/(p(x)))` Sh `= log(1/(p(x)))` Hart `= ln(1/(p(x)))` nat, where `p(x)` is the probability of event `x`
         * remarks: See ISO/IEC 2382-16, item 16.03.02. See also IEC 60027-3.
         */
        attribute :>> num: Real;
        attribute :>> mRef: InformationContentUnit[1];
    }

    attribute informationContent: InformationContentValue[*] nonunique :> scalarQuantities;

    attribute def InformationContentUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-25 entropy */
    attribute def EntropyForInformationScienceValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-25 entropy
         * symbol(s): `H`
         * application domain: information science
         * name: Entropy
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `H(X) = sum_(i=1)^n p(x_i) I(x_i)` for the set `X = {x_1, ..., x_n}`, where `p(x_i)` is the probability and `I(x_i)` is the information content of event `x_i`
         * remarks: See ISO/IEC 2382-16, item 16.03.02. See also IEC 60027-3.
         */
        attribute :>> num: Real;
        attribute :>> mRef: EntropyForInformationScienceUnit[1];
    }

    attribute entropyForInformationScience: EntropyForInformationScienceValue[*] nonunique :> scalarQuantities;

    attribute def EntropyForInformationScienceUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-26 maximum entropy */
    attribute def MaximumEntropyValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-26 maximum entropy
         * symbol(s): `H_0`, `H_"max"`
         * application domain: information science
         * name: MaximumEntropy
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: maximum entropy occurs when `p(x_i) = 1/n` for `i = 1, ..., n`
         * remarks: The maximum entropy is sometimes called "decision content" because the value is the same when the base is an integer, for the same number of events. See item 13-23.
         */
        attribute :>> num: Real;
        attribute :>> mRef: MaximumEntropyUnit[1];
    }

    attribute maximumEntropy: MaximumEntropyValue[*] nonunique :> scalarQuantities;

    attribute def MaximumEntropyUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-27 relative entropy */
    attribute def RelativeEntropyValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-27 relative entropy
         * symbol(s): `H_r`
         * application domain: information science
         * name: RelativeEntropy (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: `H_r = H / H_0`, where `H` is entropy (item 13-25) and `H_0` is maximum entropy (item 13-26)
         * remarks: See ISO/IEC 2382-16, item 16.03.04.
         */
    }
    attribute relativeEntropy: RelativeEntropyValue :> scalarQuantities;

    /* IEC-80000-13 item 13-28 redundancy */
    attribute def RedundancyValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-28 redundancy
         * symbol(s): `R`
         * application domain: information science
         * name: Redundancy
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `R = H_0 − H`, where `H` is entropy (item 13-25) and `H_0` is maximum entropy (item 13-26)
         * remarks: See ISO/IEC 2382-16, item 16.03.05.
         */
        attribute :>> num: Real;
        attribute :>> mRef: RedundancyUnit[1];
    }

    attribute redundancy: RedundancyValue[*] nonunique :> scalarQuantities;

    attribute def RedundancyUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-29 relative redundancy */
    attribute def RelativeRedundancyValue :> DimensionOneValue {
        doc
        /*
         * source: item 13-29 relative redundancy
         * symbol(s): `r`
         * application domain: information science
         * name: RelativeRedundancy (specializes DimensionOneQuantity)
         * quantity dimension: 1
         * measurement unit(s): 1
         * tensor order: 0
         * definition: `r = R / H_0`, where `R` is redundancy (item 13-28) and `H_0` is maximum entropy (item 13-26)
         * remarks: See ISO/IEC 2382-16, item 16.04.01.
         */
    }
    attribute relativeRedundancy: RelativeRedundancyValue :> scalarQuantities;

    /* IEC-80000-13 item 13-30 joint information content */
    attribute def JointInformationContentValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-30 joint information content
         * symbol(s): `I(x,y)`
         * application domain: generic
         * name: JointInformationContent
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `I(x,y) = log_2(1/(p(x,y)))` Sh `= log(1/(p(x,y)))` Hart `= ln(1/(p(x,y)))` nat, where `p(x,y)` is the joint probability of events `x` and `y`
         * remarks: None.
         */
        attribute :>> num: Real;
        attribute :>> mRef: JointInformationContentUnit[1];
    }

    attribute jointInformationContent: JointInformationContentValue[*] nonunique :> scalarQuantities;

    attribute def JointInformationContentUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-31 conditional information content */
    attribute def ConditionalInformationContentValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-31 conditional information content
         * symbol(s): `I(x|y)`
         * application domain: generic
         * name: ConditionalInformationContent
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: information content (item 13-2) of event `x` under the condition that `y` has occurred: `I(x|y) = I(x,y) − I( y)`
         * remarks: See ISO/IEC 2382-16, item 16.04.02.
         */
        attribute :>> num: Real;
        attribute :>> mRef: ConditionalInformationContentUnit[1];
    }

    attribute conditionalInformationContent: ConditionalInformationContentValue[*] nonunique :> scalarQuantities;

    attribute def ConditionalInformationContentUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-32 conditional entropy, mean conditional information content, average conditional information content */
    attribute def ConditionalEntropyValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-32 conditional entropy, mean conditional information content, average conditional information content
         * symbol(s): `H(X|Y)`
         * application domain: generic
         * name: ConditionalEntropy
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `H(X|Y) = sum_(i=1)^n sum_(j=1)^m p(x_i,y_j) I(x_i,y_j)` where `p(x_i,y_j)` is the joint probability of events `x_i` and `y_j`, and `I(x_i,y_j)` is conditional information content (item 13-31)
         * remarks: See ISO/IEC 2382-16, item 16.04.04.
         */
        attribute :>> num: Real;
        attribute :>> mRef: ConditionalEntropyUnit[1];
    }

    attribute conditionalEntropy: ConditionalEntropyValue[*] nonunique :> scalarQuantities;

    attribute def ConditionalEntropyUnit :> DimensionOneUnit {
    }

    alias MeanConditionalInformationContentUnit for ConditionalEntropyUnit;
    alias MeanConditionalInformationContentValue for ConditionalEntropyValue;
    alias meanConditionalInformationContent for conditionalEntropy;

    alias AverageConditionalInformationContentUnit for ConditionalEntropyUnit;
    alias AverageConditionalInformationContentValue for ConditionalEntropyValue;
    alias averageConditionalInformationContent for conditionalEntropy;

    /* IEC-80000-13 item 13-33 equivocation */
    attribute def EquivocationValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-33 equivocation
         * symbol(s): `H(X|Y)`
         * application domain: generic
         * name: Equivocation
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: conditional entropy (item 13-32) of a set X of emitted characters given the set Y of received characters
         * remarks: Equivocation is a quantitative measure of the loss of information due to noise. See ISO/IEC 2382-16, item 16.04.05.
         */
        attribute :>> num: Real;
        attribute :>> mRef: EquivocationUnit[1];
    }

    attribute equivocation: EquivocationValue[*] nonunique :> scalarQuantities;

    attribute def EquivocationUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-34 irrelevance */
    attribute def IrrelevanceValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-34 irrelevance
         * symbol(s): `H(Y|X)`
         * application domain: generic
         * name: Irrelevance
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: conditional entropy (item 13-32) of a set `Y` of received characters given the set `X` of emitted characters: `H(Y|X) = H(X|Y) + H(Y) − H(X)`, where `H(X|Y)` is equivocation (item 13-33) and `H` is entropy (item 13-25)
         * remarks: Irrelevance is a quantitative measure of the information added to the emitted information due to distortion. See ISO/IEC 2382 16, item 16.04.06.
         */
        attribute :>> num: Real;
        attribute :>> mRef: IrrelevanceUnit[1];
    }

    attribute irrelevance: IrrelevanceValue[*] nonunique :> scalarQuantities;

    attribute def IrrelevanceUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-35 transinformation content */
    attribute def TransinformationContentValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-35 transinformation content
         * symbol(s): `T(x,y)`
         * application domain: generic
         * name: TransinformationContent
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `T(x,y) = I(x) + I(y) − I(x,y)`, where `I(x)` and `I(y)` are the information contents (13-24) of events `x` and `y`, respectively, and `I(x,y)` is their joint information content (13-30)
         * remarks: See ISO/IEC 2382-16, item 16.04.07.
         */
        attribute :>> num: Real;
        attribute :>> mRef: TransinformationContentUnit[1];
    }

    attribute transinformationContent: TransinformationContentValue[*] nonunique :> scalarQuantities;

    attribute def TransinformationContentUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-36 mean transinformation content */
    attribute def MeanTransinformationContentValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-36 mean transinformation content
         * symbol(s): `T`
         * application domain: generic
         * name: MeanTransinformationContent
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `T(X,Y) = sum_(i=1)^n sum_(j=1)^m p(x_i,y_j) T(x_i,y_j)` for the sets `X = {x_1, ..., x_n}`, `Y = {y_1, ..., y_m}`, where `p(x_i,y_j)` is the joint probability of events `x_i` and `y_j`, and `T(x_i,y_j)` is their transinformation content (item 13-35)
         * remarks: See ISO/IEC 2382-16, item 16.04.08. In practice, the unit "shannon per character" is generally used, and sometimes the units "hartley per character" and "natural unit per character".
         */
        attribute :>> num: Real;
        attribute :>> mRef: MeanTransinformationContentUnit[1];
    }

    attribute meanTransinformationContent: MeanTransinformationContentValue[*] nonunique :> scalarQuantities;

    attribute def MeanTransinformationContentUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-37 character mean entropy */
    attribute def CharacterMeanEntropyValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-37 character mean entropy
         * symbol(s): `H'`
         * application domain: generic
         * name: CharacterMeanEntropy
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `H' = lim_(m->∞) H_m/m` where `H_m` is the entropy (item 13-3) of the set of all sequences of `m` characters
         * remarks: See ISO/IEC 2382-16, item 16.04.09.
         */
        attribute :>> num: Real;
        attribute :>> mRef: CharacterMeanEntropyUnit[1];
    }

    attribute characterMeanEntropy: CharacterMeanEntropyValue[*] nonunique :> scalarQuantities;

    attribute def CharacterMeanEntropyUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-38 average information rate */
    attribute def AverageInformationRateValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-38 average information rate
         * symbol(s): `H^"*"`
         * application domain: generic
         * name: AverageInformationRate
         * quantity dimension: T^-1
         * measurement unit(s): Sh/s, Hart/s, nat/s
         * tensor order: 0
         * definition: `H^"*" = (H')/(t(X))`, where `H'` is character mean entropy (item 13-37) and `t(X)` is the mean value of the duration of a character in the set `X`
         * remarks: See ISO/IEC 2382-16, item 16.04.10.
         */
        attribute :>> num: Real;
        attribute :>> mRef: AverageInformationRateUnit[1];
    }

    attribute averageInformationRate: AverageInformationRateValue[*] nonunique :> scalarQuantities;

    attribute def AverageInformationRateUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    /* IEC-80000-13 item 13-39 character mean transinformation content */
    attribute def CharacterMeanTransinformationContentValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-39 character mean transinformation content
         * symbol(s): `T'`
         * application domain: generic
         * name: CharacterMeanTransinformationContent
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `T' = lim_(m->∞) T_m/m` where `T_m` is the mean transinformation content (item 13-36) for all pairs of input and output sequences of `m` characters
         * remarks: See ISO/IEC 2382-16, item 16.04.11. In practice, the unit "shannon per character" is generally used, and sometimes the units "hartley per character" and "natural unit per character".
         */
        attribute :>> num: Real;
        attribute :>> mRef: CharacterMeanTransinformationContentUnit[1];
    }

    attribute characterMeanTransinformationContent: CharacterMeanTransinformationContentValue[*] nonunique :> scalarQuantities;

    attribute def CharacterMeanTransinformationContentUnit :> DimensionOneUnit {
    }

    /* IEC-80000-13 item 13-40 average transinformation rate */
    attribute def AverageTransinformationRateValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-40 average transinformation rate
         * symbol(s): `T^"*"`
         * application domain: generic
         * name: AverageTransinformationRate
         * quantity dimension: T^-1
         * measurement unit(s): Sh/s, Hart/s, nat/s
         * tensor order: 0
         * definition: `T^"*" =  (T')/(sum_(i=1)^n sum_(j=1)^m p(x_i,y_j) t(x_i,y_j) )`, where `T'` is character mean transinformation content (item 13-39) and `t(x_i,y_j)` is the mean duration of the pair of characters `(x_i,y_j)` with joint probability `p(x_i,y_j)`
         * remarks: See ISO/IEC 2382-16, item 16.04.12.
         */
        attribute :>> num: Real;
        attribute :>> mRef: AverageTransinformationRateUnit[1];
    }

    attribute averageTransinformationRate: AverageTransinformationRateValue[*] nonunique :> scalarQuantities;

    attribute def AverageTransinformationRateUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

    /* IEC-80000-13 item 13-41 channel capacity per character, channel capacity */
    attribute def ChannelCapacityPerCharacterValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-41 channel capacity per character, channel capacity
         * symbol(s): `C'`
         * application domain: generic
         * name: ChannelCapacityPerCharacter
         * quantity dimension: 1
         * measurement unit(s): Sh, Hart, nat
         * tensor order: 0
         * definition: `C' = max T'`, where `T'` is character mean transinformation content (item 13-39)
         * remarks: See ISO/IEC 2382-16, item 16.04.13. In practice, the unit "shannon per character" is generally used, and sometimes the units "hartley per character" and "natural unit per character".
         */
        attribute :>> num: Real;
        attribute :>> mRef: ChannelCapacityPerCharacterUnit[1];
    }

    attribute channelCapacityPerCharacter: ChannelCapacityPerCharacterValue[*] nonunique :> scalarQuantities;

    attribute def ChannelCapacityPerCharacterUnit :> DimensionOneUnit {
    }

    alias ChannelCapacityUnit for ChannelCapacityPerCharacterUnit;
    alias ChannelCapacityValue for ChannelCapacityPerCharacterValue;
    alias channelCapacity for channelCapacityPerCharacter;

    /* IEC-80000-13 item 13-42 channel time capacity */
    attribute def ChannelTimeCapacityValue :> ScalarQuantityValue {
        doc
        /*
         * source: item 13-42 channel time capacity
         * symbol(s): `C^"*"`
         * application domain: generic
         * name: ChannelTimeCapacity
         * quantity dimension: T^-1
         * measurement unit(s): Sh/s, Hart/s, nat/s
         * tensor order: 0
         * definition: `C^"*" = max T^"*"`, where `T^"*"` is average transinformation rate (item 13-40)
         * remarks: See ISO/IEC 2382-16, item 16.04.13. Note for SysML ISQ: Alias "channel capacity", that was present in IEC 80000-12:2008, has been removed as it duplicates the alias of channel capacity per character (item 13-41).
         */
        attribute :>> num: Real;
        attribute :>> mRef: ChannelTimeCapacityUnit[1];
    }

    attribute channelTimeCapacity: ChannelTimeCapacityValue[*] nonunique :> scalarQuantities;

    attribute def ChannelTimeCapacityUnit :> DerivedUnit {
        private attribute durationPF: QuantityPowerFactor[1] { :>> quantity = isq.T; :>> exponent = -1; }
        attribute :>> quantityDimension { :>> quantityPowerFactors = durationPF; }
    }

}
