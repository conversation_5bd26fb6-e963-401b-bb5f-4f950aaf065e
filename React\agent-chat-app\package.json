{"name": "agent-chat-app", "author": "Your Name", "private": true, "workspaces": ["apps/*"], "scripts": {"dev": "concurrently \"turbo dev --filter=web\" \"turbo dev --filter=agents\"", "build": "turbo build", "turbo:command": "turbo", "format": "turbo format", "lint": "turbo lint", "lint:fix": "turbo lint:fix"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@tsconfig/recommended": "^1.0.8", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "concurrently": "^9.1.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^5.2.3", "prettier": "^3.3.3", "raw-loader": "^4.0.2", "tsx": "^4.19.1", "turbo": "latest", "typescript": "^5", "vscode-languageclient": "~9.0.1", "worker-loader": "^3.0.8"}, "packageManager": "npm@11.2.1", "overrides": {"@langchain/core": "^0.3.42"}, "dependencies": {"@langchain/community": "^0.3.48", "@monaco-editor/react": "^4.7.0", "monaco-editor": "^0.52.2"}}