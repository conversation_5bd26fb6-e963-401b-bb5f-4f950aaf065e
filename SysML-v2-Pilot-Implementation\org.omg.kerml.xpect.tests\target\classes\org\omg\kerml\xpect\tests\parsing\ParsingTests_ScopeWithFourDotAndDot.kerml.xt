//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	classifier C{
		//* XPECT errors ---
		"Couldn't resolve reference to Feature 'OuterPackage::B'." at "OuterPackage::B"
		"Couldn't resolve reference to Feature 'b'." at "b"
		---
		*/
		feature c : OuterPackage::B.b;
	}
}
