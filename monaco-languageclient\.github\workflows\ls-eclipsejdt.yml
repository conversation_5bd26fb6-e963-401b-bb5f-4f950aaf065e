name: Eclipse JDT LS Image

on:
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  REPO_NAME: ${{ github.repository }}
  PATH_CONTEXT: ./packages/examples/resources/eclipse.jdt.ls
  CONTAINER_NAME: eclipse.jdt.ls

jobs:
  image-eclipsejdtls:
    name: Build & Deploy Eclipse JDT LS
    runs-on: ubuntu-latest

    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write

    timeout-minutes: 15
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: |
          ${{ env.REGISTRY }}/${{ env.REPO_NAME }}/${{ env.CONTAINER_NAME }}
        # enforce latest tag for now
        tags: |
          type=raw,value=latest

    - name: Build & Push
      id: push
      uses: docker/build-push-action@v6
      with:
        context: .
        file: ${{ env.PATH_CONTEXT }}/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

    - name: Attest
      uses: actions/attest-build-provenance@v1
      with:
        subject-name: ${{ env.REGISTRY }}/${{ env.REPO_NAME }}/${{ env.CONTAINER_NAME }}
        subject-digest: ${{ steps.push.outputs.digest }}
        push-to-registry: true
