'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useToast } from '@/components/ui/Toast';
import { authApi } from '@/utils/api';
import { validateUsername, validatePassword, hasValidationErrors, getFieldError } from '@/utils/validation';
import { ValidationError } from '@/types';

const RegisterPage: React.FC = () => {
  const router = useRouter();
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  
  const [errors, setErrors] = useState<ValidationError[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // 清除该字段的错误
    setErrors(prev => prev.filter(error => error.field !== field));
  };

  const validateForm = (): boolean => {
    const usernameErrors = validateUsername(formData.username);
    const passwordErrors = validatePassword(formData.password);
    
    const allErrors = [...usernameErrors, ...passwordErrors];
    setErrors(allErrors);
    
    return !hasValidationErrors(allErrors);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const response = await authApi.register(formData.username, formData.password);
      
      if (response.success) {
        showToast({
          type: 'success',
          title: '注册成功',
          message: '账号创建成功，即将跳转到登录页面',
          duration: 3000,
        });
        
        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          router.push('/login');
        }, 1500);
      } else {
        showToast({
          type: 'error',
          title: '注册失败',
          message: response.error || '注册过程中发生错误，请重试',
        });
      }
    } catch (error) {
      showToast({
        type: 'error',
        title: '注册失败',
        message: '网络错误，请检查网络连接后重试',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="text-center mb-8">
          <h1 className="auth-title">创建账号</h1>
          <p className="text-gray-600">
            欢迎使用 SysML v2 建模平台
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Input
            label="用户名"
            type="text"
            placeholder="请输入用户名"
            value={formData.username}
            onChange={(e) => handleInputChange('username', e.target.value)}
            error={getFieldError(errors, 'username')}
            required
            autoComplete="username"
          />

          <Input
            label="密码"
            type="password"
            placeholder="请输入密码（至少8位）"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            error={getFieldError(errors, 'password')}
            required
            autoComplete="new-password"
          />

          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isSubmitting}
            className="w-full"
            disabled={isSubmitting}
          >
            {isSubmitting ? '注册中...' : '注册'}
          </Button>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            已有账号？
            <Link href="/login" className="auth-link ml-1">
              去登录
            </Link>
          </p>
        </div>

        <div className="mt-8 text-center">
          <div className="text-xs text-gray-500">
            <p>注册即表示您同意我们的</p>
            <div className="mt-1">
              <a href="#" className="auth-link">服务条款</a>
              <span className="mx-2">和</span>
              <a href="#" className="auth-link">隐私政策</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;
