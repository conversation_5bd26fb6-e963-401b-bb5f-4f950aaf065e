//* 
XPECT_SETUP org.omg.sysml.xpect.tests.validation.valid.SysMLTests
	ResourceSet {
           ThisFile {}
            File {from ="/library.kernel/Base.kerml"}
            File {from ="/library.kernel/Occurrences.kerml"}
            File {from ="/library.kernel/Objects.kerml"}
            File {from ="/library.systems/Items.sysml"}
            File {from ="/library.systems/Parts.sysml"}
        }
        Workspace {
            JavaProject {
                SrcFolder {
                    ThisFile {}
			            File {from ="/library.kernel/Base.kerml"}
			            File {from ="/library.kernel/Occurrences.kerml"}
			            File {from ="/library.kernel/Objects.kerml"}
			            File {from ="/library.systems/Items.sysml"}
			            File {from ="/library.systems/Parts.sysml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package 'Redefinition Example' {
	
	part def A :> C;
	
	part def B {
		part a: A;
	}
	
	part def C :> A, B {
		part a redefines B::a;
	}

}
