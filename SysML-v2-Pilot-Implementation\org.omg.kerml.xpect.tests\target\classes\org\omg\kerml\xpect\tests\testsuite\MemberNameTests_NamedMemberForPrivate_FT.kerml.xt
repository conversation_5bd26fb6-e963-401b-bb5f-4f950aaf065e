//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/
//XPECT noErrors ---> ""
package test {
	private feature something{}
	private alias k for something;
	feature hello {
		//* XPECT scope at k ---
		hello, hello.self, hello.test, hello.test.self, hello.test.that,
		hello.test.that.self, hello.that, hello.that.self, k, k.self, k.that, k.that.self, self, something,
		something.self, something.that, something.that.self, test, test.self, test.that,
		test.that.self, that, that.self
		--- */
		//XPECT linkedName at k --> test.something
		feature test : k{}
	}
}
