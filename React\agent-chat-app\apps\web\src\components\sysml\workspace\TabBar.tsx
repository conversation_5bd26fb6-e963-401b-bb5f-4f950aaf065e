'use client';

import React from 'react';
import { useSysML } from '@/contexts/SysMLContext';
import { EditorTab } from '@/types/sysml';

const TabBar: React.FC = () => {
  const { state, dispatch } = useSysML();
  const { tabs, activeTabId } = state.editor;

  // 切换标签页
  const handleTabClick = (tabId: string) => {
    dispatch({ type: 'SET_ACTIVE_TAB', payload: tabId });
  };

  // 关闭标签页
  const handleTabClose = (tabId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    const tab = tabs.find(t => t.id === tabId);
    if (tab?.isDirty) {
      // 如果有未保存的更改，显示确认对话框
      const confirmed = window.confirm('文件有未保存的更改，确定要关闭吗？');
      if (!confirmed) {
        return;
      }
    }
    
    dispatch({ type: 'REMOVE_EDITOR_TAB', payload: tabId });
  };

  // 获取文件图标
  const getFileIcon = (tab: EditorTab) => {
    return '📄'; // SysML 文件图标
  };

  if (tabs.length === 0) {
    return null;
  }

  return (
    <div className="flex items-center bg-gray-100 border-b border-gray-200 overflow-x-auto">
      {tabs.map(tab => (
        <div
          key={tab.id}
          className={`
            flex items-center px-4 py-2 border-r border-gray-200 cursor-pointer min-w-0 max-w-xs
            ${activeTabId === tab.id 
              ? 'bg-white text-gray-900 border-b-2 border-blue-500' 
              : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }
          `}
          onClick={() => handleTabClick(tab.id)}
          title={tab.title}
        >
          <span className="text-sm mr-2">{getFileIcon(tab)}</span>
          <span className="text-sm font-medium truncate flex-1">{tab.title}</span>
          {tab.isDirty && (
            <span className="text-orange-500 ml-1 text-xs">●</span>
          )}
          <button
            className="ml-2 p-1 rounded hover:bg-gray-300 text-gray-500 hover:text-gray-700"
            onClick={(e) => handleTabClose(tab.id, e)}
            title="关闭"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      ))}
    </div>
  );
};

export default TabBar;
