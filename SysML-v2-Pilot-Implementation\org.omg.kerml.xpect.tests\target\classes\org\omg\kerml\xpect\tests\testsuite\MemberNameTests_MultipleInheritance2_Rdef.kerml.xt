//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

//XPECT noErrors ---> ""
package test{
	feature A{
		feature a;
	}
	//* XPECT scope at A ---
	A, A.a, A.a.self, A.a.that, A.a.that.self, A.self, A.that, A.that.self, B, B.b,
	B.b.self, B.b.that, B.b.that.self, B.self, B.that, B.that.self, C, C.c, C.c.self,
	C.c.that, C.c.that.self, C.self, C.that, C.that.self, D, D.self, D.that, D.that.self,
	test.A, test.A.a, test.A.a.self, test.A.a.that, test.A.a.that.self, test.A.self,
	test.A.that, test.A.that.self, test.B, test.B.b, test.B.b.self, test.B.b.that,
	test.B.b.that.self, test.B.self, test.B.that, test.B.that.self, test.C, test.C.c, test.C.c.self,
	test.C.c.that, test.C.c.that.self, test.C.self, test.C.that, test.C.that.self, test.D,
	test.D.self, test.D.that, test.D.that.self
	--- */
	feature B subsets A{
	//scope test a here get Caused by: java.lang.RuntimeException: The EReference 'Element.ownedRelationship' found at ...	--- */
	//\n		fe|ature b redef... is not valid here.
		feature b redefines a {}
	}
	//* XPECT scope at B ---
	A, A.a, A.a.self, A.a.that, A.a.that.self, A.self, A.that, A.that.self, B, B.b,
	B.b.self, B.b.that, B.b.that.self, B.self, B.that, B.that.self, C, C.c, C.c.self,
	C.c.that, C.c.that.self, C.self, C.that, C.that.self, D, D.self, D.that, D.that.self,
	test.A, test.A.a, test.A.a.self, test.A.a.that, test.A.a.that.self, test.A.self,
	test.A.that, test.A.that.self, test.B, test.B.b, test.B.b.self, test.B.b.that,
	test.B.b.that.self, test.B.self, test.B.that, test.B.that.self, test.C, test.C.c, test.C.c.self,
	test.C.c.that, test.C.c.that.self, test.C.self, test.C.that, test.C.that.self, test.D,
	test.D.self, test.D.that, test.D.that.self
	--- */
	feature C subsets B{
		feature c redefines b;
	}
	//* XPECT scope at C.c ---
	A, A.a, A.a.self, A.a.that, A.a.that.self, A.self, A.that, A.that.self, B, B.b,
	B.b.self, B.b.that, B.b.that.self, B.self, B.that, B.that.self, C, C.c, C.c.self,
	C.c.that, C.c.that.self, C.self, C.that, C.that.self, D, D.self, D.that, D.that.self,
	test.A, test.A.a, test.A.a.self, test.A.a.that, test.A.a.that.self, test.A.self,
	test.A.that, test.A.that.self, test.B, test.B.b, test.B.b.self, test.B.b.that,
	test.B.b.that.self, test.B.self, test.B.that, test.B.that.self, test.C, test.C.c, test.C.c.self,
	test.C.c.that, test.C.c.that.self, test.C.self, test.C.that, test.C.that.self, test.D,
	test.D.self, test.D.that, test.D.that.self
	--- */
	feature D subsets C.c;
}
