//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
		File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package test{
	public import OuterPackage::A;
	classifier C {
		//XPECT linkedName at A --> OuterPackage.A
		//* XPECT scope at A ---
		   A, A.a1, C, C.c, C.c.a1, C.self, C.self.that, OuterPackage.A,
		   OuterPackage.A.a1, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1, c, c.a1, self,
		   self.that, test.A, test.A.a1, test.C, test.C.c, test.C.c.a1, test.C.self,
		   test.C.self.that
		--- */
		feature c: A;
	}
}
