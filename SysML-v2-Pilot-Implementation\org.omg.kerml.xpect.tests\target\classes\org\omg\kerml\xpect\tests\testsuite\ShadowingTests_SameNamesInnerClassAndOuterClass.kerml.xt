//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package test{
	classifier A{
		classifier A {
			//XPECT linkedName at A --> test.A.A
			//* XPECT scope at A ---
			A.B.B, B.B, test.A.A.B.B,
		    A, A.B, B, 
		    test.A, test.A.A, test.A.A.B,
		--- */
			classifier B specializes A{}
		}
	}
}
