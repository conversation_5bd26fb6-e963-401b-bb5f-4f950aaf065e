// Langium Client for communicating with Web Worker
import { DiagnosticMessage } from '@/types/sysml';

export interface CompletionItem {
  label: string;
  kind: number;
  detail?: string;
  documentation?: string;
  insertText?: string;
  insertTextRules?: number;
  range?: {
    startLineNumber: number;
    startColumn: number;
    endLineNumber: number;
    endColumn: number;
  };
}

export interface ValidationResult {
  uri: string;
  diagnostics: DiagnosticMessage[];
}

export interface CompletionResult {
  uri: string;
  items: CompletionItem[];
}

export class LangiumClient {
  private worker: Worker | null = null;
  private messageId = 0;
  private pendingRequests = new Map<number, {
    resolve: (value: any) => void;
    reject: (error: any) => void;
  }>();

  constructor() {
    this.initWorker();
  }

  private async initWorker() {
    try {
      // 创建 Web Worker - 使用 Next.js 13+ 原生支持
      this.worker = new Worker(
        new URL('../../workers/sysml-language-server.worker.ts', import.meta.url),
        {
          type: 'module',
          name: 'sysml-language-server'
        }
      );

      // 监听 Worker 消息
      this.worker.addEventListener('message', this.handleWorkerMessage.bind(this));
      
      // 监听 Worker 错误
      this.worker.addEventListener('error', (event) => {
        console.error('Langium Worker error:', event.error);
      });

      // 启动语言服务器
      this.worker.postMessage({ type: 'start' });
      
      console.log('Langium Client initialized');
    } catch (error) {
      console.error('Failed to initialize Langium Worker:', error);
    }
  }

  private handleWorkerMessage(event: MessageEvent) {
    const { type, data, messageId } = event.data;

    // 处理带有 messageId 的响应
    if (messageId && this.pendingRequests.has(messageId)) {
      const request = this.pendingRequests.get(messageId)!;
      this.pendingRequests.delete(messageId);

      if (type.endsWith('-error')) {
        request.reject(new Error(data.error));
      } else {
        request.resolve(data);
      }
      return;
    }

    // 处理其他消息类型
    switch (type) {
      case 'validation-result':
        this.onValidationResult?.(data);
        break;
      case 'completion-result':
        this.onCompletionResult?.(data);
        break;
      case 'worker-error':
        console.error('Worker error:', data.error);
        break;
      default:
        console.warn('Unknown message type from worker:', type);
    }
  }

  private sendMessage(type: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.worker) {
        reject(new Error('Worker not initialized'));
        return;
      }

      const messageId = ++this.messageId;
      this.pendingRequests.set(messageId, { resolve, reject });

      this.worker.postMessage({
        type,
        data,
        messageId
      });

      // 设置超时
      setTimeout(() => {
        if (this.pendingRequests.has(messageId)) {
          this.pendingRequests.delete(messageId);
          reject(new Error('Request timeout'));
        }
      }, 10000); // 10秒超时
    });
  }

  // 验证代码
  async validateCode(code: string, uri: string = 'file:///temp.sysml'): Promise<ValidationResult> {
    try {
      const result = await this.sendMessage('validate', { code, uri });
      return {
        uri: result.uri,
        diagnostics: result.diagnostics.map((d: any) => ({
          id: `diag-${Date.now()}-${Math.random()}`,
          severity: this.mapSeverity(d.severity),
          message: d.message,
          line: d.range?.start?.line + 1 || 1,
          column: d.range?.start?.character + 1 || 1,
          source: d.source || 'SysML'
        }))
      };
    } catch (error) {
      console.error('Code validation failed:', error);
      return {
        uri,
        diagnostics: [{
          id: `error-${Date.now()}`,
          severity: 'error' as const,
          message: `Validation failed: ${error instanceof Error ? error.message : String(error)}`,
          line: 1,
          column: 1,
          source: 'SysML Client'
        }]
      };
    }
  }

  // 获取代码补全
  async getCompletions(
    code: string, 
    position: { line: number; character: number },
    uri: string = 'file:///temp.sysml'
  ): Promise<CompletionResult> {
    try {
      const result = await this.sendMessage('complete', { code, uri, position });
      return {
        uri: result.uri,
        items: result.items.map((item: any) => ({
          label: item.label,
          kind: item.kind || 1, // Text
          detail: item.detail,
          documentation: item.documentation,
          insertText: item.insertText || item.label,
          insertTextRules: item.insertTextRules,
          range: item.range
        }))
      };
    } catch (error) {
      console.error('Code completion failed:', error);
      return {
        uri,
        items: []
      };
    }
  }

  // 映射诊断严重程度
  private mapSeverity(severity: number): 'error' | 'warning' | 'info' {
    switch (severity) {
      case 1: return 'error';
      case 2: return 'warning';
      case 3: return 'info';
      default: return 'error';
    }
  }

  // 事件回调
  onValidationResult?: (result: ValidationResult) => void;
  onCompletionResult?: (result: CompletionResult) => void;

  // 销毁客户端
  dispose() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.pendingRequests.clear();
  }
}

// 单例实例
let langiumClient: LangiumClient | null = null;

export function getLangiumClient(): LangiumClient {
  if (!langiumClient) {
    langiumClient = new LangiumClient();
  }
  return langiumClient;
}

export function disposeLangiumClient() {
  if (langiumClient) {
    langiumClient.dispose();
    langiumClient = null;
  }
}
