//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private_alias'." at "VisibilityPackage::c_Private_alias"
	public import VisibilityPackage::c_Private_alias::*;
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private_alias::c_public'." at "VisibilityPackage::c_Private_alias::c_public"
	public import VisibilityPackage::c_Private_alias::c_public::*;
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private_alias::alias_private'." at "VisibilityPackage::c_Private_alias::alias_private"
	public import VisibilityPackage::c_Private_alias::alias_private::*;
}
