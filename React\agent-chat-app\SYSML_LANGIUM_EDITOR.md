# SysML v2 Langium 编辑器实现文档

## 🎯 概述

基于 `monaco-languageclient` 项目的实现方式，成功创建了一个完整的 SysML v2 代码编辑器，使用 Langium 框架和 Monaco Editor，通过 Web Worker 运行语言服务器。

## 🏗️ 技术架构

### 核心组件

```
SysML v2 Langium 编辑器
├── MonacoEditorLanguageClientWrapper (monaco-editor-wrapper)
│   ├── 编辑器配置 (WrapperConfig)
│   ├── 语言客户端配置 (LanguageClientConfig)
│   └── 主题和语法高亮 (Monarch + 自定义主题)
├── Langium 语言服务器 (Web Worker)
│   ├── SysML 语法解析 (sysml.langium)
│   ├── 语言服务 (createSysmlServices)
│   └── LSP 协议支持 (startLanguageServer)
├── React 组件层
│   ├── SysMLWrapperEditor (基于 monaco-editor-wrapper)
│   ├── EnhancedSysMLEditor (增强编辑器，支持模式切换)
│   └── 集成到现有工作区 (WorkspacePage)
└── 配置和工具
    ├── sysml-wrapper-config.ts (编辑器配置)
    ├── sysml-langium-server.worker.ts (语言服务器 Worker)
    └── Next.js 配置 (webpack 支持)
```

## 📁 文件结构

```
apps/web/src/
├── components/sysml/editor/
│   ├── SysMLWrapperEditor.tsx          # 基于 monaco-editor-wrapper 的编辑器
│   ├── EnhancedSysMLEditor.tsx         # 增强编辑器组件（支持模式切换）
│   └── MonacoEditor.tsx                # 原有的 Monaco 编辑器（保留作为备选）
├── lib/sysml/
│   └── sysml-wrapper-config.ts         # 编辑器配置文件
├── workers/
│   └── sysml-langium-server.worker.ts  # Langium 语言服务器 Worker
├── language/                           # 现有的 Langium 语言定义
│   ├── sysml.langium                   # SysML v2 语法文件
│   ├── sysml-module.ts                 # Langium 服务模块
│   ├── main-browser.ts                 # 浏览器端语言服务器
│   └── generated/                      # Langium 生成的文件
└── app/sysml/editor-test/
    └── page.tsx                        # 编辑器测试页面
```

## 🚀 主要特性

### ✅ 已实现功能

1. **完整的 Langium 集成**
   - 使用 `MonacoEditorLanguageClientWrapper`
   - Web Worker 中运行 Langium 语言服务器
   - LSP 协议支持完整的语言功能

2. **增强的编辑器功能**
   - 语法高亮（基于 Monarch tokenizer）
   - 智能代码补全（Langium 提供）
   - 实时错误诊断
   - 语法验证和错误标记

3. **用户体验优化**
   - 自定义 SysML v2 深色主题
   - 编辑器模式切换（Langium vs 传统模式）
   - 加载状态和错误处理
   - 快捷键支持（Ctrl+S 保存等）

4. **性能优化**
   - Web Worker 避免阻塞主线程
   - 异步加载和初始化
   - 资源清理和内存管理

## 🔧 技术实现细节

### 1. Monaco Editor Wrapper 配置

```typescript
// sysml-wrapper-config.ts
export const setupSysMLWrapperConfig = async (params: {
  worker: Worker;
  messageTransports?: MessageTransports;
  htmlContainer: HTMLElement;
  initialCode?: string;
}): Promise<WrapperConfig> => {
  return {
    $type: 'classic',
    htmlContainer: params.htmlContainer,
    editorAppConfig: {
      languageDef: {
        monarchLanguage: SysMLMonarchContent,
        languageExtensionConfig: { id: 'sysml' }
      }
    },
    languageClientConfigs: {
      configs: {
        sysml: {
          connection: {
            options: { $type: 'WorkerDirect', worker: params.worker },
            messageTransports: params.messageTransports
          }
        }
      }
    }
  };
};
```

### 2. Langium 语言服务器 Worker

```typescript
// sysml-langium-server.worker.ts
import { startLanguageServer } from 'langium/lsp';
import { createSysmlServices } from '../language/sysml-module';

const messageReader = new BrowserMessageReader(self);
const messageWriter = new BrowserMessageWriter(self);
const connection = createConnection(messageReader, messageWriter);

const { shared } = createSysmlServices({ connection, ...EmptyFileSystem });
startLanguageServer(shared);
```

### 3. React 组件集成

```typescript
// SysMLWrapperEditor.tsx
const SysMLWrapperEditor: React.FC<Props> = ({ value, onChange, onSave }) => {
  const createLangiumWorker = () => {
    return new Worker(
      new URL('../../../workers/sysml-langium-server.worker.ts', import.meta.url),
      { type: 'module', name: 'SysML Langium LS' }
    );
  };

  const initializeEditor = async () => {
    const worker = createLangiumWorker();
    const reader = new BrowserMessageReader(worker);
    const writer = new BrowserMessageWriter(worker);
    
    const config = await setupSysMLWrapperConfig({
      worker, messageTransports: { reader, writer }, htmlContainer
    });
    
    const wrapper = new MonacoEditorLanguageClientWrapper();
    await wrapper.initAndStart(config);
  };
};
```

## 📋 使用指南

### 1. 开发环境设置

```bash
# 安装依赖
npm install monaco-editor-wrapper@^6.9.0
npm install @codingame/monaco-vscode-api@~18.1.0
npm install @codingame/monaco-vscode-keybindings-service-override@~18.1.0

# 启动开发服务器
npm run dev
```

### 2. 测试编辑器

访问测试页面：`http://localhost:3000/sysml/editor-test`

### 3. 在工作区中使用

编辑器已集成到现有的 SysML 工作区中，支持：
- 编辑器模式切换（Langium vs 传统）
- 多标签页编辑
- 文件保存和管理

## 🔍 与原实现的对比

| 特性 | 原实现 | 新实现 (Langium) |
|------|--------|------------------|
| 语法高亮 | 基础 Monarch | 完整 Langium + Monarch |
| 代码补全 | 静态关键字 | 智能语义补全 |
| 错误诊断 | 基础验证 | 完整语法分析 |
| 语言服务器 | 简化版 | 完整 Langium LSP |
| 性能 | 主线程运行 | Web Worker 后台运行 |
| 扩展性 | 有限 | 完全可扩展 |

## 🚧 已知问题和限制

1. **依赖版本兼容性**
   - monaco-editor-wrapper 需要特定版本的 @codingame 包
   - 需要确保版本兼容性

2. **Web Worker 加载**
   - Next.js 的 Web Worker 支持需要正确配置
   - 可能需要调整 webpack 配置

3. **内存使用**
   - Langium 语言服务器会占用额外内存
   - 需要正确清理资源

## 🔮 未来改进计划

### 短期目标
- [ ] 完善错误处理和用户反馈
- [ ] 优化加载性能和用户体验
- [ ] 添加更多 SysML v2 语言功能
- [ ] 完善测试覆盖

### 长期目标
- [ ] 支持多文件项目分析
- [ ] 实现符号导航和引用查找
- [ ] 添加代码格式化功能
- [ ] 集成调试和验证工具

## 📚 参考资料

- [Monaco Language Client](https://github.com/TypeFox/monaco-languageclient)
- [Langium Framework](https://langium.org/)
- [Monaco Editor](https://microsoft.github.io/monaco-editor/)
- [SysML v2 Specification](https://www.omg.org/spec/SysML/2.0/)

---

**SysML v2 Langium 编辑器现已准备就绪！** 🎉
