//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	alias aliass for OuterPackage::A;
	//XPECT linkedName at aliass --> OuterPackage.A
	classifier C {
		//* XPECT scope at aliass---
		   C, C.c, C.c.a1, C.self, C.self.that, OuterPackage.A, OuterPackage.A.a1,
		   OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1, aliass, aliass.a1, c, c.a1, self,
		   self.that, test.C, test.C.c, test.C.c.a1, test.C.self, test.C.self.that, test.aliass,
		   test.aliass.a1
		--- */
		feature c: aliass;
	}
}
