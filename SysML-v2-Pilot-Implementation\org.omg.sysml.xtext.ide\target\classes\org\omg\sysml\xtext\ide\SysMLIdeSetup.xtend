/*
 * generated by Xtext 2.18.0.M3
 */
package org.omg.sysml.xtext.ide

import com.google.inject.Guice
import org.eclipse.xtext.util.Modules2
import org.omg.sysml.xtext.SysMLRuntimeModule
import org.omg.sysml.xtext.SysMLStandaloneSetup

/**
 * Initialization support for running Xtext languages as language servers.
 */
class SysMLIdeSetup extends SysMLStandaloneSetup {

	override createInjector() {
		Guice.createInjector(Modules2.mixin(new SysMLRuntimeModule, new SysMLIdeModule))
	}
	
}
