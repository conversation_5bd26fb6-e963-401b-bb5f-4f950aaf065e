/*
 * generated by Xtext 2.24.0
 */
package org.omg.kerml.expressions.xtext.validation


/**
 * This class contains custom validation rules. 
 *
 * See https://www.eclipse.org/Xtext/documentation/303_runtime_concepts.html#validation
 */
class KerMLExpressionsValidator extends AbstractKerMLExpressionsValidator {
	
//	public static val INVALID_NAME = 'invalidName'
//
//	@Check
//	def checkGreetingStartsWithCapital(Greeting greeting) {
//		if (!Character.isUpperCase(greeting.name.charAt(0))) {
//			warning('Name should start with a capital', 
//					KerMLExpressionsPackage.Literals.GREETING__NAME,
//					INVALID_NAME)
//		}
//	}
	
}
