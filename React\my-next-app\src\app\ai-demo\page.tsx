'use client';

import React from 'react';
import AIPromptDemo from '@/components/workspace/AIPromptDemo';

export default function AIDemoPage() {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="py-8">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🤖 AI 提示词功能演示
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              体验全新的 AI 驱动的 SysML v2 代码生成功能。
              通过自然语言描述，让 AI 帮您生成或修改代码。
            </p>
          </div>
          
          <AIPromptDemo />
          
          <div className="mt-12 text-center">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                🔧 技术特性
              </h2>
              <div className="grid md:grid-cols-3 gap-6 text-left">
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">🧠 智能理解</h3>
                  <p className="text-sm text-gray-600">
                    基于 Gemini-2.5-Pro 大模型，深度理解自然语言需求
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">🔄 上下文感知</h3>
                  <p className="text-sm text-gray-600">
                    分析现有代码结构，生成符合上下文的代码扩展
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-800 mb-2">✅ 语法准确</h3>
                  <p className="text-sm text-gray-600">
                    生成符合 SysML v2 规范的有效代码，减少语法错误
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
