//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/library/Links.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/library/Links.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	feature x;
	feature y;
	assoc A {
		end end1;
		end end2;
		
		feature f {
			feature z;
		}
		
		connector c from end1 to f.z;
	}
	
	connector a:A from x to y;
	
	// Checks that ends from connector c are redefined.
	connector :> a.c from a.end1 to a.f.z;
}
