//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

//XPECT noErrors ---> ""
package Test3{
	public import VisibilityPackage::c_clazz::*;
	classifier A specializes c_Public{}
	classifier B specializes c_Public::c_publicc{}
	classifier C specializes VisibilityPackage::c_clazz::c_Public{}
	classifier D specializes VisibilityPackage::c_clazz::c_Public::c_publicc{}
}
