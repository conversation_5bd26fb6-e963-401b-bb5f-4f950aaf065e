/******************************************************************************
 * This file was generated by langium-cli 3.5.2.
 * DO NOT EDIT MANUALLY!
 ******************************************************************************/

import type { LangiumSharedCoreServices, LangiumCoreServices, LangiumGeneratedCoreServices, LangiumGeneratedSharedCoreServices, LanguageMetaData, Module } from 'langium';
import { SysmlAstReflection } from './ast.js';
import { SysMLGrammar } from './grammar.js';

export const SysMLLanguageMetaData = {
    languageId: 'sysml',
    fileExtensions: ['.sysml'],
    caseInsensitive: false,
    mode: 'development'
} as const satisfies LanguageMetaData;

export const SysmlGeneratedSharedModule: Module<LangiumSharedCoreServices, LangiumGeneratedSharedCoreServices> = {
    AstReflection: () => new SysmlAstReflection()
};

export const SysMLGeneratedModule: Module<LangiumCoreServices, LangiumGeneratedCoreServices> = {
    Grammar: () => SysMLGrammar(),
    LanguageMetaData: () => SysMLLanguageMetaData,
    parser: {}
};
