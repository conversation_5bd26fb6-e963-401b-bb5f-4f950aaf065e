//* 
XPECT_SETUP org.omg.sysml.xpect.tests.validation.valid.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/BaseFunctions.kerml"}
		File {from ="/library.kernel/Objects.kerml"}
		File {from ="/library.kernel/Occurrences.kerml"}
		File {from ="/library.systems/Items.sysml"}
		File {from ="/library.systems/Parts.sysml"}
		File {from ="/library.systems/Ports.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/BaseFunctions.kerml"}
				File {from ="/library.kernel/Objects.kerml"}
				File {from ="/library.kernel/Occurrences.kerml"}
				File {from ="/library.systems/Items.sysml"}
				File {from ="/library.systems/Parts.sysml"}
				File {from ="/library.systems/Ports.sysml"}		}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package 'Port Example' {
	part def ABlock;
	port def OutPort {
		out ref anout : ABlock;
		in ref ain : ABlock;
	}
	port def InPort;
	part def ABlock1 {
		port aport : OutPort;
	}
	part def ABlock2 {
		port aport : ~InPort;
	}
	part tank: ABlock1 {
		port aredefinedport redefines aport {
			out ref redefines anout;
			in ref redefines ain;
		}	
	}
}
