// import type { ValidationAcceptor, ValidationChecks } from 'langium';
// import type { SysmlAstType, Person } from './generated/ast.js';
import type { SysmlServices } from './sysml-module.js';

/**
 * Register custom validation checks.
 */
export function registerValidationChecks(services: SysmlServices) {
    // const registry = services.validation.ValidationRegistry;
    // const validator = services.validation.SysmlValidator;
    // const checks: ValidationChecks<SysmlAstType> = {
    //     Person: validator.checkPersonStartsWithCapital
    // };
    // registry.register(checks, validator);
}

/**
 * Implementation of custom validations.
 */
export class SysmlValidator {

    // checkPersonStartsWithCapital(person: Person, accept: ValidationAcceptor): void {
    //     if (person.name) {
    //         const firstChar = person.name.substring(0, 1);
    //         if (firstChar.toUpperCase() !== firstChar) {
    //             accept('warning', 'Person name should start with a capital.', { node: person, property: 'name' });
    //         }
    //     }
    // }

}
