'use client';

import React, { useState, useEffect } from 'react';
import SysMLButton from '@/components/ui/sysml-button';

const WorkerTest: React.FC = () => {
  const [worker, setWorker] = useState<Worker | null>(null);
  const [messages, setMessages] = useState<string[]>([]);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // 创建 Worker
    const createWorker = async () => {
      try {
        const newWorker = new Worker(
          new URL('../../../workers/sysml-language-server.worker.ts', import.meta.url),
          { type: 'module' }
        );

        newWorker.addEventListener('message', (event) => {
          const { type, data } = event.data;
          setMessages(prev => [...prev, `Received: ${type} - ${JSON.stringify(data)}`]);
          
          if (type === 'server-started') {
            setIsConnected(true);
          }
        });

        newWorker.addEventListener('error', (error) => {
          setMessages(prev => [...prev, `Worker Error: ${error.message}`]);
        });

        setWorker(newWorker);
        setMessages(prev => [...prev, 'Worker created successfully']);
      } catch (error) {
        setMessages(prev => [...prev, `Failed to create worker: ${error}`]);
      }
    };

    createWorker();

    return () => {
      if (worker) {
        worker.terminate();
      }
    };
  }, []);

  const startServer = () => {
    if (worker) {
      worker.postMessage({ type: 'start' });
      setMessages(prev => [...prev, 'Sent: start']);
    }
  };

  const testValidation = () => {
    if (worker) {
      const testCode = `package TestPackage {
        part def Vehicle {
          attribute mass : Real
          part engine : Engine;
        }
      }`;
      
      worker.postMessage({
        type: 'validate',
        data: { code: testCode, uri: 'test://test.sysml' }
      });
      setMessages(prev => [...prev, 'Sent: validate']);
    }
  };

  const testCompletion = () => {
    if (worker) {
      worker.postMessage({
        type: 'complete',
        data: {
          code: 'package Test {\n  part ',
          uri: 'test://test.sysml',
          position: { line: 1, character: 7 }
        }
      });
      setMessages(prev => [...prev, 'Sent: complete']);
    }
  };

  const ping = () => {
    if (worker) {
      worker.postMessage({ type: 'ping' });
      setMessages(prev => [...prev, 'Sent: ping']);
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <div className="p-4 border rounded-lg bg-white">
      <h3 className="text-lg font-medium mb-4">SysML Language Server Worker Test</h3>
      
      <div className="mb-4">
        <div className={`inline-flex items-center px-2 py-1 rounded text-sm ${
          isConnected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
        }`}>
          <div className={`w-2 h-2 rounded-full mr-2 ${
            isConnected ? 'bg-green-500' : 'bg-gray-400'
          }`}></div>
          {isConnected ? 'Connected' : 'Disconnected'}
        </div>
      </div>

      <div className="space-x-2 mb-4">
        <SysMLButton onClick={startServer} size="sm">
          Start Server
        </SysMLButton>
        <SysMLButton onClick={testValidation} size="sm" variant="outline">
          Test Validation
        </SysMLButton>
        <SysMLButton onClick={testCompletion} size="sm" variant="outline">
          Test Completion
        </SysMLButton>
        <SysMLButton onClick={ping} size="sm" variant="outline">
          Ping
        </SysMLButton>
        <SysMLButton onClick={clearMessages} size="sm" variant="outline">
          Clear
        </SysMLButton>
      </div>

      <div className="border rounded p-3 bg-gray-50 max-h-64 overflow-y-auto">
        <h4 className="text-sm font-medium mb-2">Messages:</h4>
        {messages.length === 0 ? (
          <p className="text-gray-500 text-sm">No messages yet...</p>
        ) : (
          <div className="space-y-1">
            {messages.map((message, index) => (
              <div key={index} className="text-xs font-mono bg-white p-1 rounded">
                {message}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkerTest;
