# SysMLv2 编辑器 + LangGraph 完整安装指南

## 🎯 项目概述

本项目实现了一个完整的 SysMLv2 代码编辑器，集成了：
- **Monaco Editor**: 强大的代码编辑功能
- **Langium**: SysMLv2 语言服务器，提供语法解析和智能补全
- **LangGraph**: AI 驱动的工作流系统，支持代码生成、分析、验证和重构

## 🚀 快速开始

### 1. 安装依赖

```bash
# 进入项目目录
cd React/agent-chat-app

# 安装所有依赖
npm install

# 或者分步安装
npm install monaco-editor@^0.52.2
npm install langium@^3.3.1
npm install vscode-languageserver@^9.0.1
npm install vscode-languageserver-protocol@^3.17.5
npm install vscode-languageserver-types@^3.17.5
npm install monaco-languageclient@^8.8.1
npm install vscode-ws-jsonrpc@^3.3.1

# 安装开发依赖
npm install --save-dev @types/monaco-editor
npm install --save-dev worker-loader
npm install --save-dev raw-loader
```

### 2. 环境配置

创建 `.env.local` 文件：

```bash
# OpenAI API Key (用于 LangGraph 工作流)
OPENAI_API_KEY=your_openai_api_key_here

# 其他可选配置
NEXT_PUBLIC_API_URL=http://localhost:3000/api
```

### 3. 启动应用

```bash
# 启动开发服务器
npm run dev

# 应用将在 http://localhost:3000 启动
```

### 4. 访问编辑器

1. 打开浏览器访问: http://localhost:3000
2. 点击右上角的"SysML v2 建模平台"按钮
3. 使用测试账号登录: `admin` / `password123`
4. 开始使用 SysMLv2 编辑器和 AI 工作流

## 🏗️ 架构说明

### 编辑器架构

```
┌─────────────────────────────────────────────────────────────┐
│                    SysMLv2 编辑器                            │
├─────────────────────────────────────────────────────────────┤
│  Monaco Editor (前端)                                       │
│  ├── 语法高亮 (Monarch Tokenizer)                          │
│  ├── 代码补全 (Completion Provider)                        │
│  ├── 错误标记 (Diagnostic Markers)                         │
│  └── 快捷键绑定 (Key Bindings)                             │
├─────────────────────────────────────────────────────────────┤
│  Langium 语言服务器 (Web Worker)                           │
│  ├── SysMLv2 语法解析                                      │
│  ├── LSP 协议支持                                          │
│  ├── 语法验证和错误检测                                    │
│  └── 智能补全和符号解析                                    │
├─────────────────────────────────────────────────────────────┤
│  React 组件层                                              │
│  ├── SysMLCodeEditor (主编辑器)                            │
│  ├── TabBar (标签页管理)                                   │
│  ├── ProblemsPanel (问题诊断)                              │
│  └── WorkflowPanel (AI 工作流)                             │
└─────────────────────────────────────────────────────────────┘
```

### LangGraph 工作流架构

```
┌─────────────────────────────────────────────────────────────┐
│                   LangGraph 工作流系统                      │
├─────────────────────────────────────────────────────────────┤
│  工作流定义层                                              │
│  ├── 代码生成工作流 (sysml-code-generation)                │
│  ├── 代码分析工作流 (sysml-code-analysis)                  │
│  ├── 需求验证工作流 (requirement-validation)               │
│  └── 代码重构工作流 (code-refactoring)                     │
├─────────────────────────────────────────────────────────────┤
│  执行引擎层                                                │
│  ├── WorkflowExecutor (执行器)                             │
│  ├── 状态管理 (StateGraph)                                 │
│  ├── 依赖解析 (Dependency Resolution)                      │
│  └── 并行执行 (Parallel Execution)                         │
├─────────────────────────────────────────────────────────────┤
│  API 接口层                                                │
│  ├── /api/langgraph (REST API)                             │
│  ├── 工作流执行接口                                        │
│  ├── 状态查询接口                                          │
│  └── 历史记录接口                                          │
├─────────────────────────────────────────────────────────────┤
│  前端集成层                                                │
│  ├── WorkflowPanel (工作流面板)                            │
│  ├── 用户输入界面                                          │
│  ├── 执行状态显示                                          │
│  └── 结果展示和集成                                        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 功能特性

### SysMLv2 编辑器功能

#### ✅ 语法支持
- **完整的 SysMLv2 语法**: package, part def, attribute, connection, requirement 等
- **语法高亮**: 关键字、操作符、字符串、注释的不同颜色显示
- **智能补全**: 关键字补全和代码片段插入
- **语法验证**: 实时语法错误检测和标记

#### ✅ 编辑器功能
- **多标签页**: 支持同时编辑多个文件
- **问题面板**: 统一显示语法错误和警告
- **代码折叠**: 支持代码块的折叠和展开
- **快捷键**: Ctrl+S 保存、Ctrl+F 查找等

#### ✅ IDE 特性
- **项目管理**: 树形结构的项目浏览器
- **文件管理**: 创建、重命名、删除文件和文件夹
- **状态栏**: 显示当前文件状态和编辑器信息

### LangGraph 工作流功能

#### ✅ 预定义工作流

1. **SysML 代码生成工作流**
   - 输入: 自然语言需求描述
   - 输出: 完整的 SysMLv2 代码
   - 流程: 需求分析 → 结构生成 → 语法验证 → 错误修复

2. **SysML 代码分析工作流**
   - 输入: SysMLv2 代码
   - 输出: 详细的分析报告
   - 流程: 代码解析 → 结构分析 → 报告生成

3. **需求验证工作流**
   - 输入: SysMLv2 代码
   - 输出: 需求验证报告
   - 流程: 需求提取 → 完整性验证 → 一致性检查

4. **代码重构工作流**
   - 输入: SysMLv2 代码
   - 输出: 重构后的代码
   - 流程: 结构分析 → 重构建议 → 代码优化

#### ✅ 工作流执行模式

- **单个执行**: 执行单个工作流
- **链式执行**: 按顺序执行多个工作流
- **并行执行**: 同时执行多个独立工作流
- **依赖管理**: 自动解析工作流依赖关系

## 📝 使用指南

### 基本使用流程

1. **创建项目**
   ```
   1. 登录系统
   2. 点击"新建项目"
   3. 输入项目名称
   4. 创建命名空间和图文件
   ```

2. **编写代码**
   ```
   1. 双击图文件打开编辑器
   2. 编写 SysMLv2 代码
   3. 享受语法高亮和智能补全
   4. 查看实时语法验证
   ```

3. **AI 辅助**
   ```
   1. 在右侧工作流面板输入需求
   2. 选择合适的工作流
   3. 点击"AI 生成"按钮
   4. 查看生成结果并应用到编辑器
   ```

### SysMLv2 语法示例

```sysml
package VehicleSystem {
  // 车辆部件定义
  part def Vehicle {
    attribute mass : Real;
    attribute maxSpeed : Real;
    
    part engine : Engine;
    part transmission : Transmission;
    part wheels : Wheel[4];
    
    // 连接定义
    connection engineToTransmission 
      connect engine.powerOutput to transmission.powerInput;
  }
  
  // 发动机定义
  part def Engine {
    attribute power : Real;
    attribute fuelType : String;
    
    port powerOutput : PowerPort;
    port fuelInput : FuelPort;
    
    constraint powerConstraint {
      power > 0.0 and power < 1000.0
    }
  }
  
  // 需求定义
  requirement def VehicleRequirement {
    requirement maxSpeedReq {
      doc /* 最大速度不超过 200 km/h */
      require constraint {
        Vehicle.maxSpeed <= 200.0
      }
    }
  }
}
```

### 工作流使用示例

#### 代码生成
```
输入: "创建一个智能手机系统模型，包含处理器、内存、存储、摄像头和电池"

工作流: sysml-code-generation

输出: 完整的 SysMLv2 代码，包含所有组件定义和连接关系
```

#### 代码分析
```
输入: 现有的 SysMLv2 代码

工作流: sysml-code-analysis

输出: 
- 代码结构统计
- 复杂度分析
- 设计模式识别
- 改进建议
```

#### 工作流链
```
输入: "设计一个无人机控制系统"

工作流链: 
1. sysml-code-generation (生成代码)
2. sysml-code-analysis (分析代码)
3. requirement-validation (验证需求)

输出: 完整的生成、分析和验证报告
```

## 🔧 高级配置

### 自定义工作流

```typescript
// 创建自定义工作流
const customWorkflow: WorkflowDefinition = {
  id: 'custom-analysis',
  name: '自定义分析',
  description: '执行特定的代码分析任务',
  category: 'analysis',
  entryPoint: 'analyze',
  nodes: {
    analyze: async (state: WorkflowState) => {
      // 自定义分析逻辑
      const analysis = performCustomAnalysis(state.sysmlCode);
      return {
        analysisResult: analysis,
        currentStep: 'complete'
      };
    }
  },
  edges: {
    analyze: 'complete'
  }
};

// 注册工作流
workflowRegistry.set(customWorkflow.id, customWorkflow);
```

### 集成外部服务

```typescript
// 集成外部 API
const externalAnalysisNode = async (state: WorkflowState) => {
  const response = await fetch('https://your-api.com/analyze', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ code: state.sysmlCode })
  });
  
  const result = await response.json();
  
  return {
    analysisResult: result.analysis,
    currentStep: 'complete'
  };
};
```

## 🐛 故障排除

### 常见问题

1. **Monaco Editor 无法加载**
   ```
   解决方案:
   - 检查网络连接
   - 清除浏览器缓存
   - 重启开发服务器
   ```

2. **语法高亮不工作**
   ```
   解决方案:
   - 确保文件扩展名为 .sysml
   - 检查浏览器控制台错误
   - 验证 Langium 服务器是否启动
   ```

3. **工作流执行失败**
   ```
   解决方案:
   - 检查 OpenAI API Key 配置
   - 验证网络连接
   - 查看 API 错误日志
   ```

4. **Web Worker 错误**
   ```
   解决方案:
   - 检查浏览器是否支持 Web Workers
   - 验证 Worker 文件路径
   - 查看浏览器控制台错误
   ```

### 调试技巧

```javascript
// 启用详细日志
localStorage.setItem('sysml-debug', 'true');

// 查看工作流执行历史
const executor = getWorkflowExecutor();
const history = executor.getExecutionHistory('sysml-code-generation');
console.log(history);

// 检查 Langium 客户端状态
const client = getLangiumClient();
console.log('Client status:', client.isRunning());
```

## 🚀 部署指南

### 生产环境部署

```bash
# 构建应用
npm run build

# 启动生产服务器
npm start
```

### 环境变量配置

```bash
# 生产环境配置
OPENAI_API_KEY=your_production_api_key
NEXT_PUBLIC_API_URL=https://your-domain.com/api
NODE_ENV=production
```

### Docker 部署

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

## 🎉 总结

恭喜！您已经成功设置了完整的 SysMLv2 编辑器 + LangGraph 工作流系统。

### 主要成就
- ✅ **强大的代码编辑器**: 基于 Monaco Editor 和 Langium
- ✅ **智能 AI 工作流**: 基于 LangGraph 的代码生成和分析
- ✅ **完整的 IDE 体验**: 项目管理、多标签页、问题诊断
- ✅ **可扩展架构**: 易于添加新功能和工作流

### 下一步
- 🔧 自定义工作流以满足特定需求
- 🌐 集成更多外部服务和 API
- 📊 添加更多分析和可视化功能
- 🤝 实现团队协作和版本控制

**开始您的 SysMLv2 建模之旅吧！** 🚀
