//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyPackageAlias1_Feature.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyPackageAlias1_Feature.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	public import PackageAlias1::*;
	feature A{}
	//XPECT linkedName at A --> test.A
	//* XPECT scope at A ---
	A, A.self, A.that, A.that.self, AA, AA.aa, AA.aa.self, AA.aa.that,
	AA.aa.that.self, AA.aa_alias, AA.aa_alias.self, AA.aa_alias.that, AA.aa_alias.that.self,
	AA.self, AA.that, AA.that.self, AA_alias, AA_alias.aa, AA_alias.aa.self,
	AA_alias.aa.that, AA_alias.aa.that.self, AA_alias.aa_alias, AA_alias.aa_alias.self,
	AA_alias.aa_alias.that, AA_alias.aa_alias.that.self, AA_alias.self, AA_alias.that, AA_alias.that.self,
	A_alias, A_alias.a, A_alias.a.self, A_alias.a.that, A_alias.a.that.self,
	A_alias.a_alias, A_alias.a_alias.self, A_alias.a_alias.that, A_alias.a_alias.that.self,
	A_alias.self, A_alias.that, A_alias.that.self, PackageAlias1.A, PackageAlias1.A.a,
	PackageAlias1.A.a.self, PackageAlias1.A.a.that, PackageAlias1.A.a.that.self, PackageAlias1.A.a_alias,
	PackageAlias1.A.a_alias.self, PackageAlias1.A.a_alias.that, PackageAlias1.A.a_alias.that.self,
	PackageAlias1.A.self, PackageAlias1.A.that, PackageAlias1.A.that.self, PackageAlias1.AA,
	PackageAlias1.AA.aa, PackageAlias1.AA.aa.self, PackageAlias1.AA.aa.that,
	PackageAlias1.AA.aa.that.self, PackageAlias1.AA.aa_alias, PackageAlias1.AA.aa_alias.self,
	PackageAlias1.AA.aa_alias.that, PackageAlias1.AA.aa_alias.that.self, PackageAlias1.AA.self,
	PackageAlias1.AA.that, PackageAlias1.AA.that.self, PackageAlias1.AA_alias, PackageAlias1.AA_alias.aa,
	PackageAlias1.AA_alias.aa.self, PackageAlias1.AA_alias.aa.that, PackageAlias1.AA_alias.aa.that.self,
	PackageAlias1.AA_alias.aa_alias, PackageAlias1.AA_alias.aa_alias.self, PackageAlias1.AA_alias.aa_alias.that,
	PackageAlias1.AA_alias.aa_alias.that.self, PackageAlias1.AA_alias.self, PackageAlias1.AA_alias.that,
	PackageAlias1.AA_alias.that.self, PackageAlias1.A_alias, PackageAlias1.A_alias.a, PackageAlias1.A_alias.a.self,
	PackageAlias1.A_alias.a.that, PackageAlias1.A_alias.a.that.self, PackageAlias1.A_alias.a_alias,
	PackageAlias1.A_alias.a_alias.self, PackageAlias1.A_alias.a_alias.that, PackageAlias1.A_alias.a_alias.that.self,
	PackageAlias1.A_alias.self, PackageAlias1.A_alias.that, PackageAlias1.A_alias.that.self, test.A,
	test.A.self, test.A.that, test.A.that.self, test.AA, test.AA.aa, test.AA.aa.self,
	test.AA.aa.that, test.AA.aa.that.self, test.AA.aa_alias, test.AA.aa_alias.self,
	test.AA.aa_alias.that, test.AA.aa_alias.that.self, test.AA.self, test.AA.that, test.AA.that.self,
	test.AA_alias, test.AA_alias.aa, test.AA_alias.aa.self, test.AA_alias.aa.that,
	test.AA_alias.aa.that.self, test.AA_alias.aa_alias, test.AA_alias.aa_alias.self,
	test.AA_alias.aa_alias.that, test.AA_alias.aa_alias.that.self, test.AA_alias.self, test.AA_alias.that,
	test.AA_alias.that.self, test.A_alias, test.A_alias.a, test.A_alias.a.self, test.A_alias.a.that,
	test.A_alias.a.that.self, test.A_alias.a_alias, test.A_alias.a_alias.self, test.A_alias.a_alias.that,
	test.A_alias.a_alias.that.self, test.A_alias.self, test.A_alias.that, test.A_alias.that.self, test.test_a,
	test.test_a.self, test.test_a.that, test.test_a.that.self, test.test_alias, test.test_alias.a,
	test.test_alias.a.self, test.test_alias.a.that, test.test_alias.a.that.self, test.test_alias.a_alias,
	test.test_alias.a_alias.self, test.test_alias.a_alias.that, test.test_alias.a_alias.that.self,
	test.test_alias.self, test.test_alias.that, test.test_alias.that.self, test_a, test_a.self,
	test_a.that, test_a.that.self, test_alias, test_alias.a, test_alias.a.self,
	test_alias.a.that, test_alias.a.that.self, test_alias.a_alias, test_alias.a_alias.self,
	test_alias.a_alias.that, test_alias.a_alias.that.self, test_alias.self, test_alias.that,
	test_alias.that.self
	--- */
	feature test_a subsets A{} //rename test_A to test_a because xpect linking and scoping throw RuntimeError
	
	//XPECT linkedName at A_alias --> PackageAlias1.A
	//* XPECT scope at A_alias ---
		A, A.self, A.that, A.that.self, AA, AA.aa, AA.aa.self, AA.aa.that,
		AA.aa.that.self, AA.aa_alias, AA.aa_alias.self, AA.aa_alias.that, AA.aa_alias.that.self,
		AA.self, AA.that, AA.that.self, AA_alias, AA_alias.aa, AA_alias.aa.self,
		AA_alias.aa.that, AA_alias.aa.that.self, AA_alias.aa_alias, AA_alias.aa_alias.self,
		AA_alias.aa_alias.that, AA_alias.aa_alias.that.self, AA_alias.self, AA_alias.that, AA_alias.that.self,
		A_alias, A_alias.a, A_alias.a.self, A_alias.a.that, A_alias.a.that.self,
		A_alias.a_alias, A_alias.a_alias.self, A_alias.a_alias.that, A_alias.a_alias.that.self,
		A_alias.self, A_alias.that, A_alias.that.self, PackageAlias1.A, PackageAlias1.A.a,
		PackageAlias1.A.a.self, PackageAlias1.A.a.that, PackageAlias1.A.a.that.self, PackageAlias1.A.a_alias,
		PackageAlias1.A.a_alias.self, PackageAlias1.A.a_alias.that, PackageAlias1.A.a_alias.that.self,
		PackageAlias1.A.self, PackageAlias1.A.that, PackageAlias1.A.that.self, PackageAlias1.AA,
		PackageAlias1.AA.aa, PackageAlias1.AA.aa.self, PackageAlias1.AA.aa.that,
		PackageAlias1.AA.aa.that.self, PackageAlias1.AA.aa_alias, PackageAlias1.AA.aa_alias.self,
		PackageAlias1.AA.aa_alias.that, PackageAlias1.AA.aa_alias.that.self, PackageAlias1.AA.self,
		PackageAlias1.AA.that, PackageAlias1.AA.that.self, PackageAlias1.AA_alias, PackageAlias1.AA_alias.aa,
		PackageAlias1.AA_alias.aa.self, PackageAlias1.AA_alias.aa.that, PackageAlias1.AA_alias.aa.that.self,
		PackageAlias1.AA_alias.aa_alias, PackageAlias1.AA_alias.aa_alias.self, PackageAlias1.AA_alias.aa_alias.that,
		PackageAlias1.AA_alias.aa_alias.that.self, PackageAlias1.AA_alias.self, PackageAlias1.AA_alias.that,
		PackageAlias1.AA_alias.that.self, PackageAlias1.A_alias, PackageAlias1.A_alias.a, PackageAlias1.A_alias.a.self,
		PackageAlias1.A_alias.a.that, PackageAlias1.A_alias.a.that.self, PackageAlias1.A_alias.a_alias,
		PackageAlias1.A_alias.a_alias.self, PackageAlias1.A_alias.a_alias.that, PackageAlias1.A_alias.a_alias.that.self,
		PackageAlias1.A_alias.self, PackageAlias1.A_alias.that, PackageAlias1.A_alias.that.self, test.A,
		test.A.self, test.A.that, test.A.that.self, test.AA, test.AA.aa, test.AA.aa.self,
		test.AA.aa.that, test.AA.aa.that.self, test.AA.aa_alias, test.AA.aa_alias.self,
		test.AA.aa_alias.that, test.AA.aa_alias.that.self, test.AA.self, test.AA.that, test.AA.that.self,
		test.AA_alias, test.AA_alias.aa, test.AA_alias.aa.self, test.AA_alias.aa.that,
		test.AA_alias.aa.that.self, test.AA_alias.aa_alias, test.AA_alias.aa_alias.self,
		test.AA_alias.aa_alias.that, test.AA_alias.aa_alias.that.self, test.AA_alias.self, test.AA_alias.that,
		test.AA_alias.that.self, test.A_alias, test.A_alias.a, test.A_alias.a.self, test.A_alias.a.that,
		test.A_alias.a.that.self, test.A_alias.a_alias, test.A_alias.a_alias.self, test.A_alias.a_alias.that,
		test.A_alias.a_alias.that.self, test.A_alias.self, test.A_alias.that, test.A_alias.that.self, test.test_a,
		test.test_a.self, test.test_a.that, test.test_a.that.self, test.test_alias, test.test_alias.a,
		test.test_alias.a.self, test.test_alias.a.that, test.test_alias.a.that.self, test.test_alias.a_alias,
		test.test_alias.a_alias.self, test.test_alias.a_alias.that, test.test_alias.a_alias.that.self,
		test.test_alias.self, test.test_alias.that, test.test_alias.that.self, test_a, test_a.self,
		test_a.that, test_a.that.self, test_alias, test_alias.a, test_alias.a.self,
		test_alias.a.that, test_alias.a.that.self, test_alias.a_alias, test_alias.a_alias.self,
		test_alias.a_alias.that, test_alias.a_alias.that.self, test_alias.self, test_alias.that,
		test_alias.that.self
 		--- */
	feature test_alias subsets A_alias{}
}
