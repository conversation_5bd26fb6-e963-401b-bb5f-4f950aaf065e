html,body {
    background: rgb(33,33,33);
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    color: white;
    /* for monaco */
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}
h1 {
    text-align: center;
}
#minilogo-canvas {
    display: block;
    margin: 8px auto;
    text-align: center;
}
#page-wrapper {
    display: flex;
    max-width: 2000px;
    margin: 4px auto;
    padding: 4px;
    min-height: 75vh;
    justify-content: center;
}
#page-wrapper .half {
    display: flex;
    width: 40vw;
}
.build {
    display: block;
    margin: 8px auto;
    width: 300px;
    height: 30px;
    background: none;
    border: 2px #fff solid;
    color: #fff;
    transition: 0.3s;
    font-size: 1.2rem;
    border-radius: 4px;
}
.build:hover {
    border-color: #6cf;
    color: #6cf;
    cursor: pointer;
}
.build:active {
    color: #fff;
    border-color: #fff;
}
footer {
    text-align: center;
    color: #444;
    font-size: 1.2rem;
    margin-bottom: 16px;
}
@media(max-width: 1000px) {
    #page-wrapper {
        display: block;
    }
    #page-wrapper .half {
        display: block;
        width: auto;
    }
    #minilogo-canvas {
        margin-top: 32px;
    }
    #page-wrapper {
        min-height: auto;
    }
}

/* for monaco */
.wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
}

#monaco-editor-root {
    flex-grow: 1;
}

#status-msg {
    color: red;
}