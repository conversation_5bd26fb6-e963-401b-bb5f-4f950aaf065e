//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	package P{
		classifier A {}
		alias A_alias for A;
	}
	// XPECT errors --> "Couldn't resolve reference to Type 'P1::A_alias'." at "P1::A_alias"
	feature a: P1::A_alias;
}
