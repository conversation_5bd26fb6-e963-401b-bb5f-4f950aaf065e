//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	public import VisibilityPackage::c_clazz::*;
	//XPECT errors --> "Couldn't resolve reference to Classifier 'c_Protect'." at "c_Protect"
	classifier A specializes c_Protect{
	}
	//XPECT errors --> "Couldn't resolve reference to Classifier 'c_Protect::c_publicc'." at "c_Protect::c_publicc"
	classifier B specializes c_Protect::c_publicc{
	}
}
