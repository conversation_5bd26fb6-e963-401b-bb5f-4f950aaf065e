'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useSysML } from '@/contexts/SysMLContext';
import { useSysMLToast } from '@/components/ui/sysml-toast';
import { getLangiumClient, LangiumClient } from '@/lib/sysml/langium-client';
import { startSysMLLSP, stopSysMLLSP } from '@/lib/sysml/monaco-lsp-client';
import { debounce } from '@/lib/sysml/helpers';
import { EditorTab } from '@/types/sysml';

interface SysMLCodeEditorProps {
  activeTab: EditorTab | null;
}

const SysMLCodeEditor: React.FC<SysMLCodeEditorProps> = ({ activeTab }) => {
  const { state, dispatch } = useSysML();
  const { showToast } = useSysMLToast();
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<any>(null);
  const langiumClientRef = useRef<LangiumClient | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化 Monaco Editor 和 Langium 客户端
  useEffect(() => {
    let mounted = true;

    const initEditor = async () => {
      try {
        setIsLoading(true);

        // 动态导入 Monaco Editor
        const monaco = await import('monaco-editor');
        
        if (!mounted) return;

        monacoRef.current = monaco;

        // 初始化 Langium 客户端
        langiumClientRef.current = getLangiumClient();

        // 注册 SysML 语言
        monaco.languages.register({
          id: 'sysml',
          extensions: ['.sysml', '.sysmlv2'],
          aliases: ['SysML', 'sysml']
        });

        // SysML 语法高亮配置
        monaco.languages.setMonarchTokensProvider('sysml', {
          tokenizer: {
            root: [
              // 关键字
              [/\b(package|part|def|attribute|connection|interface|port|action|state|requirement|constraint|case|view|viewpoint|analysis|verification|use|concern|item|allocation|flow|binding|succession|metadata|abstract|individual|variation|snapshot|timeslice|ordered|nonunique|derived|readonly|composite|end|in|out|inout|ref|redefines|subsets|specializes|conjugates|references|typed|by|featured|chains|inverse|of|disjoint|from|import|private|protected|public|all|filter|alias|for|about|doc|comment|language|standard|library|then|if|else|while|for|do|entry|exit|when|at|after|trigger|guard|effect|assume|require|verify|satisfy|expose|render|as|hastype|istype|meta|null|true|false|and|or|xor|not|implies)\b/, 'keyword'],
              
              // 操作符
              [/[=!<>]=?/, 'operator'],
              [/[+\-*/%^]/, 'operator'],
              [/[&|~]/, 'operator'],
              [/[?:]/, 'operator'],
              [/\.\./, 'operator'],
              [/::/, 'operator'],
              [/:>/, 'operator'],
              [/:>>/, 'operator'],
              [/::>/, 'operator'],
              [/=>/, 'operator'],
              
              // 数字
              [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
              [/\d+/, 'number'],
              
              // 字符串
              [/"([^"\\]|\\.)*$/, 'string.invalid'],
              [/"/, 'string', '@string'],
              [/'([^'\\]|\\.)*$/, 'string.invalid'],
              [/'/, 'string', '@string_single'],
              
              // 注释
              [/\/\*/, 'comment', '@comment'],
              [/\/\/.*$/, 'comment'],
              
              // 标识符
              [/[a-zA-Z_]\w*/, 'identifier'],
              
              // 分隔符
              [/[{}()\[\]]/, '@brackets'],
              [/[<>](?!@symbols)/, '@brackets'],
              [/[,.]/, 'delimiter'],
              [/;/, 'delimiter'],
            ],
            
            comment: [
              [/[^\/*]+/, 'comment'],
              [/\/\*/, 'comment', '@push'],
              [/\*\//, 'comment', '@pop'],
              [/[\/*]/, 'comment']
            ],
            
            string: [
              [/[^\\"]+/, 'string'],
              [/\\./, 'string.escape.invalid'],
              [/"/, 'string', '@pop']
            ],
            
            string_single: [
              [/[^\\']+/, 'string'],
              [/\\./, 'string.escape.invalid'],
              [/'/, 'string', '@pop']
            ],
          },
        });

        // 语言配置
        monaco.languages.setLanguageConfiguration('sysml', {
          comments: {
            lineComment: '//',
            blockComment: ['/*', '*/']
          },
          brackets: [
            ['{', '}'],
            ['[', ']'],
            ['(', ')']
          ],
          autoClosingPairs: [
            { open: '{', close: '}' },
            { open: '[', close: ']' },
            { open: '(', close: ')' },
            { open: '"', close: '"' },
            { open: "'", close: "'" }
          ],
          surroundingPairs: [
            { open: '{', close: '}' },
            { open: '[', close: ']' },
            { open: '(', close: ')' },
            { open: '"', close: '"' },
            { open: "'", close: "'" }
          ],
          folding: {
            markers: {
              start: new RegExp('^\\s*//\\s*#?region\\b'),
              end: new RegExp('^\\s*//\\s*#?endregion\\b')
            }
          }
        });

        // 注册代码补全提供器
        monaco.languages.registerCompletionItemProvider('sysml', {
          provideCompletionItems: async (model, position, _context, _token) => {
            if (!langiumClientRef.current) return { suggestions: [] };

            try {
              const code = model.getValue();
              const result = await langiumClientRef.current.getCompletions(
                code,
                { line: position.lineNumber - 1, character: position.column - 1 }
              );

              return {
                suggestions: result.items.map(item => ({
                  label: item.label,
                  kind: item.kind || monaco.languages.CompletionItemKind.Text,
                  detail: item.detail,
                  documentation: item.documentation,
                  insertText: item.insertText || item.label,
                  insertTextRules: item.insertTextRules || 0,
                  range: item.range || {
                    startLineNumber: position.lineNumber,
                    startColumn: position.column,
                    endLineNumber: position.lineNumber,
                    endColumn: position.column
                  }
                }))
              };
            } catch (error) {
              console.error('Completion provider error:', error);
              return { suggestions: [] };
            }
          }
        });

        // 定义主题
        monaco.editor.defineTheme('sysml-dark', {
          base: 'vs-dark',
          inherit: true,
          rules: [
            { token: 'keyword', foreground: '569cd6', fontStyle: 'bold' },
            { token: 'operator', foreground: 'd4d4d4' },
            { token: 'number', foreground: 'b5cea8' },
            { token: 'number.float', foreground: 'b5cea8' },
            { token: 'string', foreground: 'ce9178' },
            { token: 'comment', foreground: '6a9955', fontStyle: 'italic' },
            { token: 'identifier', foreground: '9cdcfe' },
            { token: 'delimiter', foreground: 'd4d4d4' },
          ],
          colors: {
            'editor.background': '#1e1e1e',
            'editor.foreground': '#d4d4d4',
            'editorLineNumber.foreground': '#858585',
            'editorCursor.foreground': '#aeafad',
            'editor.selectionBackground': '#264f78',
            'editor.lineHighlightBackground': '#2a2d2e',
          }
        });

        // 创建编辑器实例
        if (containerRef.current) {
          const editor = monaco.editor.create(containerRef.current, {
            value: activeTab?.content || '',
            language: 'sysml',
            theme: 'sysml-dark',
            automaticLayout: true,
            fontSize: 14,
            lineNumbers: 'on',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: true,
            contextmenu: true,
            mouseWheelZoom: true,
            smoothScrolling: true,
            cursorBlinking: 'blink',
            cursorSmoothCaretAnimation: 'on',
            renderLineHighlight: 'line',
            selectOnLineNumbers: true,
            roundedSelection: false,
            colorDecorators: true,
            codeLens: true,
            suggest: {
              showKeywords: true,
              showSnippets: true,
              showFunctions: true,
              showConstructors: true,
              showFields: true,
              showVariables: true,
              showClasses: true,
              showStructs: true,
              showInterfaces: true,
              showModules: true,
              showProperties: true,
              showEvents: true,
              showOperators: true,
              showUnits: true,
              showValues: true,
              showConstants: true,
              showEnums: true,
              showEnumMembers: true,
              showColors: true,
              showFiles: true,
              showReferences: true,
              showFolders: true,
              showTypeParameters: true,
            }
          });

          editorRef.current = editor;

          // 监听内容变化
          const disposable = editor.onDidChangeModelContent(() => {
            const newValue = editor.getValue();
            if (activeTab) {
              dispatch({
                type: 'UPDATE_EDITOR_TAB',
                payload: {
                  id: activeTab.id,
                  updates: {
                    content: newValue,
                    isDirty: true,
                  },
                },
              });
            }
            
            // 防抖验证
            debouncedValidation(newValue);
          });

          // 添加快捷键
          editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
            handleSave();
          });

          setIsEditorReady(true);
          setIsLoading(false);

          return () => {
            disposable.dispose();
            editor.dispose();
          };
        }
      } catch (error) {
        console.error('Failed to initialize editor:', error);
        setIsLoading(false);
        showToast({
          type: 'error',
          title: '编辑器初始化失败',
          message: '无法加载代码编辑器，请刷新页面重试',
        });
      }
    };

    initEditor();

    return () => {
      mounted = false;
      if (editorRef.current) {
        editorRef.current.dispose();
      }
    };
  }, []);

  // 防抖的代码验证
  const debouncedValidation = useCallback(
    debounce(async (code: string) => {
      if (!langiumClientRef.current || !activeTab) return;

      try {
        const result = await langiumClientRef.current.validateCode(code);
        dispatch({ type: 'SET_DIAGNOSTICS', payload: result.diagnostics });

        // 在编辑器中显示错误标记
        if (editorRef.current && monacoRef.current) {
          const markers = result.diagnostics.map(d => ({
            severity: d.severity === 'error' ? monacoRef.current.MarkerSeverity.Error :
                     d.severity === 'warning' ? monacoRef.current.MarkerSeverity.Warning :
                     monacoRef.current.MarkerSeverity.Info,
            message: d.message,
            startLineNumber: d.line,
            startColumn: d.column,
            endLineNumber: d.line,
            endColumn: d.column + 10, // 简化处理
            source: d.source
          }));

          monacoRef.current.editor.setModelMarkers(
            editorRef.current.getModel(),
            'sysml',
            markers
          );
        }
      } catch (error) {
        console.error('Validation error:', error);
      }
    }, 500),
    [activeTab, dispatch]
  );

  // 更新编辑器内容
  useEffect(() => {
    if (isEditorReady && editorRef.current && activeTab) {
      const currentValue = editorRef.current.getValue();
      if (currentValue !== activeTab.content) {
        editorRef.current.setValue(activeTab.content);
      }
    }
  }, [activeTab, isEditorReady]);

  // 保存文件
  const handleSave = async () => {
    if (!activeTab) return;

    try {
      // 这里应该调用实际的保存 API
      dispatch({
        type: 'UPDATE_EDITOR_TAB',
        payload: {
          id: activeTab.id,
          updates: { isDirty: false },
        },
      });

      showToast({
        type: 'success',
        message: '文件已保存',
        duration: 2000,
      });
    } catch (error) {
      showToast({
        type: 'error',
        title: '保存失败',
        message: '保存文件时发生错误',
      });
    }
  };

  if (!activeTab) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">欢迎使用 SysML v2 编辑器</h3>
          <p className="text-gray-500 mb-4">选择或创建一个文件开始编辑</p>
          <div className="text-sm text-gray-400 space-y-1">
            <p>• 支持 SysML v2 语法高亮和智能补全</p>
            <p>• 实时语法验证和错误提示</p>
            <p>• 基于 Langium 的语言服务器</p>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">正在加载编辑器...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-50 border-b">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{activeTab.title}</span>
          {activeTab.isDirty && <span className="text-orange-500">●</span>}
          <span className="text-xs text-gray-500">SysML v2</span>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSave}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            title="保存 (Ctrl+S)"
          >
            保存
          </button>
        </div>
      </div>

      {/* 编辑器区域 */}
      <div ref={containerRef} className="flex-1" />
    </div>
  );
};

export default SysMLCodeEditor;
