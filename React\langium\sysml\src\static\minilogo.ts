// import { MonacoEditorLanguageClientWrapper, UserConfig } from "monaco-editor-wrapper/bundle";
import { buildWorkerDefinition } from "monaco-editor-workers";
// import { addMonacoStyles } from "monaco-editor-wrapper/styles";

/**
 * Setup Monaco's own workers and also incorporate the necessary styles for the monaco-editor
 */
function setup() {
    buildWorkerDefinition(
        './monaco-editor-workers/workers',
        new URL('', window.location.href).href,
        false
    );
    // addMonacoStyles('monaco-editor-styles');
}