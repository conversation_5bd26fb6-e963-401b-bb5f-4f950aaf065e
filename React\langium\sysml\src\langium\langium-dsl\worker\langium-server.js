"use strict";
/* --------------------------------------------------------------------------------------------
 * Copyright (c) 2018-2022 TypeFox and others.
 * Licensed under the MIT License. See LICENSE in the package root for license information.
 * ------------------------------------------------------------------------------------------ */
/// <reference lib="WebWorker" />
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var langium_1 = require("langium");
var lsp_1 = require("langium/lsp");
var grammar_1 = require("langium/grammar");
var browser_js_1 = require("vscode-languageserver/browser.js");
/* browser specific setup code */
var messageReader = new browser_js_1.BrowserMessageReader(self);
var messageWriter = new browser_js_1.BrowserMessageWriter(self);
messageReader.listen(function (message) {
    console.log('Received message from main thread:', message);
});
// Inject the shared services and language-specific services
var context = __assign({ connection: (0, browser_js_1.createConnection)(messageReader, messageWriter) }, langium_1.EmptyFileSystem);
var shared = (0, grammar_1.createLangiumGrammarServices)(context).shared;
console.log('Starting langium-dsl server...');
// Start the language server with the shared services
(0, lsp_1.startLanguageServer)(shared);
