//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyPackageAlias1_Feature.kerml"}
		File {from ="/src/DependencyPackageAlias2_Feature_FT.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyPackageAlias1_Feature.kerml"}
				File {from ="/src/DependencyPackageAlias2_Feature_FT.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	public import PackageAlias1::*;
	feature A{}
	alias A_alias for A;
	//XPECT linkedName at  A_alias --> test.A
	//* XPECT scope at  A_alias ---
		A, A.self, A.that, A.that.self, AA, AA.aa, AA.aa.self, AA.aa.that,
		AA.aa.that.self, AA.aa_alias, AA.aa_alias.self, AA.aa_alias.that, AA.aa_alias.that.self,
		AA.self, AA.that, AA.that.self, AA_alias, AA_alias.aa, AA_alias.aa.self,
		AA_alias.aa.that, AA_alias.aa.that.self, AA_alias.aa_alias, AA_alias.aa_alias.self,
		AA_alias.aa_alias.that, AA_alias.aa_alias.that.self, AA_alias.self, AA_alias.that, AA_alias.that.self,
		A_alias, A_alias.self, A_alias.that, A_alias.that.self, PackageAlias1.A,
		PackageAlias1.A.a, PackageAlias1.A.a.self, PackageAlias1.A.a.that, PackageAlias1.A.a.that.self,
		PackageAlias1.A.a_alias, PackageAlias1.A.a_alias.self, PackageAlias1.A.a_alias.that,
		PackageAlias1.A.a_alias.that.self, PackageAlias1.A.self, PackageAlias1.A.that, PackageAlias1.A.that.self,
		PackageAlias1.AA, PackageAlias1.AA.aa, PackageAlias1.AA.aa.self, PackageAlias1.AA.aa.that,
		PackageAlias1.AA.aa.that.self, PackageAlias1.AA.aa_alias, PackageAlias1.AA.aa_alias.self,
		PackageAlias1.AA.aa_alias.that, PackageAlias1.AA.aa_alias.that.self, PackageAlias1.AA.self,
		PackageAlias1.AA.that, PackageAlias1.AA.that.self, PackageAlias1.AA_alias, PackageAlias1.AA_alias.aa,
		PackageAlias1.AA_alias.aa.self, PackageAlias1.AA_alias.aa.that, PackageAlias1.AA_alias.aa.that.self,
		PackageAlias1.AA_alias.aa_alias, PackageAlias1.AA_alias.aa_alias.self, PackageAlias1.AA_alias.aa_alias.that,
		PackageAlias1.AA_alias.aa_alias.that.self, PackageAlias1.AA_alias.self, PackageAlias1.AA_alias.that,
		PackageAlias1.AA_alias.that.self, PackageAlias1.A_alias, PackageAlias1.A_alias.a, PackageAlias1.A_alias.a.self,
		PackageAlias1.A_alias.a.that, PackageAlias1.A_alias.a.that.self, PackageAlias1.A_alias.a_alias,
		PackageAlias1.A_alias.a_alias.self, PackageAlias1.A_alias.a_alias.that, PackageAlias1.A_alias.a_alias.that.self,
		PackageAlias1.A_alias.self, PackageAlias1.A_alias.that, PackageAlias1.A_alias.that.self, PackageAlias2.A,
		PackageAlias2.A.a, PackageAlias2.A.a.self, PackageAlias2.A.a.that, PackageAlias2.A.a.that.self,
		PackageAlias2.A.a_alias, PackageAlias2.A.a_alias.self, PackageAlias2.A.a_alias.that,
		PackageAlias2.A.a_alias.that.self, PackageAlias2.A.self, PackageAlias2.A.that, PackageAlias2.A.that.self,
		PackageAlias2.AA, PackageAlias2.AA.aa, PackageAlias2.AA.aa.self, PackageAlias2.AA.aa.that,
		PackageAlias2.AA.aa.that.self, PackageAlias2.AA.aa_alias, PackageAlias2.AA.aa_alias.self,
		PackageAlias2.AA.aa_alias.that, PackageAlias2.AA.aa_alias.that.self, PackageAlias2.AA.self,
		PackageAlias2.AA.that, PackageAlias2.AA.that.self, PackageAlias2.AA_alias, PackageAlias2.AA_alias.aa,
		PackageAlias2.AA_alias.aa.self, PackageAlias2.AA_alias.aa.that, PackageAlias2.AA_alias.aa.that.self,
		PackageAlias2.AA_alias.aa_alias, PackageAlias2.AA_alias.aa_alias.self, PackageAlias2.AA_alias.aa_alias.that,
		PackageAlias2.AA_alias.aa_alias.that.self, PackageAlias2.AA_alias.self, PackageAlias2.AA_alias.that,
		PackageAlias2.AA_alias.that.self, PackageAlias2.A_alias, PackageAlias2.A_alias.a, PackageAlias2.A_alias.a.self,
		PackageAlias2.A_alias.a.that, PackageAlias2.A_alias.a.that.self, PackageAlias2.A_alias.a_alias,
		PackageAlias2.A_alias.a_alias.self, PackageAlias2.A_alias.a_alias.that, PackageAlias2.A_alias.a_alias.that.self,
		PackageAlias2.A_alias.self, PackageAlias2.A_alias.that, PackageAlias2.A_alias.that.self, PackageAlias2.B,
		PackageAlias2.B.a, PackageAlias2.B.a.self, PackageAlias2.B.a.that, PackageAlias2.B.a.that.self,
		PackageAlias2.B.a_alias, PackageAlias2.B.a_alias.self, PackageAlias2.B.a_alias.that,
		PackageAlias2.B.a_alias.that.self, PackageAlias2.B.b, PackageAlias2.B.b.self, PackageAlias2.B.b.that,
		PackageAlias2.B.b.that.self, PackageAlias2.B.self, PackageAlias2.B.that, PackageAlias2.B.that.self, test.A,
		test.A.self, test.A.that, test.A.that.self, test.AA, test.AA.aa, test.AA.aa.self,
		test.AA.aa.that, test.AA.aa.that.self, test.AA.aa_alias, test.AA.aa_alias.self,
		test.AA.aa_alias.that, test.AA.aa_alias.that.self, test.AA.self, test.AA.that, test.AA.that.self,
		test.AA_alias, test.AA_alias.aa, test.AA_alias.aa.self, test.AA_alias.aa.that,
		test.AA_alias.aa.that.self, test.AA_alias.aa_alias, test.AA_alias.aa_alias.self,
		test.AA_alias.aa_alias.that, test.AA_alias.aa_alias.that.self, test.AA_alias.self, test.AA_alias.that,
		test.AA_alias.that.self, test.A_alias, test.A_alias.self, test.A_alias.that, test.A_alias.that.self,
		test.test_A, test.test_A.self, test.test_A.that, test.test_A.that.self, test_A, test_A.self,
		test_A.that, test_A.that.self
 	--- */
	feature test_A : A_alias{}
}