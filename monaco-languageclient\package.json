{"private": true, "type": "module", "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2", "pnpm": "10.3.0", "yarn": "4.6.0"}, "devDependencies": {"@codingame/esbuild-import-meta-url-plugin": "~1.0.3", "@codingame/monaco-vscode-rollup-vsix-plugin": "~18.1.0", "@eslint/eslintrc": "~3.3.1", "@eslint/js": "~9.29.0", "@stylistic/eslint-plugin": "~5.0.0", "@testing-library/react": "~16.3.0", "@types/node": "~22.15.32", "@types/react": "~19.1.8", "@types/react-dom": "~19.1.6", "@typescript-eslint/eslint-plugin": "~8.35.0", "@typescript-eslint/parser": "~8.35.0", "@vitejs/plugin-react": "~4.6.0", "@vitest/browser": "~3.2.4", "editorconfig": "~3.0.0", "esbuild": "~0.25.5", "eslint": "~9.29.0", "eslint-plugin-header": "~3.1.1", "eslint-plugin-import": "~2.32.0", "eslint-plugin-unused-imports": "~4.1.4", "globals": "~16.2.0", "http-server": "~14.1.1", "minimatch": "~10.0.3", "playwright": "~1.53.1", "shx": "~0.4.0", "tsx": "~4.20.3", "typescript": "~5.8.3", "vite": "~6.3.5", "vitest": "~3.2.4"}, "scripts": {"clean": "npm run clean --workspaces", "compile": "npm run compile --workspaces", "watch:clean": "tsc --build tsconfig.build.json --clean", "watch": "tsc --build tsconfig.build.json --watch --verbose", "lint": "eslint", "production:build": "npm run production:build --workspace packages/examples", "production:preview:build": "npm run production:preview:build --workspace packages/examples", "production:preview": "npm run production:preview --workspace packages/examples", "dev": "vite", "dev:debug": "vite --debug --force", "report:versions:vite": "echo vite: && vite --version && echo vitest: && vitest --version", "report:versions:node": "echo npm: && npm --version && echo node: && node --version && echo eslint: && eslint --version", "report:versions:tools": "echo tsc: && tsc --version && echo eslint: && eslint --version && echo pnpm: && pnpm --version && echo yarn: && yarn --version", "report:versions": "echo Reporting versions: && npm run report:versions:node && npm run report:versions:vite && npm run report:versions:tools", "build": "npm run build --workspaces", "build:client": "npm run build --workspace packages/client", "build:vscode-ws-jsonrpc": "npm run build --workspace packages/vscode-ws-jsonrpc", "build:wrapper": "npm run build --workspace packages/wrapper", "build:wrapper-react": "npm run build --workspace packages/wrapper-react", "build:examples": "npm run build --workspace packages/examples", "start:example:server:json": "npm run start:server:json --workspace packages/examples", "start:example:server:python": "npm run start:server:python --workspace packages/examples", "release:prepare": "npm run reset:repo && npm ci && npm run build && npm run lint && npm run test:run", "reset:chrome": "shx rm -fr ./.chrome", "reset:repo": "git clean -f -d -x --exclude=.chrome", "test": "npm run test:install && npm run test:direct", "test:direct": "vitest --config vitest.config.ts", "test:run": "npm run test:install && npm run test:direct:run", "test:direct:run": "vitest --config vitest.config.ts --run", "test:debug": "npm run test:playwright -- --inspect-brk=20222 --browser --no-file-parallelism", "test:install": "playwright install --with-deps chromium"}, "workspaces": ["packages/client", "packages/vscode-ws-jsonrpc", "packages/wrapper", "packages/wrapper-react", "packages/examples"], "dependencies": {"monaco-editor-wrapper": "^6.9.0"}}