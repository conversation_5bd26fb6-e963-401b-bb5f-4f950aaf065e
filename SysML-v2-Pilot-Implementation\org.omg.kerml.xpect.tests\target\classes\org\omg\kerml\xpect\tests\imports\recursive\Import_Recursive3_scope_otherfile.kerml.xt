//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.recursive.KerMLImportRecursiveTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="Import_Recursive3.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="Import_Recursive3.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package RecursiveImport {
  
  
   	public import P::**;

  	// NOTE: There is some duplication due to the implicit public import of "P" itself
  	// as part of the recursive public import.
	//* XPECT scope at X ---
	P.X, RecursiveImport.P.X,
	P.X.X1, RecursiveImport.P.X.X1, 
	P.Q.Y, RecursiveImport.P.Q.Y, 
	P.Q.R.Z, RecursiveImport.P.Q.R.Z, 
	
	P.S.S1, RecursiveImport.P.S.S1, 
	P.S.S1.X1, RecursiveImport.P.S.S1.X1, 
	P.S.T.T1, RecursiveImport.P.S.T.T1, 
	P.S.T.U.U1, RecursiveImport.P.S.T.U.U1, 
	
	P.X,
	P.X.X1, 
	P.Q.Y, 
	P.Q.R.Z, 
	
	X, RecursiveImport.X,
	X1, RecursiveImport.X1,
	X.X1, RecursiveImport.X.X1,
	Q.Y, RecursiveImport.Q.Y,
	Q.R.Z,  RecursiveImport.Q.R.Z,	  
	Y, RecursiveImport.Y,
	R.Z, RecursiveImport.R.Z,
	Z, RecursiveImport.Z,
	
	P.S.S1, 
	P.S.S1.X1, 
	P.S.T.T1, 
	P.S.T.U.U1,
	
	S.S1, RecursiveImport.S.S1,
	S.S1.X1, RecursiveImport.S.S1.X1,
	S1.X1, RecursiveImport.S1.X1,
	S.T.T1, RecursiveImport.S.T.T1,
	S.T.U.U1, RecursiveImport.S.T.U.U1,
	
	S1, RecursiveImport.S1,
	T.T1, RecursiveImport.T.T1,
	T1,  RecursiveImport.T1,
	T.U.U1, RecursiveImport.T.U.U1,
	U.U1, RecursiveImport.U.U1,
	U1, RecursiveImport.U1,
	
	x, RecursiveImport.x,
	y, RecursiveImport.y,
	z, RecursiveImport.z,
	s, RecursiveImport.s,
	t, RecursiveImport.t,
	u, RecursiveImport.u,
	xx1, RecursiveImport.xx1,
	
	x.X1, RecursiveImport.x.X1,
	s.X1, RecursiveImport.s.X1,
	
	--- */
	x: X;	
	y: Y;
	z: Z;
	s: S1;
	t: T1;
	u: U1;
	xx1: S1::X1;
}
