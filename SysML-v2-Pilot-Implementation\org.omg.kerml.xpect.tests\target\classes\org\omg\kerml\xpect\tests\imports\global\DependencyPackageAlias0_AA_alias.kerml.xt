//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.global.KerMLImportGlobalTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyPackageAlias1.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyPackageAlias1.kerml"}
			}
		}
	}
END_SETUP 
*/
//
// XPECT noErrors ---> ""
package test{
	//* XPECT scope at PackageAlias1::AA_alias ---
	    test, PackageAlias1,
		AA_alias, AA_alias.aa, AA_alias.aa_alias,  test.AA_alias, test.AA_alias.aa, test.AA_alias.aa_alias,
	   	PackageAlias1.A, PackageAlias1.A.a, PackageAlias1.A.a_alias,
	   	PackageAlias1.AA, PackageAlias1.AA.aa, PackageAlias1.AA.aa_alias,
	   	PackageAlias1.A_alias, PackageAlias1.A_alias.a, PackageAlias1.A_alias.a_alias,
    	PackageAlias1.AA_alias, PackageAlias1.AA_alias.aa, PackageAlias1.AA_alias.aa_alias,
 --- */
	public import PackageAlias1::AA_alias;
}
//*
package PackageAlias1{
	alias A_alias for A;
	class A{
		class a{}
		alias a_alias for a;
	}
	alias AA_alias for AA;
	class AA{
		class aa{}
		alias aa_alias for aa;
	}
}
*/