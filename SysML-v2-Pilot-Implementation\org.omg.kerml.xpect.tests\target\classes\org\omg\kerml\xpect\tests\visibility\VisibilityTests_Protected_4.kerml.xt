//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package Test3{
	classifier P{
		protected classifier PP {
			protected classifier PPP{}
		}
	}
	classifier A specializes P{
		classifier B specializes PP {
			feature CC: PPP;
		}
	}
}
	


