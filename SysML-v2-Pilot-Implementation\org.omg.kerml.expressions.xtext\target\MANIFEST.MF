Manifest-Version: 1.0
Automatic-Module-Name: org.omg.kerml.expressions.xtext
Bundle-ManifestVersion: 2
Bundle-Name: org.omg.kerml.xtext
Bundle-Version: 0.51.0.202509041722
Bundle-SymbolicName: org.omg.kerml.expressions.xtext; singleton:=true
Bundle-ActivationPolicy: lazy
Require-Bundle: org.eclipse.xtext,org.eclipse.xtext.xbase,org.eclipse.eq
 uinox.common;bundle-version="3.5.0",org.eclipse.xtext.xbase.lib;bundle-
 version="2.14.0",org.eclipse.xtext.util,org.eclipse.xtend.lib;bundle-ve
 rsion="2.14.0",org.antlr.runtime;bundle-version="[3.2.0,3.2.1)",org.omg
 .sysml;bundle-version="0.2.0",org.eclipse.uml2.uml,org.eclipse.uml2.uml
 .resources,org.eclipse.xtext.common.types,org.objectweb.asm;bundle-vers
 ion="9.3.0";resolution:=optional
Export-Package: org.omg.kerml.expressions.xtext.scoping,org.omg.kerml.ex
 pressions.xtext.validation,org.omg.kerml.expressions.xtext.generator,or
 g.omg.kerml.expressions.xtext.services,org.omg.kerml.expressions.xtext,
 org.omg.kerml.expressions.xtext.parser.antlr,org.omg.kerml.expressions.
 xtext.parser.antlr.internal,org.omg.kerml.expressions.xtext.serializer
Import-Package: org.apache.log4j
Bundle-Vendor: SysML v2 Submission Team
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

