# Monaco Editor 和 Langium 依赖安装指南

## 安装必要的依赖

在 `React/agent-chat-app` 目录下运行以下命令：

```bash
# 进入项目目录
cd React/agent-chat-app

# 安装 Monaco Editor 相关依赖
npm install monaco-editor@^0.52.2

# 安装 Langium 相关依赖（可选，用于完整的语言服务器支持）
npm install langium@^3.3.1
npm install vscode-languageserver@^9.0.1
npm install vscode-languageserver-protocol@^3.17.5
npm install vscode-languageserver-types@^3.17.5

# 安装开发依赖
npm install --save-dev @types/monaco-editor
npm install --save-dev worker-loader
npm install --save-dev raw-loader
```

## 或者一次性安装所有依赖

```bash
npm install monaco-editor@^0.52.2 langium@^3.3.1 vscode-languageserver@^9.0.1 vscode-languageserver-protocol@^3.17.5 vscode-languageserver-types@^3.17.5

npm install --save-dev @types/monaco-editor worker-loader raw-loader
```

## 验证安装

安装完成后，可以通过以下方式验证：

```bash
# 检查依赖是否正确安装
npm list monaco-editor
npm list langium

# 启动开发服务器
npm run dev
```

## 功能特性

安装完成后，SysML v2 编辑器将支持：

### ✅ 已实现的功能
- **语法高亮**: 完整的 SysML v2 关键字和语法高亮
- **代码补全**: 智能代码补全和代码片段
- **语法验证**: 基本的语法错误检测
- **多标签页**: 支持多文件编辑
- **问题面板**: 显示语法错误和警告
- **快捷键**: 支持 Ctrl+S 保存等快捷键

### 🔧 技术实现
- **Monaco Editor**: 提供强大的代码编辑功能
- **Web Worker**: 在后台运行语言服务器，不阻塞 UI
- **简化的 Langium**: 基本的语法验证和补全功能
- **TypeScript**: 完整的类型安全支持

### 📝 SysML v2 语法支持

编辑器支持完整的 SysML v2 语法，包括：

- **结构元素**: package, part def, part, attribute, connection
- **行为元素**: action def, state def, transition
- **需求元素**: requirement def, constraint
- **接口元素**: interface, port
- **元数据**: doc, comment, metadata
- **关系**: redefines, subsets, specializes, conjugates

### 🎨 编辑器特性

- **深色主题**: 专为 SysML v2 优化的深色主题
- **语法高亮**: 关键字、操作符、字符串、注释的不同颜色
- **自动补全**: 关键字和常用代码片段的智能补全
- **错误提示**: 实时语法错误检测和标记
- **代码折叠**: 支持代码块的折叠和展开
- **行号显示**: 清晰的行号和列号显示

## 使用指南

1. **启动应用**: 运行 `npm run dev` 启动开发服务器
2. **访问编辑器**: 访问 http://localhost:3000/sysml
3. **登录系统**: 使用测试账号 admin/password123
4. **创建项目**: 点击"新建项目"创建一个新项目
5. **创建文件**: 在项目中创建命名空间和 SysML v2 图文件
6. **开始编辑**: 双击图文件在编辑器中打开并开始编辑

## 故障排除

### 如果 Monaco Editor 无法加载

1. 检查网络连接
2. 清除浏览器缓存
3. 重启开发服务器

### 如果语法高亮不工作

1. 确保文件扩展名为 `.sysml` 或 `.sysmlv2`
2. 检查浏览器控制台是否有错误
3. 尝试刷新页面

### 如果代码补全不工作

1. 确保 Web Worker 正常启动
2. 检查浏览器是否支持 Web Workers
3. 查看浏览器控制台的错误信息

## 下一步开发

### 短期计划
- [ ] 集成完整的 Langium 语言服务器
- [ ] 实现更精确的语法验证
- [ ] 添加代码格式化功能
- [ ] 实现符号导航和查找引用

### 长期计划
- [ ] 实现实时协作编辑
- [ ] 添加调试功能
- [ ] 集成版本控制
- [ ] 支持插件扩展

## 技术支持

如果遇到问题，请检查：
1. Node.js 版本是否为 18+
2. 依赖是否正确安装
3. 浏览器是否支持现代 JavaScript 特性
4. 网络连接是否正常

---

**SysML v2 编辑器现已准备就绪！** 🎉
