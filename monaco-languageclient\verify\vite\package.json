{"name": "example-client-vite", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "dependencies": {"monaco-languageclient-examples": "~2025.6.2"}, "devDependencies": {"shx": "~0.3.4", "vite": "~6.2.1"}, "scripts": {"verify": "npm install && npm run build && npm run start", "verify:ci": "npm install && npm run build", "clean": "shx rm -fr dist", "build:msg": "echo Building client-vite example:", "build": "npm run build:msg && npm run clean && vite build", "start": "vite preview"}}