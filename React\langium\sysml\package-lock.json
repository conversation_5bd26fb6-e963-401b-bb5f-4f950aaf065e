{"name": "sysml", "version": "0.0.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "sysml", "version": "0.0.1", "dependencies": {"@codingame/monaco-vscode-editor-service-override": "~3.2.3", "@codingame/monaco-vscode-keybindings-service-override": "~3.2.3", "chalk": "~5.3.0", "commander": "~11.0.0", "langium": "~3.5.0", "monaco-editor": "npm:@codingame/monaco-vscode-editor-api@~3.2.3", "monaco-editor-workers": "^0.45.0", "monaco-editor-wrapper": "^6.9.0", "monaco-languageclient": "~8.1.1", "shelljs": "^0.10.0", "vscode": "npm:@codingame/monaco-vscode-api@~3.2.3", "vscode-languageclient": "~9.0.1", "vscode-languageserver": "~9.0.1"}, "bin": {"sysml-cli": "bin/cli.js"}, "devDependencies": {"@codingame/esbuild-import-meta-url-plugin": "~1.0.2", "@types/node": "^18.0.0", "@types/vscode": "~1.67.0", "@typescript-eslint/eslint-plugin": "~7.3.1", "@typescript-eslint/parser": "~7.3.1", "concurrently": "~8.2.1", "esbuild": "^0.20.2", "eslint": "~8.57.0", "http-server": "~14.1.1", "langium-cli": "~3.5.0", "typescript": "~5.1.6", "vite": "~5.2.7", "vitest": "~1.4.0"}, "engines": {"node": ">=18.0.0", "vscode": "^1.67.0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@chevrotain/cst-dts-gen": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/cst-dts-gen/-/cst-dts-gen-11.0.3.tgz", "integrity": "sha512-BvIKpRLeS/8UbfxXxgC33xOumsacaeCKAjAeLyOn7Pcp95HiRbrpl14S+9vaZLolnbssPIUuiUd8IvgkRyt6NQ==", "license": "Apache-2.0", "dependencies": {"@chevrotain/gast": "11.0.3", "@chevrotain/types": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/@chevrotain/gast": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/gast/-/gast-11.0.3.tgz", "integrity": "sha512-+qNfcoNk70PyS/uxmj3li5NiECO+2YKZZQMbmjTqRI3Qchu8Hig/Q9vgkHpI3alNjr7M+a2St5pw5w5F6NL5/Q==", "license": "Apache-2.0", "dependencies": {"@chevrotain/types": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/@chevrotain/regexp-to-ast": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/regexp-to-ast/-/regexp-to-ast-11.0.3.tgz", "integrity": "sha512-1fMHaBZxLFvWI067AVbGJav1eRY7N8DDvYCTwGBiE/ytKBgP8azTdgyrKyWZ9Mfh09eHWb5PgTSO8wi7U824RA==", "license": "Apache-2.0"}, "node_modules/@chevrotain/types": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/types/-/types-11.0.3.tgz", "integrity": "sha512-gsiM3G8b58kZC2HaWR50gu6Y1440cHiJ+i3JUvcp/35JchYejb2+5MVeJK0iKThYpAa/P2PYFV4hoi44HD+aHQ==", "license": "Apache-2.0"}, "node_modules/@chevrotain/utils": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/utils/-/utils-11.0.3.tgz", "integrity": "sha512-YslZMgtJUyuMbZ+aKvfF3x1f5liK4mWNxghFRv7jqRR9C3R3fAOGTTKvxXDa2Y1s9zSbcpuO0cAxDYsc9SrXoQ==", "license": "Apache-2.0"}, "node_modules/@codingame/esbuild-import-meta-url-plugin": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@codingame/esbuild-import-meta-url-plugin/-/esbuild-import-meta-url-plugin-1.0.3.tgz", "integrity": "sha512-SAIOsWZteIWYAk04BCqQ+ugu8KiJm8EplQbMvxJl905uZv3r+21+XjtGg/zzrbxlVAY1cP+hGAG7z7sBPmy63w==", "dev": true, "license": "ISC", "dependencies": {"esbuild": ">=0.19.x", "import-meta-resolve": "^4.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-HJvIQWbe7SOCypt6sgWqmeKeCcQ6S6vDwSwLn4yEjAb6hosfFj6pxXi5oRR9BOyp5JTvadzw/xtkj4/t0BC+Hw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-GHOb3InFWTFpVAzd+O73giA2Ru6xciTmkuNYbLl776jgqMmzlHuTNtWEXURzjDDsfKQVbGm0nTqe4Je7v9E7kg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-P7deCRc6kwox2xs/7ktxfOw/9JD5lWKINaA6ISiiLdIsMf44NaBvjXunWBM7Z8pd3SFrrAyxqKBWlNu1hmjI3A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-/a8uYSyiFLwmTKva6/JA1tEyacxldULl0e+XLEiMprec34+V6JlQxJ+pOfqk0O1jU2G+FJIomBiLdsDrdRZlhw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-83eK4k7F64+ZURZv0rsBbARZa3nSs5UHvwlo7G+087EBAKZRgvSgvBTLFC5CT0fN4bpxW93QTR819pHDRREP1Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-CblbOEgtnQSeeQoecVTV3vYL3yKE01ag2k3EeegzyiVh+KXiuPV0OHPl85GTDHjghD4FRoSGPr2j0U93UnIcyA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-LuPoeUq7u8xaSJeHKA55KirzCZbiLAUzYHD3Lu1ZbIUsEPuoVgE6jIKpPDAapGzsjz8yFI86CzW9sFi0hL42fQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-v8ZPShK8Ie6k499K43A7KiQNrKTMvfXAca93xye7dxc7q0LLl3NCIckSb0sbCpSFXK4FKRAwvmgQ4QiofyuY/Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-6CtI8Y2PpqQpOnY9C9HVxCpsj9KmWar96dD8Fnr3zGXaeyiAMAPad2NSC8grOZ+ISkt/OgRW73OQlZJSLxd3Hg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-QC6s1KtwSlFTOCYkgMhau3bFL/0mmjWTHyyH7s888ehVxTjQ4tZEejN8ibkDsYfcwor+vEdpnAoDOJiCWIM8pA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-/PaKLciS5XeyJ+RnSXRtpSm4nvZvBW9ItY5PZ4UIy06VGPY4lqVdR0a0goIv1njgygjAPKm3w2BIQTOF4HzREQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-<PERSON><PERSON>4Rb19wBW2mLks2GbNerHG1VPtsGZaZJIeUm++EW4BQEQ1M7EiAPZ6eoOaW6NvNl2ZK5j4ioOc5mr+xeXVw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-6FguOn0F88gO6UDcZqlBG6jsJAHXFRUKf0aJGngUJVT08YkTKh8ycAPlntU3zBKaG80k7mM/OvNJzgXO5Y7kKQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-NhM1Z0oeyLylu8qT4vZhDlOEYt6mRdr/s3UKYRW5VSFF8yyCFwZumVNxloGuTVInWLkG5mvWHA53BrfvDDxjIQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-nV0IIt4PaVXEe+dX3YP94C2bRAeXz9MMsad7Q8+amNXz+mV2JM5gFtF89Q012NvRq8DaqiHi5Ttkj+VCUDqwug==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-QnIntEFOqNn4tttiUwfcZgzz+hfEB2tvUd0P1NRAeBD16riXaaVzkN1s7gxBxv3cFL4mrq7Xd1h8BAPElAXcFQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-6/e71/feyFf0gEm6R07hzc7JepOQeAMRSe/KhqJCQEZ3hHXGr0+CaKLLWQtGUnDD/KNm0WwKFeQFpPWcnK0dhg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Q/pOirRWinAoorDD3616rvyrMV/IkjkvDPsaNjQR0tjcPO2llIqObA8gVtN2coDjfLqHASYU+eptwOqeoAonJw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-MTrVphdn4Xc3ZtOFe8SsKE5Xfh7ClcwIHf+GdXrT1wwlA3yozjNJJ+hsWUG59mH56hsXLeHOPE+gs1VbHENTow==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-inHcfBzEaOw7SAFXxwNIxeh2D61Aa2xZknnMBD6JTAnt0qRmrSoNPR6Tu4e6GeLyoxx5ligvqg2SZhZOSOwrpg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-F+n2jH08UEFVf8ZQEH9xiZroOlO8mvOEaBmTfZkAVW499Yq8ooBVZxVKXio4y9cp/ebdvywcJPsllVWdF8m8+Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-D2dv5d1kkdzg23YSxm0Ml/tkWBh6Wu9eWK7ZydqLPC7NvZzHEFL819a5yRWEO1vtH3/M63rb11XLv5g0dGavJA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-v/I+LwzCtYbJiWhYRswpKoSt79RgD710X4H5Q7YgBK3xsisq3hmvdy5CdBXjcOMOU4gMnVUeeVD3P+i+Hva+JA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-5os2MqReKx9BJlHMbGJmJsXPnkYtiOlTZr2vjLFSQ907xULFiaqbnM0Qdmz+TOrAMX4MX/9GIX43z+lOCAUfnA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-6LUnsErqhAhMmUl2gy/ygwX3L1HSsZ3X61ax4fvFpGwX+c+kZTyXNsuqginCpDwWIShB+5eTdEjA9RkxnIG3ig==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-SvD4MF63G3jRrRxoV6JVd4RUfLA3cFB2zl1P1ezJUXGH6c9h5jlxZO+u2D6Fe3BqJuZAIRK4CDUcu6XBmaJR5Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-wnUBrqjH3w1buKFBj3aNrbfqIeGE4SjgIMk7/1eIEK5oSAI/trN29ijw5XumEQiZRDu//chfR134bCobEHi9Zg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-gh6r4+tUYvprIFEYzaXMaBbveOVtRcM0A0gbX7oAUYPblYXnIWoMSWQ1ngqS+ssNvSc88/1CRUDoQu3Qw3FX9g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-691nOngAaBZ/iesJ5FvznPsiqYU7LIYJCIPJnm3MLOEmbzUGiA+RFZzjQfCkd+xR5i7whDQakpShmhe7SqPbSQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-wylddLLfzZs6XkYotn6weum0hfKHQ5/GqkNuxlHzPlqJaZFTuVXcOKoYahtGt8ADE2rUhLbpqdDPqjkPmtJnXQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-uhCNvXJbeLlLL6lGwqrImSKsdrc1VjwFUStXNB5NJXXkxpYMhcWVDnpF/a9Im7pPxTLLAQ7GDup7uZptWmL52A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-nG1rwNLLuWujEpYQGg9+41xwS7tixHKiiTP0WHRyopdqz/LqxIFu16j4IqQDENq9kZUCvpGkbRno8zX30TOcQg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Nd5Jz+j3AT4+hftiyb/7AXEC5qGuOxcVdM8gc30BHMLqtaEfOHwFK2SxqIFJUeIqhdIj8q4cculmmXOudNatAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-PMHm6wXqiLrm0TXOFRcWAlzg1nAK/+4rHPfO5SEbbSpM1ASGZAr6OzdVcqsaOMMihctryjU2szolTMcpnxXnKw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-ju0/cwJ2VdFfjNtnn9+zm71p6B3Ka6dnGWB0n8j0ms3LZ0GEcVFF66xKoufyd5I3iJI9WD/NESUtRBJq8V0Xcw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-9x0Wckw7YzWaIWAKTbZ9xsosu02yz3apFCpLwB1YJobErDMmupD4ZqjeAb0seBDe2ON6Lr45qaY6nBarLzbMHw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-8/beoPg81mSYVLK5h80/0Fx8wPr2QTEE3Z9SMPrcEVqY0sMEB2WrJDqnJVhTENOcOUaln/BgwnBKroJWeCq6vw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-aRLR9+cABqQYPix1V3zTb42EC9MR4fwbf5MjZprdEBscqb9ogDNuLddMArfXbNoCxsLr40X6cyH7nfbNMkrnlA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-3EuK3nkpNTqAgtlwBbJU1UkWGEdkgJYeGmCfoDnBCR/alwk6cQfmDD32zYIgwScLPskGA3Fj65TwwDr7ANhXVg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-heeuABE9EP5p4beTkMb/ujE41ASo+mgPaPZn9Dk/4dztWLovFYPul2sdc5dBjBf1VsYfOQAivnkG9aFavgRhOw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-isKwOTY0KphGfI8wXDlZwNttci99aW7RwvilZZftvNNvk+xowQcySHwqrHkUOZYfWG5m3nMi6mH8uG8jOX/b1Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-bRZdYqY/1kI5WSvR7pv1a7D3hLcuoSA9/TBN+F0QZ4h4IkaD4Snwu+uGKOX0XOsbWaIbNKJdUfAGBYUBMN3RiQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-psXDcF13Z8+QFt0Uk+Dk1yuVxKUBHNDMAfDd4hDMZzIITzBus/ZveSXogRoryPUU2KcUNa0zx5jlbMINFgdDmQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Io7qXcmU44AILpS9shVnW1LSz6hgEuuR9AF5gw+N9SyN//AHAjK78Y8IC8OO/ZP+itfS06r+IKMBOqIT9sF1Hw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-O7ruiIJEpspj2+lvFxtT6FDsBBB6S0ekr1PH2MNtMZsCFSy2kM6UeK6grLQhyDN66tUOk0V14y+mUmKAc4UeiA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Am8FthHOOXaKoexNVaz64AIEQIoe3qiWQ9E/njBPKO8QscOr2c1aSWLfbirm4uoZ0XRoBL65hb7aVat6m4q9Xw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Px2BvDnz4EA1buW1HygUDVfhiMberd+7WjXxz/ZWzYbFj1lWZHniLIAunJh2ErDmYruZiw8K1qaNZ28UXf2uyQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-sAgkkbuPSZ0rKX974k4n74U4vkg+PZnmoGBpBjRP/98cmBm/rETuE2BkLf4TvZJOLA5KXkRIErg8CuwZyx3cWA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-e26pexUKPD5KJlr6DxGNlWtZxXMDDA0r1WMkyWYS2JSdt/ay/QGTDW9AV9OEsUZEHhitlDDuMZeGaf4yw68bFg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-iMuHHVnhzMxApBshHOMGYB2HOkGbfyqtSvfcTEwtk+HroJ4z7RXWY4UwWnsul2siDCS6R651rP2XXhmzRY7jLA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-ssSWmBjR2+KiQqKE5ZKU1O8KcbFlPFBP6yY4Vv92zVF9DjRouH6/gSzdQGYeoM0mIV4EptSFziBJxN1wiUSDsA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-41ZdBeYk5Om7dzP8XnzN8PXE5IPfAxxbIE+wiaVl4LECM0SbTpXeE/KIpdOZlHCE02f91P35nYZXR2O0uw9hig==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-UIaGVrrx8SOPEG86tvg+OXnh7xbrmxpIU+yFNdY672cjrBbfoPg7YCH0t+iD30Ky7IB//H8OY7R7qfbk8N06Gw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-H6WxwsKcshvL7SlSBZNWHLIchj1gWhMjJHeA+6SPuxHPOvuwtPLm+5olxBZImxje1m9cd9S9Nvw4rWFP0R9Cdw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Xg3zqztIlBuCsUjgkuUVy4/XUnfsa+zoG2P/xdMgW2U3WFaT7di/2o3Hywk312uw2aXl/G3mkz8te/aA24y8Ng==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-7iR5nVXkk+u9vvSJ/5Vsfz0pGrjoxnG0gKh0MoIuDDOsr4BRiYHCP5aHOzV3eIxdYsyUXlTXWzjxfvUmSm/dgQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-3HNLxqEoyv5DtbzR2No9/cW29wCkQu3Uw7W4C4Ow6wY3G33zpYnMWwL/GxqtJH9dnzcIKbvwg5F5hF87QJhZBA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-+nEx0S/8A/Q6oPQLJe0oYcjw4HMxdLb0TRa3XKIsv0eYzF/pdg8kou2kEtpTP7H1nEBu3/HQ2c0n3gVyD2tfFw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-TjiNqwhiXMQiPqeeJ3WJc9f2XTSVqk7cdsC2N32BjRliE2YmD/yu17KgK43iquMD/iMg1vFRqbQO3xRAGtBk0w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-8u1ZsCEk4/Yw4CFDlUiYYaEfzy52q5QfvhtFvc1C1Pq0F2nJqO8CYzWawb7ySmVv2kotypEbxjw+4Kz9XeT1MA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-sdwU5CBy3yP7d0phYAR8LGoyKOgIyE1QRWbcPx5SepxaZKc+CbCXMzaUZCaqrp1JiavyOSOGG0YfE1fDZfXJ7w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-aYzRZudyJaJ6OjZAH48ED132pOv+8vZywAlSZeN9F29L9f7VnC7ZKVRLFpkewohkNbW/klZxzduXgLas1Hfrbw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-6Bh/iNPAo9s5lxVjvuQnQDHhtxVTuSEc1IyfiHvgJS1s/guZieeJGwXYmCvwwelDjr4s0iuvcIx6nbmWb9huaQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-wl+AR4PcKRG9CSuJsILBrtpCW83xmBqDYqRHo9pU4QB8frRpM59xh8oFW8+mdgSa1cyX6d+owrefXI2a70lOWA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-329x1rsU24qzKwHc5QtFnrfh8Nx1Gqjok7uVpAhgM/dOw75z7kQkDmmGf1/+PsHwwB1uYjqT5mHcNAgChuvaLw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-DnkZ7Ljx3bMNlzhbhLx7VElBBrQ1AWLhVHYIsJD5hyN4pgduEM96iSUiO92B5f6Z6iPKC3Zcxi0jgTWKIkEaMw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-api/-/monaco-vscode-api-18.1.3.tgz", "integrity": "sha512-ZVYh3Vd6Frvcfx/F0QjXontURXET7/J4CMxzn5SKAe6CfqDG0xVMV9VjQ3RHX7tutSV9vWBIZLtic0+X5r0iGA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-base-service-override": "18.1.3", "@codingame/monaco-vscode-environment-service-override": "18.1.3", "@codingame/monaco-vscode-extensions-service-override": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3", "@codingame/monaco-vscode-host-service-override": "18.1.3", "@codingame/monaco-vscode-layout-service-override": "18.1.3", "@codingame/monaco-vscode-quickaccess-service-override": "18.1.3", "@vscode/iconv-lite-umd": "0.7.0", "dompurify": "3.2.6", "jschardet": "3.1.4", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-base-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-base-service-override/-/monaco-vscode-base-service-override-18.1.3.tgz", "integrity": "sha512-QFMJZEicuBVZjgSQZhCNgEejUJ2V4s55GuOVvEhonf8k32f+MUaAWkdMcjOTNQhPsga2BvQpgAW6Q0La+PZ4fw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-environment-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-environment-service-override/-/monaco-vscode-environment-service-override-18.1.3.tgz", "integrity": "sha512-BTiNazufHF3FfP/ulw+vEFs2v2XT1hfcj7+JEFhpjUp2i7P1pvUTjAllZs22HCCyiief/6qhM8FgB1d6HtbCaA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-extensions-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extensions-service-override/-/monaco-vscode-extensions-service-override-18.1.3.tgz", "integrity": "sha512-GXrQ88rtcjrPmcwLWxO9qC7g6oWtiFeeGa65FvjlDUiu4qf/4vN6ObNHs+YfkmTc6dpx2m8rsIe9SDuU1NgCng==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-host-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-host-service-override/-/monaco-vscode-host-service-override-18.1.3.tgz", "integrity": "sha512-afXrOGHdLJOt/Y9NSAKw6Y58jklvakASV4TrQBDZs0KUANYzWAR5uslPRAD5jiVTmQGGLaAXWwb7a4DWgzJCPw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-layout-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-layout-service-override/-/monaco-vscode-layout-service-override-18.1.3.tgz", "integrity": "sha512-M1SNWE/Dd9xm5wdwTPXkEugbPBUCL6GFMcXWfns+PrqvhEizVYjQV82A5PaMP3vO9xkaYacADUVcBPSWkNHfxQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-api/node_modules/@codingame/monaco-vscode-quickaccess-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-quickaccess-service-override/-/monaco-vscode-quickaccess-service-override-18.1.3.tgz", "integrity": "sha512-eDhYCBQA4SpmAjBicfiAAeM0LN/+9RmvA8ziz92rvtOihxr431pk1o8I/rIxyxjdo/Z43rI6weiNzkEZPOTNeQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-JaJ1zc2XNSrCWJmczAcKuqVEq16jqNY5btY8KpKQcU72owpFHVZUlw30wpAaaUX/kMUFnNgilA46XuwvLLif1Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-base-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-base-service-override/-/monaco-vscode-base-service-override-3.2.3.tgz", "integrity": "sha512-<PERSON>VtyMIbcFPC68Fv6jEg7p7MYIK9UzymnbwPRetuOHFj1xMALI7qkl9DH0czBBOZAk+4BttXqZLlJ9Ph3B4HHg==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-DEU7ILAcWzVXn0aD8pU5nY8+FvGEVMkefTF9e2pjioPljdYHbVLY/4tYalMzrt/ShFJiRYtZmknbCePap+CGLQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-gwhnNt7wb4+FtoH5OsVLn4qkjE0gfV2gGmMC5cPkUooDA/+jpo9Xz96AAP1qS5ZFuU9f1hHOKz1/2Y2BY8Pj3Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-bulk-edit-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-bulk-edit-service-override/-/monaco-vscode-bulk-edit-service-override-18.1.3.tgz", "integrity": "sha512-GihiHmJBq2PRPZuHJvD7euVy58zR5LprkeHPwFE/oieML9KI09n1g2Jodp2/DSB8gU9R/rp/1cVfFYO37MPM9A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-dXGyDxIkjnCp4dxvEXxe29BsAIb91fqmt6f+nrPzkTetjlquPm0yLNJVSVo4r0rAkVAK96lal53biSaBSBVe/w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-1ToeIZdV6hIRx0GSyKWj+kx5GSShLwGlGprlKxzxPUmDdV0zKkAy13uVYJHL2v6+BvJEUI6c7z0h8An9Phfylw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-6kevMdaj9zbGPcWoEQuy8///HY/wcbcX69hRMJzjb3vuDgrC1ezyGYkG2F5z87M3sHIAL6dPztjXkDsZGFMN2Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-XshqhvAlbi+8F91xzBIyJs/kCCyM9aMqw4vRKj6yK5yM3kIdzQw82LbJHch1E09sdnptFhnVftnjip0ylOtQBw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-/wN1xYAdNpmLBYf0+fCZI3OG5rbSN9mfqj+o7bX9+CPAoE71pgIIJaQ06i/4hToTYhcuk2+ZAItIgE+6JzljiQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-configuration-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-configuration-service-override/-/monaco-vscode-configuration-service-override-18.1.3.tgz", "integrity": "sha512-JSDE3CV5BF2zG6l+djuKjQG9/kEicd9ga46PJWmuI8/EoCJIRQqn9q+muYjzC7CFp5p75xTN4+6ofTABUOmY0Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-configuration-service-override/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Cuvc2nV/qZa4AoROJqnzdoC8FkITNOLIgDvrrC5PIXGWhw21RjobAOU12SobwBCrmdsOUjODmuyoG33m2gp/Cw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-5JfFl4/X3DG962uY4e/EfcNkmYi72D+v/H2GfhcJhEGr1hAroEeLo6x9zWLp4wz2wczuNqaN1qMqWHTbqXMoxA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-lzIXfOzZfprFtH30jxUkFBMpFfENqhrhoPrdQvc4orq0QXi7gzHOxPxzUgl9CTuhjTAZNPxi+sphCJGhVCXsbg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-YbBDEWxpmktYUiAD4jGuK4YOADdI42PblksFQev7nbTfyqQ30BZPMO4Z/MC2+kmgBJ2Z6P/CbIHPEZ4f2K9fmw==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-xoRKmTVJmdRtqOPIolNGYQp/ipfeFmfCImcplTK+B87i7PLhzYMH+JLgpNjBiBuJyLpArPS1NhHJsKzIDr3T1A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-8Y7X7VgC8rLgcphWYDA77Eps5tNbVb338+CbAUw5A8ihtLYbKTFPk/rT7YQxgw/JUL1djW0f/jkCJo9y2QNZVQ==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-+gGpFBkEHA31bmKp+drCEp5UnUtaBnfYHinr/SAwMruXZVmItA5L84RzzfrrhtfiB0nnB62hxDktP+pugiMYIA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-FMjL7KNENLT+5DMC4FCS7E2rjrKlv6hD/xzl9KKLUgPS8E62MPBlcQl04JZdyRKOCnf6acLXbiIiB36wDqRJeg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-rcvbcg3wMJxFczZ9JR/83aJgf/DR/fDrrP4AcXeYmXpSfAMewHSCZotrDWaMXevzyMrTRFk71DCABxsiR9hY9w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-2gHjmDliIu42KCO1bVWVH4s1ckhZ/20r/LqMFL4Vfl9lS1hq8CyZdXLoQaziJN54Vq7VZg0Fqg3lxuyk75l0ew==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-editor-api": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-api/-/monaco-vscode-editor-api-18.1.3.tgz", "integrity": "sha512-VSLWmToMS5/CiaP8tRX+JvUqDA2iECX9MZqUkTkaNgK976OPigVjyEdqia+85xwLjYnNLYfbh2YijFAXR3F5eA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-editor-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-service-override/-/monaco-vscode-editor-service-override-3.2.3.tgz", "integrity": "sha512-/h9lZbgXaui1GlRTkxehrbkTBFX/zOv+ywAb38liZUsmjqGjBfegATOHlzTmS48SkiokF1vvAZCBGmQCXHK6aQ==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-environment-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-environment-service-override/-/monaco-vscode-environment-service-override-3.2.3.tgz", "integrity": "sha512-7YVJhS0RuKkrjwzz2ZKJeU98t29Yc2U/NfQfpTTbLfnZVBxPnC4Gbm59bRWswjtbBrIVpO5Dil+00o32HwXzyw==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-extension-api": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extension-api/-/monaco-vscode-extension-api-18.1.3.tgz", "integrity": "sha512-jv1sRVVIE4MXBcUGgPiDOZQMYOYL277CuJUdzt8GwvirgiEVvWJyl2aql572SgdkKRkAr42Jq5KFlgns0NSMeg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-extensions-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-extension-api/node_modules/@codingame/monaco-vscode-extensions-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extensions-service-override/-/monaco-vscode-extensions-service-override-18.1.3.tgz", "integrity": "sha512-GXrQ88rtcjrPmcwLWxO9qC7g6oWtiFeeGa65FvjlDUiu4qf/4vN6ObNHs+YfkmTc6dpx2m8rsIe9SDuU1NgCng==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-extension-api/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-extensions-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extensions-service-override/-/monaco-vscode-extensions-service-override-3.2.3.tgz", "integrity": "sha512-YZfEmKaXyXh54rIJ/c9k+D1JeWfHiryW5UfuVgscmLS3QoiLsPRurfVBY4zIBN88rTQknOViqqW7llpnmaiFYQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-base-service-override": "3.2.3", "@codingame/monaco-vscode-environment-service-override": "3.2.3", "@codingame/monaco-vscode-extensions-service-override": "3.2.3", "@codingame/monaco-vscode-files-service-override": "3.2.3", "@codingame/monaco-vscode-host-service-override": "3.2.3", "@codingame/monaco-vscode-layout-service-override": "3.2.3", "@codingame/monaco-vscode-quickaccess-service-override": "3.2.3", "vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-yEdMrkIIj5wjYdlvHdn3aIo/pLv859Wge5Q0GkPXYdC2GufGPJhv8501DKY4bUBQsrKJOLdlWE5w4dtKOkpljQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-W/7/VmZgbpk2DZ1VwyxxNgB/lL+WCzld+P++koA01tlPdFnQJB5DNHghKgte+GsGYkgAyJ/5Cm6tHRHiX/5Q4A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-UQoytTOkJmVMTqTjpNwVdkW42uzIGdMTmPTakej/9eeGtdwvIYIL25spcr+wAiHQEfXCepOS6L7ydMxoqn5vFQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-Hq6/lKT4kPucXw3SqH8ROmSy4ewoip8XZLArFy7F6KlDLnVFAFDL1znKVNxh04Dpey0E7yQ2tUAQdlS8/FocTA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-2zCpzMb+xb7CFMuZRUePvRxZxKdrScL5eydPtIbLpTPQRVNnApsozqoWAcenkk7gQpDTvtjlegPK0b+JZvJ4Tg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-SOGSgyhAVKYBWeLcm/Y6Cf5unzyCKAJbZ+jP0Bku2QiR7u5sLS56BUeJPXHVFCMk1xHRG/0Y8Ext2ikYaI47AA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-PecY8zwjSp2Xgh7T0H8t3PJHzV1D68ryi6qP5dEn5Q7zw0IBsXAJ4YVFHJ0tvJCMrVJ6wAw2cuE+tmpDsc6WXQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.3.tgz", "integrity": "sha512-BystEB15Jx9LLqVPebei2VrzdnyyyDjedV0bKgd3UGw+imusom5oQ+YT+Z3ZvjpWF4FYbX3mKgsxrepbg6SG9Q==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-files-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-3.2.3.tgz", "integrity": "sha512-M/yEyD4zSYt7eQISeH/OSUELxx7nXtmXIHjC9mQXBo0VD47nAgjy9Yo3vS1al3+Sz3j1Uj2je96x/6Q9zGwg4A==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-host-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-host-service-override/-/monaco-vscode-host-service-override-3.2.3.tgz", "integrity": "sha512-zY54k4czioGczMOmWPF6aHx7jrHSnkU1D36MYyY3wrvFgI5diJCrCSz35VPf0WTlLh4OT1jJ+D9ufmJ1cn1moQ==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-keybindings-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-keybindings-service-override/-/monaco-vscode-keybindings-service-override-3.2.3.tgz", "integrity": "sha512-gjBoR/biIytOfXc493IinF7htMuu/lNQd970LdotcDJKgqrDGd36exl3KW6ndvR/OzlwMB+E5oL3absSwqHtag==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-base-service-override": "3.2.3", "@codingame/monaco-vscode-environment-service-override": "3.2.3", "@codingame/monaco-vscode-extensions-service-override": "3.2.3", "@codingame/monaco-vscode-files-service-override": "3.2.3", "@codingame/monaco-vscode-host-service-override": "3.2.3", "@codingame/monaco-vscode-layout-service-override": "3.2.3", "@codingame/monaco-vscode-quickaccess-service-override": "3.2.3", "vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-cs": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-cs/-/monaco-vscode-language-pack-cs-18.1.3.tgz", "integrity": "sha512-gqGX6MkFOhG9s+uGDP9cl5lhqfXNCTFQQzizWEcoqVRJghhnlFkoN6p/xkcILhn7REBOHFfYZGkfeLSdRoGXaQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-de": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-de/-/monaco-vscode-language-pack-de-18.1.3.tgz", "integrity": "sha512-ZOJRV7n+SVttK1Vj+lljNkvrz18VCPlAk74KZLON0jeBLKb0c8enAXy+uPPSQ6+LBjAc1wc8vdif7kUSmZKl2g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-es": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-es/-/monaco-vscode-language-pack-es-18.1.3.tgz", "integrity": "sha512-PcawLxQp3WAM35MzC2G0yHZIv1GsZ13XiXFVYJAFfhcrHww4BEwyzqZnFp8V4Qrwl93jZwj/5v0WYwdblNmBdw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-fr": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-fr/-/monaco-vscode-language-pack-fr-18.1.3.tgz", "integrity": "sha512-526ck9/+eRZLI8TklzArvUQJWNcilozX1d88sWVYvbG5G1zwY/u3Yp/7q+6TQGvvX/6HYsKqKd/En5nB/51zaA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-it": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-it/-/monaco-vscode-language-pack-it-18.1.3.tgz", "integrity": "sha512-tz73m/oCOOsB5kIPdRTRgZc+IxARUAAGfIyQpTR0zistuVtzVS4QxH7JOWSUQFuwtdVVm6t+yWUEIjxiPdmJag==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-ja": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ja/-/monaco-vscode-language-pack-ja-18.1.3.tgz", "integrity": "sha512-Y+C8zZeTJQe71N2fNQJy62BBj6eYp4tijeQCblu7/gnHpNo8Aat2iksO5vA4/KY3E9oZLaEzs8Ql3u0qf8jANA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-ko": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ko/-/monaco-vscode-language-pack-ko-18.1.3.tgz", "integrity": "sha512-7oEvLf2XklSxgBPaTyb5CTtdbpZjngNT3SuiFZL+X0TvwEnhfTvjR+DxRKZLdRu/d9XnFxbQIM2pTJT5kRMmPQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-pl": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-pl/-/monaco-vscode-language-pack-pl-18.1.3.tgz", "integrity": "sha512-y52bRiPTyDHXwo4+1e08FvIK6ySISICDh+V0GzVvxRSNVjBMtgjyMlpNejvjNtgmOpPaHOz5Mnr9CLjZAOrkvw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-pt-br": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-pt-br/-/monaco-vscode-language-pack-pt-br-18.1.3.tgz", "integrity": "sha512-UoowVBi4GkChNMk2sDCdFOi0PgtL8kJAVf/+X1iBdqm8MFkfLp9+BZaw4fvekzvE717em3dImMnIxYX94IKJtA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-qps-ploc": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-qps-ploc/-/monaco-vscode-language-pack-qps-ploc-18.1.3.tgz", "integrity": "sha512-W8oIsL+kOSDyaPd7M6AhWiv//NJpWyHlLGg0j+S8fdzNcZYnp2KNDNsco5Cen3+SYiNwk1vgV7XAjlc1h9CnXw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-ru": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ru/-/monaco-vscode-language-pack-ru-18.1.3.tgz", "integrity": "sha512-jymkqyI/nybCxZIujPiHl3sE6mBTHhPXOMrWScyxod8/gCWEpNUW4Yty8v7GJ7Hv/KbYrBHzM5Xp5pvX4Ok1Ow==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-tr": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-tr/-/monaco-vscode-language-pack-tr-18.1.3.tgz", "integrity": "sha512-0/+BOEEPTc+8lkQtrYnKI60nPCooYokzdVZPRUIovbrq20Lg0vp10LONGnN2Ef0+uoyn7eO8IfCK2Lz86powsg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-zh-hans": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-zh-hans/-/monaco-vscode-language-pack-zh-hans-18.1.3.tgz", "integrity": "sha512-1WM7elRCyP0ohoFdvOJAeA0t2nIWQqhjJch509zj0dyVLiGBJCyel+TdXI7HBVwZDk9AWmOwwq5r08U19xLdmw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-language-pack-zh-hant": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-zh-hant/-/monaco-vscode-language-pack-zh-hant-18.1.3.tgz", "integrity": "sha512-ETxf7aPrGcsmQqDEtoFB3P1MtnULppfRAn1HS+qWPBZRj60MuNQCYLrBuIai9hnNiNBDCwuhMmA78dujTV6plA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-languages-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-languages-service-override/-/monaco-vscode-languages-service-override-3.2.3.tgz", "integrity": "sha512-EO3JDyqY1wNe/2NN1S8TmdHGr+H53UvvC+LWD/sUXyz21LqO1cK/bA7ZRNo0yTizYz080RYxBEzmVZ5UtPqkcg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-files-service-override": "3.2.3", "vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-layout-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-layout-service-override/-/monaco-vscode-layout-service-override-3.2.3.tgz", "integrity": "sha512-ZMSjElmJdvMb4C7TJe71QJXvn4EZ8YvDGCNChuUHmbGfaX2MEmtMBvMjZH+z5RF5IA1Mtx6RztSBJHDhhYgjBA==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-localization-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-localization-service-override/-/monaco-vscode-localization-service-override-18.1.3.tgz", "integrity": "sha512-NasQQH/sLMdEXS2r9bMFVvmokY6hpUiotURf0Jxp0Y+jAyF/IH5yFQJI4WqF3UB6K7WHIhRFcUxxx6USiiOQgw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-log-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-log-service-override/-/monaco-vscode-log-service-override-18.1.3.tgz", "integrity": "sha512-f+9BuxPzjQirlJJzduihWvIW6DGRam51Srf6wiz5cZVuRTYcP7+Z5BCDZuazeStGqMX5SpmuT5pV/j4rsMSLqg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-environment-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-log-service-override/node_modules/@codingame/monaco-vscode-environment-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-environment-service-override/-/monaco-vscode-environment-service-override-18.1.3.tgz", "integrity": "sha512-BTiNazufHF3FfP/ulw+vEFs2v2XT1hfcj7+JEFhpjUp2i7P1pvUTjAllZs22HCCyiief/6qhM8FgB1d6HtbCaA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-model-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-model-service-override/-/monaco-vscode-model-service-override-3.2.3.tgz", "integrity": "sha512-rpLIBbl5LL4bx8YooiosL+LbDpLO8S+Vs6EFW9n2yzTn1CA6BOY5I0ANNyBsfQ+Bw4jEzzSWnjVDgxfyRFVqZQ==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-monarch-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-monarch-service-override/-/monaco-vscode-monarch-service-override-18.1.3.tgz", "integrity": "sha512-hEgGHZenOBg4gbWdHsBQDCApfbgjjIE+jRbHmmVkQYrdu7oTtMn8hh/xZX6uyldoPBJZ0TGNp3GU6FOd42Ap7w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-quickaccess-service-override": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-quickaccess-service-override/-/monaco-vscode-quickaccess-service-override-3.2.3.tgz", "integrity": "sha512-vr6l1tvA81yOfYKG5zdCaMoGeE7ruWXfISeQv8XcHpr1/VvxCdeBOi22GhRaomEKg9ioAqxzAe0WK8cS96ftIA==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/@codingame/monaco-vscode-textmate-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-textmate-service-override/-/monaco-vscode-textmate-service-override-18.1.3.tgz", "integrity": "sha512-py+NDboyrcB0d20ovCPcNNWyxuhfP4qpTUllSg7B1Eb5zSy2XHINMIK5xlBIIhfoqvfPdVymIR/M/v0fCbUePA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3", "vscode-oniguruma": "1.7.0", "vscode-textmate": "9.2.0"}}, "node_modules/@codingame/monaco-vscode-textmate-service-override/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-theme-defaults-default-extension": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-theme-defaults-default-extension/-/monaco-vscode-theme-defaults-default-extension-18.1.3.tgz", "integrity": "sha512-Dbv4VpVb2aIJUA6BkT/AaRp9Pu5Ob/8akQOKQck+NecDQX1+Hp4Zz97YEPPUbt5faWt7UXMKTNNY70ql3shTEw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-theme-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-theme-service-override/-/monaco-vscode-theme-service-override-18.1.3.tgz", "integrity": "sha512-93sIwc+Pfbh+v1daYKKDv40zKzXz2agYvouvgof2POe3YhaPbIV7kIJq52x0IiP7SIsEYcAngPSZZiQ5SEbQ5A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-theme-service-override/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-view-banner-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-banner-service-override/-/monaco-vscode-view-banner-service-override-18.1.3.tgz", "integrity": "sha512-GB1OCSNF33b88KcJq7R6KjjHH2aHXAlIteKDY+3I287TWqFe1FbEFUehaSYAB2zRRCIwNokYp2dev1DovX0a7g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-view-common-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-common-service-override/-/monaco-vscode-view-common-service-override-18.1.3.tgz", "integrity": "sha512-pCP898KRKkbsx11Hy10gccn1fjAd7y2aArabom6+KD88EPqazRZ9MV2RBzAny1sc4tBYOmXJ8JTKNwjn0RqrUg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-bulk-edit-service-override": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-view-status-bar-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-status-bar-service-override/-/monaco-vscode-view-status-bar-service-override-18.1.3.tgz", "integrity": "sha512-qX5SMupu6onTSG8Q6E8xn+PUUTB4+/vmDZy4+GfIyfOYHo2SxkgAX0eFu6Ni1z/6scAgatKAVdHNlvXx2PvV6w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-view-title-bar-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-title-bar-service-override/-/monaco-vscode-view-title-bar-service-override-18.1.3.tgz", "integrity": "sha512-uX1LRnsjJW0uWz8Vl9vpn/R1MRjTuhBv0DlgMoko91E5mJpiI9c3+lHR84g0ZGRw5JDfJF2eiGKiB81Z6f3T4w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-views-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-views-service-override/-/monaco-vscode-views-service-override-18.1.3.tgz", "integrity": "sha512-J/Y6aVXrtRW2vb/P3J4GgFhVV9p6UgETo+6KhfIAkfSLk4UAgxpxhUyWrFS53xzKduTsSDqrtGJrvRI5yFumQQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-keybindings-service-override": "18.1.3", "@codingame/monaco-vscode-layout-service-override": "18.1.3", "@codingame/monaco-vscode-quickaccess-service-override": "18.1.3", "@codingame/monaco-vscode-view-common-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-views-service-override/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-views-service-override/node_modules/@codingame/monaco-vscode-keybindings-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-keybindings-service-override/-/monaco-vscode-keybindings-service-override-18.1.3.tgz", "integrity": "sha512-odfG30ZQdrZ9iLdihlVD20h4+5OT5t/D9a1a9FWnma1PsLs7Zg8Xe4642PzdpUmx3kS/4mz/IzFRFNaNoEqOZA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-views-service-override/node_modules/@codingame/monaco-vscode-layout-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-layout-service-override/-/monaco-vscode-layout-service-override-18.1.3.tgz", "integrity": "sha512-M1SNWE/Dd9xm5wdwTPXkEugbPBUCL6GFMcXWfns+PrqvhEizVYjQV82A5PaMP3vO9xkaYacADUVcBPSWkNHfxQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-views-service-override/node_modules/@codingame/monaco-vscode-quickaccess-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-quickaccess-service-override/-/monaco-vscode-quickaccess-service-override-18.1.3.tgz", "integrity": "sha512-eDhYCBQA4SpmAjBicfiAAeM0LN/+9RmvA8ziz92rvtOihxr431pk1o8I/rIxyxjdo/Z43rI6weiNzkEZPOTNeQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-workbench-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-workbench-service-override/-/monaco-vscode-workbench-service-override-18.1.3.tgz", "integrity": "sha512-X3qFsr4GUguLUaj1XHrj7mxNUJWRKZLxyWkO3CqIqvJZnyOh0GgzZcuuYWjfem/Qurq3/MCmTtPXIaO498gFxQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-keybindings-service-override": "18.1.3", "@codingame/monaco-vscode-quickaccess-service-override": "18.1.3", "@codingame/monaco-vscode-view-banner-service-override": "18.1.3", "@codingame/monaco-vscode-view-common-service-override": "18.1.3", "@codingame/monaco-vscode-view-status-bar-service-override": "18.1.3", "@codingame/monaco-vscode-view-title-bar-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-workbench-service-override/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-workbench-service-override/node_modules/@codingame/monaco-vscode-keybindings-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-keybindings-service-override/-/monaco-vscode-keybindings-service-override-18.1.3.tgz", "integrity": "sha512-odfG30ZQdrZ9iLdihlVD20h4+5OT5t/D9a1a9FWnma1PsLs7Zg8Xe4642PzdpUmx3kS/4mz/IzFRFNaNoEqOZA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/@codingame/monaco-vscode-workbench-service-override/node_modules/@codingame/monaco-vscode-quickaccess-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-quickaccess-service-override/-/monaco-vscode-quickaccess-service-override-18.1.3.tgz", "integrity": "sha512-eDhYCBQA4SpmAjBicfiAAeM0LN/+9RmvA8ziz92rvtOihxr431pk1o8I/rIxyxjdo/Z43rI6weiNzkEZPOTNeQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.20.2.tgz", "integrity": "sha512-D+EBOJHXdNZcLJRBkhENNG8Wji2kgc9AZ9KiPr1JuZjsNtyHzrsfLRrY0tk2H2aoFu6RANO1y1iPPUCDYWkb5g==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.20.2.tgz", "integrity": "sha512-t98Ra6pw2VaDhqNWO2Oph2LXbz/EJcnLmKLGBJwEwXX/JAN83Fym1rU8l0JUWK6HkIbWONCSSatf4sf2NBRx/w==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-arm64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.20.2.tgz", "integrity": "sha512-mRzjLacRtl/tWU0SvD8lUEwb61yP9cqQo6noDZP/O8VkwafSYwZ4yWy24kan8jE/IMERpYncRt2dw438LP3Xmg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/android-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.20.2.tgz", "integrity": "sha512-btzExgV+/lMGDDa194CcUQm53ncxzeBrWJcncOBxuC6ndBkKxnHdFJn86mCIgTELsooUmwUm9FkhSp5HYu00Rg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.20.2.tgz", "integrity": "sha512-4J6IRT+10J3aJH3l1yzEg9y3wkTDgDk7TSDFX+wKFiWjqWp/iCfLIYzGyasx9l0SAFPT1HwSCR+0w/h1ES/MjA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.20.2.tgz", "integrity": "sha512-tBcXp9KNphnNH0dfhv8KYkZhjc+H3XBkF5DKtswJblV7KlT9EI2+jeA8DgBjp908WEuYll6pF+UStUCfEpdysA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.20.2.tgz", "integrity": "sha512-d3qI41G4SuLiCGCFGUrKsSeTXyWG6yem1KcGZVS+3FYlYhtNoNgYrWcvkOoaqMhwXSMrZRl69ArHsGJ9mYdbbw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.20.2.tgz", "integrity": "sha512-d+DipyvHRuqEeM5zDivKV1KuXn9WeRX6vqSqIDgwIfPQtwMP4jaDsQsDncjTDDsExT4lR/91OLjRo8bmC1e+Cw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.20.2.tgz", "integrity": "sha512-VhLPeR8HTMPccbuWWcEUD1Az68TqaTYyj6nfE4QByZIQEQVWBB8vup8PpR7y1QHL3CpcF6xd5WVBU/+SBEvGTg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.20.2.tgz", "integrity": "sha512-9pb6rBjGvTFNira2FLIWqDk/uaf42sSyLE8j1rnUpuzsODBq7FvpwHYZxQ/It/8b+QOS1RYfqgGFNLRI+qlq2A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.20.2.tgz", "integrity": "sha512-o10utieEkNPFDZFQm9CoP7Tvb33UutoJqg3qKf1PWVeeJhJw0Q347PxMvBgVVFgouYLGIhFYG0UGdBumROyiig==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.20.2.tgz", "integrity": "sha512-PR7sp6R/UC4CFVomVINKJ80pMFlfDfMQMYynX7t1tNTeivQ6XdX5r2XovMmha/VjR1YN/HgHWsVcTRIMkymrgQ==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.20.2.tgz", "integrity": "sha512-4BlTqeutE/KnOiTG5Y6Sb/Hw6hsBOZapOVF6njAESHInhlQAghVVZL1ZpIctBOoTFbQyGW+LsVYZ8lSSB3wkjA==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.20.2.tgz", "integrity": "sha512-rD3KsaDprDcfajSKdn25ooz5J5/fWBylaaXkuotBDGnMnDP1Uv5DLAN/45qfnf3JDYyJv/ytGHQaziHUdyzaAg==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.20.2.tgz", "integrity": "sha512-snwmBKacKmwTMmhLlz/3aH1Q9T8v45bKYGE3j26TsaOVtjIag4wLfWSiZykXzXuE1kbCE+zJRmwp+ZbIHinnVg==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.20.2.tgz", "integrity": "sha512-wcWISOobRWNm3cezm5HOZcYz1sKoHLd8VL1dl309DiixxVFoFe/o8HnwuIwn6sXre88Nwj+VwZUvJf4AFxkyrQ==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/linux-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.20.2.tgz", "integrity": "sha512-1MdwI6OOTsfQfek8sLwgyjOXAu+wKhLEoaOLTjbijk6E2WONYpH9ZU2mNtR+lZ2B4uwr+usqGuVfFT9tMtGvGw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.20.2.tgz", "integrity": "sha512-K8/DhBxcVQkzYc43yJXDSyjlFeHQJBiowJ0uVL6Tor3jGQfSGHNNJcWxNbOI8v5k82prYqzPuwkzHt3J1T1iZQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.20.2.tgz", "integrity": "sha512-eMpKlV0SThJmmJgiVyN9jTPJ2VBPquf6Kt/nAoo6DgHAoN57K15ZghiHaMvqjCye/uU4X5u3YSMgVBI1h3vKrQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.20.2.tgz", "integrity": "sha512-2UyFtRC6cXLyejf/YEld4Hajo7UHILetzE1vsRcGL3earZEW77JxrFjH4Ez2qaTiEfMgAXxfAZCm1fvM/G/o8w==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.20.2.tgz", "integrity": "sha512-GRibxoawM9ZCnDxnP3usoUDO9vUkpAxIIZ6GQI+IlVmr5kP3zUq+l17xELTHMWTWzjxa2guPNyrpq1GWmPvcGQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.20.2.tgz", "integrity": "sha512-HfLOfn9YWmkSKRQqovpnITazdtquEW8/SoHW7pWpuEeguaZI4QnCRW6b+oZTztdBnZOS2hqJ6im/D5cPzBTTlQ==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@esbuild/win32-x64": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.20.2.tgz", "integrity": "sha512-N49X4lJX27+l9jbLKSqZ6bKNjzQvHaT8IIFUy+YIqmXQdjYCToGWwOItDrfby14c78aDd5NHQl29xingXfCdLQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "resolved": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-2.1.4.tgz", "integrity": "sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "8.57.1", "resolved": "https://registry.npmjs.org/@eslint/js/-/js-8.57.1.tgz", "integrity": "sha512-d9zaMRSTIKDLhctzH12MtXvJKSSUhaHcjV+2Z+GK+EEY7XKpP5yR4x+N3TAcHTcu963nIr+TMcCb4DBCYX1z6Q==", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "resolved": "https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.13.0.tgz", "integrity": "sha512-DZLEEqFWQFiyK6h5YIeynKx7JlvCYWL0cImfSRXZ9l4Sg2efkFGTuFf6vzXjK1cq6IYkU+Eg/JizXw+TD2vRNw==", "deprecated": "Use @eslint/config-array instead", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz", "integrity": "sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==", "deprecated": "Use @eslint/object-schema instead", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@jest/schemas": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz", "integrity": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz", "integrity": "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw==", "dev": true, "license": "MIT"}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.44.2.tgz", "integrity": "sha512-g0dF8P1e2QYPOj1gu7s/3LVP6kze9A7m6x0BZ9iTdXK8N5c2V7cpBKHV3/9A4Zd8xxavdhK0t4PnqjkqVmUc9Q==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.44.2.tgz", "integrity": "sha512-Yt5MKrOosSbSaAK5Y4J+vSiID57sOvpBNBR6K7xAaQvk3MkcNVV0f9fE20T+41WYN8hDn6SGFlFrKudtx4EoxA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.44.2.tgz", "integrity": "sha512-EsnFot9ZieM35YNA26nhbLTJBHD0jTwWpPwmRVDzjylQT6gkar+zenfb8mHxWpRrbn+WytRRjE0WKsfaxBkVUA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.44.2.tgz", "integrity": "sha512-dv/t1t1RkCvJdWWxQ2lWOO+b7cMsVw5YFaS04oHpZRWehI1h0fV1gF4wgGCTyQHHjJDfbNpwOi6PXEafRBBezw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.44.2.tgz", "integrity": "sha512-W4tt4BLorKND4qeHElxDoim0+BsprFTwb+vriVQnFFtT/P6v/xO5I99xvYnVzKWrK6j7Hb0yp3x7V5LUbaeOMg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.44.2.tgz", "integrity": "sha512-tdT1PHopokkuBVyHjvYehnIe20fxibxFCEhQP/96MDSOcyjM/shlTkZZLOufV3qO6/FQOSiJTBebhVc12JyPTA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.44.2.tgz", "integrity": "sha512-+xmiDGGaSfIIOXMzkhJ++Oa0Gwvl9oXUeIiwarsdRXSe27HUIvjbSIpPxvnNsRebsNdUo7uAiQVgBD1hVriwSQ==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.44.2.tgz", "integrity": "sha512-bDHvhzOfORk3wt8yxIra8N4k/N0MnKInCW5OGZaeDYa/hMrdPaJzo7CSkjKZqX4JFUWjUGm88lI6QJLCM7lDrA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.44.2.tgz", "integrity": "sha512-NMsDEsDiYghTbeZWEGnNi4F0hSbGnsuOG+VnNvxkKg0IGDvFh7UVpM/14mnMwxRxUf9AdAVJgHPvKXf6FpMB7A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.44.2.tgz", "integrity": "sha512-lb5bxXnxXglVq+7imxykIp5xMq+idehfl+wOgiiix0191av84OqbjUED+PRC5OA8eFJYj5xAGcpAZ0pF2MnW+A==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.44.2.tgz", "integrity": "sha512-Yl5Rdpf9pIc4GW1PmkUGHdMtbx0fBLE1//SxDmuf3X0dUC57+zMepow2LK0V21661cjXdTn8hO2tXDdAWAqE5g==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.44.2.tgz", "integrity": "sha512-03vUDH+w55s680YYryyr78jsO1RWU9ocRMaeV2vMniJJW/6HhoTBwyyiiTPVHNWLnhsnwcQ0oH3S9JSBEKuyqw==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.44.2.tgz", "integrity": "sha512-iYtAqBg5eEMG4dEfVlkqo05xMOk6y/JXIToRca2bAWuqjrJYJlx/I7+Z+4hSrsWU8GdJDFPL4ktV3dy4yBSrzg==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.44.2.tgz", "integrity": "sha512-e6vEbgaaqz2yEHqtkPXa28fFuBGmUJ0N2dOJK8YUfijejInt9gfCSA7YDdJ4nYlv67JfP3+PSWFX4IVw/xRIPg==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.44.2.tgz", "integrity": "sha512-evFOtkmVdY3udE+0QKrV5wBx7bKI0iHz5yEVx5WqDJkxp9YQefy4Mpx3RajIVcM6o7jxTvVd/qpC1IXUhGc1Mw==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.44.2.tgz", "integrity": "sha512-/bXb0bEsWMyEkIsUL2Yt5nFB5naLAwyOWMEviQfQY1x3l5WsLKgvZf66TM7UTfED6erckUVUJQ/jJ1FSpm3pRQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.44.2.tgz", "integrity": "sha512-3D3OB1vSSBXmkGEZR27uiMRNiwN08/RVAcBKwhUYPaiZ8bcvdeEwWPvbnXvvXHY+A/7xluzcN+kaiOFNiOZwWg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.44.2.tgz", "integrity": "sha512-VfU0fsMK+rwdK8mwODqYeM2hDrF2WiHaSmCBrS7gColkQft95/8tphyzv2EupVxn3iE0FI78wzffoULH1G+dkw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.44.2.tgz", "integrity": "sha512-+qMUrkbUurpE6DVRjiJCNGZBGo9xM4Y0FXU5cjgudWqIBWbcLkjE3XprJUsOFgC6xjBClwVa9k6O3A7K3vxb5Q==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.44.2.tgz", "integrity": "sha512-3+QZROYfJ25PDcxFF66UEk8jGWigHJeecZILvkPkyQN7oc5BvFo4YEXFkOs154j3FTMp9mn9Ky8RCOwastduEA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "resolved": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz", "integrity": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==", "dev": true, "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz", "integrity": "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w==", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "18.19.115", "resolved": "https://registry.npmjs.org/@types/node/-/node-18.19.115.tgz", "integrity": "sha512-kNrFiTgG4a9JAn1LMQeLOv3MvXIPokzXziohMrMsvpYgLpdEt/mMiVYc4sGKtDfyxM5gIDF4VgrPRyCw4fHOYg==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~5.26.4"}}, "node_modules/@types/semver": {"version": "7.7.0", "resolved": "https://registry.npmjs.org/@types/semver/-/semver-7.7.0.tgz", "integrity": "sha512-k107IF4+Xr7UHjwDc7Cfd6PRQfbdkiRabXGRjo07b4WyPahFBZCZ1sE+BNxYIJPPg73UkfOsVOLwqVc/6ETrIA==", "dev": true, "license": "MIT"}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "license": "MIT", "optional": true}, "node_modules/@types/vscode": {"version": "1.67.0", "resolved": "https://registry.npmjs.org/@types/vscode/-/vscode-1.67.0.tgz", "integrity": "sha512-GH8BDf8cw9AC9080uneJfulhSa7KHSMI2s/CyKePXoGNos9J486w2V4YKoeNUqIEkW4hKoEAWp6/cXTwyGj47g==", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-7.3.1.tgz", "integrity": "sha512-STEDMVQGww5lhCuNXVSQfbfuNII5E08QWkvAw5Qwf+bj2WT+JkG1uc+5/vXA3AOYMDHVOSpL+9rcbEUiHIm2dw==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.5.1", "@typescript-eslint/scope-manager": "7.3.1", "@typescript-eslint/type-utils": "7.3.1", "@typescript-eslint/utils": "7.3.1", "@typescript-eslint/visitor-keys": "7.3.1", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.4", "natural-compare": "^1.4.0", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^7.0.0", "eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-7.3.1.tgz", "integrity": "sha512-Rq49+pq7viTRCH48XAbTA+wdLRrB/3sRq4Lpk0oGDm0VmnjBrAOVXH/Laalmwsv2VpekiEfVFwJYVk6/e8uvQw==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "7.3.1", "@typescript-eslint/types": "7.3.1", "@typescript-eslint/typescript-estree": "7.3.1", "@typescript-eslint/visitor-keys": "7.3.1", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-7.3.1.tgz", "integrity": "sha512-fVS6fPxldsKY2nFvyT7IP78UO1/I2huG+AYu5AMjCT9wtl6JFiDnsv4uad4jQ0GTFzcUV5HShVeN96/17bTBag==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "7.3.1", "@typescript-eslint/visitor-keys": "7.3.1"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-7.3.1.tgz", "integrity": "sha512-iFhaysxFsMDQlzJn+vr3OrxN8NmdQkHks4WaqD4QBnt5hsq234wcYdyQ9uquzJJIDAj5W4wQne3yEsYA6OmXGw==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "7.3.1", "@typescript-eslint/utils": "7.3.1", "debug": "^4.3.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/types/-/types-7.3.1.tgz", "integrity": "sha512-2tUf3uWggBDl4S4183nivWQ2HqceOZh1U4hhu4p1tPiIJoRRXrab7Y+Y0p+dozYwZVvLPRI6r5wKe9kToF9FIw==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-7.3.1.tgz", "integrity": "sha512-tLpuqM46LVkduWP7JO7yVoWshpJuJzxDOPYIVWUUZbW+4dBpgGeUdl/fQkhuV0A8eGnphYw3pp8d2EnvPOfxmQ==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "7.3.1", "@typescript-eslint/visitor-keys": "7.3.1", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "9.0.3", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-7.3.1.tgz", "integrity": "sha512-jIERm/6bYQ9HkynYlNZvXpzmXWZGhMbrOvq3jJzOSOlKXsVjrrolzWBjDW6/TvT5Q3WqaN4EkmcfdQwi9tDjBQ==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@types/json-schema": "^7.0.12", "@types/semver": "^7.5.0", "@typescript-eslint/scope-manager": "7.3.1", "@typescript-eslint/types": "7.3.1", "@typescript-eslint/typescript-estree": "7.3.1", "semver": "^7.5.4"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "7.3.1", "resolved": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-7.3.1.tgz", "integrity": "sha512-9RMXwQF8knsZvfv9tdi+4D/j7dMG28X/wMJ8Jj6eOHyHWwDW4ngQJcqEczSsqIKKjFiLFr40Mnr7a5ulDD3vmw==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "7.3.1", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@ungap/structured-clone": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/@ungap/structured-clone/-/structured-clone-1.3.0.tgz", "integrity": "sha512-WmoN8qaIAo7WTYWbAZuG8PYEhn5fkz7dZrqTBZ7dtt//lL2Gwms1IcnQ5yHqjDfX8Ft5j4YzDM23f87zBfDe9g==", "dev": true, "license": "ISC"}, "node_modules/@vitest/expect": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@vitest/expect/-/expect-1.4.0.tgz", "integrity": "sha512-Jths0sWCJZ8BxjKe+p+eKsoqev1/T8lYcrjavEaz8auEJ4jAVY0GwW3JKmdVU4mmNPLPHixh4GNXP7GFtAiDHA==", "dev": true, "license": "MIT", "dependencies": {"@vitest/spy": "1.4.0", "@vitest/utils": "1.4.0", "chai": "^4.3.10"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@vitest/runner/-/runner-1.4.0.tgz", "integrity": "sha512-<PERSON>D<PERSON>VSmesqlQ4RD2VvWo3hQgTJ7ZrFQ2VSJdfiJiArkCerDAGeyF1i6dHkmySqk573jLp6d/cfqCN+7wUB5tLgg==", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "1.4.0", "p-limit": "^5.0.0", "pathe": "^1.1.1"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner/node_modules/p-limit": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-5.0.0.tgz", "integrity": "sha512-/Eaoq+QyLSiXQ4lyYV23f14mZRQcXnxfHrN0vCai+ak9G0pp9iEQukIIZq5NccEvwRB8PUnZT0KsOoDCINS1qQ==", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@vitest/runner/node_modules/yocto-queue": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-1.2.1.tgz", "integrity": "sha512-AyeEbWOu/TAXdxlV9wmGcR0+yh2j3vYPGOECcIj2S7MkrLyC7ne+oye2BKTItt0ii2PHk4cDy+95+LshzbXnGg==", "dev": true, "license": "MIT", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@vitest/snapshot": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-1.4.0.tgz", "integrity": "sha512-saAFnt5pPIA5qDGxOHxJ/XxhMFKkUSBJmVt5VgDsAqPTX6JP326r5C/c9UuCMPoXNzuudTPsYDZCoJ5ilpqG2A==", "dev": true, "license": "MIT", "dependencies": {"magic-string": "^0.30.5", "pathe": "^1.1.1", "pretty-format": "^29.7.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/spy": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@vitest/spy/-/spy-1.4.0.tgz", "integrity": "sha512-Ywau/Qs1DzM/8Uc+yA77CwSegizMlcgTJuYGAi0jujOteJOUf1ujunHThYo243KG9nAyWT3L9ifPYZ5+As/+6Q==", "dev": true, "license": "MIT", "dependencies": {"tinyspy": "^2.2.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/utils": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/@vitest/utils/-/utils-1.4.0.tgz", "integrity": "sha512-mx3Yd1/6e2Vt/PUC98DcqTirtfxUyAZ32uK82r8rZzbtBeBo+nqgnjx/LvqQdWsrvNtm14VmurNgcf4nqY5gJg==", "dev": true, "license": "MIT", "dependencies": {"diff-sequences": "^29.6.3", "estree-walker": "^3.0.3", "loupe": "^2.3.7", "pretty-format": "^29.7.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vscode/iconv-lite-umd": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/@vscode/iconv-lite-umd/-/iconv-lite-umd-0.7.0.tgz", "integrity": "sha512-bRRFxLfg5dtAyl5XyiVWz/ZBPahpOpPrNYnnHpOpUZvam4tKH35wdhP4Kj6PbM0+KdliOsPzbGWpkxcdpNB/sg==", "license": "MIT"}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "integrity": "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true, "license": "Python-2.0"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz", "integrity": "sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/assertion-error": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-1.1.0.tgz", "integrity": "sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/async": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/async/-/async-3.2.6.tgz", "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "dev": true, "license": "MIT"}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT"}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/cac": {"version": "6.7.14", "resolved": "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz", "integrity": "sha512-b6<PERSON>lus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chai": {"version": "4.5.0", "resolved": "https://registry.npmjs.org/chai/-/chai-4.5.0.tgz", "integrity": "sha512-RITGBfijLkBddZvnn8jdqoTypxvqbOLYQkGGxXzeFjVHvudaPw0HNFD9x928/eUwYWd2dPCugVqspGALTZZQKw==", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^1.1.0", "check-error": "^1.0.3", "deep-eql": "^4.1.3", "get-func-name": "^2.0.2", "loupe": "^2.3.6", "pathval": "^1.1.1", "type-detect": "^4.1.0"}, "engines": {"node": ">=4"}}, "node_modules/chalk": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/check-error": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/check-error/-/check-error-1.0.3.tgz", "integrity": "sha512-iKEoDYaRmd1mxM90a2OEfWhjsjPpYPuQ+lMYsoxB126+t8fw7ySEO48nmDg5COTjxDI65/Y2OWpeEHk3ZOe8zg==", "dev": true, "license": "MIT", "dependencies": {"get-func-name": "^2.0.2"}, "engines": {"node": "*"}}, "node_modules/chevrotain": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/chevrotain/-/chevrotain-11.0.3.tgz", "integrity": "sha512-ci2iJH6LeIkvP9eJW6gpueU8cnZhv85ELY8w8WiFtNjMHA5ad6pQLaJo9mEly/9qUyCpvqX8/POVUTf18/HFdw==", "license": "Apache-2.0", "dependencies": {"@chevrotain/cst-dts-gen": "11.0.3", "@chevrotain/gast": "11.0.3", "@chevrotain/regexp-to-ast": "11.0.3", "@chevrotain/types": "11.0.3", "@chevrotain/utils": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/chevrotain-allstar": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/chevrotain-allstar/-/chevrotain-allstar-0.3.1.tgz", "integrity": "sha512-b7g+y9A0v4mxCW1qUhf3BSVPg+/NvGErk/dOkrDaHA0nQIQGAtrOjlX//9OQtRlSCy+x9rfB5N8yC71lH1nvMw==", "license": "MIT", "dependencies": {"lodash-es": "^4.17.21"}, "peerDependencies": {"chevrotain": "^11.0.0"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-11.0.0.tgz", "integrity": "sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true, "license": "MIT"}, "node_modules/concurrently": {"version": "8.2.2", "resolved": "https://registry.npmjs.org/concurrently/-/concurrently-8.2.2.tgz", "integrity": "sha512-1dP4gpXFhei8IOtlXRE/T/4H88ElHgTiUzh71YUmtjTEHMSRS2Z/fgOxHSxxusGHogsRfxNq1vyAwxSC+EVyDg==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "rxjs": "^7.8.1", "shell-quote": "^1.8.1", "spawn-command": "0.0.2", "supports-color": "^8.1.1", "tree-kill": "^1.2.2", "yargs": "^17.7.2"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "engines": {"node": "^14.13.0 || >=16.0.0"}, "funding": {"url": "https://github.com/open-cli-tools/concurrently?sponsor=1"}}, "node_modules/concurrently/node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/concurrently/node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/concurrently/node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/confbox": {"version": "0.1.8", "resolved": "https://registry.npmjs.org/confbox/-/confbox-0.1.8.tgz", "integrity": "sha512-RMtmw0iFkeR4YV+fUOSucriAQNb9g8zFR52MWCtl+cCZOFRNL6zeB395vPzFhEjjn4fMxXudmELnl/KF/WrK6w==", "dev": true, "license": "MIT"}, "node_modules/corser": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/corser/-/corser-2.0.1.tgz", "integrity": "sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/date-fns": {"version": "2.30.0", "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-2.30.0.tgz", "integrity": "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=0.11"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-eql": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/deep-eql/-/deep-eql-4.1.4.tgz", "integrity": "sha512-SUwdGfqdKOwxCPeVYjwSyRpJ7Z+fhpwIAtmCUdZIWZ/YP5R9WAsyuSgpLVDi9bjWoN2LXHNss/dk3urXtdQxGg==", "dev": true, "license": "MIT", "dependencies": {"type-detect": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==", "dev": true, "license": "MIT"}, "node_modules/diff-sequences": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz", "integrity": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==", "dev": true, "license": "MIT", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz", "integrity": "sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dompurify": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-3.2.6.tgz", "integrity": "sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ==", "license": "(MPL-2.0 OR Apache-2.0)", "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true, "license": "MIT"}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/esbuild": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.20.2.tgz", "integrity": "sha512-WdOOppmUNU+IbZ0PaDiTst80zjnrOkyJNHoKupIcVyU8Lvla3Ugx94VzkQ32Ijqd7UhHJy75gNWDMUekcrSJ6g==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.20.2", "@esbuild/android-arm": "0.20.2", "@esbuild/android-arm64": "0.20.2", "@esbuild/android-x64": "0.20.2", "@esbuild/darwin-arm64": "0.20.2", "@esbuild/darwin-x64": "0.20.2", "@esbuild/freebsd-arm64": "0.20.2", "@esbuild/freebsd-x64": "0.20.2", "@esbuild/linux-arm": "0.20.2", "@esbuild/linux-arm64": "0.20.2", "@esbuild/linux-ia32": "0.20.2", "@esbuild/linux-loong64": "0.20.2", "@esbuild/linux-mips64el": "0.20.2", "@esbuild/linux-ppc64": "0.20.2", "@esbuild/linux-riscv64": "0.20.2", "@esbuild/linux-s390x": "0.20.2", "@esbuild/linux-x64": "0.20.2", "@esbuild/netbsd-x64": "0.20.2", "@esbuild/openbsd-x64": "0.20.2", "@esbuild/sunos-x64": "0.20.2", "@esbuild/win32-arm64": "0.20.2", "@esbuild/win32-ia32": "0.20.2", "@esbuild/win32-x64": "0.20.2"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.57.1", "resolved": "https://registry.npmjs.org/eslint/-/eslint-8.57.1.tgz", "integrity": "sha512-ypowyDxpVSYpkXr9WPv2PAZCtNip1Mv5KTW0SCurXv/9iOpcrH9PaqUElksqEB6pChqHGDRCFTyrZlGhnLNGiA==", "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-scope": {"version": "7.2.2", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz", "integrity": "sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/eslint/node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/espree": {"version": "9.6.1", "resolved": "https://registry.npmjs.org/espree/-/espree-9.6.1.tgz", "integrity": "sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz", "integrity": "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz", "integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "dev": true, "license": "MIT"}, "node_modules/execa": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/execa/-/execa-8.0.1.tgz", "integrity": "sha512-VyhnebXciFV2DESc+p6B+y0LjSm0krU4OgJN44qFAhBY0TJ+1V61tYD2+wHusZ6F9n5K+vl8k0sTy7PEfV4qpg==", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^8.0.1", "human-signals": "^5.0.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^4.1.0", "strip-final-newline": "^3.0.0"}, "engines": {"node": ">=16.17"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.19.1.tgz", "integrity": "sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz", "integrity": "sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz", "integrity": "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/fs-extra": {"version": "11.1.1", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-11.1.1.tgz", "integrity": "sha512-MGIE4HOvQCeUCzmlHs0vXpih4ysz4wg9qiSAu6cd42lVwPbTM1TjV7RusoyQqMmk/95gdQZX72u+YW+c3eEpFQ==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-func-name": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/get-func-name/-/get-func-name-2.0.2.tgz", "integrity": "sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-8.0.1.tgz", "integrity": "sha512-VaUJspBffn/LMCJVoMvSAdmscJyS1auj5Zulnn5UoYcY531UWmdwhRWkcGKnGU93m5HSXP9LP2usOryrBtQowA==", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/globals": {"version": "13.24.0", "resolved": "https://registry.npmjs.org/globals/-/globals-13.24.0.tgz", "integrity": "sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "https://registry.npmjs.org/globby/-/globby-11.1.0.tgz", "integrity": "sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/html-encoding-sniffer": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "integrity": "sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-server": {"version": "14.1.1", "resolved": "https://registry.npmjs.org/http-server/-/http-server-14.1.1.tgz", "integrity": "sha512-+cbxadF40UXd9T01zUHgA+rlo2Bg1Srer4+B4NwIHdaGxAGGv59nYRnGGDJ9LBk7alpS0US+J+bLLdQOOkJq4A==", "dev": true, "license": "MIT", "dependencies": {"basic-auth": "^2.0.1", "chalk": "^4.1.2", "corser": "^2.0.1", "he": "^1.2.0", "html-encoding-sniffer": "^3.0.0", "http-proxy": "^1.18.1", "mime": "^1.6.0", "minimist": "^1.2.6", "opener": "^1.5.1", "portfinder": "^1.0.28", "secure-compare": "3.0.1", "union": "~0.5.0", "url-join": "^4.0.1"}, "bin": {"http-server": "bin/http-server"}, "engines": {"node": ">=12"}}, "node_modules/http-server/node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/http-server/node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/http-server/node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/human-signals": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/human-signals/-/human-signals-5.0.0.tgz", "integrity": "sha512-AXcZb6vzzrFAUE61HnN4mpLqd/cSIwNQjtNWR0euPm6y0iqx3G4gOXaIDdtdDwZmhwe82LA6+zinmW4UBWVePQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=16.17.0"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz", "integrity": "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-meta-resolve": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/import-meta-resolve/-/import-meta-resolve-4.1.0.tgz", "integrity": "sha512-I6fiaX09Xivtk+THaMfAwnA3MVA5Big1WHF1Dfx9hFuvNIWpXnorlkzhcQf6ehrqQiiZECRt1poOAkPmer3ruw==", "dev": true, "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true, "license": "ISC"}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/is-path-inside/-/is-path-inside-3.0.3.tgz", "integrity": "sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-stream": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-3.0.0.tgz", "integrity": "sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "license": "ISC"}, "node_modules/js-tokens": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jschardet": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/jschardet/-/jschardet-3.1.4.tgz", "integrity": "sha512-/kmVISmrwVwtyYU40iQUOp3SUPk2dhNCMsZBQX0R1/jZ8maaXJ/oZIzUOiyOqcgtLnETFKYChbJ5iDC/eWmFHg==", "license": "LGPL-2.1+", "engines": {"node": ">=0.1.90"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==", "dev": true, "license": "MIT"}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonschema": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsonschema/-/jsonschema-1.4.1.tgz", "integrity": "sha512-S6cATIPVv1z0IlxdN+zUk5EPjkGCdnhN4wVSBlvoUO1tOLJootbo9CquNJmbIh4yikWHiUedhRYrNPn1arpEmQ==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/langium": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/langium/-/langium-3.5.0.tgz", "integrity": "sha512-tnqVzWOkUcoiY0bWlyE8diFrZjmGBCF7MesC1bjUaZM+YGQSfdPC+KkhmHM0DWFG+uLcPxidKaPP1SYGtg3J0Q==", "license": "MIT", "dependencies": {"chevrotain": "~11.0.3", "chevrotain-allstar": "~0.3.0", "vscode-languageserver": "~9.0.1", "vscode-languageserver-textdocument": "~1.0.11", "vscode-uri": "~3.0.8"}, "engines": {"node": ">=18.0.0"}}, "node_modules/langium-cli": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/langium-cli/-/langium-cli-3.5.2.tgz", "integrity": "sha512-EVI462CIx8YnJfLSGArvvrlTzAXafA8zNeNbX6FPI5eX8ahicJcvnqW0eAmZ6mLFP7aipzV2F0Sehexe7KHmzA==", "dev": true, "license": "MIT", "dependencies": {"chalk": "~5.3.0", "commander": "~11.0.0", "fs-extra": "~11.1.1", "jsonschema": "~1.4.1", "langium": "~3.5.0", "langium-railroad": "~3.5.0", "lodash": "~4.17.21"}, "bin": {"langium": "bin/langium.js"}, "engines": {"node": ">=18.0.0"}}, "node_modules/langium-railroad": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/langium-railroad/-/langium-railroad-3.5.0.tgz", "integrity": "sha512-80Enc6bOR6oHZD18IQlVTdfCh07rbrM5SOsPUPc1kyh7n3zQbmLs5P2E9hV4SoWlkhOjGKL1I9Z6uORpgy+jTQ==", "dev": true, "license": "MIT", "dependencies": {"langium": "~3.5.0", "railroad-diagrams": "~1.0.0"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/local-pkg": {"version": "0.5.1", "resolved": "https://registry.npmjs.org/local-pkg/-/local-pkg-0.5.1.tgz", "integrity": "sha512-9rrA30MRRP3gBD3HTGnC6cDFpaE1kVDWxWgqWJUN0RvDNAo+Nz/9GxB+nHOH0ifbVFy0hSA1V6vFDvnx54lTEQ==", "dev": true, "license": "MIT", "dependencies": {"mlly": "^1.7.3", "pkg-types": "^1.2.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true, "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "dev": true, "license": "MIT"}, "node_modules/loupe": {"version": "2.3.7", "resolved": "https://registry.npmjs.org/loupe/-/loupe-2.3.7.tgz", "integrity": "sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==", "dev": true, "license": "MIT", "dependencies": {"get-func-name": "^2.0.1"}}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/marked": {"version": "14.0.0", "resolved": "https://registry.npmjs.org/marked/-/marked-14.0.0.tgz", "integrity": "sha512-uIj4+faQ+MgHgwUW1l2PsPglZLOLOT1uErt06dAPtx2kjteLAkbsd/0FiYg/MGS+i7ZKLb7w2WClxHkzOOuryQ==", "license": "MIT", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 18"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mimic-fn": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-4.0.0.tgz", "integrity": "sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/minimatch": {"version": "9.0.3", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.3.tgz", "integrity": "sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mlly": {"version": "1.7.4", "resolved": "https://registry.npmjs.org/mlly/-/mlly-1.7.4.tgz", "integrity": "sha512-qmdSIPC4bDJXgZTCR7XosJiNKySV7O215tsPtDN9iEO/7q/76b/ijtgRu/+epFXSJhijtTCCGp3DWS549P3xKw==", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.14.0", "pathe": "^2.0.1", "pkg-types": "^1.3.0", "ufo": "^1.5.4"}}, "node_modules/mlly/node_modules/pathe": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "dev": true, "license": "MIT"}, "node_modules/monaco-editor": {"name": "@codingame/monaco-vscode-editor-api", "version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-api/-/monaco-vscode-editor-api-3.2.3.tgz", "integrity": "sha512-7EaQf5n3lTtCIciz7UyfoYt4FowhrD8xAc/T7gbyo2LkuSD7A8h4CHbnUOZNhsTqocFqOUSkcQl0z5n0T1LoTQ==", "license": "MIT", "dependencies": {"vscode": "npm:@codingame/monaco-vscode-api@3.2.3"}}, "node_modules/monaco-editor-workers": {"version": "0.45.0", "resolved": "https://registry.npmjs.org/monaco-editor-workers/-/monaco-editor-workers-0.45.0.tgz", "integrity": "sha512-KSN7FXdehjwnu1JbpfERVP8KGqioXabNmpDfmh1P5RcG2k6OTAyh5cmLg55AsI/upzUqbEuq1F4NUh7mASsY9w==", "license": "MIT", "dependencies": {"monaco-editor": "~0.45.0"}, "peerDependencies": {"monaco-editor": "~0.45.0"}}, "node_modules/monaco-editor-workers/node_modules/monaco-editor": {"version": "0.45.0", "resolved": "https://registry.npmjs.org/monaco-editor/-/monaco-editor-0.45.0.tgz", "integrity": "sha512-mjv1G1ZzfEE3k9HZN0dQ2olMdwIfaeAAjFiwNprLfYNRSz7ctv9XuCT7gPtBGrMUeV1/iZzYKj17Khu1hxoHOA==", "license": "MIT"}, "node_modules/monaco-editor-wrapper": {"version": "6.9.0", "resolved": "https://registry.npmjs.org/monaco-editor-wrapper/-/monaco-editor-wrapper-6.9.0.tgz", "integrity": "sha512-JvmSNNdeyGXeTNDD+iRAt2Cf8wjaWfMKpA8YKFkBQ5KoA47Rwndcbu2dGcKNmT4AT/TwGQw4HvhsdEvvI7jySw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-language-pack-cs": "~18.1.0", "@codingame/monaco-vscode-language-pack-de": "~18.1.0", "@codingame/monaco-vscode-language-pack-es": "~18.1.0", "@codingame/monaco-vscode-language-pack-fr": "~18.1.0", "@codingame/monaco-vscode-language-pack-it": "~18.1.0", "@codingame/monaco-vscode-language-pack-ja": "~18.1.0", "@codingame/monaco-vscode-language-pack-ko": "~18.1.0", "@codingame/monaco-vscode-language-pack-pl": "~18.1.0", "@codingame/monaco-vscode-language-pack-pt-br": "~18.1.0", "@codingame/monaco-vscode-language-pack-qps-ploc": "~18.1.0", "@codingame/monaco-vscode-language-pack-ru": "~18.1.0", "@codingame/monaco-vscode-language-pack-tr": "~18.1.0", "@codingame/monaco-vscode-language-pack-zh-hans": "~18.1.0", "@codingame/monaco-vscode-language-pack-zh-hant": "~18.1.0", "@codingame/monaco-vscode-monarch-service-override": "~18.1.0", "@codingame/monaco-vscode-textmate-service-override": "~18.1.0", "@codingame/monaco-vscode-theme-defaults-default-extension": "~18.1.0", "@codingame/monaco-vscode-theme-service-override": "~18.1.0", "@codingame/monaco-vscode-views-service-override": "~18.1.0", "@codingame/monaco-vscode-workbench-service-override": "~18.1.0", "monaco-languageclient": "~9.8.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1", "vscode-languageserver-protocol": "~3.17.5", "vscode-ws-jsonrpc": "~3.4.0"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "node_modules/monaco-editor-wrapper/node_modules/@codingame/monaco-vscode-editor-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-service-override/-/monaco-vscode-editor-service-override-18.1.3.tgz", "integrity": "sha512-2YQ3rSohlycw56MtEQzRg6OjWHxYUBqOGmEAEafMXaIeul6RANzO+KNTB/ovXSm0kxjfnzuQxMJ+uT8UtAmzOw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/monaco-editor-wrapper/node_modules/@codingame/monaco-vscode-extensions-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extensions-service-override/-/monaco-vscode-extensions-service-override-18.1.3.tgz", "integrity": "sha512-GXrQ88rtcjrPmcwLWxO9qC7g6oWtiFeeGa65FvjlDUiu4qf/4vN6ObNHs+YfkmTc6dpx2m8rsIe9SDuU1NgCng==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/monaco-editor-wrapper/node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.3.tgz", "integrity": "sha512-EuZx23+f/vXWMOUBCN7HoosiwqA5q9B6+Z66yzAK8vXqvSBOMb6EJw/vugCgD5LSyEyftUOxMMThsak78IFbAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3"}}, "node_modules/monaco-editor-wrapper/node_modules/@codingame/monaco-vscode-languages-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-languages-service-override/-/monaco-vscode-languages-service-override-18.1.3.tgz", "integrity": "sha512-Kpaztomrh03dVSz8t+2mSh0q9maOx2zemdyC+WRxNf1eJOooOVaB9WJNUfbr8XKULm2gtr1JvzwZ0CV4mn2+Rw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-files-service-override": "18.1.3"}}, "node_modules/monaco-editor-wrapper/node_modules/@codingame/monaco-vscode-model-service-override": {"version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-model-service-override/-/monaco-vscode-model-service-override-18.1.3.tgz", "integrity": "sha512-dJrZzPJfuum9piB0OSMMz1lb/Qo2bflFu5CIApVtsZHbnTtMXL2hizu5l5pDDvaBDl57J64F7nhoXz1YqbLLyg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3"}}, "node_modules/monaco-editor-wrapper/node_modules/monaco-languageclient": {"version": "9.8.0", "resolved": "https://registry.npmjs.org/monaco-languageclient/-/monaco-languageclient-9.8.0.tgz", "integrity": "sha512-xQ82vs0xWdeaxDK0lXJdN81CGC0gzMOVhmr7bN71y+tFezsToAW0+tt+IpQbyw1i3lQQxjdh1ci+Hu5CCx43ew==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-configuration-service-override": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-extensions-service-override": "~18.1.0", "@codingame/monaco-vscode-languages-service-override": "~18.1.0", "@codingame/monaco-vscode-localization-service-override": "~18.1.0", "@codingame/monaco-vscode-log-service-override": "~18.1.0", "@codingame/monaco-vscode-model-service-override": "~18.1.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "node_modules/monaco-editor-wrapper/node_modules/vscode": {"name": "@codingame/monaco-vscode-extension-api", "version": "18.1.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extension-api/-/monaco-vscode-extension-api-18.1.3.tgz", "integrity": "sha512-jv1sRVVIE4MXBcUGgPiDOZQMYOYL277CuJUdzt8GwvirgiEVvWJyl2aql572SgdkKRkAr42Jq5KFlgns0NSMeg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-************************************-common": "18.1.3", "@codingame/monaco-vscode-api": "18.1.3", "@codingame/monaco-vscode-extensions-service-override": "18.1.3"}}, "node_modules/monaco-languageclient": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/monaco-languageclient/-/monaco-languageclient-8.1.1.tgz", "integrity": "sha512-33MLy4uk0s8hafD1WWdo0x/7ymKwDY5aBoC8Q4D+PBjae8xEHg+yIGyZneWF1ouO0/prn973hzBJ1kRZp6RTNw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-extensions-service-override": "~3.2.3", "@codingame/monaco-vscode-languages-service-override": "~3.2.3", "@codingame/monaco-vscode-model-service-override": "~3.2.3", "monaco-editor": "npm:@codingame/monaco-vscode-editor-api@~3.2.3", "vscode": "npm:@codingame/monaco-vscode-api@~3.2.3", "vscode-languageclient": "~9.0.1"}, "engines": {"node": ">=16.11.0", "npm": ">=9.0.0"}, "peerDependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@~3.2.3", "vscode": "npm:@codingame/monaco-vscode-api@~3.2.3"}, "peerDependenciesMeta": {"monaco-editor": {"optional": false}, "vscode": {"optional": false}}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "dev": true, "license": "MIT"}, "node_modules/npm-run-path": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-5.3.0.tgz", "integrity": "sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/npm-run-path/node_modules/path-key": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz", "integrity": "sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/onetime/-/onetime-6.0.0.tgz", "integrity": "sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/opener": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz", "integrity": "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==", "dev": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz", "integrity": "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz", "integrity": "sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/pathe": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/pathe/-/pathe-1.1.2.tgz", "integrity": "sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==", "dev": true, "license": "MIT"}, "node_modules/pathval": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/pathval/-/pathval-1.1.1.tgz", "integrity": "sha512-Dp6zGqpTdETdR63lehJYPeIOqpiNBNtc7BpWSLrOje7UaIsE5aY92r/AunQA7rsXvet3lrJ3JnZX29UPTKXyKQ==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pkg-types": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/pkg-types/-/pkg-types-1.3.1.tgz", "integrity": "sha512-/Jm5M4RvtBFVkKWRu2BLUTNP8/M2a+UwuAX+ae4770q1qVGtfjG+WTCupoZixokjmHiry8uI+dlY8KXYV5HVVQ==", "dev": true, "license": "MIT", "dependencies": {"confbox": "^0.1.8", "mlly": "^1.7.4", "pathe": "^2.0.1"}}, "node_modules/pkg-types/node_modules/pathe": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "dev": true, "license": "MIT"}, "node_modules/portfinder": {"version": "1.0.37", "resolved": "https://registry.npmjs.org/portfinder/-/portfinder-1.0.37.tgz", "integrity": "sha512-yuGIEjDAYnnOex9ddMnKZEMFE0CcGo6zbfzDklkmT1m5z734ss6JMzN9rNB3+RR7iS+F10D4/BVIaXOyh8PQKw==", "dev": true, "license": "MIT", "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "engines": {"node": ">= 10.12"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/pretty-format": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz", "integrity": "sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/railroad-diagrams": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/railroad-diagrams/-/railroad-diagrams-1.0.0.tgz", "integrity": "sha512-cz93DjNeLY0idrCNOH6PviZGRN9GJhsdm9hpn1YCS879fj4W+x5IFJhhkRZcwVgMmFF7R82UA/7Oh+R8lLZg6A==", "dev": true, "license": "CC0-1.0"}, "node_modules/react-is": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "dev": true, "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==", "dev": true, "license": "MIT"}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.1.0.tgz", "integrity": "sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rollup": {"version": "4.44.2", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.44.2.tgz", "integrity": "sha512-PVoapzTwSEcelaWGth3uR66u7ZRo6qhPHc0f2uRO9fX6XDVNrIiGYS0Pj9+R8yIIYSD/mCx2b16Ws9itljKSPg==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.8"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.44.2", "@rollup/rollup-android-arm64": "4.44.2", "@rollup/rollup-darwin-arm64": "4.44.2", "@rollup/rollup-darwin-x64": "4.44.2", "@rollup/rollup-freebsd-arm64": "4.44.2", "@rollup/rollup-freebsd-x64": "4.44.2", "@rollup/rollup-linux-arm-gnueabihf": "4.44.2", "@rollup/rollup-linux-arm-musleabihf": "4.44.2", "@rollup/rollup-linux-arm64-gnu": "4.44.2", "@rollup/rollup-linux-arm64-musl": "4.44.2", "@rollup/rollup-linux-loongarch64-gnu": "4.44.2", "@rollup/rollup-linux-powerpc64le-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-gnu": "4.44.2", "@rollup/rollup-linux-riscv64-musl": "4.44.2", "@rollup/rollup-linux-s390x-gnu": "4.44.2", "@rollup/rollup-linux-x64-gnu": "4.44.2", "@rollup/rollup-linux-x64-musl": "4.44.2", "@rollup/rollup-win32-arm64-msvc": "4.44.2", "@rollup/rollup-win32-ia32-msvc": "4.44.2", "@rollup/rollup-win32-x64-msvc": "4.44.2", "fsevents": "~2.3.2"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true, "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true, "license": "MIT"}, "node_modules/secure-compare": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha512-<PERSON>ckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==", "dev": true, "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.3", "resolved": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz", "integrity": "sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/shelljs": {"version": "0.10.0", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.10.0.tgz", "integrity": "sha512-Jex+xw5Mg2qMZL3qnzXIfaxEtBaC4n7xifqaqtrZDdlheR70OGkydrPJWT0V1cA1k3nanC86x9FwAmQl6w3Klw==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"execa": "^5.1.1", "fast-glob": "^3.3.2"}, "engines": {"node": ">=18"}}, "node_modules/shelljs/node_modules/execa": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/shelljs/node_modules/get-stream": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/shelljs/node_modules/human-signals": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==", "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/shelljs/node_modules/is-stream": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/shelljs/node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/shelljs/node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shelljs/node_modules/onetime": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/shelljs/node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "license": "ISC"}, "node_modules/shelljs/node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/siginfo": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/siginfo/-/siginfo-2.0.0.tgz", "integrity": "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==", "dev": true, "license": "ISC"}, "node_modules/signal-exit": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "integrity": "sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/spawn-command": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/spawn-command/-/spawn-command-0.0.2.tgz", "integrity": "sha512-zC8zGoGkmc8J9ndvml8Xksr1Amk9qBujgbF0JAIWO7kXr43w0h/0GJNM/Vustixu+YE8N/MTrQ7N31FvHUACxQ==", "dev": true}, "node_modules/stackback": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/stackback/-/stackback-0.0.2.tgz", "integrity": "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==", "dev": true, "license": "MIT"}, "node_modules/std-env": {"version": "3.9.0", "resolved": "https://registry.npmjs.org/std-env/-/std-env-3.9.0.tgz", "integrity": "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==", "dev": true, "license": "MIT"}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-3.0.0.tgz", "integrity": "sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-literal": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/strip-literal/-/strip-literal-2.1.1.tgz", "integrity": "sha512-631UJ6O00eNGfMiWG78ck80dfBab8X6IVFB51jZK5Icd7XAs60Z5y7QdSd/wGIklnWvRbUNloVzhOKKmutxQ6Q==", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^9.0.1"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/supports-color": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/text-table": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz", "integrity": "sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==", "dev": true, "license": "MIT"}, "node_modules/tinybench": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/tinybench/-/tinybench-2.9.0.tgz", "integrity": "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==", "dev": true, "license": "MIT"}, "node_modules/tinypool": {"version": "0.8.4", "resolved": "https://registry.npmjs.org/tinypool/-/tinypool-0.8.4.tgz", "integrity": "sha512-i11VH5gS6IFeLY3gMBQ00/MmLncVP7JLXOw1vlgkytLmJK7QnEr7NXf0LBdxfmNPAeyetukOk0bOYrJrFGjYJQ==", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/tinyspy/-/tinyspy-2.2.1.tgz", "integrity": "sha512-KYad6Vy5VDWV4GH3fjpseMQ/XU2BhIYP7Vzd0LG44qRWm/Yt2WCOTicFdvmgo6gWaqooMQCawTtILVQJupKu7A==", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/tree-kill": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz", "integrity": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/ts-api-utils": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-1.4.3.tgz", "integrity": "sha512-i3eMG77UTMD0hZhgRS562pv83RC6ukSAC2GMNWc+9dieh/+jDM5u5YG+NHX6VNDRHQcHwmsTHctP9LhbC3WxVw==", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "peerDependencies": {"typescript": ">=4.2.0"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-detect": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/type-detect/-/type-detect-4.1.0.tgz", "integrity": "sha512-Acylog8/luQ8L7il+geoSxhEkazvkslg7PSNKOX59mbB9cOveP5aq9h74Y7YU8yDpJwetzQQrfIwtf4Wp4LKcw==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "5.1.6", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.1.6.tgz", "integrity": "sha512-zaWCozRZ6DLEWAWFrVDz1H6FVXzUSfTy5FUMWsQlU8Ym5JP9eO4xkTIROFCQvhQf61z6O/G6ugw3SgAnvvm+HA==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/ufo": {"version": "1.6.1", "resolved": "https://registry.npmjs.org/ufo/-/ufo-1.6.1.tgz", "integrity": "sha512-9a4/uxlTWJ4+a5i0ooc1rU7C7YOw3wT+UGqdeNNHWnOF9qcMBgLRS+4IYUqbczewFx4mLEig6gawh7X6mFlEkA==", "dev": true, "license": "MIT"}, "node_modules/undici-types": {"version": "5.26.5", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz", "integrity": "sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==", "dev": true, "license": "MIT"}, "node_modules/union": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/union/-/union-0.5.0.tgz", "integrity": "sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==", "dev": true, "dependencies": {"qs": "^6.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/universalify": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-join": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz", "integrity": "sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==", "dev": true, "license": "MIT"}, "node_modules/vite": {"version": "5.2.14", "resolved": "https://registry.npmjs.org/vite/-/vite-5.2.14.tgz", "integrity": "sha512-TFQLuwWLPms+NBNlh0D9LZQ+HXW471COABxw/9TEUBrjuHMo9BrYBPrN/SYAwIuVL+rLerycxiLT41t4f5MZpA==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.20.1", "postcss": "^8.4.38", "rollup": "^4.13.0"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || >=20.0.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/vite-node": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/vite-node/-/vite-node-1.4.0.tgz", "integrity": "sha512-VZDAseqjrHgNd4Kh8icYHWzTKSCZMhia7GyHfhtzLW33fZlG9SwsB6CEhgyVOWkJfJ2pFLrp/Gj1FSfAiqH9Lw==", "dev": true, "license": "MIT", "dependencies": {"cac": "^6.7.14", "debug": "^4.3.4", "pathe": "^1.1.1", "picocolors": "^1.0.0", "vite": "^5.0.0"}, "bin": {"vite-node": "vite-node.mjs"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/vitest": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/vitest/-/vitest-1.4.0.tgz", "integrity": "sha512-gujzn0g7fmwf83/WzrDTnncZt2UiXP41mHuFYFrdwaLRVQ6JYQEiME2IfEjU3vcFL3VKa75XhI3lFgn+hfVsQw==", "dev": true, "license": "MIT", "dependencies": {"@vitest/expect": "1.4.0", "@vitest/runner": "1.4.0", "@vitest/snapshot": "1.4.0", "@vitest/spy": "1.4.0", "@vitest/utils": "1.4.0", "acorn-walk": "^8.3.2", "chai": "^4.3.10", "debug": "^4.3.4", "execa": "^8.0.1", "local-pkg": "^0.5.0", "magic-string": "^0.30.5", "pathe": "^1.1.1", "picocolors": "^1.0.0", "std-env": "^3.5.0", "strip-literal": "^2.0.0", "tinybench": "^2.5.1", "tinypool": "^0.8.2", "vite": "^5.0.0", "vite-node": "1.4.0", "why-is-node-running": "^2.2.2"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/node": "^18.0.0 || >=20.0.0", "@vitest/browser": "1.4.0", "@vitest/ui": "1.4.0", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "node_modules/vscode": {"name": "@codingame/monaco-vscode-api", "version": "3.2.3", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-api/-/monaco-vscode-api-3.2.3.tgz", "integrity": "sha512-YOad82N2G5Ae/Ry14LOM3Mka6EjzbGXfwIs44xP/kFdhk9m7c1vXiIQxJIyUjTsJgzfEqYvcQKULlt4cqC7cEA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-base-service-override": "3.2.3", "@codingame/monaco-vscode-environment-service-override": "3.2.3", "@codingame/monaco-vscode-extensions-service-override": "3.2.3", "@codingame/monaco-vscode-files-service-override": "3.2.3", "@codingame/monaco-vscode-host-service-override": "3.2.3", "@codingame/monaco-vscode-layout-service-override": "3.2.3", "@codingame/monaco-vscode-quickaccess-service-override": "3.2.3"}}, "node_modules/vscode-jsonrpc": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-8.2.0.tgz", "integrity": "sha512-C+r0eKJUIfiDIfwJhria30+TYWPtuHJXHtI7J0YlOmKAo7ogxP20T0zxB7HZQIFhIyvoBPwWskjxrvAtfjyZfA==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/vscode-languageclient": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/vscode-languageclient/-/vscode-languageclient-9.0.1.tgz", "integrity": "sha512-JZiimVdvimEuHh5olxhxkht09m3JzUGwggb5eRUkzzJhZ2KjCN0nh55VfiED9oez9DyF8/fz1g1iBV3h+0Z2EA==", "license": "MIT", "dependencies": {"minimatch": "^5.1.0", "semver": "^7.3.7", "vscode-languageserver-protocol": "3.17.5"}, "engines": {"vscode": "^1.82.0"}}, "node_modules/vscode-languageclient/node_modules/minimatch": {"version": "5.1.6", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/vscode-languageserver": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/vscode-languageserver/-/vscode-languageserver-9.0.1.tgz", "integrity": "sha512-woByF3PDpkHFUreUa7Hos7+pUWdeWMXRd26+ZX2A8cFx6v/JPTtd4/uN0/jB6XQHYaOlHbio03NTHCqrgG5n7g==", "license": "MIT", "dependencies": {"vscode-languageserver-protocol": "3.17.5"}, "bin": {"installServerIntoExtension": "bin/installServerIntoExtension"}}, "node_modules/vscode-languageserver-protocol": {"version": "3.17.5", "resolved": "https://registry.npmjs.org/vscode-languageserver-protocol/-/vscode-languageserver-protocol-3.17.5.tgz", "integrity": "sha512-mb1bvRJN8SVznADSGWM9u/b07H7Ecg0I3OgXDuLdn307rl/J3A9YD6/eYOssqhecL27hK1IPZAsaqh00i/Jljg==", "license": "MIT", "dependencies": {"vscode-jsonrpc": "8.2.0", "vscode-languageserver-types": "3.17.5"}}, "node_modules/vscode-languageserver-textdocument": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/vscode-languageserver-textdocument/-/vscode-languageserver-textdocument-1.0.12.tgz", "integrity": "sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==", "license": "MIT"}, "node_modules/vscode-languageserver-types": {"version": "3.17.5", "resolved": "https://registry.npmjs.org/vscode-languageserver-types/-/vscode-languageserver-types-3.17.5.tgz", "integrity": "sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==", "license": "MIT"}, "node_modules/vscode-oniguruma": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/vscode-oniguruma/-/vscode-oniguruma-1.7.0.tgz", "integrity": "sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==", "license": "MIT"}, "node_modules/vscode-textmate": {"version": "9.2.0", "resolved": "https://registry.npmjs.org/vscode-textmate/-/vscode-textmate-9.2.0.tgz", "integrity": "sha512-rkvG4SraZQaPSN/5XjwKswdU0OP9MF28QjrYzUBbhb8QyG3ljB1Ky996m++jiI7KdiAP2CkBiQZd9pqEDTClqA==", "license": "MIT"}, "node_modules/vscode-uri": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.0.8.tgz", "integrity": "sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==", "license": "MIT"}, "node_modules/vscode-ws-jsonrpc": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/vscode-ws-jsonrpc/-/vscode-ws-jsonrpc-3.4.0.tgz", "integrity": "sha512-jkNZvX0LdHt4skPxMw/jFePr3jRCJU6ZmO28oPoQ7RwNSkwU3uN8mgtxACYEbOY68bYmi/b/uJzhxewKCz1P4w==", "license": "MIT", "dependencies": {"vscode-jsonrpc": "~8.2.1"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/vscode-ws-jsonrpc/node_modules/vscode-jsonrpc": {"version": "8.2.1", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-8.2.1.tgz", "integrity": "sha512-kdjOSJ2lLIn7r1rtrMbbNCHjyMPfRnowdKjBQ+mGq6NAW5QY2bEZC/khaC5OR8svbbjvLEaIXkOq45e2X9BIbQ==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/whatwg-encoding": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "integrity": "sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=12"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/why-is-node-running": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.3.0.tgz", "integrity": "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==", "dev": true, "license": "MIT", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha512-B<PERSON>22B<PERSON>eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi/node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true, "license": "ISC"}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}