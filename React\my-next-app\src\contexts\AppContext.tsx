'use client';

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { 
  AppState, 
  AppEvent, 
  User, 
  Project, 
  EditorTab, 
  DiagnosticMessage,
  ContextMenuState,
  ModalState 
} from '@/types';
import { authApi, projectApi, getAuthToken, removeAuthToken } from '@/utils/api';

// 初始状态
const initialState: AppState = {
  auth: {
    user: null,
    isAuthenticated: false,
    isLoading: true,
  },
  projects: [],
  editor: {
    tabs: [],
    activeTabId: null,
    isLoading: false,
  },
  contextMenu: {
    isVisible: false,
    x: 0,
    y: 0,
    items: [],
  },
  modal: {
    isOpen: false,
    type: null,
  },
  diagnostics: [],
  selectedProjectId: null,
  isLoading: false,
};

// Action 类型
type AppAction = 
  | { type: 'SET_AUTH_USER'; payload: User | null }
  | { type: 'SET_AUTH_LOADING'; payload: boolean }
  | { type: 'SET_PROJECTS'; payload: Project[] }
  | { type: 'ADD_PROJECT'; payload: Project }
  | { type: 'UPDATE_PROJECT'; payload: { id: string; updates: Partial<Project> } }
  | { type: 'REMOVE_PROJECT'; payload: string }
  | { type: 'SET_SELECTED_PROJECT'; payload: string | null }
  | { type: 'ADD_EDITOR_TAB'; payload: EditorTab }
  | { type: 'UPDATE_EDITOR_TAB'; payload: { id: string; updates: Partial<EditorTab> } }
  | { type: 'REMOVE_EDITOR_TAB'; payload: string }
  | { type: 'SET_ACTIVE_TAB'; payload: string | null }
  | { type: 'SET_EDITOR_LOADING'; payload: boolean }
  | { type: 'SET_CONTEXT_MENU'; payload: Partial<ContextMenuState> }
  | { type: 'HIDE_CONTEXT_MENU' }
  | { type: 'SET_MODAL'; payload: Partial<ModalState> }
  | { type: 'HIDE_MODAL' }
  | { type: 'SET_DIAGNOSTICS'; payload: DiagnosticMessage[] }
  | { type: 'ADD_DIAGNOSTIC'; payload: DiagnosticMessage }
  | { type: 'CLEAR_DIAGNOSTICS' }
  | { type: 'SET_LOADING'; payload: boolean };

// Reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'SET_AUTH_USER':
      return {
        ...state,
        auth: {
          ...state.auth,
          user: action.payload,
          isAuthenticated: !!action.payload,
          isLoading: false,
        },
      };

    case 'SET_AUTH_LOADING':
      return {
        ...state,
        auth: {
          ...state.auth,
          isLoading: action.payload,
        },
      };

    case 'SET_PROJECTS':
      return {
        ...state,
        projects: action.payload,
      };

    case 'ADD_PROJECT':
      return {
        ...state,
        projects: [...state.projects, action.payload],
      };

    case 'UPDATE_PROJECT':
      return {
        ...state,
        projects: state.projects.map(project =>
          project.id === action.payload.id
            ? { ...project, ...action.payload.updates }
            : project
        ),
      };

    case 'REMOVE_PROJECT':
      return {
        ...state,
        projects: state.projects.filter(project => project.id !== action.payload),
        selectedProjectId: state.selectedProjectId === action.payload ? null : state.selectedProjectId,
      };

    case 'SET_SELECTED_PROJECT':
      return {
        ...state,
        selectedProjectId: action.payload,
      };

    case 'ADD_EDITOR_TAB':
      return {
        ...state,
        editor: {
          ...state.editor,
          tabs: [...state.editor.tabs, action.payload],
          activeTabId: action.payload.id,
        },
      };

    case 'UPDATE_EDITOR_TAB':
      return {
        ...state,
        editor: {
          ...state.editor,
          tabs: state.editor.tabs.map(tab =>
            tab.id === action.payload.id
              ? { ...tab, ...action.payload.updates }
              : tab
          ),
        },
      };

    case 'REMOVE_EDITOR_TAB':
      const remainingTabs = state.editor.tabs.filter(tab => tab.id !== action.payload);
      const newActiveTabId = state.editor.activeTabId === action.payload
        ? remainingTabs.length > 0 ? remainingTabs[remainingTabs.length - 1].id : null
        : state.editor.activeTabId;

      return {
        ...state,
        editor: {
          ...state.editor,
          tabs: remainingTabs,
          activeTabId: newActiveTabId,
        },
      };

    case 'SET_ACTIVE_TAB':
      return {
        ...state,
        editor: {
          ...state.editor,
          activeTabId: action.payload,
        },
      };

    case 'SET_EDITOR_LOADING':
      return {
        ...state,
        editor: {
          ...state.editor,
          isLoading: action.payload,
        },
      };

    case 'SET_CONTEXT_MENU':
      return {
        ...state,
        contextMenu: {
          ...state.contextMenu,
          ...action.payload,
        },
      };

    case 'HIDE_CONTEXT_MENU':
      return {
        ...state,
        contextMenu: {
          ...state.contextMenu,
          isVisible: false,
        },
      };

    case 'SET_MODAL':
      return {
        ...state,
        modal: {
          ...state.modal,
          ...action.payload,
        },
      };

    case 'HIDE_MODAL':
      return {
        ...state,
        modal: {
          isOpen: false,
          type: null,
          data: undefined,
        },
      };

    case 'SET_DIAGNOSTICS':
      return {
        ...state,
        diagnostics: action.payload,
      };

    case 'ADD_DIAGNOSTIC':
      return {
        ...state,
        diagnostics: [...state.diagnostics, action.payload],
      };

    case 'CLEAR_DIAGNOSTICS':
      return {
        ...state,
        diagnostics: [],
      };

    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };

    default:
      return state;
  }
};

// Context
const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);

// Provider
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      const token = getAuthToken();
      if (token) {
        try {
          const response = await authApi.getCurrentUser();
          if (response.success && response.data) {
            dispatch({ type: 'SET_AUTH_USER', payload: response.data });
          } else {
            removeAuthToken();
            dispatch({ type: 'SET_AUTH_USER', payload: null });
          }
        } catch (error) {
          removeAuthToken();
          dispatch({ type: 'SET_AUTH_USER', payload: null });
        }
      } else {
        dispatch({ type: 'SET_AUTH_LOADING', payload: false });
      }
    };

    initAuth();
  }, []);

  // 加载项目数据
  useEffect(() => {
    const loadProjects = async () => {
      if (state.auth.isAuthenticated && state.auth.user) {
        try {
          const response = await projectApi.getProjects();
          if (response.success && response.data) {
            dispatch({ type: 'SET_PROJECTS', payload: response.data });
          }
        } catch (error) {
          console.error('Failed to load projects:', error);
        }
      }
    };

    loadProjects();
  }, [state.auth.isAuthenticated]);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};

// Hook
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};
