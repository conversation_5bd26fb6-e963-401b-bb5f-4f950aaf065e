//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/
package Test {
	package Circular {
		classifier A { 
			public import A::*;
			feature a: A;
		}

		alias Circ for Circular;
		package P {
			public import Circular::*;
		}
		
		classifier B :> B;		
		feature b :> b;
		
		// XPECT errors ---> "Must directly or indirectly specialize Base::Anything" at "classifier C ~ C;"
		classifier C ~ C;
		
		// XPECT errors ---> "Features must have at least one type" at "feature c ~ c;"
		feature c ~ c;
	}
}
