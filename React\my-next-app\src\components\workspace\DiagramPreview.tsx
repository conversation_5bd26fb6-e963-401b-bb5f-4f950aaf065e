'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useApp } from '@/contexts/AppContext';

interface DiagramPreviewProps {
  svgContent?: string;
}

const DiagramPreview: React.FC<DiagramPreviewProps> = ({ svgContent }) => {
  const { state } = useApp();
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);

  // 重置视图
  const resetView = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };

  // 缩放控制
  const handleZoomIn = () => {
    setScale(prev => Math.min(prev * 1.2, 5));
  };

  const handleZoomOut = () => {
    setScale(prev => Math.max(prev / 1.2, 0.1));
  };

  // 适应窗口
  const fitToWindow = () => {
    if (!containerRef.current || !svgContent) return;
    
    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    
    // 这里应该根据 SVG 的实际尺寸计算合适的缩放比例
    // 简化处理，设置为适中的缩放
    setScale(0.8);
    setPosition({ x: 0, y: 0 });
  };

  // 鼠标滚轮缩放
  const handleWheel = (event: React.WheelEvent) => {
    event.preventDefault();
    
    const delta = event.deltaY > 0 ? 0.9 : 1.1;
    const newScale = Math.max(0.1, Math.min(5, scale * delta));
    
    setScale(newScale);
  };

  // 拖拽开始
  const handleMouseDown = (event: React.MouseEvent) => {
    if (event.button === 0) { // 左键
      setIsDragging(true);
      setDragStart({
        x: event.clientX - position.x,
        y: event.clientY - position.y,
      });
    }
  };

  // 拖拽中
  const handleMouseMove = (event: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: event.clientX - dragStart.x,
        y: event.clientY - dragStart.y,
      });
    }
  };

  // 拖拽结束
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey) {
        switch (event.key) {
          case '0':
            event.preventDefault();
            resetView();
            break;
          case '=':
          case '+':
            event.preventDefault();
            handleZoomIn();
            break;
          case '-':
            event.preventDefault();
            handleZoomOut();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="ide-preview flex flex-col">
      {/* 预览工具栏 */}
      <div className="flex items-center justify-between p-2 border-b bg-gray-50">
        <h3 className="text-sm font-medium text-gray-700">图形预览</h3>
        
        <div className="flex items-center space-x-1">
          <button
            onClick={handleZoomOut}
            className="p-1 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded"
            title="缩小 (Ctrl + -)"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>
          
          <span className="text-xs text-gray-600 min-w-12 text-center">
            {Math.round(scale * 100)}%
          </span>
          
          <button
            onClick={handleZoomIn}
            className="p-1 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded"
            title="放大 (Ctrl + +)"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
          
          <button
            onClick={fitToWindow}
            className="p-1 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded"
            title="适应窗口"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
            </svg>
          </button>
          
          <button
            onClick={resetView}
            className="p-1 text-gray-600 hover:text-gray-800 hover:bg-gray-200 rounded"
            title="重置视图 (Ctrl + 0)"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>

      {/* 预览区域 */}
      <div
        ref={containerRef}
        className="flex-1 overflow-hidden bg-white relative cursor-grab"
        onWheel={handleWheel}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
      >
        {svgContent ? (
          <div
            className="absolute inset-0 flex items-center justify-center"
            style={{
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transformOrigin: 'center',
            }}
          >
            <div
              dangerouslySetInnerHTML={{ __html: svgContent }}
              className="select-none"
            />
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-4">
                <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">暂无图形预览</h3>
              <p className="text-gray-500 mb-4">编写 SysML v2 代码并点击"生成图"按钮</p>
              <div className="text-sm text-gray-400">
                <p>支持的操作：</p>
                <ul className="mt-2 space-y-1">
                  <li>• 鼠标滚轮：缩放</li>
                  <li>• 鼠标拖拽：平移</li>
                  <li>• Ctrl + 0：重置视图</li>
                  <li>• Ctrl + +/-：缩放</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 状态栏 */}
      {svgContent && (
        <div className="px-3 py-1 bg-gray-50 border-t text-xs text-gray-600">
          缩放: {Math.round(scale * 100)}% | 位置: ({Math.round(position.x)}, {Math.round(position.y)})
        </div>
      )}
    </div>
  );
};

export default DiagramPreview;
