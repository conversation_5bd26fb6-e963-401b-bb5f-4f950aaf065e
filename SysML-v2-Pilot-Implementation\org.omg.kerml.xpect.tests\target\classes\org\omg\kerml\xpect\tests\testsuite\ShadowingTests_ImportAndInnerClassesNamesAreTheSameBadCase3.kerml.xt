//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyOuterPackage.kerml"}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{				
	classifier A{
		classifier a2{}
	}
	classifier inner{
		public import OuterPackage::*;
		classifier B specializes A {
			//XPECT linkedName at a1 --> OuterPackage.A.a1
			//* XPECT scope at a1 ---
			inner.A, inner.A.a1, test.inner.A, test.inner.A.a1,
		    b, a1, 
		    A, A.a1, B, B.a1, B.b, inner, inner.B, inner.B.a1, inner.B.b,
		    test.A, test.A.a2, test.inner, test.inner.B, test.inner.B.a1, test.inner.B.b,
		    OuterPackage.A, OuterPackage.A.a1, OuterPackage.B, OuterPackage.B.b.a1,
		--- */
			classifier b specializes a1{}
		}
	}
}
