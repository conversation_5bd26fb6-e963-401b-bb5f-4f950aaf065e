services:
  groovyls:
    image: ghcr.io/typefox/monaco-languageclient/groovy.ls:latest
    build:
      dockerfile: ./packages/examples/resources/groovy/Dockerfile
      context: ../../../..
      # only linux/amd64 for now
      platforms:
        - "linux/amd64"
    platform: linux/amd64
    command: [
      "bash", "-c", "npm run start:example:server:groovy"
    ]
    ports:
      - 30002:30002
    working_dir: /home/<USER>/mlc
    container_name: groovyls
