// SysML v2 示例代码

export const sampleSysMLCode = `package VehicleExample {
  // 车辆系统的基本定义
  part def Vehicle {
    doc /* 
      车辆是一个复杂的系统，包含多个子系统
      这个定义展示了 SysML v2 的基本语法
    */
    
    // 车辆的基本属性
    attribute mass : Real;
    attribute maxSpeed : Real;
    attribute fuelCapacity : Real;
    
    // 车辆的主要部件
    part engine : Engine;
    part transmission : Transmission;
    part wheels : Wheel[4];
    part body : Body;
    
    // 端口定义
    port fuelPort : FuelPort;
    port powerPort : PowerPort;
    
    // 约束
    constraint massConstraint {
      mass > 0.0 and mass < 5000.0
    }
    
    constraint speedConstraint {
      maxSpeed > 0.0 and maxSpeed < 300.0
    }
  }
  
  // 发动机定义
  part def Engine {
    attribute power : Real;
    attribute fuelType : String;
    attribute displacement : Real;
    
    port fuelInput : FuelPort;
    port powerOutput : PowerPort;
    
    constraint powerConstraint {
      power > 0.0 and power < 1000.0
    }
  }
  
  // 变速箱定义
  part def Transmission {
    attribute type : String;
    attribute gears : Integer;
    attribute efficiency : Real;
    
    port powerInput : PowerPort;
    port powerOutput : PowerPort;
    
    constraint gearsConstraint {
      gears >= 3 and gears <= 10
    }
  }
  
  // 车轮定义
  part def Wheel {
    attribute diameter : Real;
    attribute material : String;
    attribute pressure : Real;
    
    constraint diameterConstraint {
      diameter > 0.3 and diameter < 1.0
    }
  }
  
  // 车身定义
  part def Body {
    attribute material : String;
    attribute color : String;
    attribute doors : Integer;
    
    constraint doorsConstraint {
      doors >= 2 and doors <= 5
    }
  }
  
  // 端口定义
  port def FuelPort {
    attribute flowRate : Real;
    attribute fuelType : String;
  }
  
  port def PowerPort {
    attribute voltage : Real;
    attribute current : Real;
    attribute power : Real;
  }
  
  // 连接定义
  connection def PowerConnection {
    end source : PowerPort;
    end target : PowerPort;
    
    constraint powerBalance {
      source.power == target.power
    }
  }
  
  // 具体的车辆实例
  part myCar : Vehicle {
    // 重新定义属性值
    :>> mass = 1500.0;
    :>> maxSpeed = 180.0;
    :>> fuelCapacity = 60.0;
    
    // 具体的发动机配置
    part :>> engine {
      :>> power = 150.0;
      :>> fuelType = "Gasoline";
      :>> displacement = 2.0;
    }
    
    // 具体的变速箱配置
    part :>> transmission {
      :>> type = "Automatic";
      :>> gears = 6;
      :>> efficiency = 0.95;
    }
    
    // 具体的车轮配置
    part :>> wheels[0] {
      :>> diameter = 0.65;
      :>> material = "Aluminum";
      :>> pressure = 2.2;
    }
    
    // 连接定义
    connection engineToPower : PowerConnection 
      connect engine.powerOutput to transmission.powerInput;
  }
  
  // 需求定义
  requirement def VehicleRequirement {
    doc /* 车辆的基本需求 */
    
    requirement fuelEfficiency {
      doc /* 燃油效率要求 */
      attribute targetEfficiency : Real = 8.0; // L/100km
      
      require constraint {
        myCar.fuelCapacity / myCar.maxSpeed * 100 <= targetEfficiency
      }
    }
    
    requirement safetyRequirement {
      doc /* 安全性要求 */
      
      require constraint {
        myCar.maxSpeed <= 200.0
      }
    }
  }
  
  // 用例定义
  use case DrivingUseCase {
    doc /* 驾驶用例 */
    
    actor driver : Driver;
    subject vehicle : Vehicle;
    
    objective {
      doc /* 安全高效地驾驶车辆 */
    }
    
    include use case StartEngine;
    include use case Accelerate;
    include use case Brake;
    include use case StopEngine;
  }
  
  // 状态定义
  state def VehicleState {
    entry / initializeVehicle();
    exit / shutdownVehicle();
    
    state off;
    state starting;
    state running;
    state stopping;
    
    transition off to starting
      when startSignal
      do / startEngine();
    
    transition starting to running
      when engineStarted
      if engineTemperature > minTemp;
    
    transition running to stopping
      when stopSignal
      do / beginShutdown();
    
    transition stopping to off
      when engineStopped;
  }
  
  // 动作定义
  action def StartEngine {
    in fuelAvailable : Boolean;
    out engineRunning : Boolean;
    
    action checkFuel {
      out fuelOK : Boolean;
    }
    
    action ignite {
      in fuelOK : Boolean;
      out ignitionSuccess : Boolean;
    }
    
    succession checkFuel then ignite;
  }
}`;

export const sampleRequirementsCode = `package RequirementsModel {
  // 系统需求定义
  requirement def SystemRequirement {
    doc /* 系统级需求定义 */
    
    requirement performance {
      doc /* 性能需求 */
      attribute maxResponseTime : Real = 100.0; // ms
      attribute throughput : Real = 1000.0; // requests/sec
      
      require constraint {
        responseTime <= maxResponseTime and
        actualThroughput >= throughput
      }
    }
    
    requirement reliability {
      doc /* 可靠性需求 */
      attribute availability : Real = 0.999; // 99.9%
      attribute mtbf : Real = 8760.0; // hours
      
      require constraint {
        systemAvailability >= availability and
        meanTimeBetweenFailures >= mtbf
      }
    }
    
    requirement security {
      doc /* 安全性需求 */
      
      require constraint {
        encryptionEnabled == true and
        accessControlEnabled == true
      }
    }
  }
  
  // 功能需求
  requirement def FunctionalRequirement {
    doc /* 功能性需求定义 */
    
    requirement userAuthentication {
      doc /* 用户认证需求 */
      
      require constraint {
        authenticationMethod in ["password", "biometric", "token"]
      }
    }
    
    requirement dataProcessing {
      doc /* 数据处理需求 */
      
      require constraint {
        dataIntegrity == true and
        dataConsistency == true
      }
    }
  }
}`;

export const sampleArchitectureCode = `package ArchitectureModel {
  // 系统架构定义
  part def System {
    doc /* 系统架构的顶层定义 */
    
    // 系统组件
    part frontend : FrontendComponent;
    part backend : BackendComponent;
    part database : DatabaseComponent;
    part cache : CacheComponent;
    
    // 接口定义
    interface userInterface : UserInterface;
    interface apiInterface : APIInterface;
    interface dataInterface : DataInterface;
    
    // 连接关系
    connection frontendToBackend : APIConnection
      connect frontend.apiPort to backend.apiPort;
    
    connection backendToDatabase : DataConnection
      connect backend.dataPort to database.dataPort;
    
    connection backendToCache : CacheConnection
      connect backend.cachePort to cache.cachePort;
  }
  
  // 前端组件
  part def FrontendComponent {
    attribute framework : String = "React";
    attribute version : String = "18.0";
    
    port apiPort : APIPort;
    port userPort : UserPort;
    
    part userInterface : UserInterfaceModule;
    part stateManager : StateManagerModule;
    part router : RouterModule;
  }
  
  // 后端组件
  part def BackendComponent {
    attribute language : String = "TypeScript";
    attribute runtime : String = "Node.js";
    
    port apiPort : APIPort;
    port dataPort : DataPort;
    port cachePort : CachePort;
    
    part apiGateway : APIGatewayModule;
    part businessLogic : BusinessLogicModule;
    part dataAccess : DataAccessModule;
  }
  
  // 数据库组件
  part def DatabaseComponent {
    attribute type : String = "PostgreSQL";
    attribute version : String = "15.0";
    
    port dataPort : DataPort;
    
    part queryEngine : QueryEngineModule;
    part storage : StorageModule;
    part indexing : IndexingModule;
  }
}`;

export const getRandomSampleCode = (): string => {
  const samples = [sampleSysMLCode, sampleRequirementsCode, sampleArchitectureCode];
  return samples[Math.floor(Math.random() * samples.length)];
};
