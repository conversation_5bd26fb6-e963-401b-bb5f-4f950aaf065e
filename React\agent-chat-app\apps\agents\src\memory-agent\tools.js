"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeTools = initializeTools;
var configuration_js_1 = require("./configuration.js");
var uuid_1 = require("uuid");
var tools_1 = require("@langchain/core/tools");
var zod_1 = require("zod");
var utils_js_1 = require("./utils.js");
/**
 * Initialize tools within a function so that they have access to the current
 * state and config at runtime.
 */
function initializeTools(config) {
    /**
     * Upsert a memory in the database.
     * @param content The main content of the memory.
     * @param context Additional context for the memory.
     * @param memoryId Optional ID to overwrite an existing memory.
     * @returns A string confirming the memory storage.
     */
    function upsertMemory(opts) {
        return __awaiter(this, void 0, void 0, function () {
            var content, context, memoryId, configurable, memId, store;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        content = opts.content, context = opts.context, memoryId = opts.memoryId;
                        if (!config || !config.store) {
                            throw new Error("Config or store not provided");
                        }
                        configurable = (0, configuration_js_1.ensureConfiguration)(config);
                        memId = memoryId || (0, uuid_1.v4)();
                        store = (0, utils_js_1.getStoreFromConfigOrThrow)(config);
                        return [4 /*yield*/, store.put(["memories", configurable.userId], memId, {
                                content: content,
                                context: context,
                            })];
                    case 1:
                        _a.sent();
                        return [2 /*return*/, "Stored memory ".concat(memId)];
                }
            });
        });
    }
    var upsertMemoryTool = (0, tools_1.tool)(upsertMemory, {
        name: "upsertMemory",
        description: "Upsert a memory in the database. If a memory conflicts with an existing one, \
      update the existing one by passing in the memory_id instead of creating a duplicate. \
      If the user corrects a memory, update it. Can call multiple times in parallel \
      if you need to store or update multiple memories.",
        schema: zod_1.z.object({
            content: zod_1.z.string().describe("The main content of the memory. For example: \
          'User expressed interest in learning about French.'"),
            context: zod_1.z.string().describe("Additional context for the memory. For example: \
          'This was mentioned while discussing career options in Europe.'"),
            memoryId: zod_1.z
                .string()
                .optional()
                .describe("The memory ID to overwrite. Only provide if updating an existing memory."),
        }),
    });
    return [upsertMemoryTool];
}
