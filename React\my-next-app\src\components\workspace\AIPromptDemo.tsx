'use client';

import React, { useState } from 'react';
import { sysmlApi } from '@/utils/api';

const AIPromptDemo: React.FC = () => {
  const [prompt, setPrompt] = useState('');
  const [currentCode, setCurrentCode] = useState('');
  const [generatedCode, setGeneratedCode] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState('');

  const handleGenerate = async () => {
    if (!prompt.trim()) {
      setError('请输入提示词');
      return;
    }

    setIsGenerating(true);
    setError('');
    
    try {
      const response = await sysmlApi.generateCodeWithCustomPrompt(prompt, currentCode);
      
      if (response.success && response.data) {
        setGeneratedCode(response.data.code);
      } else {
        setError(response.error || '生成失败');
      }
    } catch (err) {
      setError('网络错误，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const examplePrompts = [
    '创建一个车辆系统模型',
    '生成一个机器人控制系统',
    '添加性能需求定义',
    '创建传感器组件',
    '定义状态机'
  ];

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          🤖 AI 提示词功能演示
        </h2>
        
        {/* 提示词输入 */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              AI 提示词：
            </label>
            <input
              type="text"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !isGenerating) {
                  handleGenerate();
                }
              }}
              placeholder="输入修改意见..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              disabled={isGenerating}
            />
          </div>

          {/* 示例提示词 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              示例提示词：
            </label>
            <div className="flex flex-wrap gap-2">
              {examplePrompts.map((example, index) => (
                <button
                  key={index}
                  onClick={() => setPrompt(example)}
                  className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                  disabled={isGenerating}
                >
                  {example}
                </button>
              ))}
            </div>
          </div>

          {/* 当前代码输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              当前代码（可选）：
            </label>
            <textarea
              value={currentCode}
              onChange={(e) => setCurrentCode(e.target.value)}
              placeholder="输入现有的 SysML v2 代码，AI 将基于此进行修改..."
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent font-mono text-sm"
              disabled={isGenerating}
            />
          </div>

          {/* 生成按钮 */}
          <button
            onClick={handleGenerate}
            disabled={isGenerating}
            className={`w-full py-2 px-4 rounded-md font-medium ${
              isGenerating
                ? 'bg-purple-400 text-white cursor-not-allowed'
                : 'bg-purple-600 text-white hover:bg-purple-700'
            }`}
          >
            {isGenerating ? '生成中...' : '🚀 生成代码'}
          </button>

          {/* 错误显示 */}
          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
        </div>
      </div>

      {/* 生成结果 */}
      {generatedCode && (
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            ✨ 生成的代码：
          </h3>
          <div className="bg-gray-900 rounded-md p-4 overflow-auto">
            <pre className="text-green-400 text-sm font-mono whitespace-pre-wrap">
              {generatedCode}
            </pre>
          </div>
          <div className="mt-4 flex space-x-2">
            <button
              onClick={() => navigator.clipboard.writeText(generatedCode)}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              📋 复制代码
            </button>
            <button
              onClick={() => setCurrentCode(generatedCode)}
              className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
            >
              ⬆️ 作为当前代码
            </button>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-3">
          📖 使用说明
        </h3>
        <ul className="text-sm text-blue-800 space-y-2">
          <li>• 在「AI 提示词」中输入您的需求描述</li>
          <li>• 可以点击示例提示词快速填入</li>
          <li>• 如果有现有代码，可以在「当前代码」中输入，AI 将基于此进行修改</li>
          <li>• 点击「生成代码」或按 Enter 键开始生成</li>
          <li>• 生成的代码可以复制或作为新的当前代码继续修改</li>
        </ul>
      </div>
    </div>
  );
};

export default AIPromptDemo;
