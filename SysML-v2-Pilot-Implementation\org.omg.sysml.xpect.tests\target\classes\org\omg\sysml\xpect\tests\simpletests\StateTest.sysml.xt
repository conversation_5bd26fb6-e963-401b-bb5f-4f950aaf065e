//* 
XPECT_SETUP org.omg.sysml.xpect.tests.simpletests.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/Links.kerml"}
		File {from ="/library.kernel/Occurrences.kerml"}
        File {from ="/library.kernel/Objects.kerml"}
        File {from ="/library.kernel/Performances.kerml"}
		File {from ="/library.kernel/ControlPerformances.kerml"}
        File {from ="/library.kernel/StatePerformances.kerml"}
        File {from ="/library.kernel/TransitionPerformances.kerml"}
        File {from ="/library.kernel/Transfers.kerml"}
        File {from ="/library.kernel/ScalarValues.kerml"}
        File {from ="/library.kernel/BaseFunctions.kerml"}
        File {from ="/library.kernel/ControlFunctions.kerml"}
		File {from ="/library.systems/Items.sysml"}
		File {from ="/library.systems/Parts.sysml"}
		File {from ="/library.systems/Ports.sysml"}
		File {from ="/library.systems/Actions.sysml"}
		File {from ="/library.systems/States.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/Links.kerml"}
				File {from ="/library.kernel/Occurrences.kerml"}
		        File {from ="/library.kernel/Objects.kerml"}
		        File {from ="/library.kernel/Performances.kerml"}
		        File {from ="/library.kernel/ControlPerformances.kerml"}
		        File {from ="/library.kernel/StatePerformances.kerml"}
		        File {from ="/library.kernel/TransitionPerformances.kerml"}
		        File {from ="/library.kernel/Transfers.kerml"}
		        File {from ="/library.kernel/ScalarValues.kerml"}
		        File {from ="/library.kernel/BaseFunctions.kerml"}
		        File {from ="/library.kernel/ControlFunctions.kerml"}
				File {from ="/library.systems/Items.sysml"}
				File {from ="/library.systems/Parts.sysml"}
				File {from ="/library.systems/Ports.sysml"}
				File {from ="/library.systems/Actions.sysml"}
				File {from ="/library.systems/States.sysml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package StateTest {
	attribute def Sig {
		x;
	}
	attribute def Exit;
	
	part p;
	
	action act;
	
	state def S {
		do action B;
		entry action A; then S1;
		
		state S1;
			accept s : Sig
			do action D
			then S2;
		
		state S2 {
			do send new Sig(T.s.x) to p;
			state S3;
		}
		accept Exit then done;
		
		transition
			first S1
			accept s : Sig via p
			do action D
			then S2.S3;
		
		transition T
			first S2.S3
			accept s : Sig
			if true
			do send s to p
			then S1;
				
		exit act;
	}
	
	state s0 {
  		state s1 {
    		state s2;
  		}
  		state s3 {
  			state s4;
  		}
  		transition t1 first s1.s2 then s3.s4;
	}
	
	state s parallel {
		state s1;
		state s2;
	}

	state s4 {
		do action a;
  		action c;
	}
	
	state s5 :> s4 {
  		do action b :>> c;
	}
	
}