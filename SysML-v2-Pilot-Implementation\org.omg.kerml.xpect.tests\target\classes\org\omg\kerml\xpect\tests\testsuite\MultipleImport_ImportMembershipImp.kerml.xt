//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
		File {from ="/src/DependencyMultipleImport.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
				File {from ="/src/DependencyMultipleImport.kerml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package test{
	public import OuterPackage2::C;
	//XPECT linkedName at C --> OuterPackage2.C
	//* XPECT scope at C ---
	   	   C.b.a1, test.C.b.a1, Test.try.a1, test.Test.try.a1, Test.b.a1, test.Test.b.a1,
		   C, Test, test.C, test.Test,
		   OuterPackage.A, OuterPackage.A.a1, OuterPackage.B, OuterPackage.B.b.a1, 
		   OuterPackage2.A, OuterPackage2.A.a1, OuterPackage2.B, OuterPackage2.B.b.a1,  
		   OuterPackage2.C, OuterPackage2.C.b.a1,
	--- */
	classifier Test specializes C {
		//XPECT linkedName at b --> OuterPackage.B.b
		//* XPECT scope at b ---	
			 C, C.b, C.b.a1, C.c, C.c.self, C.c.that, C.c.that.self, OuterPackage.A,
			 OuterPackage.A.a1, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1, OuterPackage2.A,
			 OuterPackage2.A.a1, OuterPackage2.B, OuterPackage2.B.b, OuterPackage2.B.b.a1, OuterPackage2.C,
			 OuterPackage2.C.b, OuterPackage2.C.b.a1, OuterPackage2.C.c, OuterPackage2.C.c.self,
			 OuterPackage2.C.c.that, OuterPackage2.C.c.that.self, Test, Test.b, Test.b.a1, Test.c, Test.c.self,
			 Test.c.that, Test.c.that.self, Test.self, Test.self.that, Test.try, Test.try.a1,
			 Test.try.self, Test.try.that, Test.try.that.self, b, b.a1, c, c.self, c.that, c.that.self,
			 self, self.that, test.C, test.C.b, test.C.b.a1, test.C.c, test.C.c.self,
			 test.C.c.that, test.C.c.that.self, test.Test, test.Test.b, test.Test.b.a1, test.Test.c,
			 test.Test.c.self, test.Test.c.that, test.Test.c.that.self, test.Test.self, test.Test.self.that,
			 test.Test.try, test.Test.try.a1, test.Test.try.self, test.Test.try.that,
			 test.Test.try.that.self, try, try.a1, try.self, try.that, try.that.self
		--- */
		feature try : b;
	}
}
