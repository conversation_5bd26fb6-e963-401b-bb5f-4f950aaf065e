# LangGraph + SysMLv2 编辑器架构文档

## 🏗️ 整体架构

本项目实现了一个完整的 SysMLv2 代码编辑器，集成了 Monaco Editor、Langium 语言服务器和 LangGraph 工作流系统。

```
┌─────────────────────────────────────────────────────────────────┐
│                    Next.js 应用框架                              │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   SysMLv2       │  │     Monaco      │  │   LangGraph     │  │
│  │   编辑器        │  │     Editor      │  │   工作流        │  │
│  │                 │  │                 │  │                 │  │
│  │ • 语法高亮      │  │ • 代码编辑      │  │ • AI 生成       │  │
│  │ • 智能补全      │  │ • 错误诊断      │  │ • 代码分析      │  │
│  │ • 语法验证      │  │ • 快捷键        │  │ • 需求验证      │  │
│  │ • 多标签页      │  │ • 代码折叠      │  │ • 代码重构      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Langium       │  │   Web Worker    │  │   API Routes    │  │
│  │   语言服务器    │  │   运行环境      │  │   后端接口      │  │
│  │                 │  │                 │  │                 │  │
│  │ • 语法解析      │  │ • 独立线程      │  │ • 工作流执行    │  │
│  │ • LSP 协议      │  │ • 不阻塞 UI     │  │ • 状态管理      │  │
│  │ • 错误检测      │  │ • 消息传递      │  │ • 历史记录      │  │
│  │ • 符号解析      │  │ • 性能优化      │  │ • 错误处理      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 📁 项目结构

```
apps/web/src/
├── app/
│   ├── api/langgraph/route.ts         # LangGraph API 路由
│   └── sysml/                         # SysML 应用路由
├── components/sysml/
│   ├── editor/
│   │   ├── SysMLCodeEditor.tsx        # 主编辑器组件
│   │   └── MonacoEditor.tsx           # Monaco 编辑器封装
│   ├── workspace/
│   │   ├── WorkspacePage.tsx          # 工作区主页面
│   │   ├── TabBar.tsx                 # 标签页管理
│   │   └── ProblemsPanel.tsx          # 问题诊断面板
│   └── langgraph/
│       └── WorkflowPanel.tsx          # AI 工作流面板
├── lib/
│   ├── sysml/
│   │   ├── langium-language-server.ts # Langium 语言服务器
│   │   ├── langium-worker.ts          # Web Worker 实现
│   │   ├── monaco-lsp-client.ts       # Monaco LSP 客户端
│   │   └── sample-code.ts             # 示例代码
│   └── langgraph/
│       ├── workflow-definitions.ts    # 工作流定义
│       └── workflow-executor.ts       # 工作流执行引擎
├── contexts/
│   └── SysMLContext.tsx               # 应用状态管理
└── types/
    └── sysml.ts                       # TypeScript 类型定义
```

## 🔧 技术栈详解

### 1. SysMLv2 编辑器核心

**Monaco Editor**
- 提供强大的代码编辑功能
- 支持语法高亮、代码补全、错误标记
- 集成 LSP 客户端实现智能功能

**Langium 语言服务器**
- 基于 Langium 框架构建的 SysMLv2 语言服务器
- 运行在 Web Worker 中，不阻塞主线程
- 提供语法解析、验证、补全等功能

**技术特性**:
- ✅ 完整的 SysMLv2 语法支持
- ✅ 实时语法验证和错误提示
- ✅ 智能代码补全和代码片段
- ✅ 多标签页编辑
- ✅ 问题诊断面板

### 2. LangGraph 工作流系统

**工作流定义格式**:
```typescript
interface WorkflowDefinition {
  id: string;                    // 工作流唯一标识
  name: string;                  // 显示名称
  description: string;           // 描述信息
  category: 'generation' | 'analysis' | 'validation' | 'transformation';
  entryPoint: string;            // 入口节点
  nodes: Record<string, WorkflowNode>;     // 节点定义
  edges: Record<string, string | string[]>; // 边定义
  dependencies?: string[];       // 依赖的其他工作流
}
```

**工作流节点格式**:
```typescript
type WorkflowNode = (state: WorkflowState) => Promise<Partial<WorkflowState>>;

interface WorkflowState {
  messages: BaseMessage[];       // 消息历史
  sysmlCode?: string;           // SysML 代码
  analysisResult?: string;      // 分析结果
  generatedCode?: string;       // 生成的代码
  validationResult?: string;    // 验证结果
  currentStep?: string;         // 当前步骤
  error?: string;               // 错误信息
  metadata?: Record<string, any>; // 元数据
}
```

## 🔄 工作流定义

### 1. SysML 代码生成工作流

```typescript
const sysmlCodeGenerationWorkflow = {
  id: 'sysml-code-generation',
  name: 'SysML 代码生成',
  category: 'generation',
  entryPoint: 'analyze_requirements',
  nodes: {
    analyze_requirements: async (state) => {
      // 分析用户需求，提取系统元素
      // 使用 LLM 理解自然语言描述
    },
    generate_structure: async (state) => {
      // 基于分析结果生成 SysML v2 代码结构
      // 包括 package、part def、attribute 等
    },
    validate_syntax: async (state) => {
      // 验证生成代码的语法正确性
      // 集成 Langium 语法验证
    },
    fix_syntax: async (state) => {
      // 修复语法错误
      // 使用 LLM 进行代码修复
    }
  },
  edges: {
    analyze_requirements: 'generate_structure',
    generate_structure: 'validate_syntax',
    validate_syntax: ['complete', 'fix_syntax'],
    fix_syntax: 'complete'
  }
};
```

### 2. 代码分析工作流

```typescript
const sysmlCodeAnalysisWorkflow = {
  id: 'sysml-code-analysis',
  name: 'SysML 代码分析',
  category: 'analysis',
  entryPoint: 'parse_code',
  nodes: {
    parse_code: async (state) => {
      // 解析 SysML 代码结构
      // 统计组件、属性、连接等
    },
    generate_report: async (state) => {
      // 生成详细的分析报告
      // 包括复杂度、设计模式、改进建议
    }
  },
  edges: {
    parse_code: 'generate_report',
    generate_report: 'complete'
  }
};
```

### 3. 需求验证工作流

```typescript
const requirementValidationWorkflow = {
  id: 'requirement-validation',
  name: '需求验证',
  category: 'validation',
  dependencies: ['sysml-code-analysis'],
  entryPoint: 'extract_requirements',
  nodes: {
    extract_requirements: async (state) => {
      // 从代码中提取需求和约束
    },
    validate_completeness: async (state) => {
      // 验证需求的完整性和一致性
    }
  },
  edges: {
    extract_requirements: 'validate_completeness',
    validate_completeness: 'complete'
  }
};
```

## 🔗 工作流关系定义

### 1. 依赖关系
```typescript
// 工作流可以声明依赖关系
const workflowWithDependencies = {
  id: 'advanced-analysis',
  dependencies: ['sysml-code-analysis', 'requirement-validation'],
  // ...
};
```

### 2. 工作流链
```typescript
// 顺序执行多个工作流
const workflowChain = [
  'sysml-code-generation',
  'sysml-code-analysis',
  'requirement-validation'
];

// API 调用
await executeWorkflowChain(workflowChain, inputData);
```

### 3. 并行执行
```typescript
// 并行执行多个工作流
const parallelConfigs = [
  { id: 'sysml-code-analysis', input: { sysmlCode: code1 } },
  { id: 'requirement-validation', input: { sysmlCode: code2 } }
];

// API 调用
await executeWorkflowsParallel(parallelConfigs);
```

## 🌐 Next.js 集成

### 1. API 路由设计

```typescript
// GET /api/langgraph?action=list
// 获取所有可用工作流

// GET /api/langgraph?action=status
// 获取工作流执行状态

// POST /api/langgraph
{
  "action": "execute",
  "workflowId": "sysml-code-generation",
  "input": {
    "message": "创建一个车辆系统模型",
    "sysmlCode": "existing code..."
  }
}

// POST /api/langgraph
{
  "action": "execute-chain",
  "workflowIds": ["sysml-code-generation", "sysml-code-analysis"],
  "input": { ... }
}
```

### 2. 前端组件集成

**WorkflowPanel 组件**:
- 显示可用的工作流列表
- 提供用户输入界面
- 执行工作流并显示结果
- 支持工作流链和并行执行

**集成到编辑器**:
- 右侧面板显示 AI 工作流
- 点击"AI 生成"按钮执行工作流
- 结果自动插入到编辑器中
- 实时显示执行状态和进度

### 3. 状态管理

```typescript
// SysML Context 集成
const { state, dispatch } = useSysML();

// 获取当前编辑器代码
const getCurrentCode = () => {
  const activeTab = state.editor.tabs.find(tab => 
    tab.id === state.editor.activeTabId
  );
  return activeTab?.content || '';
};

// 执行工作流
const executeWorkflow = async (workflowId, input) => {
  const result = await fetch('/api/langgraph', {
    method: 'POST',
    body: JSON.stringify({
      action: 'execute',
      workflowId,
      input: {
        sysmlCode: getCurrentCode(),
        ...input
      }
    })
  });
  
  // 处理结果并更新编辑器
};
```

## 🚀 使用指南

### 1. 基本使用流程

1. **启动应用**: `npm run dev`
2. **访问编辑器**: http://localhost:3000/sysml
3. **登录系统**: 使用测试账号
4. **创建项目**: 新建 SysML 项目
5. **编写代码**: 在编辑器中编写 SysML v2 代码
6. **AI 辅助**: 使用右侧工作流面板进行 AI 生成和分析

### 2. 工作流使用

**代码生成**:
1. 在工作流面板输入需求描述
2. 选择"SysML 代码生成"工作流
3. 点击"AI 生成"按钮
4. 查看生成的代码并插入编辑器

**代码分析**:
1. 在编辑器中编写或打开 SysML 代码
2. 选择"SysML 代码分析"工作流
3. 执行工作流查看分析报告

**工作流链**:
1. 点击"生成并分析"按钮
2. 系统自动执行代码生成 → 代码分析的工作流链
3. 查看完整的生成和分析结果

## 🔮 扩展能力

### 1. 自定义工作流

```typescript
// 添加新的工作流
const customWorkflow: WorkflowDefinition = {
  id: 'custom-workflow',
  name: '自定义工作流',
  category: 'transformation',
  entryPoint: 'custom_node',
  nodes: {
    custom_node: async (state) => {
      // 自定义逻辑
      return { /* 返回状态更新 */ };
    }
  },
  edges: {
    custom_node: 'complete'
  }
};

// 注册工作流
workflowRegistry.set(customWorkflow.id, customWorkflow);
```

### 2. 集成外部服务

```typescript
// 集成外部 API
const externalServiceNode = async (state: WorkflowState) => {
  const response = await fetch('https://external-api.com/analyze', {
    method: 'POST',
    body: JSON.stringify({ code: state.sysmlCode })
  });
  
  const result = await response.json();
  
  return {
    analysisResult: result.analysis,
    currentStep: 'complete'
  };
};
```

### 3. 高级工作流模式

```typescript
// 条件分支
const conditionalWorkflow = {
  edges: {
    analyze: ['simple_generation', 'complex_generation'],
    // 基于状态条件选择不同路径
  }
};

// 循环处理
const iterativeWorkflow = {
  nodes: {
    iterate: async (state) => {
      if (state.metadata.iteration < 3) {
        return { 
          currentStep: 'process',
          metadata: { 
            ...state.metadata, 
            iteration: (state.metadata.iteration || 0) + 1 
          }
        };
      }
      return { currentStep: 'complete' };
    }
  }
};
```

## 🎯 总结

本架构实现了：

✅ **完整的 SysMLv2 编辑器**
- Monaco Editor + Langium 语言服务器
- 语法高亮、智能补全、错误诊断
- 多标签页、问题面板等 IDE 功能

✅ **强大的 LangGraph 工作流系统**
- 模块化的工作流定义
- 支持依赖、链式、并行执行
- 完整的 API 和前端集成

✅ **无缝的 Next.js 集成**
- API 路由处理工作流执行
- React 组件展示工作流界面
- 状态管理和实时更新

✅ **可扩展的架构设计**
- 易于添加新的工作流
- 支持外部服务集成
- 灵活的工作流组合模式

这个架构为 SysMLv2 建模提供了完整的 AI 辅助开发环境！🚀
