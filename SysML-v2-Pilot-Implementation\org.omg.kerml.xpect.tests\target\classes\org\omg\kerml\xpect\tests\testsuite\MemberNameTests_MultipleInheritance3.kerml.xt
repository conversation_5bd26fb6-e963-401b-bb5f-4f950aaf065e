//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/
//XPECT noErrors ---> ""
package test{
	classifier <'A_Id'> A{
		classifier <'a_Id'> a {
			classifier <'aa_Id'> aa{}		
		}
	}
	classifier <'B_Id'> B specializes A{
		classifier <'b_Id'> b specializes a {}
	}
	classifier <'C_Id'> C specializes B{
		classifier <'c1_Id'> c1 specializes a {}
		classifier <'c2_Id'> c2 specializes b {}
	}
	classifier <'D_Id'> D specializes C::a {
		//XPECT linkedName at aa_Id --> test.A.a.aa
		//* XPECT scope at aa_Id ---
 		A, A_Id, A.a, A.a_Id, A_Id.a, A_Id.a_Id, A.a.aa, A.a.aa_Id, A.a_Id.aa, A.a_Id.aa_Id,
 		A_Id.a.aa, A_Id.a.aa_Id, A_Id.a_Id.aa, A_Id.a_Id.aa_Id,   
 		B, B_Id, B.a, B_Id.a, B.a_Id, B_Id.a_Id, B.a.aa, B.a.aa_Id, B.a_Id.aa_Id, B.a_Id.aa, B_Id.a.aa, B_Id.a.aa_Id, B_Id.a_Id.aa_Id, B_Id.a_Id.aa, 
 		B.b, B.b_Id, B_Id.b, B_Id.b_Id, B.b.aa, B.b.aa_Id, B.b_Id.aa, B.b_Id.aa_Id, B_Id.b.aa, B_Id.b.aa_Id, B_Id.b_Id.aa, B_Id.b_Id.aa_Id, 
		C, C_Id, C.a, C.a_Id, C_Id.a, C_Id.a_Id, C.a.aa, C.a.aa_Id, C.a_Id.aa, C.a_Id.aa_Id, C_Id.a.aa, C_Id.a.aa_Id, C_Id.a_Id.aa, C_Id.a_Id.aa_Id,
		C.b, C.b_Id, C_Id.b, C_Id.b_Id,
		C.b.aa, C.b.aa_Id, C.b_Id.aa, C.b_Id.aa_Id, 
		C_Id.b.aa, C_Id.b.aa_Id, C_Id.b_Id.aa, C_Id.b_Id.aa_Id,
		C.c1, C.c1_Id, C_Id.c1, C_Id.c1_Id, 
		C.c1.aa, C.c1.aa_Id, C.c1_Id.aa, C.c1_Id.aa_Id, 
		C_Id.c1.aa, C_Id.c1.aa_Id, C_Id.c1_Id.aa, C_Id.c1_Id.aa_Id,
		C.c2, C.c2_Id, C_Id.c2, C_Id.c2_Id, 
		C.c2.aa, C.c2.aa_Id, C.c2_Id.aa, C.c2_Id.aa_Id,
		C_Id.c2.aa, C_Id.c2.aa_Id, C_Id.c2_Id.aa, C_Id.c2_Id.aa_Id,
		D, D_Id, D.aa, D.aa_Id, D_Id.aa, D_Id.aa_Id, D.d, D.d_Id, D_Id.d, D_Id.d_Id, aa, aa_Id, d, d_Id,
		dd_Id, dd, test.D.dd_Id, test.D_Id.dd_Id, test.D.dd, test.D_Id.dd,
		D_Id.dd, D_Id.dd_Id, D.dd, D.dd_Id,
		ddd_Id, ddd, test.D.ddd_Id, test.D_Id.ddd_Id, test.D.ddd, test.D_Id.ddd,
		D_Id.ddd, D_Id.ddd_Id, D.ddd, D.ddd_Id,
		dddd_Id, dddd, test.D.dddd_Id, test.D_Id.dddd_Id, test.D.dddd, test.D_Id.dddd,
		D_Id.dddd, D_Id.dddd_Id, D.dddd, D.dddd_Id,
		test.A, test.A_Id, test.A.a, test.A.a_Id, test.A_Id.a, test.A_Id.a_Id, test.A.a.aa, test.A.a.aa_Id, test.A.a_Id.aa, test.A.a_Id.aa_Id,
 		test.A_Id.a.aa, test.A_Id.a.aa_Id, test.A_Id.a_Id.aa, test.A_Id.a_Id.aa_Id,   
 		test.B, test.B_Id, test.B.a, test.B_Id.a, test.B.a_Id, test.B_Id.a_Id, test.B.a.aa, test.B.a.aa_Id, test.B.a_Id.aa_Id, test.B.a_Id.aa, test.B_Id.a.aa, test.B_Id.a.aa_Id, test.B_Id.a_Id.aa_Id, test.B_Id.a_Id.aa, 
 		test.B.b, test.B.b_Id, test.B_Id.b, test.B_Id.b_Id, test.B.b.aa, test.B.b.aa_Id, test.B.b_Id.aa, test.B.b_Id.aa_Id, test.B_Id.b.aa, test.B_Id.b.aa_Id, test.B_Id.b_Id.aa, test.B_Id.b_Id.aa_Id, 
		test.C, test.C_Id, test.C.a, test.C.a_Id, test.C_Id.a, test.C_Id.a_Id, test.C.a.aa, test.C.a.aa_Id, test.C.a_Id.aa, test.C.a_Id.aa_Id, test.C_Id.a.aa, test.C_Id.a.aa_Id, test.C_Id.a_Id.aa, test.C_Id.a_Id.aa_Id,
		test.C.b, test.C.b_Id, test.C_Id.b, test.C_Id.b_Id,
		test.C.b.aa, test.C.b.aa_Id, test.C.b_Id.aa, test.C.b_Id.aa_Id, 
		test.C_Id.b.aa, test.C_Id.b.aa_Id, test.C_Id.b_Id.aa, test.C_Id.b_Id.aa_Id,
		test.C.c1, test.C.c1_Id, test.C_Id.c1, test.C_Id.c1_Id, 
		test.C.c1.aa, test.C.c1.aa_Id, test.C.c1_Id.aa, test.C.c1_Id.aa_Id, 
		test.C_Id.c1.aa, test.C_Id.c1.aa_Id, test.C_Id.c1_Id.aa, test.C_Id.c1_Id.aa_Id,
		test.C.c2, test.C.c2_Id, test.C_Id.c2, test.C_Id.c2_Id, 
		test.C.c2.aa, test.C.c2.aa_Id, test.C.c2_Id.aa, test.C.c2_Id.aa_Id,
		test.C_Id.c2.aa, test.C_Id.c2.aa_Id, test.C_Id.c2_Id.aa, test.C_Id.c2_Id.aa_Id,
		test.D, test.D_Id, test.D.aa, test.D.aa_Id, test.D_Id.aa, test.D_Id.aa_Id, test.D.d, test.D.d_Id, test.D_Id.d, test.D_Id.d_Id
	--- */
		classifier <'d_Id'> d specializes aa_Id {}
		classifier <'dd_Id'> dd specializes test::D_Id::aa_Id;
		classifier <'ddd_Id'> ddd specializes test::D::aa_Id; 
		classifier <'dddd_Id'> dddd specializes test::D_Id::aa;
		 
		
	}
}
