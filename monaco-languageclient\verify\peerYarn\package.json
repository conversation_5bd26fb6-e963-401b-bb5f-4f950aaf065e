{"name": "@typefox/peer-check-yarn", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": ">=20.10.0", "npm": ">=10.2.3", "yarn": ">=4.6.0"}, "volta": {"node": "22.16.0", "yarn": "4.6.0"}, "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-configuration-service-override": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-extensions-service-override": "~18.1.0", "@codingame/monaco-vscode-languages-service-override": "~18.1.0", "@codingame/monaco-vscode-localization-service-override": "~18.1.0", "@codingame/monaco-vscode-log-service-override": "~18.1.0", "@codingame/monaco-vscode-model-service-override": "~18.1.0", "monaco-languageclient-examples": "~2025.6.2", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1"}, "devDependencies": {"typescript": "~5.8.2"}, "scripts": {"build": "tsc --build tsconfig.json", "verify:ci": "yarn install && yarn run build"}}