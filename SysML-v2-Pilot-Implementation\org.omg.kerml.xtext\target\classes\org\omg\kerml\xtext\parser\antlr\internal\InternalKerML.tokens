'!='=132
'!=='=134
'#'=118
'$'=152
'%'=145
'&'=129
'('=98
')'=99
'*'=35
'**'=34
'+'=142
','=20
'-'=143
'->'=148
'.'=116
'..'=92
'.?'=149
'/'=144
':'=73
'::'=33
'::>'=76
':='=88
':>'=43
':>>'=80
';'=15
'<'=13
'<='=140
'='=87
'=='=131
'==='=133
'=>'=78
'>'=14
'>='=141
'?'=121
'??'=124
'@'=119
'@@'=137
'['=91
']'=36
'^'=146
'about'=23
'abstract'=41
'alias'=29
'all'=32
'and'=130
'as'=138
'assoc'=96
'behavior'=104
'binding'=100
'bool'=110
'by'=70
'chains'=66
'class'=94
'classifier'=57
'comment'=22
'composite'=60
'conjugate'=55
'conjugates'=46
'conjugation'=54
'connector'=97
'const'=63
'crosses'=79
'datatype'=93
'default'=89
'dependency'=18
'derived'=59
'differences'=50
'disjoining'=56
'disjoint'=47
'doc'=25
'else'=122
'end'=64
'expr'=108
'false'=113
'feature'=65
'featured'=69
'featuring'=83
'filter'=40
'first'=102
'flow'=115
'for'=30
'from'=19
'function'=106
'hastype'=135
'if'=123
'implies'=125
'import'=31
'in'=156
'inout'=158
'interaction'=114
'intersects'=49
'inv'=111
'inverse'=67
'inverting'=82
'istype'=136
'language'=27
'library'=38
'locale'=24
'member'=51
'meta'=139
'metaclass'=117
'metadata'=120
'multiplicity'=90
'namespace'=28
'new'=150
'nonunique'=72
'not'=147
'null'=151
'of'=68
'or'=127
'ordered'=71
'out'=157
'package'=39
'portion'=61
'predicate'=109
'private'=154
'protected'=155
'public'=153
'redefines'=81
'redefinition'=86
'references'=77
'rep'=26
'return'=107
'specialization'=52
'specializes'=44
'standard'=37
'step'=105
'struct'=95
'subclassifier'=58
'subset'=85
'subsets'=75
'subtype'=53
'succession'=101
'then'=103
'to'=21
'true'=112
'type'=42
'typed'=74
'typing'=84
'unions'=48
'var'=62
'xor'=128
'{'=16
'|'=126
'}'=17
'~'=45
RULE_DECIMAL_VALUE=6
RULE_EXP_VALUE=7
RULE_ID=8
RULE_ML_NOTE=10
RULE_REGULAR_COMMENT=5
RULE_SL_NOTE=11
RULE_STRING_VALUE=4
RULE_UNRESTRICTED_NAME=9
RULE_WS=12
T__100=100
T__101=101
T__102=102
T__103=103
T__104=104
T__105=105
T__106=106
T__107=107
T__108=108
T__109=109
T__110=110
T__111=111
T__112=112
T__113=113
T__114=114
T__115=115
T__116=116
T__117=117
T__118=118
T__119=119
T__120=120
T__121=121
T__122=122
T__123=123
T__124=124
T__125=125
T__126=126
T__127=127
T__128=128
T__129=129
T__130=130
T__131=131
T__132=132
T__133=133
T__134=134
T__135=135
T__136=136
T__137=137
T__138=138
T__139=139
T__13=13
T__140=140
T__141=141
T__142=142
T__143=143
T__144=144
T__145=145
T__146=146
T__147=147
T__148=148
T__149=149
T__14=14
T__150=150
T__151=151
T__152=152
T__153=153
T__154=154
T__155=155
T__156=156
T__157=157
T__158=158
T__15=15
T__16=16
T__17=17
T__18=18
T__19=19
T__20=20
T__21=21
T__22=22
T__23=23
T__24=24
T__25=25
T__26=26
T__27=27
T__28=28
T__29=29
T__30=30
T__31=31
T__32=32
T__33=33
T__34=34
T__35=35
T__36=36
T__37=37
T__38=38
T__39=39
T__40=40
T__41=41
T__42=42
T__43=43
T__44=44
T__45=45
T__46=46
T__47=47
T__48=48
T__49=49
T__50=50
T__51=51
T__52=52
T__53=53
T__54=54
T__55=55
T__56=56
T__57=57
T__58=58
T__59=59
T__60=60
T__61=61
T__62=62
T__63=63
T__64=64
T__65=65
T__66=66
T__67=67
T__68=68
T__69=69
T__70=70
T__71=71
T__72=72
T__73=73
T__74=74
T__75=75
T__76=76
T__77=77
T__78=78
T__79=79
T__80=80
T__81=81
T__82=82
T__83=83
T__84=84
T__85=85
T__86=86
T__87=87
T__88=88
T__89=89
T__90=90
T__91=91
T__92=92
T__93=93
T__94=94
T__95=95
T__96=96
T__97=97
T__98=98
T__99=99
