Manifest-Version: 1.0
Automatic-Module-Name: org.omg.kerml.xtext.ui
Bundle-ManifestVersion: 2
Bundle-Name: org.omg.kerml.xtext.ui
Bundle-Vendor: SysML v2 Submission Team
Bundle-Version: 0.51.0.202509041722
Bundle-SymbolicName: org.omg.kerml.xtext.ui; singleton:=true
Bundle-ActivationPolicy: lazy
Require-Bundle: org.omg.kerml.xtext,org.omg.kerml.xtext.ide,org.eclipse.
 xtext.ui,org.eclipse.xtext.ui.shared,org.eclipse.xtext.ui.codetemplates
 .ui,org.eclipse.ui.editors;bundle-version="3.5.0",org.eclipse.ui.ide;bu
 ndle-version="3.5.0",org.eclipse.ui,org.eclipse.compare,org.eclipse.xte
 xt.builder,org.eclipse.xtext.xbase.lib;bundle-version="2.14.0",org.ecli
 pse.xtend.lib;bundle-version="2.14.0";resolution:=optional,org.omg.sysm
 l;bundle-version="0.2.0",org.omg.kerml.expressions.xtext;bundle-version
 ="0.9.0",org.omg.kerml.expressions.xtext.ide;bundle-version="0.9.0",org
 .omg.kerml.expressions.xtext.ui;bundle-version="0.9.0",org.eclipse.xtex
 t.common.types.ui,org.eclipse.emf.ecore.editor
Import-Package: org.apache.log4j
Export-Package: org.omg.kerml.xtext.ui,org.omg.kerml.xtext.ui.internal,o
 rg.omg.kerml.xtext.ui.labeling,org.omg.kerml.xtext.ui.library,org.omg.k
 erml.xtext.ui.outline,org.omg.kerml.xtext.ui.quickfix
Bundle-Activator: org.omg.kerml.xtext.ui.KerMLActivator
Require-Capability: osgi.ee;filter:="(&(osgi.ee=JavaSE)(version=21))"

