//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.recursive.KerMLImportRecursiveTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File "Import_Recursive2.kerml" {}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="Import_Recursive2.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package RecursiveImport {
  
   public import P::**;

	//All X, Y and Z are visible here.
	x: X;	
	y: Y;
	z: Z;
	s: S1;
	t: T1;
	u: U1;
}
