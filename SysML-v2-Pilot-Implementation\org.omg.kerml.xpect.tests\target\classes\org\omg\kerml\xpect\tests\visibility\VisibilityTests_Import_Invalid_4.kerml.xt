//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

package Classes {
	public import VisibilityPackage;
	// XPECT errors ---> "Couldn't resolve reference to Classifier 'c_Public'." at "c_Public"
	classifier Try specializes c_Public{
		// XPECT errors ---> "Couldn't resolve reference to Type 'c_public'." at "c_public"
		feature feature4 : c_public;
	}
}

