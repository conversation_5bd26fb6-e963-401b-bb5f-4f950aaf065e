//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package Test3{
	public import VisibilityPackage::*;
	classifier A specializes c_Public_alias{
 		classifier AA specializes alias_protected{}
 		
 		// A::alias_protected does not resolve because qualified names currently require public visibility.
 		// XPECT errors --> "Couldn't resolve reference to Classifier 'A::alias_protected'." at "A::alias_protected" 		
 		classifier BB specializes A::alias_protected{}
 		// XPECT errors --> "Couldn't resolve reference to Classifier 'Test3::A::alias_protected'." at "Test3::A::alias_protected" 		
 		classifier CC specializes Test3::A::alias_protected{}
	}
}
