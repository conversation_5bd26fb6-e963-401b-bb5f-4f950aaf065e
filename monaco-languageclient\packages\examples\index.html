<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>monaco-languageclient Examples</title>
    <link rel="stylesheet" href="./style.css">
</head>

<body>
    <div style="padding: 5px">
        <h2>Examples</h2>

        This page contains all examples not requiring a backend.

        <h2>Monaco Editor Wrapper</h2>

        <h3>Langium</h3>

        Langium Grammar DSL: <a href="./ghp_langium_extended.html">Extended Mode</a> - <a href="./ghp_langium_classic.html">Classic Mode</a>
        <br>
        <a href="./ghp_statemachine.html">Langium Statemachine Client & Language Server (Worker)</a>
        <br>
        Localizations:
        <a href="./ghp_statemachine.html?locale=cs">cs</a> -
        <a href="./ghp_statemachine.html?locale=de">de</a> -
        <a href="./ghp_statemachine.html?locale=es">es</a> -
        <a href="./ghp_statemachine.html?locale=fr">fr</a> -
        <a href="./ghp_statemachine.html?locale=it">it</a> -
        <a href="./ghp_statemachine.html?locale=ja">ja</a> -
        <a href="./ghp_statemachine.html?locale=ko">ko</a> -
        <a href="./ghp_statemachine.html?locale=pl">pl</a> -
        <a href="./ghp_statemachine.html?locale=pt-br">pt-br</a> -
        <a href="./ghp_statemachine.html?locale=qps-ploc">qps-ploc</a> -
        <a href="./ghp_statemachine.html?locale=ru">ru</a> -
        <a href="./ghp_statemachine.html?locale=tr">tr</a> -
        <a href="./ghp_statemachine.html?locale=zh-hans">zh-hans</a> -
        <a href="./ghp_statemachine.html?locale=zh-hant">zh-hant</a>
        <br>

        <h3>Cpp / Clangd</h3>
        <a href="./ghp_clangd.html">Cpp Language Client & Clangd Language Server (Worker/Wasm)</a>
        <br>

        <h3>Application Playground</h3>
        <a href="./ghp_appPlayground.html">Application Playground</a>
        <br>

        <h3>JSON</h3>
        <a href="./ghp_browser.html">Language Client Pure Browser Example</a>
        <br>

        <h3>TypeScript</h3>
        <a href="./ghp_tsExtHost.html">TypeScript Extension Host Worker</a>
        <br>

        <h2>Monaco Editor React</h2>
        <a href="./ghp_react_appPlayground.html">React: Application Playground</a>
        <br>
        <a href="./ghp_react_statemachine.html">React: Langium Statemachine Language Client & Language Server (Worker)</a>
        <br>
    </div>
</body>

</html>
