//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	feature A {
		public import test::*;
		//XPECT linkedName at A --> test.A
		//*XPECT scope at A ---
		A, A.a, A.a.a, A.a.self, A.a.that, A.a.that.self, A.self, A.that, A.that.self,
		a, a.A, a.A.a, a.A.self, a.A.that, a.A.that.self, a.a, a.self, a.that,
		a.that.self, self, test.A, test.A.A, test.A.A.a, test.A.A.a.a, test.A.A.a.self,
		test.A.A.a.that, test.A.A.a.that.self, test.A.A.self, test.A.A.that, test.A.A.that.self,
		test.A.a, test.A.a.A, test.A.a.A.a, test.A.a.A.self, test.A.a.A.that,
		test.A.a.A.that.self, test.A.a.a, test.A.a.self, test.A.a.that, test.A.a.that.self, test.A.self,
		test.A.that, test.A.that.self, that, that.self
		--- */
		feature a : A;
	}
}
