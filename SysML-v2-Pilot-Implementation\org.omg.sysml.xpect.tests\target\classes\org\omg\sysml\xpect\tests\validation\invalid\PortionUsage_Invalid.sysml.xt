//* 
XPECT_SETUP org.omg.sysml.xpect.tests.validation.valid.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/Links.kerml"}
		File {from ="/library.kernel/Occurrences.kerml"}
		File {from ="/library.kernel/Objects.kerml"}
		File {from ="/library.kernel/Performances.kerml"}
		File {from ="/library.kernel/BaseFunctions.kerml"}
		File {from ="/library.kernel/ScalarValues.kerml"}
		File {from ="/library.kernel/ScalarFunctions.kerml"}
		File {from ="/library.systems/Items.sysml"}
		File {from ="/library.systems/Parts.sysml"}
		File {from ="/library.systems/Ports.sysml"}
		File {from ="/library.domain/Quantities and Units/ISQ.sysml"}
		File {from ="/library.domain/Quantities and Units/SI.sysml"}
		

	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/Links.kerml"}
		       	File {from ="/library.kernel/Occurrences.kerml"}
				File {from ="/library.kernel/Objects.kerml"}
				File {from ="/library.kernel/Performances.kerml"}
				File {from ="/library.kernel/BaseFunctions.kerml"}
				File {from ="/library.kernel/ScalarValues.kerml"}
				File {from ="/library.kernel/ScalarFunctions.kerml"}
				File {from ="/library.systems/Items.sysml"}
				File {from ="/library.systems/Parts.sysml"}
				File {from ="/library.systems/Ports.sysml"}
				File {from ="/library.domain/Quantities and Units/ISQ.sysml"}
				File {from ="/library.domain/Quantities and Units/SI.sysml"}
			}
		}
	}
END_SETUP 
*/
package PortionUsage_Invalid {	
	part p1 {
		snapshot s1;
		timeslice t1 {
			snapshot s2;
		}
	}
	
	// XPECT errors -->"Must be owned by an occurrence definition or usage." at "snapshot s2;"
	snapshot s2;
	// XPECT errors -->"Must be owned by an occurrence definition or usage." at "timeslice t2;"	
	timeslice t2;
}
