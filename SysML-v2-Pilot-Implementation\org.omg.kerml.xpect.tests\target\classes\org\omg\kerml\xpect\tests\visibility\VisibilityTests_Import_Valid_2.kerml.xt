//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

//XPECT noErrors ---> ""
package Test3{
	public import VisibilityPackage::c_Public::*;
	public import VisibilityPackage::c_Public::c_public::*;
	public import VisibilityPackage::c_Public_alias::*;
	public import VisibilityPackage::c_Public_alias::c_public::*;
	public import VisibilityPackage::c_Public_alias::alias_public::*;
	public import VisibilityPackage::c_clazz::*;
	public import VisibilityPackage::c_clazz::c_Public::*;
	public import VisibilityPackage::c_clazz::c_Public::c_publicc::*;
	public import VisibilityPackage::c_clazz::c_Public::c_publicc;
	feature try : c_publicc;
}
