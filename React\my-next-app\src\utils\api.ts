import { ApiResponse, User, Project, Namespace, SysMLDiagram } from '@/types';
import {
  mockLogin,
  mockRegister,
  mockProjects,
  mockValidateCode,
  mockGenerateSVG,
  mockAIGenerate,
  mockAIGenerateWithPrompt,
  mockDelay
} from './mockData';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';
const USE_MOCK_DATA = process.env.NODE_ENV === 'development' || !process.env.NEXT_PUBLIC_API_URL;

// 构建完整的提示词
function buildFullPrompt(userPrompt: string, currentCode?: string): string {
  let fullPrompt = `请根据以下要求生成或修改SysML v2代码：

用户要求：${userPrompt}

`;

  if (currentCode && currentCode.trim()) {
    fullPrompt += `当前代码：
\`\`\`sysml
${currentCode}
\`\`\`

请基于当前代码进行修改或扩展。`;
  } else {
    fullPrompt += `请生成新的SysML v2代码。`;
  }

  fullPrompt += `

要求：
1. 生成有效的SysML v2语法
2. 代码应该结构清晰、注释完整
3. 只返回代码，不要包含解释文字
4. 使用适当的SysML v2关键字和结构`;

  return fullPrompt;
}

// 通用请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const token = localStorage.getItem('auth_token');
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      return {
        success: false,
        error: data.message || `HTTP error! status: ${response.status}`,
      };
    }

    return {
      success: true,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '网络请求失败',
    };
  }
}

// 认证相关 API
export const authApi = {
  // 用户注册
  register: async (username: string, password: string): Promise<ApiResponse<User>> => {
    if (USE_MOCK_DATA) {
      return mockRegister(username, password);
    }
    return apiRequest<User>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  },

  // 用户登录
  login: async (username: string, password: string): Promise<ApiResponse<{ user: User; token: string }>> => {
    if (USE_MOCK_DATA) {
      return mockLogin(username, password);
    }
    return apiRequest<{ user: User; token: string }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  },

  // 获取当前用户信息
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    if (USE_MOCK_DATA) {
      const token = getAuthToken();
      if (token) {
        await mockDelay(300);
        return {
          success: true,
          data: {
            id: 'user-1',
            username: 'admin',
            email: '<EMAIL>',
            createdAt: new Date(),
          },
        };
      }
      return { success: false, error: 'No token found' };
    }
    return apiRequest<User>('/auth/me');
  },

  // 用户登出
  logout: async (): Promise<ApiResponse<void>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(200);
      return { success: true };
    }
    return apiRequest<void>('/auth/logout', {
      method: 'POST',
    });
  },
};

// 项目相关 API
export const projectApi = {
  // 获取用户的所有项目
  getProjects: async (): Promise<ApiResponse<Project[]>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(500);
      return {
        success: true,
        data: mockProjects,
      };
    }
    return apiRequest<Project[]>('/projects');
  },

  // 创建新项目
  createProject: async (name: string, description?: string): Promise<ApiResponse<Project>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(800);
      const newProject: Project = {
        id: 'project-' + Date.now(),
        name,
        description: description || '',
        createdAt: new Date(),
        updatedAt: new Date(),
        ownerId: 'user-1',
        namespaces: [],
      };
      mockProjects.push(newProject);
      return {
        success: true,
        data: newProject,
      };
    }
    return apiRequest<Project>('/projects', {
      method: 'POST',
      body: JSON.stringify({ name, description }),
    });
  },

  // 更新项目
  updateProject: async (id: string, updates: Partial<Project>): Promise<ApiResponse<Project>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(500);
      const projectIndex = mockProjects.findIndex(p => p.id === id);
      if (projectIndex >= 0) {
        mockProjects[projectIndex] = { ...mockProjects[projectIndex], ...updates };
        return {
          success: true,
          data: mockProjects[projectIndex],
        };
      }
      return { success: false, error: 'Project not found' };
    }
    return apiRequest<Project>(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // 删除项目
  deleteProject: async (id: string): Promise<ApiResponse<void>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(500);
      const projectIndex = mockProjects.findIndex(p => p.id === id);
      if (projectIndex >= 0) {
        mockProjects.splice(projectIndex, 1);
        return { success: true };
      }
      return { success: false, error: 'Project not found' };
    }
    return apiRequest<void>(`/projects/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取项目详情
  getProject: async (id: string): Promise<ApiResponse<Project>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(300);
      const project = mockProjects.find(p => p.id === id);
      if (project) {
        return {
          success: true,
          data: project,
        };
      }
      return { success: false, error: 'Project not found' };
    }
    return apiRequest<Project>(`/projects/${id}`);
  },
};

// 命名空间相关 API
export const namespaceApi = {
  // 创建命名空间
  createNamespace: async (projectId: string, name: string): Promise<ApiResponse<Namespace>> => {
    return apiRequest<Namespace>('/namespaces', {
      method: 'POST',
      body: JSON.stringify({ projectId, name }),
    });
  },

  // 更新命名空间
  updateNamespace: async (id: string, updates: Partial<Namespace>): Promise<ApiResponse<Namespace>> => {
    return apiRequest<Namespace>(`/namespaces/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // 删除命名空间
  deleteNamespace: async (id: string): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/namespaces/${id}`, {
      method: 'DELETE',
    });
  },
};

// 图相关 API
export const diagramApi = {
  // 创建图
  createDiagram: async (namespaceId: string, name: string): Promise<ApiResponse<SysMLDiagram>> => {
    return apiRequest<SysMLDiagram>('/diagrams', {
      method: 'POST',
      body: JSON.stringify({ namespaceId, name, content: '' }),
    });
  },

  // 更新图
  updateDiagram: async (id: string, updates: Partial<SysMLDiagram>): Promise<ApiResponse<SysMLDiagram>> => {
    return apiRequest<SysMLDiagram>(`/diagrams/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  },

  // 删除图
  deleteDiagram: async (id: string): Promise<ApiResponse<void>> => {
    return apiRequest<void>(`/diagrams/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取图内容
  getDiagram: async (id: string): Promise<ApiResponse<SysMLDiagram>> => {
    return apiRequest<SysMLDiagram>(`/diagrams/${id}`);
  },

  // 保存图内容
  saveDiagram: async (id: string, content: string): Promise<ApiResponse<SysMLDiagram>> => {
    return apiRequest<SysMLDiagram>(`/diagrams/${id}`, {
      method: 'PUT',
      body: JSON.stringify({ content }),
    });
  },
};

// SysML 相关 API
export const sysmlApi = {
  // 验证 SysML 代码
  validateCode: async (code: string): Promise<ApiResponse<{ isValid: boolean; errors: any[] }>> => {
    if (USE_MOCK_DATA) {
      return mockValidateCode(code);
    }
    return apiRequest<{ isValid: boolean; errors: any[] }>('/sysml/validate', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  },

  // 生成 SVG 图
  generateSVG: async (code: string): Promise<ApiResponse<{ svg: string }>> => {
    if (USE_MOCK_DATA) {
      return mockGenerateSVG(code);
    }
    return apiRequest<{ svg: string }>('/sysml/generate-svg', {
      method: 'POST',
      body: JSON.stringify({ code }),
    });
  },

  // AI 生成代码
  generateCode: async (prompt: string): Promise<ApiResponse<{ code: string }>> => {
    if (USE_MOCK_DATA) {
      return mockAIGenerate(prompt);
    }
    return apiRequest<{ code: string }>('/sysml/ai-generate', {
      method: 'POST',
      body: JSON.stringify({ prompt }),
    });
  },

  // AI 生成代码（使用自定义提示词和大模型）
  generateCodeWithCustomPrompt: async (
    userPrompt: string,
    currentCode?: string
  ): Promise<ApiResponse<{ code: string }>> => {
    if (USE_MOCK_DATA) {
      // 模拟使用自定义提示词的AI生成
      return mockAIGenerateWithPrompt(userPrompt, currentCode);
    }

    // 实际API调用，使用配置的大模型
    const fullPrompt = buildFullPrompt(userPrompt, currentCode);

    try {
      // 调用外部大模型API
      const response = await fetch('https://api.chatfire.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer sk-nuFHtM3uPjkLbXjv55inifI4wOpswe1WZu5E647rQCwOzqdc',
        },
        body: JSON.stringify({
          model: 'gemini-2.5-pro',
          messages: [
            {
              role: 'system',
              content: 'You are a SysML v2 expert. Generate valid SysML v2 code based on user requirements. Only return the code without explanations.'
            },
            {
              role: 'user',
              content: fullPrompt
            }
          ],
          temperature: 0.7,
          max_tokens: 2000,
        }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const generatedCode = data.choices?.[0]?.message?.content || '';

      return {
        success: true,
        data: { code: generatedCode },
      };
    } catch (error) {
      console.error('AI generation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '大模型调用失败',
      };
    }
  },

  // 获取代码补全建议
  getCompletions: async (code: string, position: number): Promise<ApiResponse<any[]>> => {
    if (USE_MOCK_DATA) {
      await mockDelay(200);
      return {
        success: true,
        data: [
          { label: 'part def', kind: 'keyword', insertText: 'part def ' },
          { label: 'attribute', kind: 'keyword', insertText: 'attribute ' },
          { label: 'connection', kind: 'keyword', insertText: 'connection ' },
        ],
      };
    }
    return apiRequest<any[]>('/sysml/completions', {
      method: 'POST',
      body: JSON.stringify({ code, position }),
    });
  },
};

// 工具函数
export const setAuthToken = (token: string): void => {
  localStorage.setItem('auth_token', token);
};

export const removeAuthToken = (): void => {
  localStorage.removeItem('auth_token');
};

export const getAuthToken = (): string | null => {
  return localStorage.getItem('auth_token');
};
