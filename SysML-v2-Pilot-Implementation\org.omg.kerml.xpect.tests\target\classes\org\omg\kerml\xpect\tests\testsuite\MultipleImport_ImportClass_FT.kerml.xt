//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
		File {from ="/src/DependencyMultipleMembership_Feature_FT.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage_Feature_FT.kerml"}
				File {from ="/src/DependencyMultipleMembership_Feature_FT.kerml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package test{
	public import OuterPackage2::C;
	//XPECT linkedName at C --> OuterPackage2.C
	//* XPECT scope at C ---
	C, C.b, C.b.a1, C.b.a1.self, C.b.a1.that, C.b.a1.that.self, C.b.self, C.b.that,
	C.b.that.self, C.c, C.c.self, C.c.that, C.c.that.self, C.self, C.that, C.that.self, D, D.b,
	D.b.a1, D.b.a1.self, D.b.a1.that, D.b.a1.that.self, D.b.self, D.b.that, D.b.that.self,
	D.c, D.c.self, D.c.that, D.c.that.self, D.self, D.that, D.that.self, D.try,
	D.try.a1, D.try.a1.self, D.try.a1.that, D.try.a1.that.self, D.try.self, D.try.that,
	D.try.that.self, OuterPackage.A, OuterPackage.A.a1, OuterPackage.A.a1.self,
	OuterPackage.A.a1.that, OuterPackage.A.a1.that.self, OuterPackage.A.self, OuterPackage.A.that,
	OuterPackage.A.that.self, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1,
	OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that, OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self,
	OuterPackage.B.b.that, OuterPackage.B.b.that.self, OuterPackage.B.self, OuterPackage.B.that,
	OuterPackage.B.that.self, OuterPackage2.B, OuterPackage2.B.b, OuterPackage2.B.b.a1,
	OuterPackage2.B.b.a1.self, OuterPackage2.B.b.a1.that, OuterPackage2.B.b.a1.that.self,
	OuterPackage2.B.b.self, OuterPackage2.B.b.that, OuterPackage2.B.b.that.self, OuterPackage2.B.self,
	OuterPackage2.B.that, OuterPackage2.B.that.self, OuterPackage2.C, OuterPackage2.C.b,
	OuterPackage2.C.b.a1, OuterPackage2.C.b.a1.self, OuterPackage2.C.b.a1.that,
	OuterPackage2.C.b.a1.that.self, OuterPackage2.C.b.self, OuterPackage2.C.b.that, OuterPackage2.C.b.that.self,
	OuterPackage2.C.c, OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self,
	OuterPackage2.C.self, OuterPackage2.C.that, OuterPackage2.C.that.self, test.C, test.C.b, test.C.b.a1,
	test.C.b.a1.self, test.C.b.a1.that, test.C.b.a1.that.self, test.C.b.self, test.C.b.that,
	test.C.b.that.self, test.C.c, test.C.c.self, test.C.c.that, test.C.c.that.self, test.C.self,
	test.C.that, test.C.that.self, test.D, test.D.b, test.D.b.a1, test.D.b.a1.self,
	test.D.b.a1.that, test.D.b.a1.that.self, test.D.b.self, test.D.b.that, test.D.b.that.self,
	test.D.c, test.D.c.self, test.D.c.that, test.D.c.that.self, test.D.self, test.D.that,
	test.D.that.self, test.D.try, test.D.try.a1, test.D.try.a1.self, test.D.try.a1.that,
	test.D.try.a1.that.self, test.D.try.self, test.D.try.that, test.D.try.that.self
	--- */
	feature D : C {
		//XPECT linkedName at b --> OuterPackage.B.b
		//* XPECT scope at b ---
		   C, C.b, C.b.a1, C.b.a1.self, C.b.a1.that, C.b.a1.that.self, C.b.self, C.b.that,
		   C.b.that.self, C.c, C.c.self, C.c.that, C.c.that.self, C.self, C.that, C.that.self, D, D.b,
		   D.b.a1, D.b.a1.self, D.b.a1.that, D.b.a1.that.self, D.b.self, D.b.that, D.b.that.self,
		   D.c, D.c.self, D.c.that, D.c.that.self, D.self, D.that, D.that.self, D.try,
		   D.try.a1, D.try.a1.self, D.try.a1.that, D.try.a1.that.self, D.try.self, D.try.that,
		   D.try.that.self, OuterPackage.A, OuterPackage.A.a1, OuterPackage.A.a1.self,
		   OuterPackage.A.a1.that, OuterPackage.A.a1.that.self, OuterPackage.A.self, OuterPackage.A.that,
		   OuterPackage.A.that.self, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1,
		   OuterPackage.B.b.a1.self, OuterPackage.B.b.a1.that, OuterPackage.B.b.a1.that.self, OuterPackage.B.b.self,
		   OuterPackage.B.b.that, OuterPackage.B.b.that.self, OuterPackage.B.self, OuterPackage.B.that,
		   OuterPackage.B.that.self, OuterPackage2.B, OuterPackage2.B.b, OuterPackage2.B.b.a1,
		   OuterPackage2.B.b.a1.self, OuterPackage2.B.b.a1.that, OuterPackage2.B.b.a1.that.self,
		   OuterPackage2.B.b.self, OuterPackage2.B.b.that, OuterPackage2.B.b.that.self, OuterPackage2.B.self,
		   OuterPackage2.B.that, OuterPackage2.B.that.self, OuterPackage2.C, OuterPackage2.C.b,
		   OuterPackage2.C.b.a1, OuterPackage2.C.b.a1.self, OuterPackage2.C.b.a1.that,
		   OuterPackage2.C.b.a1.that.self, OuterPackage2.C.b.self, OuterPackage2.C.b.that, OuterPackage2.C.b.that.self,
		   OuterPackage2.C.c, OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self,
		   OuterPackage2.C.self, OuterPackage2.C.that, OuterPackage2.C.that.self, b, b.a1, b.a1.self, b.a1.that,
		   b.a1.that.self, b.self, b.that, b.that.self, c, c.self, c.that, c.that.self, self, test.C,
		   test.C.b, test.C.b.a1, test.C.b.a1.self, test.C.b.a1.that, test.C.b.a1.that.self,
		   test.C.b.self, test.C.b.that, test.C.b.that.self, test.C.c, test.C.c.self, test.C.c.that,
		   test.C.c.that.self, test.C.self, test.C.that, test.C.that.self, test.D, test.D.b, test.D.b.a1,
		   test.D.b.a1.self, test.D.b.a1.that, test.D.b.a1.that.self, test.D.b.self, test.D.b.that,
		   test.D.b.that.self, test.D.c, test.D.c.self, test.D.c.that, test.D.c.that.self, test.D.self,
		   test.D.that, test.D.that.self, test.D.try, test.D.try.a1, test.D.try.a1.self,
		   test.D.try.a1.that, test.D.try.a1.that.self, test.D.try.self, test.D.try.that,
		   test.D.try.that.self, that, that.self, try, try.a1, try.a1.self, try.a1.that, try.a1.that.self,
		   try.self, try.that, try.that.self
  		--- */
		feature try: b;
	}
}
