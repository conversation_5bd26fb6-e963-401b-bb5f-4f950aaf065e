<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>React: Python Language Client & Language Server (Web Socket)</title>
        <link rel="stylesheet" href="style.css">
        <link rel="stylesheet" href="./resources/styles/views.css">
    </head>

    <body>
        <div class="exampleHeadelineDiv">
            <b class="exampleHeadeline">React: Python Language Client & Language Server (Web Socket)</b> - [<a href="../../index.html">Back to Index</a>]
        </div>
        <div id="react-root"></div>
        <script type="module">
            import { runPythonReact } from './src/python/client/reactPython.tsx';

            runPythonReact();
        </script>
    </body>

</html>
