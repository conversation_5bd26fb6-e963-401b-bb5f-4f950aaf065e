//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/
package Classes {
	public import VisibilityPackage::c_clazz::*;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Protect'." at "c_Protect"
	feature a : c_Protect;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Protect::c_protect'." at "c_Protect::c_protect"
	feature b : c_Protect::c_protect;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Package'." at "c_Package"
	feature c : c_Package;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Package::c_publicc'." at "c_Package::c_publicc"
	feature d : c_Package::c_publicc;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Public::c_protect'." at "c_Public::c_protect"
	feature e : c_Public::c_protect;
}
