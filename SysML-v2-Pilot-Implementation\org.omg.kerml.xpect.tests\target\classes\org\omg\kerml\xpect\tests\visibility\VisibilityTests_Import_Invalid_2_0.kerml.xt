//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private'." at "VisibilityPackage::c_Private"
	public import VisibilityPackage::c_Private::*;
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private::c_private'." at "VisibilityPackage::c_Private::c_private"
	public import VisibilityPackage::c_Private::c_private::*;
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private::c_public'." at "VisibilityPackage::c_Private::c_public"
	public import VisibilityPackage::c_Private::c_public::*;
}
	
