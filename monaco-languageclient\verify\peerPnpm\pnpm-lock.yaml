lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      monaco-languageclient-examples:
        specifier: ~2025.3.6
        version: 2025.3.6(@xterm/xterm@5.6.0-beta.98)
      vscode:
        specifier: npm:@codingame/monaco-vscode-extension-api@~15.0.2
        version: '@codingame/monaco-vscode-extension-api@15.0.2'
    devDependencies:
      typescript:
        specifier: ~5.8.2
        version: 5.8.2

packages:

  '@chevrotain/cst-dts-gen@11.0.3':
    resolution: {integrity: sha512-BvIKpRLeS/8UbfxXxgC33xOumsacaeCKAjAeLyOn7Pcp95HiRbrpl14S+9vaZLolnbssPIUuiUd8IvgkRyt6NQ==}

  '@chevrotain/gast@11.0.3':
    resolution: {integrity: sha512-+qNfcoNk70PyS/uxmj3li5NiECO+2YKZZQMbmjTqRI3Qchu8Hig/Q9vgkHpI3alNjr7M+a2St5pw5w5F6NL5/Q==}

  '@chevrotain/regexp-to-ast@11.0.3':
    resolution: {integrity: sha512-1fMHaBZxLFvWI067AVbGJav1eRY7N8DDvYCTwGBiE/ytKBgP8azTdgyrKyWZ9Mfh09eHWb5PgTSO8wi7U824RA==}

  '@chevrotain/types@11.0.3':
    resolution: {integrity: sha512-gsiM3G8b58kZC2HaWR50gu6Y1440cHiJ+i3JUvcp/35JchYejb2+5MVeJK0iKThYpAa/P2PYFV4hoi44HD+aHQ==}

  '@chevrotain/utils@11.0.3':
    resolution: {integrity: sha512-YslZMgtJUyuMbZ+aKvfF3x1f5liK4mWNxghFRv7jqRR9C3R3fAOGTTKvxXDa2Y1s9zSbcpuO0cAxDYsc9SrXoQ==}

  '@codingame/monaco-vscode-039b5553-0838-562a-97c2-30d6e54a7b42-common@15.0.2':
    resolution: {integrity: sha512-R4UijfvKYNS6QvjALl7MdSmYAD43C4zCIzR+BxduCCnpOiajpP6zGbJCtXENJQTEW5omKBPRxZlNWHO1i/cymg==}

  '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common@15.0.2':
    resolution: {integrity: sha512-Gh4IS1cpzu98ifRU2sMPZgoOXlp6YuS4tZZBU4rygcnCiJVlzSg1IGF3/qWvGsYb4XdCiF/88qP++hEuOBVftg==}

  '@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common@15.0.2':
    resolution: {integrity: sha512-xKAMWUmNibtc0Ae2Js0045JmamqpecGxGQOJzxz6fdwDocEb7vhjoaRnDINeoGvtd7kiXXCCUiE2WRhpiSRG4A==}

  '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common@15.0.2':
    resolution: {integrity: sha512-AAdh+y22C36NUNOyU5bRP2iNEzyGE6HXOy/5fuiCpFerV/rwMFtr0yeAq9haCPdFaYNspnkt3Fj8zSZzr+bU/w==}

  '@codingame/monaco-vscode-0f5ced28-abde-558b-8652-db8e7d4d64aa-common@15.0.2':
    resolution: {integrity: sha512-rK/EYD70iQ6XF4Lx7P4FSIbAe1X6BVFFu85mZSrJnWJdWlHZ8XU3HkxnY0dSZJc+4bBh3EvvbYVkoISSDjcuAg==}

  '@codingame/monaco-vscode-1021b67c-93e5-5c78-a270-cbdb2574d980-common@15.0.2':
    resolution: {integrity: sha512-u9dGcfjGCSPE2zy4k1LqstH/oydUBJ5o91K/OewJkMVj27JFlP1p5ynWrGQgLvLEEjcRXOaI8r43qAu6lZOK1w==}

  '@codingame/monaco-vscode-10418ae3-ee63-5700-a757-89cbe6564ee4-common@15.0.2':
    resolution: {integrity: sha512-ID0M2FbCsFdu3bR2id/GC8LxWR6gR0KDTFB9rMkmI/yNboGgO71G46R9LWnQ5iaw0MLS99zcTRn165hN5YT/gg==}

  '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common@15.0.2':
    resolution: {integrity: sha512-XBECAkPyaHKfIoNAPH6HHKRGZ9Rl8j/k+gxbsr3vYeAEEWUkEXCKgzA3mnQuqOA8krsU7vFwsjN/ywQU3LkbPA==}

  '@codingame/monaco-vscode-15626ec7-b165-51e1-8caf-7bcc2ae9b95a-common@15.0.2':
    resolution: {integrity: sha512-nfjgdgxGKjn9HfMUPa8lA/yNMwj9DoqeATBW8r+Ew8MBJTDJguXfehBXX8ul3x8I7QknwKwPnHx+yT2jvUiGsQ==}

  '@codingame/monaco-vscode-168b98e5-dc20-5807-b1f9-798f1f92b37f-common@15.0.2':
    resolution: {integrity: sha512-8eQ+qKVfgh9cojK+fsRrSRFLFHjfV4rwNPhAYp6fyB5JFJUdTyp8SDO2NJHcTJOsTIJDW3J+xRFgrcMEwB/N/g==}

  '@codingame/monaco-vscode-1ae7d696-d960-5ac6-97a3-9fe7c8c3a793-common@15.0.2':
    resolution: {integrity: sha512-W3m13XvLSTXH/o1Dm0o552QFZ92u4NtcGX8rFoATecUkLQPxnBKtUxqlhBMVXp/eBjzNPDLBIvfG9azu4dq35w==}

  '@codingame/monaco-vscode-1b4486de-4fe4-59c4-9e6d-34f265ff6625-common@15.0.2':
    resolution: {integrity: sha512-cZAfrwox+dWL0cs2W+V9i0q/96ZmhsqT0LdBNEyj3NGE/DTdhlQEC3hF3P9s/LFRwgKsECUfWCAnlhnUI2bfRw==}

  '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common@15.0.2':
    resolution: {integrity: sha512-WCcpvOSAmPT/nDilAsqYx6U+x1Yj48YZQ+gB+XQ5WJFW1QIl2HZkkTk2vLXCva6T5ESAsfMGGivDKAcPBs/yCw==}

  '@codingame/monaco-vscode-210e86a9-a91b-5273-b05d-390c776dde1f-common@15.0.2':
    resolution: {integrity: sha512-RXlx72SlAe7mI/8g5J5zgAARFzL5fCtlBo1suCEmiM5f9S4Zn+HVcEjkkchduq+0JNS5WVO5oH+24NQ/t/Lz7Q==}

  '@codingame/monaco-vscode-256d5b78-0649-50e9-8354-2807f95f68f4-common@15.0.2':
    resolution: {integrity: sha512-JV1rxYGOSsv2Hps1XpdeobUmI0wt4xcOKT0qFY1pzSt4PrJAwC6g1g+AU5W4Kh+Ya1CjqBJKkXxJ9Je/gecpOA==}

  '@codingame/monaco-vscode-29bc1406-2925-5b8f-b25e-d04a7772d896-common@15.0.2':
    resolution: {integrity: sha512-LnXzby9mbOgtn239CdyAgRoPJFqhEBujyX7tUb+1QxfQGBZXrnbPAoXtDkIJhx76rjWHnGR7fRBArgBa4SRt2A==}

  '@codingame/monaco-vscode-2a22c7b4-b906-5914-8cd1-3ed912fb738f-common@15.0.2':
    resolution: {integrity: sha512-FKtBIj8IeTBIb/pvFEFJxrMFltP/7ZUyOmkrbCvhoa3+0KVYrNhlL3vnldi+WOSTlRBWC7KHXXehtZ6oRB7ERA==}

  '@codingame/monaco-vscode-2e67e044-0db9-5fa6-8bd1-3737a7d586d4-common@15.0.2':
    resolution: {integrity: sha512-tM5GUYVKrgoqeY1eJ5yXxYPybiDwKWBzRZoOcrJcmlrUWXz8h3AoWRJ4Udt7FO2CjOCcyX0QzpWWypLhzEchAw==}

  '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common@15.0.2':
    resolution: {integrity: sha512-tljZkApxt/PCWFSWIZ1wfilXi/Du1PxQXkvZGRZSLkEaNyJQ3XEfJtjelebpXW7ZRnYmyLWfMC+eP4UwVcqAiQ==}

  '@codingame/monaco-vscode-2f06fe84-148e-5e6b-a7ca-c7989c5f128a-common@15.0.2':
    resolution: {integrity: sha512-kE0eB/sKEPljMj7LmbeQSs4NG0jxzmQ/luFWMEErnw7GqiyfTp8wl2XrPerLoQ51k2y496WzcO+KRAy+hUQm5w==}

  '@codingame/monaco-vscode-33833ac7-3af3-5e9d-8fb9-11838d852c59-common@15.0.2':
    resolution: {integrity: sha512-GPgJ0/gt1EV8OXpFKGD/DozVfQS/2nCJA9Q1f1fzM7Dr6Dv4anZaSvHGtd0nscVu3baEz73tGfBP8fOcn+Qtfw==}

  '@codingame/monaco-vscode-3607c442-ae7a-594b-b840-038378c24fef-common@15.0.2':
    resolution: {integrity: sha512-1PgXgl0LdCWM/mqOR7A9slWuTQpf7y9kivSxBIu6c0bJy3LxJeegNZ2EMZUenqR0imDRQTOh99FMY3L/M++65g==}

  '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common@15.0.2':
    resolution: {integrity: sha512-j2l3zM65AsJFIsTyOxzAX1XY20IOPsWGHLRUmoem9eDnaPhwfhHQ7ia/j6UQMveBH8xEOm4IIovedFhAIaGI+Q==}

  '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common@15.0.2':
    resolution: {integrity: sha512-6QMu8OiCoucyKCVNwue53TYGJsTpB+0lOe3W1CRON4rJyoP+jZVkU3i7Qj5q9OxpNY4cOiFT13sa60lWjyLtjQ==}

  '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common@15.0.2':
    resolution: {integrity: sha512-Fp1q86xW/RsvoFI3FDK2oTzjjH3L0ybwUpHpojvdXs7qSe9xIABRdC2H+j6wqA48RWGvNfnzopw5x/AsAf3IWA==}

  '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common@15.0.2':
    resolution: {integrity: sha512-897LD2JzqZ2mE5D1Z4gb9QIqeK1QdOkANFeXNfrcVBW3I/+Fl0Vx/JnKpDUKAd4L9B+aa7pztUkv1N5cjchItg==}

  '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common@15.0.2':
    resolution: {integrity: sha512-+o0ivOGA5SShRp22CcbmW+qHoiUan/VN5joo4lgJ8bdCsEgTa59hWCddzx0KHgTjQxLuDlV9CmwulrsEvhJK0g==}

  '@codingame/monaco-vscode-44b92f80-48ea-5562-a8d0-18a015f8d845-common@15.0.2':
    resolution: {integrity: sha512-aZTg+g286eUZfsu/Jxo70QOr8J4y6Jpwij/vu4Mh/i8nNiSF91TL67cWC3Cwbx2IERVyUSZxRiBYjWd25sRepQ==}

  '@codingame/monaco-vscode-45a408c6-90ed-5d8b-801d-f3f69c7a97f2-common@15.0.2':
    resolution: {integrity: sha512-f8dl3Cza5K5kjoCR3rwnxHCj8FNRRCW2eGxr8/MsFx2OQ6rvBKsV2JatBeJv6DpanBcG9LNh7EfyJCQr772GDg==}

  '@codingame/monaco-vscode-494be54c-bd37-5b3c-af70-02f086e28768-common@15.0.2':
    resolution: {integrity: sha512-uOilmDHGZXeO2y+RrgAFy1ZC6WERu4uF4fM6bl8nhfSeqeZ6ArlS3jsnaVCZp849nRqWGJbwlEro2PgUmJH5Bg==}

  '@codingame/monaco-vscode-4a316137-39d1-5d77-8b53-112db3547c1e-common@15.0.2':
    resolution: {integrity: sha512-NFzdN+mduRpev3iWvTWtErNXDFunp43Scvl46xAD1b9unoi+orcDAdUUNWbBltXFqMcM83C/xEskoUmnyYnEAw==}

  '@codingame/monaco-vscode-4ead9d5f-54da-5c5a-b093-32be4a84d711-common@15.0.2':
    resolution: {integrity: sha512-STEjQ+Wt3eK423yJGGY23v+FU2zoUXjZ1j9ktHMvoTfwWNyrCWE0Sae55bDSmintBrg+/O43X1kcck1uxkg5Rg==}

  '@codingame/monaco-vscode-523730aa-81e6-55d7-9916-87ad537fe087-common@15.0.2':
    resolution: {integrity: sha512-vCoR/kVApfn3IgKQNt3JM9Pb3I2t7iDvLKA7emek3LRQCuE+i2aLHIiQlhefcu5hCrNHhH1nfLO1P7lwiQkGlg==}

  '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common@15.0.2':
    resolution: {integrity: sha512-hCgaESRGEDQHskDlRg18POBprzoQhfV9QCTiC7O8mNHpDLLL+3Xi3Ffmk3jZAERvTP1dndp35Wnxsk5/WREgIQ==}

  '@codingame/monaco-vscode-5452e2b7-9081-5f95-839b-4ab3544ce28f-common@15.0.2':
    resolution: {integrity: sha512-L66aIitO0gZxVskynJBjfSJ6Kk4vmDbgG/5/1RJ104TsL0a02A3ojC1j+chU4b/NBNXhuRY7jiZqgdlLQwesmg==}

  '@codingame/monaco-vscode-55ed5668-e8ca-5335-a587-790bcf1200c0-common@15.0.2':
    resolution: {integrity: sha512-B9qbbn24k0zZTC10cltPD5+BlR14gEoG5Vp2crp2awxtd136L4O2Xxjdl7/4vN81zNMsXXkjRM3NK1EmPYLyzw==}

  '@codingame/monaco-vscode-625898ab-0e33-5b7f-a3ae-29c10271dc1c-common@15.0.2':
    resolution: {integrity: sha512-KSVLZMY0oEu6tAddYZ3cpYM8JsbKZUE0RXEk6WW2rTUL4offHp/LV1qNvQzdBH1uXU4ReFcgA8abF7k4mi6uxA==}

  '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common@15.0.2':
    resolution: {integrity: sha512-/LMFDaoD37l3hVZvbTSzljebcajUlQrqv/wea/sGsRYn40pxNYlbr1BS0A3U3wYdWk0iW6YxeWu6rx4YG40Zcw==}

  '@codingame/monaco-vscode-6845754f-e617-5ed9-8aaa-6ca3653a9532-common@15.0.2':
    resolution: {integrity: sha512-RvVJDncM+AbsxqCNsJT7W5o6+A1wunlGkB8beIXemirxteBbQkoVhd9JkcH+Tm8BJvqtDil4SKX3zf3MP6IaXA==}

  '@codingame/monaco-vscode-695440c8-a687-5594-b476-bbc7b36bafe9-common@15.0.2':
    resolution: {integrity: sha512-yq0ELxTe31ivgH9hno0urKw369aCQL60GuOeTn8i8MkdMesYDHH1t7f8vFZBVTFmDyDBSVcoE1oeQfts0/aCqg==}

  '@codingame/monaco-vscode-6980eeab-47bb-5a48-8e15-32caf0785565-common@15.0.2':
    resolution: {integrity: sha512-Lbwz1oVdLpn/cIU6/FeZSMM9zXDFL6fYQa1uP2rJRzQuehrZtHjHmKGLu4kW9XrvIAFrvxKTqmZIAaB5n+mNZQ==}

  '@codingame/monaco-vscode-6f9bc782-77e0-5716-93bd-b05210c768c5-common@15.0.2':
    resolution: {integrity: sha512-WKGCm94vwwc3tzDkBcljWVvb0FwHoQdiY3MoOidAolihork9I1kADJxI25/xdijEKVFDSy/td2sqYjqVtONlHA==}

  '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common@15.0.2':
    resolution: {integrity: sha512-d3oGEVxucAudJ/tSl1JteduABCKXii27ekbEm/t8Xo8A9ximAoTRJ5tX6ne04cbXLisWdTcTeSzxirR9BdNVqA==}

  '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common@15.0.2':
    resolution: {integrity: sha512-cb7YE+yQVMNGOh1KI7E0VPm6M1bUVz3magAnwDXiSNXfTSjg8yLKBbJFUoAE/g18q+FY1PXg6DOS4wajWVCmAg==}

  '@codingame/monaco-vscode-7bbc9e7d-eeae-55fc-8bf9-dc2f66e0dc73-common@15.0.2':
    resolution: {integrity: sha512-W0580lGhTml4h/QbxGMM5zMTpaamUdZ/I/EF7t0Z/XCI31QIHzrVdw+8oRStHvnjCrz1587EyeKoUOC4jo4vEA==}

  '@codingame/monaco-vscode-7f39b6f1-3542-5430-8760-0f404d7a7cee-common@15.0.2':
    resolution: {integrity: sha512-WaqYkXMFauFHesL2bCqE7os8rY4MxQYzjILZRpspFcwJCf0ohujrkAVwYoCLLt7HOnG/xKmruF8I5mJIPAvm8w==}

  '@codingame/monaco-vscode-805e9c2f-56b6-5a43-8b5b-d2dc2d3805fc-common@15.0.2':
    resolution: {integrity: sha512-Eua39mWE3K+kKLXm9uSsg6fvBJgVr7BPxWXRVPc17Rm2GjJ+/4WMIyue88KkFuBUCp/Y9sqaVhT5AiR+6DS/sg==}

  '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common@15.0.2':
    resolution: {integrity: sha512-c7qujmYpvQ9AmAvQoGjQ5GysvokdV8R+aDiosHDVtOfq9x3MVRkzdTNtlGL27UaauBKMpFFY7ZOM7rq/dnwZRw==}

  '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common@15.0.2':
    resolution: {integrity: sha512-rf25cJ0J5useUnN1WpRqK+JXRFa6/s9nZ4LxcX7mIPvCV9VArfL4Q6Mc/ulUHdDe7ComvfmCHi5/TZpBzqCdTg==}

  '@codingame/monaco-vscode-88aa9a78-75f5-5aaa-89e9-bbdd580b3b5c-common@15.0.2':
    resolution: {integrity: sha512-Ndc3v4zkGc6w1lsQ3W1a/WLt4tAcJkyR3Q9a+Un4o7v+JAtpBAHe+l3Vu8Iq6fvmwonn0n0gaiD+s91X9vFzfw==}

  '@codingame/monaco-vscode-897bebad-39df-57cb-8a57-36a271d038be-common@15.0.2':
    resolution: {integrity: sha512-VDX/WY6oaD7/CukLt+4n7T6ZGMU9gu0MP4pSrnSWTml4psiPcM4e/MvOQdx0Gh9fRD3/GwsvgHIxg31LvVAixg==}

  '@codingame/monaco-vscode-89a82baf-8ded-5b2f-b8af-e5fbd72dc5ad-common@15.0.2':
    resolution: {integrity: sha512-YzLGDNQThRPmo+EbxapqhKgP3j6yqk84GpCHh02bWR8Ya1wpsvlRWPBDpZAEI+JmsqKE5VIRgYyiXTp0c7jmsQ==}

  '@codingame/monaco-vscode-8ccb7637-50ea-5359-97bf-00015d7fe567-common@15.0.2':
    resolution: {integrity: sha512-QSDFeX/hTk5c+3EOIjioG234EQikOCaKtqUVDf/CWu8kVjetxdAPRAR2c269R5WbrtDPDSBvxhquJk8yvOjQGw==}

  '@codingame/monaco-vscode-9a1a5840-af83-5d07-a156-ba32a36c5c4b-common@15.0.2':
    resolution: {integrity: sha512-zop7+H3CEirMXdtZtjDQ3/+udWVWTf6hjHxGlGCrLKHIM1UoMci55SY2n+UVLsx2UTswo670SIjYge48CdLZPA==}

  '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common@15.0.2':
    resolution: {integrity: sha512-ka4sYGK3SQ9jVPXSUP+SoCp1bMTIc90TL87pu+VMI9yo2NKq5f85y51Aibq/Vo5DBovkHB/FNSZMGAkWKf/mUA==}

  '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common@15.0.2':
    resolution: {integrity: sha512-AVrF/77g2A/L38bJ2vxM4VnJsV+o24kMNOViWJoF8GT1VVDg3MjsWHLFPQOudGm64tWgx2X7CyVVGKBqXruuHg==}

  '@codingame/monaco-vscode-9d0168a3-519b-57f3-9bcc-89efc41f951a-common@15.0.2':
    resolution: {integrity: sha512-2UzdDFPya+d1LRrhyr+44/8IsIq5HvTfDOXQI6Yl3eGGAPQPTHbMd30tNl9uFftnG5Ha0AVYxwFDRJQ6Df14tA==}

  '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common@15.0.2':
    resolution: {integrity: sha512-BxdBZz6ScRuo7a4LIN98VRpXEJKEx9fUyJ0Kri1ze/QsrwnGBz2IGF7jFoTP3mTzUtKKQPBuvghbzvK8z0tvLQ==}

  '@codingame/monaco-vscode-a2719803-af40-5ae9-a29f-8a2231c33056-common@15.0.2':
    resolution: {integrity: sha512-D/Bos7Tx8sCsTFcnyDAO4ZlWIJuKnjY1mSKCb31bwcUDb0Izt+3Zsw+xN/4OOsHqZobskvA3kaJ3g22sCMwP9A==}

  '@codingame/monaco-vscode-a3f28a41-ba19-5a7e-8f5a-d6c1403b507d-common@15.0.2':
    resolution: {integrity: sha512-QNUfOAH6utrekJoKIO9s+wLPLQK4VEQUxrFHD7Rrv2PHj8YIcOM9sKfSuICilLmDQz9tp3RRh9kO6BVjXMj5jQ==}

  '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common@15.0.2':
    resolution: {integrity: sha512-0mE/43ivJDlyCGrFavJml7GWJ5y5dM9Lwe75pRzCmeNXGKTUNrgDwX/wvAbZH5jo9OOLVqpHUmpsIqOfyG59RA==}

  '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common@15.0.2':
    resolution: {integrity: sha512-mTMFpzR6ZY5eimtEbBUjlvnDgTnuU8SrYnmXQylXagoZ5ea/bDhZA/hh2WlgLrkwUJ+FgCrQUbyv4VQ40fEOcw==}

  '@codingame/monaco-vscode-ab07af84-42e8-5a0f-8aef-b83fb90ede21-common@15.0.2':
    resolution: {integrity: sha512-wrFz50JBi7mdMCnmAQ5yQrl1W0ai7z84kKw2tqWlhTI8gAPqxfJqzXDG/ky4sRIns5kWQuU5yBYi3cYUbKefNg==}

  '@codingame/monaco-vscode-abed5a84-8a82-5f84-9412-88a736235bae-common@15.0.2':
    resolution: {integrity: sha512-o+IJzhVvpVcfAEF0mNg9iUHyzYIdfKTftC6aojVv/xZAPf2M80dURmhCyCcGjxztGDHZl7e84cm0CSUOQ26RNw==}

  '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common@15.0.2':
    resolution: {integrity: sha512-pmr0Vc+jmtZPP1tkyicMmTNclw7DQwdkkpSm0nN8JpnlRlmEyaw9nsuVODsjqcZhjOMUSDAACndgsqjN49EFYA==}

  '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common@15.0.2':
    resolution: {integrity: sha512-RMubUplntAaj2hSj0PnPD3fkjspZ19dIFobkwtFMMZeVQqHQ74HcneiVfXr1yujhW8LlCYagp02teSjW6go9GA==}

  '@codingame/monaco-vscode-api@15.0.2':
    resolution: {integrity: sha512-hoSiyyE1Jm+Gx7b4sQioE25CniuODomOqIsjkZwQcMb+N/FrTmSrSGDxx0GIZrvsLCmowI4S4vILDUKB4UAP7w==}

  '@codingame/monaco-vscode-b1249c5b-1339-5278-b002-746f08105c6d-common@15.0.2':
    resolution: {integrity: sha512-k4t1jvaxq8+1GBlwR0TuCHCHsVe/WRJy50e4Aj55W1IRaYyo8q4mw6aNbxN/ZoqFcxLjKCDiff70yeGjfexIwQ==}

  '@codingame/monaco-vscode-b1e8558d-1726-5299-bc75-e43ee6d1a124-common@15.0.2':
    resolution: {integrity: sha512-JIwDNq6ta1ZO7ri3E1wjJvRsQtkY/ZhT4eCa1d8ipy5w9TsaJuLvPFPsjBGax2IA6GTCrxLxKOWsHfyBUQA0vw==}

  '@codingame/monaco-vscode-b71b5434-ce96-5581-8993-e8da380bd63f-common@15.0.2':
    resolution: {integrity: sha512-6A11la9Qz30uYzfNwNKyoi5xaA7sq3mrzHLGfNJmkjCKyp+ntTBiULkNxchNOqTbNXR0VD8M2QSoFkBAvk0pIA==}

  '@codingame/monaco-vscode-base-service-override@15.0.2':
    resolution: {integrity: sha512-NApfW6rJUS/JmbGCcLpywRRUEhc2hXT3hbymjQGoJ/SfIAssV+rS0B+3vV2/PVc5It+lz+gK9s9w76CHCAsbgQ==}

  '@codingame/monaco-vscode-bba55be6-41a2-50cd-a3cc-8bafa35bfa89-common@15.0.2':
    resolution: {integrity: sha512-KlcPmFOl4wkv5MVxcZ1c+GJqbnXn5wxhhWTbq73cIaMTz58FV1g5XlRfb87+W9Lekr8jur+4uficzSFYf8Dusw==}

  '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common@15.0.2':
    resolution: {integrity: sha512-aM1FtyF1FU0Lqm0l2jZIkhpLgh5yX+tUksKLFxVfSgOpSOaGkZFxQgzRmekJQhgFgrRdukf7IcNb/dpk4GU/lQ==}

  '@codingame/monaco-vscode-be8ddbb5-094a-5657-b1cc-fe106c94c632-common@15.0.2':
    resolution: {integrity: sha512-ghZ5ejDZhlH1FJz5j4NnY+hhmMfGvFiV2aw9LyaqssrplO22jvxEHMA9lM0xHEM4WcDKarsv+VaQ9tVIHUGpHQ==}

  '@codingame/monaco-vscode-bf94ddb5-e436-506a-9763-5ab86b642508-common@15.0.2':
    resolution: {integrity: sha512-ucOOfIxAFe5lFD2dwf7YMesclMzlsW+ilZSqqFmdcmb3EF4aQ+WuemBQl34bmmn5L6jiGOvaN5UfYdv5DoLU/Q==}

  '@codingame/monaco-vscode-bulk-edit-service-override@15.0.2':
    resolution: {integrity: sha512-QISWI6Q0BPPxatVPtI63dQHXP1VOHrO6JKD9aqdXZo7+SL7bpszsqOvbejpf0J2WUIjOdvle+7mx0R0OYHLLjg==}

  '@codingame/monaco-vscode-c3b3387c-7bce-5b8f-9e14-bebeb636f1c8-common@15.0.2':
    resolution: {integrity: sha512-tgNGU+PorIwbxCWEHjEwZMDYKAtMp7vu9IDkZZrFXx68Ol9n6949+3w5j3To8tMg6w6e1Y9YmpdDHfDXMVAXsQ==}

  '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common@15.0.2':
    resolution: {integrity: sha512-EcN1gJVQTqZ+Hj6Ij/w2zveDrRZV25HS8y/ufJ5xabm7f7b+3I2wH9t7UoYK1cTLJHwV0Ek2QW5QR2WbkCLxww==}

  '@codingame/monaco-vscode-cea4d01f-6526-5c2f-8b09-b168fead499f-common@15.0.2':
    resolution: {integrity: sha512-XqZN0s440ipfNmztYZKZ5rexUydQCNNnw9UsVhsh2VZYSWdUjmojxz6dn5uphHgWdIZv9/TiuE1VNtdEEs4tZA==}

  '@codingame/monaco-vscode-configuration-service-override@15.0.2':
    resolution: {integrity: sha512-pC63gLhK4dszUSGWj1kjKL09K5GPkhX+IhshnZpbpzVIiLIdsKAOoKiRZylx3v6288SMWwvpX6ME7Of6Ol7V7A==}

  '@codingame/monaco-vscode-cpp-default-extension@15.0.2':
    resolution: {integrity: sha512-a/4uYe4gLVcFJyjeHXp/JQzFz1LB9v+eH3DJjRaWYP419EhIHbmfDdC6P8FvvdKy3UJ8hUgVzSfOZ6g2G2Hc+A==}

  '@codingame/monaco-vscode-d481a59e-259c-524e-bee1-76483d75d3a1-common@15.0.2':
    resolution: {integrity: sha512-nO+nKlZfoozH6WjGCnkS//8mQxrL4iI5P84vtmriYWiOz9Fj3CJVKFmu2ZBLV7ilD+QIXigXCa9vUtfH2JWNkw==}

  '@codingame/monaco-vscode-d56fc266-2991-5e70-8f69-134ad70e1700-common@15.0.2':
    resolution: {integrity: sha512-AiYTjTrbpvbyNR9G95cUmBvevNZKapKQS0IvRzFvBsI9ciZTvbTjsGWrj72m+trttUhiQ2ioh1qWpBfid7pPwg==}

  '@codingame/monaco-vscode-d609a7d3-bf87-551a-884f-550a8b327ec5-common@15.0.2':
    resolution: {integrity: sha512-J5YNlKjwAm/nyESCm96GqezvOmMyRrfBGhvpAWAcln5awLFETTlr7x3XbNgHBLCqi3MnpdZTY+W+pR4zRmTCKA==}

  '@codingame/monaco-vscode-d7f659f5-da33-5ea8-a3b8-9b94f2cf5f33-common@15.0.2':
    resolution: {integrity: sha512-PLfB4PGtrRACUPhFdN5PtMkli9mC/xScrPdapS7f2LrLoGfiiX1qi3jn/ovCBCNd/45CCBFFK6TvJoHYp2PImQ==}

  '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common@15.0.2':
    resolution: {integrity: sha512-T13joFSRpmrmC7L0IvNb+xP1r061v0OMf8E/fbLbO7+LweuodX4nulkbBvSp0Rgx/75wcsT3jQ2LoHA8rZZHjg==}

  '@codingame/monaco-vscode-d987325e-3e05-53aa-b9ff-6f97476f64db-common@15.0.2':
    resolution: {integrity: sha512-QZqXxOk85tHMQ1f+s+pI3vdl3XW4RMU+JWbifqfKbX/EaC7rbmlGC1qCrXMCt4IIDUkDfgANg6QcrnaXZpK/6Q==}

  '@codingame/monaco-vscode-debug-service-override@15.0.2':
    resolution: {integrity: sha512-2f1IFPwLjnaDUFL0/HMg+hILzfHArTwcVOTw4LWG6KDFkhtAJLXpuEAkUk9g3/25/Flcd9fsN8EXFwLjEhUwRw==}

  '@codingame/monaco-vscode-e59ecb8c-db32-5324-8fe4-cf9921fd92b8-common@15.0.2':
    resolution: {integrity: sha512-76p/DFTjmzcngmxnGxA3OybJhaTwcBX6j3tLObufLidzOURKrx+GfRZko7W5ZcteDToVs9EGQR1fALXkJYpOlg==}

  '@codingame/monaco-vscode-e72c94ca-257a-5b75-8b68-5a5fa3c18255-common@15.0.2':
    resolution: {integrity: sha512-W1CSlxHBEyCwhhc5Q7QUEb4YLebISFa8h4Ae/jEAMefxCaD0+DXArBh82IDlcNNesmnYBnm2pXW3NsLCpr1/Og==}

  '@codingame/monaco-vscode-ea14e352-8f1c-5569-b79a-8a96a53e8abe-common@15.0.2':
    resolution: {integrity: sha512-5fW7UH7G5UQZz3rgxu7f+ppNKi5bhCJKsyCh2ST/EHR+hls6lYXYvcsi41lJZivc4KGAEYLh1dx6pd6NDoo6Ig==}

  '@codingame/monaco-vscode-eb7d5efd-2e60-59f8-9ba4-9a8ae8cb2957-common@15.0.2':
    resolution: {integrity: sha512-L9+4QDctDTFUVGEOjyYuFRXvsuM1HU8ttPOEpBIFgkbNsi/0xqV2Lh/wKoECeWYPKZGfCXVaR8pg5d/63bOmEQ==}

  '@codingame/monaco-vscode-eba0b9b3-174c-5dae-9867-a37810ca1808-common@15.0.2':
    resolution: {integrity: sha512-rGIV6WdMp2LlpB6dA04TOiX48OpX/QFmevxL6V9RCz7eLOdWthIM8urq761xQI3wEUqQoZPNXPLotDRO0gTFFA==}

  '@codingame/monaco-vscode-editor-api@15.0.2':
    resolution: {integrity: sha512-to1b/4bqfMDUTYZzkDZUFxJr7xYWDZ1lRKPKCyqhxt8TRShAIsItEQ6rZ3HrhjKJR4T1prwDyG1N4eCObFX4/g==}

  '@codingame/monaco-vscode-editor-service-override@15.0.2':
    resolution: {integrity: sha512-utrHOJYb2++T1biVbDzSMbonuex6CMy8jZB1c74BYbl0TAHHazpKX3BRIg6u9A2QMGJ1smibsC+pe+XI3+AXAQ==}

  '@codingame/monaco-vscode-environment-service-override@15.0.2':
    resolution: {integrity: sha512-cTZHfj0oPKzyKJu8Gjc4E2oh2M6vHsFbmdrwtQ1+yCkOkmLaSjza7G6v6OBrGGhvdSTVWb8Fv1fWJSiBL2rpBA==}

  '@codingame/monaco-vscode-explorer-service-override@15.0.2':
    resolution: {integrity: sha512-ojv0N/w+3gwoBfhusIpkzQo7stjgJIfmSL0RNOghxNwiovl+1oB2IsclYWWqMro/yzDhvPQGjIVBIjJLrtEXpg==}

  '@codingame/monaco-vscode-extension-api@15.0.2':
    resolution: {integrity: sha512-qdt2VEKR6aI5Z7KAw5ex/dXD72k6lmKi4V+Po++De0YdSPPzqo4+KaNLiHqcM2FWXbgox4J+qE8ul9CZ131SMg==}

  '@codingame/monaco-vscode-extensions-service-override@15.0.2':
    resolution: {integrity: sha512-7UtMt7FwcWQ05vlaMNXsFWRP3nwktKiGNqf68Dg3cAmcKMNRP1ajR5I/CnRdotzOoXd7v1Jjb4yygHS9+evBWg==}

  '@codingame/monaco-vscode-f405f7ba-995b-5117-bc17-5bc7f77d92e9-common@15.0.2':
    resolution: {integrity: sha512-wlmIHh0VobtQYcPXZyxZoOJIvAZc5MMHIAgrO39X1O1BUgINcCmJChiFFlFJ6OocKqGksF5TBkKfeoD/j7F4XQ==}

  '@codingame/monaco-vscode-f6ab89b2-83b0-5a43-8772-cb0eafa650b5-common@15.0.2':
    resolution: {integrity: sha512-YB+VoM7nu08w/4JpjZyqKD1EDmmdsTMSsoBWXIzNIH7sbmJLeXEQwcJ0jPfyLXSFu8jeubDZhidoHTa7MIONRg==}

  '@codingame/monaco-vscode-fc985c90-0334-5b62-88bc-73e2efa0b80b-common@15.0.2':
    resolution: {integrity: sha512-1p8jf03EnVsLkNFUtAJwwITBoN3kh8DPYihsqvznG7foFlR3XI6gj34oCSc23AlFPNvRZC8O+FuKCZ7o777+hg==}

  '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common@15.0.2':
    resolution: {integrity: sha512-NQYvXrruzWh6vKYfbrEvLBzthF1ffcPZcLuspc0GW11J9+ZJO1X1JXIymO69ANrkYJGGdLc/6E67LU/dCmku/Q==}

  '@codingame/monaco-vscode-ff9fa663-eae3-5274-8573-c2b918871e4b-common@15.0.2':
    resolution: {integrity: sha512-ot6TbkgL2YSkWbXU8FKrFji9z/Rvwg8STJibZ9mig0+VdhJKNnpJxsHqFcdFSGb5aHAcuUZ8i0RyDw1nakSQZg==}

  '@codingame/monaco-vscode-files-service-override@15.0.2':
    resolution: {integrity: sha512-gyT52tpPZlie9yM2y4a8Gd37Y12G6eIOy0b5FhcBd2l5S0b1APLavdf+31zDVQxZ6USjmCyZBURl6h19Y11JLg==}

  '@codingame/monaco-vscode-groovy-default-extension@15.0.2':
    resolution: {integrity: sha512-ltJ/SpZ/iCxaDi4wk02D868hpt7bKYO6/jHxVGFX9CCyPXQmjvx2+5zPbqlpWpP/26xedK6br3agmb3Ms4wAtw==}

  '@codingame/monaco-vscode-host-service-override@15.0.2':
    resolution: {integrity: sha512-Vp3hcSar94Td1AEkrs6Q3aVgKx0+FEIesU132xNms7tJvDdMAlP1kH0c7lqZGjZdk91mml+r2pzQacjLLwQ5zg==}

  '@codingame/monaco-vscode-java-default-extension@15.0.2':
    resolution: {integrity: sha512-QLsGk/6lcMODlfBMJM9Z4acQwiSavhEODQghuPtKQuqzd/7tGeL5+tl68b6Wusg8oX3thI8TuKRaXa9SagkH/Q==}

  '@codingame/monaco-vscode-javascript-default-extension@15.0.2':
    resolution: {integrity: sha512-/W+rv14rdtp/Cz1/FVkkUPP2Y+eBvMB0bCFWTnwzoPbS/5RXq9HoqS3MpohvMiLA/GEb6nGy+vV0AJtwLR89nw==}

  '@codingame/monaco-vscode-json-default-extension@15.0.2':
    resolution: {integrity: sha512-S8EFZ9W4PBtRFshCmj3zYRrJQP56tcSCMAb0WRuLZljQCBPr02NAeyzSmYQCyku0nFkc3LghtLwAqna84h44mw==}

  '@codingame/monaco-vscode-keybindings-service-override@15.0.2':
    resolution: {integrity: sha512-gsSLHOGEY/QLwmqE1W8hzWBDmEY/nK11HN6rfvuVJhbkI7i5pqNLtFaNWus1iTw8gB+xjWXlziC+vg8AWjqgNw==}

  '@codingame/monaco-vscode-language-pack-cs@15.0.2':
    resolution: {integrity: sha512-/cJyjswDFE+gJn0sRXdiLwjfqpS73Z2dFKQrv8xsX7UZDvn7G2Fvj+y9MUano0kLcdYQcNybUrOFpbY6tQbr5g==}

  '@codingame/monaco-vscode-language-pack-de@15.0.2':
    resolution: {integrity: sha512-0kBqcXL6Y1+maU567/4N648AEfBFuS3emIYfh2BHsYO/GK6tVh1CabiocxD0Vwy110FivMt9QaD39UK0ESM+Bg==}

  '@codingame/monaco-vscode-language-pack-es@15.0.2':
    resolution: {integrity: sha512-MKBr5Rgwdn6ikLDPbS3OPcVoQyrHk05K5znv8n1Aa8vplwUMGIwhVGQruvyZuDhwb/tHj42pvMcfI6KR1BXG/Q==}

  '@codingame/monaco-vscode-language-pack-fr@15.0.2':
    resolution: {integrity: sha512-uW1B8sDiiXf8jvYGNcSPCm7LLbb4YkRHwN4eM3c2W7xfRlR313ajqRYe2HCN7zp5ghAZEUS9pJytAMK2nGKO4w==}

  '@codingame/monaco-vscode-language-pack-it@15.0.2':
    resolution: {integrity: sha512-nkkzdhHmlZQDBmO/ff+lfkP7/7VSsEK/myNpELLNGqylFE6Hz0odtlQZaldF2VA6BMj6z3cacBMn5Ru3Qr2cTw==}

  '@codingame/monaco-vscode-language-pack-ja@15.0.2':
    resolution: {integrity: sha512-IvH80s09OBvHNNy9e+ILQoWUnOxqMi+DLDch/y1oObpGQNR3relszHM56TvEPljc6DbDafh02NwiC154vByHmA==}

  '@codingame/monaco-vscode-language-pack-ko@15.0.2':
    resolution: {integrity: sha512-38YoVtGQUQogUSOokyVZia5c2J+Dv7YK90kREusZIPpH7bg0zkO85Dpa6HjgNf1LmlCI59mOdqRdXeiE/4zQ/A==}

  '@codingame/monaco-vscode-language-pack-pl@15.0.2':
    resolution: {integrity: sha512-V+nwSAEzoGyX48y29zOf7g2Shl2VVaZJ6jl4KDb/C427S5+IkyC4q3htyJS0YxMcBAVBkp23/t+A0F7+8pMmxw==}

  '@codingame/monaco-vscode-language-pack-pt-br@15.0.2':
    resolution: {integrity: sha512-OSrd0ORd9eZiRl1gLvxJ5i5t+C7TfLw9seMlhE7xTCEQ6jgbJ9a4cVsu/YDSRIGSXxlqr8bIpkH3ZLRp97MeAw==}

  '@codingame/monaco-vscode-language-pack-qps-ploc@15.0.2':
    resolution: {integrity: sha512-PVn+/JevTRbO/IsQQwwr45M9y3RprG/rhD3hRXXPRsehjJx8pvuUrSsunmV5Va7m5Zz0LPRXZBlLTpoVWRYkCQ==}

  '@codingame/monaco-vscode-language-pack-ru@15.0.2':
    resolution: {integrity: sha512-YuGWXQnKxoSmUwa9449jc0xAWqCTHiu2Er8v3MI3wyccacokPdcNzqMvpmPlL6J5fyE/LEimbKxQZ0KkNOlbGw==}

  '@codingame/monaco-vscode-language-pack-tr@15.0.2':
    resolution: {integrity: sha512-UK4/qaw0WAYJrJtk/vcWb5pWmSEzfp88UYD5lRdlNGyCteJoL7Fa/6xahHWKxzRleADHhCfxKfJpxZaRX3cZfw==}

  '@codingame/monaco-vscode-language-pack-zh-hans@15.0.2':
    resolution: {integrity: sha512-2o7CjPQ77yY/g5uv2Nu3CZsRsexssSrdqvx6A3LHvQg67JtqYI/aeQg8vB7RIi8GttDqg5ReJrTrXJt8KFLwwA==}

  '@codingame/monaco-vscode-language-pack-zh-hant@15.0.2':
    resolution: {integrity: sha512-5hY9cR43I6TeVBCybQZb/R44Cm5M1fdmCo4kqndtlPiRkAuMBir1AwCo8quMV7sdQGsYIVhqnXLhHrr5hcAIVQ==}

  '@codingame/monaco-vscode-languages-service-override@15.0.2':
    resolution: {integrity: sha512-jrf12nVSIpFdRbkDQBFWLK9fzOxpUCWJZK9i3p6Ta5qU59Ff2XR9ybSMjWJQMCn9ZnGZoH/hRWl4+ZZ2zi4Lng==}

  '@codingame/monaco-vscode-layout-service-override@15.0.2':
    resolution: {integrity: sha512-2CPl9z0MBN8HP71H5lf8uV60Z5Tgo1QdX9iQLQec0WWKBmQXL0Eid60F1pB1RT//ABPspebZV+C9me4NrYo/hg==}

  '@codingame/monaco-vscode-lifecycle-service-override@15.0.2':
    resolution: {integrity: sha512-kXk5CW92DOIoYere02eLQ/zYpNMRhcPIbGvzWXX9bc4NA77JxCNMZqiR1w83KzIchXrvOl3ONeN1P7WbTIQbvA==}

  '@codingame/monaco-vscode-localization-service-override@15.0.2':
    resolution: {integrity: sha512-kRKbOmHof6o9w0zkUCMOyNA9/hwVJuRqOI74WHKHF7efisJaurn1fqQbFhwNsO0uD0ax0taYjSvU1nI2iCMH8A==}

  '@codingame/monaco-vscode-log-service-override@15.0.2':
    resolution: {integrity: sha512-2ihDMYGJ0gkR0Uv8Lqjky66IbOzqHh3EfNcY0Jwl3gkKnie5D0OC4FOP6JWMuYjfFMS97a5LTeiiJ7xoAjD42w==}

  '@codingame/monaco-vscode-model-service-override@15.0.2':
    resolution: {integrity: sha512-FnORHgAmY5+zx5pZgaVxnaj12CjA0j1FiSrYiRjWj//AvRcaGBTh8QuRtZZvB6wk6Lh6SgmMN5ALwbzdQuDkSA==}

  '@codingame/monaco-vscode-monarch-service-override@15.0.2':
    resolution: {integrity: sha512-G7LFMgUrq1sIYR+5lsX8t5UZI7X6vvREvRlewaofr44R1+NlARkGKJ5S431ruOlVUf8YxwuwqjeuNWAFGDbbfA==}

  '@codingame/monaco-vscode-preferences-service-override@15.0.2':
    resolution: {integrity: sha512-1Vohw10/0YtFsvgwy5rKVx7URG5JQqwFhjZFpcgJru5rcSZ8Gq15wmv1IwMEwUcQp/LQ84JyyYY+Ap6yvNjRNQ==}

  '@codingame/monaco-vscode-python-default-extension@15.0.2':
    resolution: {integrity: sha512-ip8ZjA4CrDxVirbsdXQp4iA5CMAwyYtgkvrmxzIDaGNl4oZJ9gkXdMxofQQJsrqMXUxEEu23ZnkGGs3e4E1VPg==}

  '@codingame/monaco-vscode-quickaccess-service-override@15.0.2':
    resolution: {integrity: sha512-KK/n7l1bJOSW1RKDL1DvIIWrxd3WRnhVtkEz4mNDMreGYsfbaUF5t6gw4pKP/r+blk9ZO/ZTm6+udZMUZ2gEnQ==}

  '@codingame/monaco-vscode-remote-agent-service-override@15.0.2':
    resolution: {integrity: sha512-YZT9zGncgA4mTFkA8/fGdpbP1cpOEzzTKZwCQg5F52FLOyDqiODj3cArWYaUt/AbVbL0fJB+NKU7DDMgYLHysA==}

  '@codingame/monaco-vscode-search-result-default-extension@15.0.2':
    resolution: {integrity: sha512-UjgRQMN1DpWeWXt6YJDEv+V2cvNowrkTjnptu6xMG22UVCxd8y0DtBiVwpZAjKyoDCczSUM4CKJVq4Rn/uijDA==}

  '@codingame/monaco-vscode-search-service-override@15.0.2':
    resolution: {integrity: sha512-rH+9Btqowdg9FcjWc6nfO/5JFD70OMNaf9vylVkj4g46QBCtr927Hq+esNBczyVLUbOtII2NP4nPNw8kKgic7w==}

  '@codingame/monaco-vscode-secret-storage-service-override@15.0.2':
    resolution: {integrity: sha512-zW2GVQqDaLfMGHVSEwm7Z/prNky8uj3mgKMeVV3Mx2L1Nq3Wzmp59j8sa7QIixqpFM8n/HyGOJZjaRksuLMr5A==}

  '@codingame/monaco-vscode-standalone-json-language-features@15.0.2':
    resolution: {integrity: sha512-RRBxi7UsTKYW9fmvArcFly4CHZTwtngFhk9YbgO+ghYO21TeUlrkGisnBO5FuKJkHE/63nOSEz3kCUjzmvkUIg==}

  '@codingame/monaco-vscode-standalone-languages@15.0.2':
    resolution: {integrity: sha512-ah83ra+eIW/TlZoYOBmJWEqVzcMVFTtJtZrnsrut8O4Lw7LAQHJdq9Iy0Xno75ss+AIj5j4InLiyn3Y56jOlUQ==}

  '@codingame/monaco-vscode-standalone-typescript-language-features@15.0.2':
    resolution: {integrity: sha512-9XX7VIBKc3ht2GtoUnezrpfCETWMp4xXizj2MVzfsT+dppEQK93oTumMnCu+OvygW7PFLzzsJrZloNBLwGAvaw==}

  '@codingame/monaco-vscode-storage-service-override@15.0.2':
    resolution: {integrity: sha512-qmpAARkcn9r47bSOPP1Rw+O8NuIgFSq6jJdYos8XQ8AvPyCUwyB8KmnQlNpzqI1cy8dzX9ppf++VcxRxURRoBg==}

  '@codingame/monaco-vscode-terminal-service-override@15.0.2':
    resolution: {integrity: sha512-4q4xGbttstM9DYyil2GorlsjrT4nF20g9rQMENkkD9huPRKEP1E1g++TeMv/I0sCaWDPVH1e6yQO3prHSiy1Rg==}

  '@codingame/monaco-vscode-testing-service-override@15.0.2':
    resolution: {integrity: sha512-/6/wv4gFcl0gs1nexUhfhyLJCoSgQneSeIHyyKVoOuyc4dXaEyuFIgmvJbIsn2XGx4WxwsmptEx/CW80c6Ynog==}

  '@codingame/monaco-vscode-textmate-service-override@15.0.2':
    resolution: {integrity: sha512-3owPZ8QbpQGVIYw5xeoJQhEPvrHRyrNQ9Cp04MmbjXyj+PnNd6q9VtD6gK4vd+J5XXX0BtdaBS1EhsG1E4LNMA==}

  '@codingame/monaco-vscode-theme-defaults-default-extension@15.0.2':
    resolution: {integrity: sha512-o66dXwgoNlj3rx9MT8TX2OtSugeK7bd8/PWlcwZLt6XrqE4T082mnFBPpGPpCOQXgsWJbYFTvbJMMuVM4XWfqA==}

  '@codingame/monaco-vscode-theme-service-override@15.0.2':
    resolution: {integrity: sha512-vNi4Gk8okoLYJi77qXe5l8L4WGFHsUpgQYa5T+yPtfcupOX1ZEGr2W9DCjAzWPijWdbaL73kttFw/3QTs3z+tA==}

  '@codingame/monaco-vscode-typescript-basics-default-extension@15.0.2':
    resolution: {integrity: sha512-ze2c+XOsPvwghY9Hu6nc9zyR2pFrqVLPjP1cDgHeCb9u3IabD18MNfQ8FTTWVFSgpilUoEhbAb4oWWSxzM27JQ==}

  '@codingame/monaco-vscode-typescript-language-features-default-extension@15.0.2':
    resolution: {integrity: sha512-iAeVUjBN32/v9IW/7osm9v/1cjPGgcFHjA8xmmSn/ogN7j947Ycgx12Fw65jTrb/wBu7m11Vq0WMFYRCxQCYUA==}

  '@codingame/monaco-vscode-view-banner-service-override@15.0.2':
    resolution: {integrity: sha512-L0ZjRIl+u8G6IHy2JvjEZp7RkGxFgJMnqTyJun1W3vINQe2eiVgM/7m+0Ac1pz6kOJC7cziRupa34LCidH4sJw==}

  '@codingame/monaco-vscode-view-common-service-override@15.0.2':
    resolution: {integrity: sha512-tbKXC7T30bFDc+EoI9yAVcJ2ruVdoanskZRmcRUM3SJglELj4FRAVO9pmb/jthDWSQgiEdBEQ7v6UdU9l6aQcg==}

  '@codingame/monaco-vscode-view-status-bar-service-override@15.0.2':
    resolution: {integrity: sha512-/C09z2qfq/erWhu1msN+DbfsJAQrH4rM6MqkE/+zZsdcl6OqRFYvnV0aBBp9x3SXthnYWWZ7iwxX+lVCAq3MBQ==}

  '@codingame/monaco-vscode-view-title-bar-service-override@15.0.2':
    resolution: {integrity: sha512-9uB8ADGv071CNDHNbat7WsJPXCeB6yRvgKt0q1f10f4NJFR+IbLidGVcdwLwzeNeELHEZBIe6WrPxWAaiWeqiA==}

  '@codingame/monaco-vscode-views-service-override@15.0.2':
    resolution: {integrity: sha512-jbZ6AV9jnF22Q29DAl8nuJcQeOvDjXJrkT8OEMXs3dYAdZqkg7Y+P3ggW2G+EuMo2ytNrfhLBez8It7jJS+tAQ==}

  '@codingame/monaco-vscode-workbench-service-override@15.0.2':
    resolution: {integrity: sha512-w7SU4pQw1DWhMEZjWL+XIjxr/3v3hJdZRX5QDk8AQXvPZ+m4HxrPRHY8LF/kuPwHuB4oYrKQvU7WKy/OAS/PSw==}

  '@typefox/monaco-editor-react@6.6.0':
    resolution: {integrity: sha512-8cqr4CCHQgdAbF+cxZNg7QOF4e3Oy2QAM7buTqiwAaKJxbWL5eZJPAO49fHYd3a/DpyUUZxjPURRoV788/zEeA==}
    engines: {node: '>=18.19.0', npm: '>=10.2.3'}

  '@vscode/iconv-lite-umd@0.7.0':
    resolution: {integrity: sha512-bRRFxLfg5dtAyl5XyiVWz/ZBPahpOpPrNYnnHpOpUZvam4tKH35wdhP4Kj6PbM0+KdliOsPzbGWpkxcdpNB/sg==}

  '@vscode/l10n@0.0.18':
    resolution: {integrity: sha512-KYSIHVmslkaCDyw013pphY+d7x1qV8IZupYfeIfzNA+nsaWHbn5uPuQRvdRFsa9zFzGeudPuoGoZ1Op4jrJXIQ==}

  '@xterm/addon-clipboard@0.2.0-beta.81':
    resolution: {integrity: sha512-vDxRyBO9VHzsl+gUQsDlUM9o2ZxSJKzE2eYQtuILKcf5D0EXYI86aMwKT/1iguX41vcMg42WXQBQ0TLWZ2MyTQ==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-image@0.9.0-beta.98':
    resolution: {integrity: sha512-yJaezwUc1Y3QYCmYSpjFW9IzMTLPSqrRCgdPnQ0JbCAVASRVmH6DLRfeikheJCvoXW6VUwMmGkb3fSplTxiV1w==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-ligatures@0.10.0-beta.98':
    resolution: {integrity: sha512-1zYeS9OUBR2ThG7dsxsGKOqeSlUo+DNTd5aaV5ZFbKQsQ1w+sQCaL73ZrXp1kuVK7pBiP5NCo4ffcXfzcztztA==}
    engines: {node: '>8.0.0'}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-progress@0.2.0-beta.4':
    resolution: {integrity: sha512-7t1nlaANdEjUBVvuTTs5gw6UQgB+unFLwSGGnYXIvdQroYdkXQXSSATSqpYKVCd/6QFhBerzdB2VwPM5L5lxIw==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-search@0.16.0-beta.98':
    resolution: {integrity: sha512-7gtx7eYvwFLxlb5q2IKxa7jG1KEinVwTlT3ijnSsPawwn7rGi6nR135rGiR8DAjEV0GUO406ICeoYyVbgiFNwQ==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-serialize@0.14.0-beta.98':
    resolution: {integrity: sha512-Tsr8j3wnun2raYR1DgsNClQP/I5a85u/uW/5EiYH+/iPPua6EWJvPlr5Q6TCU/cdIKW1o27Z3L5/mw0pfMUXrQ==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-unicode11@0.9.0-beta.98':
    resolution: {integrity: sha512-3GjjEYAPWAEGE1CaTFDjQxKcY5NiHHPmeufTsRp3IL5850ZiaMMq9bIL2WSdoFIbVgfbxh5mRy2cJPdu9m0uRQ==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/addon-webgl@0.19.0-beta.98':
    resolution: {integrity: sha512-09FbNHgN2ad/8JI+AEyg8C3msyk04ET1FihQIpTeWPfd2LJIAdps7G4St2+qzZbhlFkR6m9Dgrgh/AC2uegh8A==}
    peerDependencies:
      '@xterm/xterm': ^5.6.0-beta.98

  '@xterm/xterm@5.6.0-beta.98':
    resolution: {integrity: sha512-fJexj3XKDAMGsR8KKaiEhGrtJD1eRANbT3094E3KSgvbHRa3524tSFvDCx5+5KRE/hYaECmi0knAUIWJCvSPTg==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  body-parser@1.20.3:
    resolution: {integrity: sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  call-bind-apply-helpers@1.0.1:
    resolution: {integrity: sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.3:
    resolution: {integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==}
    engines: {node: '>= 0.4'}

  chevrotain-allstar@0.3.1:
    resolution: {integrity: sha512-b7g+y9A0v4mxCW1qUhf3BSVPg+/NvGErk/dOkrDaHA0nQIQGAtrOjlX//9OQtRlSCy+x9rfB5N8yC71lH1nvMw==}
    peerDependencies:
      chevrotain: ^11.0.0

  chevrotain@11.0.3:
    resolution: {integrity: sha512-ci2iJH6LeIkvP9eJW6gpueU8cnZhv85ELY8w8WiFtNjMHA5ad6pQLaJo9mEly/9qUyCpvqX8/POVUTf18/HFdw==}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.7.1:
    resolution: {integrity: sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==}
    engines: {node: '>= 0.6'}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  express@4.21.2:
    resolution: {integrity: sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==}
    engines: {node: '>= 0.10.0'}

  finalhandler@1.3.1:
    resolution: {integrity: sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==}
    engines: {node: '>= 0.8'}

  font-finder@1.1.0:
    resolution: {integrity: sha512-wpCL2uIbi6GurJbU7ZlQ3nGd61Ho+dSU6U83/xJT5UPFfN35EeCW/rOtS+5k+IuEZu2SYmHzDIPL9eA5tSYRAw==}
    engines: {node: '>8.0.0'}

  font-ligatures@1.4.1:
    resolution: {integrity: sha512-7W6zlfyhvCqShZ5ReUWqmSd9vBaUudW0Hxis+tqUjtHhsPU+L3Grf8mcZAtCiXHTzorhwdRTId2WeH/88gdFkw==}
    engines: {node: '>8.0.0'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-intrinsic@1.2.7:
    resolution: {integrity: sha512-VW6Pxhsrk0KAOqs3WEd0klDiF/+V7gQOpAvY1jVU/LHmaD/kQO4523aiJuikX/QAKYiW6x8Jh+RJej1almdtCA==}
    engines: {node: '>= 0.4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-system-fonts@2.0.2:
    resolution: {integrity: sha512-zzlgaYnHMIEgHRrfC7x0Qp0Ylhw/sHpM6MHXeVBTYIsvGf5GpbnClB+Q6rAPdn+0gd2oZZIo6Tj3EaWrt4VhDQ==}
    engines: {node: '>8.0.0'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  immediate@3.0.6:
    resolution: {integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  js-base64@3.7.7:
    resolution: {integrity: sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==}

  jschardet@3.1.4:
    resolution: {integrity: sha512-/kmVISmrwVwtyYU40iQUOp3SUPk2dhNCMsZBQX0R1/jZ8maaXJ/oZIzUOiyOqcgtLnETFKYChbJ5iDC/eWmFHg==}
    engines: {node: '>=0.1.90'}

  jsonc-parser@3.3.1:
    resolution: {integrity: sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==}

  jszip@3.10.1:
    resolution: {integrity: sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==}

  langium@3.4.0:
    resolution: {integrity: sha512-7xufsaF5jYGFMXHOTka8bN48b9FHn2vZGL2R+PGgyi+JY/xgimUFDKYcz/h4gm5m8p3sSRtZDh+sK2U63K0MNg==}
    engines: {node: '>=18.0.0'}

  lie@3.3.0:
    resolution: {integrity: sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  marked@14.0.0:
    resolution: {integrity: sha512-uIj4+faQ+MgHgwUW1l2PsPglZLOLOT1uErt06dAPtx2kjteLAkbsd/0FiYg/MGS+i7ZKLb7w2WClxHkzOOuryQ==}
    engines: {node: '>= 18'}
    hasBin: true

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  merge-descriptors@1.0.3:
    resolution: {integrity: sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}

  monaco-editor-wrapper@6.6.0:
    resolution: {integrity: sha512-VkLf/zaiAphMq0VlTmlVWAswD4/zDbYDUDAro7bpWsgM7vLxDjm5boF+nHY+vcy585CVX+ce0t5uWI10Njmu9A==}
    engines: {node: '>=18.19.0', npm: '>=10.2.3'}

  monaco-languageclient-examples@2025.3.6:
    resolution: {integrity: sha512-TGUeLLmDYBTeJVeZo51G2yMR4hpwsUl9/PpjyZmfaDGlNJGX+81vCbXRMBO9+LphmPgNUyT/KSE3HaCFx3WDAA==}
    engines: {node: '>=18.19.0', npm: '>=10.2.3'}

  monaco-languageclient@9.5.0:
    resolution: {integrity: sha512-kl9DHbKxNh3fe5MDOZZLobIkU8q7BNUIEW1wUcnIW1V+Ti+pqbbgLEEM4sWQaVkMMaJE2ySoPtF3VhDh5rF3Rg==}
    engines: {node: '>=18.19.0', npm: '>=10.2.3'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  opentype.js@0.8.0:
    resolution: {integrity: sha512-FQHR4oGP+a0m/f6yHoRpBOIbn/5ZWxKd4D/djHVJu8+KpBTYrJda0b7mLcgDEMWXE9xBCJm+qb0yv6FcvPjukg==}
    hasBin: true

  pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-to-regexp@0.1.12:
    resolution: {integrity: sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  promise-stream-reader@1.0.1:
    resolution: {integrity: sha512-Tnxit5trUjBAqqZCGWwjyxhmgMN4hGrtpW3Oc/tRI4bpm/O2+ej72BB08l6JBnGQgVDGCLvHFGjGgQS6vzhwXg==}
    engines: {node: '>8.0.0'}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  pyright@1.1.396:
    resolution: {integrity: sha512-+/8GN9ZRlqS/EFUjSW3yb2FN9XF7KjGpnLVYLtfTPDiiH+tfua898acKenUTGYbdfvSf7J0GD/g1b5RItnyYPw==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  qs@6.13.0:
    resolution: {integrity: sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==}
    engines: {node: '>=0.6'}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  react-dom@19.0.0:
    resolution: {integrity: sha512-4GV5sHFG0e/0AD4X+ySy6UJd3jVl1iNsNHdpad0qhABJ11twS3TTBnseqsKurKcsNqCEFeGL3uLpVChpIO3QfQ==}
    peerDependencies:
      react: ^19.0.0

  react@19.0.0:
    resolution: {integrity: sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ==}
    engines: {node: '>=0.10.0'}

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  request-light@0.8.0:
    resolution: {integrity: sha512-bH6E4PMmsEXYrLX6Kr1vu+xI3HproB1vECAwaPSJeroLE1kpWE3HR27uB4icx+6YORu1ajqBJXxuedv8ZQg5Lw==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  scheduler@0.25.0:
    resolution: {integrity: sha512-xFVuu11jh+xcO7JOAGJNOXld8/TcEHK/4CituBUeUb5hqxJLj9YuemAEuvm9gQ/+pgXYfbQuqAkiYu+u7YEsNA==}

  semver@7.6.3:
    resolution: {integrity: sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}

  setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  tiny-inflate@1.0.3:
    resolution: {integrity: sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typescript@5.8.2:
    resolution: {integrity: sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vscode-json-languageservice@5.4.3:
    resolution: {integrity: sha512-NVSEQDloP9NYccuqKg4eI46kutZpwucBY4csBB6FCxbM7AZVoBt0oxTItPVA+ZwhnG1bg/fmiBRAwcGJyNQoPA==}

  vscode-jsonrpc@8.2.0:
    resolution: {integrity: sha512-C+r0eKJUIfiDIfwJhria30+TYWPtuHJXHtI7J0YlOmKAo7ogxP20T0zxB7HZQIFhIyvoBPwWskjxrvAtfjyZfA==}
    engines: {node: '>=14.0.0'}

  vscode-jsonrpc@8.2.1:
    resolution: {integrity: sha512-kdjOSJ2lLIn7r1rtrMbbNCHjyMPfRnowdKjBQ+mGq6NAW5QY2bEZC/khaC5OR8svbbjvLEaIXkOq45e2X9BIbQ==}
    engines: {node: '>=14.0.0'}

  vscode-languageclient@9.0.1:
    resolution: {integrity: sha512-JZiimVdvimEuHh5olxhxkht09m3JzUGwggb5eRUkzzJhZ2KjCN0nh55VfiED9oez9DyF8/fz1g1iBV3h+0Z2EA==}
    engines: {vscode: ^1.82.0}

  vscode-languageserver-protocol@3.17.5:
    resolution: {integrity: sha512-mb1bvRJN8SVznADSGWM9u/b07H7Ecg0I3OgXDuLdn307rl/J3A9YD6/eYOssqhecL27hK1IPZAsaqh00i/Jljg==}

  vscode-languageserver-textdocument@1.0.12:
    resolution: {integrity: sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==}

  vscode-languageserver-types@3.17.5:
    resolution: {integrity: sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==}

  vscode-languageserver@9.0.1:
    resolution: {integrity: sha512-woByF3PDpkHFUreUa7Hos7+pUWdeWMXRd26+ZX2A8cFx6v/JPTtd4/uN0/jB6XQHYaOlHbio03NTHCqrgG5n7g==}
    hasBin: true

  vscode-oniguruma@1.7.0:
    resolution: {integrity: sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==}

  vscode-textmate@9.2.0:
    resolution: {integrity: sha512-rkvG4SraZQaPSN/5XjwKswdU0OP9MF28QjrYzUBbhb8QyG3ljB1Ky996m++jiI7KdiAP2CkBiQZd9pqEDTClqA==}

  vscode-uri@3.0.8:
    resolution: {integrity: sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==}

  vscode-uri@3.1.0:
    resolution: {integrity: sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==}

  vscode-ws-jsonrpc@3.4.0:
    resolution: {integrity: sha512-jkNZvX0LdHt4skPxMw/jFePr3jRCJU6ZmO28oPoQ7RwNSkwU3uN8mgtxACYEbOY68bYmi/b/uJzhxewKCz1P4w==}
    engines: {node: '>=18.19.0', npm: '>=10.2.3'}

  ws@8.18.0:
    resolution: {integrity: sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  wtd-core@4.0.1:
    resolution: {integrity: sha512-q6sV6Slw47bwlhwbztot0MklWaVzywUAi0wAKWwOuL/LTY4IpVFgoHQ+cnlhG2ZUms/OkJUhyfhsfoHNYkKjzA==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

snapshots:

  '@chevrotain/cst-dts-gen@11.0.3':
    dependencies:
      '@chevrotain/gast': 11.0.3
      '@chevrotain/types': 11.0.3
      lodash-es: 4.17.21

  '@chevrotain/gast@11.0.3':
    dependencies:
      '@chevrotain/types': 11.0.3
      lodash-es: 4.17.21

  '@chevrotain/regexp-to-ast@11.0.3': {}

  '@chevrotain/types@11.0.3': {}

  '@chevrotain/utils@11.0.3': {}

  '@codingame/monaco-vscode-039b5553-0838-562a-97c2-30d6e54a7b42-common@15.0.2': {}

  '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-7bbc9e7d-eeae-55fc-8bf9-dc2f66e0dc73-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-2e67e044-0db9-5fa6-8bd1-3737a7d586d4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2

  '@codingame/monaco-vscode-0f5ced28-abde-558b-8652-db8e7d4d64aa-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-1021b67c-93e5-5c78-a270-cbdb2574d980-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-10418ae3-ee63-5700-a757-89cbe6564ee4-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-15626ec7-b165-51e1-8caf-7bcc2ae9b95a-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-2e67e044-0db9-5fa6-8bd1-3737a7d586d4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-168b98e5-dc20-5807-b1f9-798f1f92b37f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-1ae7d696-d960-5ac6-97a3-9fe7c8c3a793-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-1b4486de-4fe4-59c4-9e6d-34f265ff6625-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-9a1a5840-af83-5d07-a156-ba32a36c5c4b-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common': 15.0.2

  '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-210e86a9-a91b-5273-b05d-390c776dde1f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-256d5b78-0649-50e9-8354-2807f95f68f4-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-29bc1406-2925-5b8f-b25e-d04a7772d896-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-210e86a9-a91b-5273-b05d-390c776dde1f-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2

  '@codingame/monaco-vscode-2a22c7b4-b906-5914-8cd1-3ed912fb738f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-2e67e044-0db9-5fa6-8bd1-3737a7d586d4-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2
      '@vscode/iconv-lite-umd': 0.7.0
      jschardet: 3.1.4

  '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-2f06fe84-148e-5e6b-a7ca-c7989c5f128a-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-33833ac7-3af3-5e9d-8fb9-11838d852c59-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-3607c442-ae7a-594b-b840-038378c24fef-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-29bc1406-2925-5b8f-b25e-d04a7772d896-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-44b92f80-48ea-5562-a8d0-18a015f8d845-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-45a408c6-90ed-5d8b-801d-f3f69c7a97f2-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2

  '@codingame/monaco-vscode-494be54c-bd37-5b3c-af70-02f086e28768-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-4a316137-39d1-5d77-8b53-112db3547c1e-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-4ead9d5f-54da-5c5a-b093-32be4a84d711-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-ea14e352-8f1c-5569-b79a-8a96a53e8abe-common': 15.0.2

  '@codingame/monaco-vscode-523730aa-81e6-55d7-9916-87ad537fe087-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2

  '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-5452e2b7-9081-5f95-839b-4ab3544ce28f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-168b98e5-dc20-5807-b1f9-798f1f92b37f-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-55ed5668-e8ca-5335-a587-790bcf1200c0-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-625898ab-0e33-5b7f-a3ae-29c10271dc1c-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-7bbc9e7d-eeae-55fc-8bf9-dc2f66e0dc73-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-6845754f-e617-5ed9-8aaa-6ca3653a9532-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-695440c8-a687-5594-b476-bbc7b36bafe9-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-6980eeab-47bb-5a48-8e15-32caf0785565-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2

  '@codingame/monaco-vscode-6f9bc782-77e0-5716-93bd-b05210c768c5-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-805e9c2f-56b6-5a43-8b5b-d2dc2d3805fc-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-7bbc9e7d-eeae-55fc-8bf9-dc2f66e0dc73-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-7f39b6f1-3542-5430-8760-0f404d7a7cee-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-805e9c2f-56b6-5a43-8b5b-d2dc2d3805fc-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-88aa9a78-75f5-5aaa-89e9-bbdd580b3b5c-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-897bebad-39df-57cb-8a57-36a271d038be-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-89a82baf-8ded-5b2f-b8af-e5fbd72dc5ad-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-256d5b78-0649-50e9-8354-2807f95f68f4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-8ccb7637-50ea-5359-97bf-00015d7fe567-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-210e86a9-a91b-5273-b05d-390c776dde1f-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-897bebad-39df-57cb-8a57-36a271d038be-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-9a1a5840-af83-5d07-a156-ba32a36c5c4b-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-805e9c2f-56b6-5a43-8b5b-d2dc2d3805fc-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-a3f28a41-ba19-5a7e-8f5a-d6c1403b507d-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-d56fc266-2991-5e70-8f69-134ad70e1700-common': 15.0.2
      marked: 14.0.0

  '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-d56fc266-2991-5e70-8f69-134ad70e1700-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-9d0168a3-519b-57f3-9bcc-89efc41f951a-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-a2719803-af40-5ae9-a29f-8a2231c33056-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-a3f28a41-ba19-5a7e-8f5a-d6c1403b507d-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-ab07af84-42e8-5a0f-8aef-b83fb90ede21-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-abed5a84-8a82-5f84-9412-88a736235bae-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-api@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-base-service-override': 15.0.2
      '@codingame/monaco-vscode-environment-service-override': 15.0.2
      '@codingame/monaco-vscode-extensions-service-override': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2
      '@codingame/monaco-vscode-host-service-override': 15.0.2
      '@codingame/monaco-vscode-layout-service-override': 15.0.2
      '@codingame/monaco-vscode-quickaccess-service-override': 15.0.2
      marked: 14.0.0

  '@codingame/monaco-vscode-b1249c5b-1339-5278-b002-746f08105c6d-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-b1e8558d-1726-5299-bc75-e43ee6d1a124-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-2e67e044-0db9-5fa6-8bd1-3737a7d586d4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-b71b5434-ce96-5581-8993-e8da380bd63f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d56fc266-2991-5e70-8f69-134ad70e1700-common': 15.0.2

  '@codingame/monaco-vscode-base-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-039b5553-0838-562a-97c2-30d6e54a7b42-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-9a1a5840-af83-5d07-a156-ba32a36c5c4b-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d987325e-3e05-53aa-b9ff-6f97476f64db-common': 15.0.2

  '@codingame/monaco-vscode-bba55be6-41a2-50cd-a3cc-8bafa35bfa89-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-be8ddbb5-094a-5657-b1cc-fe106c94c632-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-bf94ddb5-e436-506a-9763-5ab86b642508-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-bulk-edit-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-c3b3387c-7bce-5b8f-9e14-bebeb636f1c8-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0f5ced28-abde-558b-8652-db8e7d4d64aa-common': 15.0.2
      '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common': 15.0.2
      '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1249c5b-1339-5278-b002-746f08105c6d-common': 15.0.2

  '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-cea4d01f-6526-5c2f-8b09-b168fead499f-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-configuration-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1249c5b-1339-5278-b002-746f08105c6d-common': 15.0.2
      '@codingame/monaco-vscode-d987325e-3e05-53aa-b9ff-6f97476f64db-common': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2

  '@codingame/monaco-vscode-cpp-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-d481a59e-259c-524e-bee1-76483d75d3a1-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-523730aa-81e6-55d7-9916-87ad537fe087-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1e8558d-1726-5299-bc75-e43ee6d1a124-common': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-d56fc266-2991-5e70-8f69-134ad70e1700-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2

  '@codingame/monaco-vscode-d609a7d3-bf87-551a-884f-550a8b327ec5-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-d7f659f5-da33-5ea8-a3b8-9b94f2cf5f33-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-a2719803-af40-5ae9-a29f-8a2231c33056-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-3607c442-ae7a-594b-b840-038378c24fef-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-d987325e-3e05-53aa-b9ff-6f97476f64db-common@15.0.2': {}

  '@codingame/monaco-vscode-debug-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-88aa9a78-75f5-5aaa-89e9-bbdd580b3b5c-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-ab07af84-42e8-5a0f-8aef-b83fb90ede21-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1249c5b-1339-5278-b002-746f08105c6d-common': 15.0.2
      '@codingame/monaco-vscode-e59ecb8c-db32-5324-8fe4-cf9921fd92b8-common': 15.0.2

  '@codingame/monaco-vscode-e59ecb8c-db32-5324-8fe4-cf9921fd92b8-common@15.0.2': {}

  '@codingame/monaco-vscode-e72c94ca-257a-5b75-8b68-5a5fa3c18255-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d609a7d3-bf87-551a-884f-550a8b327ec5-common': 15.0.2

  '@codingame/monaco-vscode-ea14e352-8f1c-5569-b79a-8a96a53e8abe-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-eb7d5efd-2e60-59f8-9ba4-9a8ae8cb2957-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-a3f28a41-ba19-5a7e-8f5a-d6c1403b507d-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-eba0b9b3-174c-5dae-9867-a37810ca1808-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-editor-api@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-5452e2b7-9081-5f95-839b-4ab3544ce28f-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-editor-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1e8558d-1726-5299-bc75-e43ee6d1a124-common': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2

  '@codingame/monaco-vscode-environment-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-abed5a84-8a82-5f84-9412-88a736235bae-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-explorer-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-15626ec7-b165-51e1-8caf-7bcc2ae9b95a-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1e8558d-1726-5299-bc75-e43ee6d1a124-common': 15.0.2
      '@codingame/monaco-vscode-b71b5434-ce96-5581-8993-e8da380bd63f-common': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-d481a59e-259c-524e-bee1-76483d75d3a1-common': 15.0.2

  '@codingame/monaco-vscode-extension-api@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common': 15.0.2
      '@codingame/monaco-vscode-extensions-service-override': 15.0.2

  '@codingame/monaco-vscode-extensions-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-168b98e5-dc20-5807-b1f9-798f1f92b37f-common': 15.0.2
      '@codingame/monaco-vscode-256d5b78-0649-50e9-8354-2807f95f68f4-common': 15.0.2
      '@codingame/monaco-vscode-3607c442-ae7a-594b-b840-038378c24fef-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common': 15.0.2
      '@codingame/monaco-vscode-6845754f-e617-5ed9-8aaa-6ca3653a9532-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-7f39b6f1-3542-5430-8760-0f404d7a7cee-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-8ccb7637-50ea-5359-97bf-00015d7fe567-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-bba55be6-41a2-50cd-a3cc-8bafa35bfa89-common': 15.0.2
      '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common': 15.0.2
      '@codingame/monaco-vscode-be8ddbb5-094a-5657-b1cc-fe106c94c632-common': 15.0.2
      '@codingame/monaco-vscode-bf94ddb5-e436-506a-9763-5ab86b642508-common': 15.0.2
      '@codingame/monaco-vscode-d7f659f5-da33-5ea8-a3b8-9b94f2cf5f33-common': 15.0.2
      '@codingame/monaco-vscode-eb7d5efd-2e60-59f8-9ba4-9a8ae8cb2957-common': 15.0.2
      '@codingame/monaco-vscode-eba0b9b3-174c-5dae-9867-a37810ca1808-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2

  '@codingame/monaco-vscode-f405f7ba-995b-5117-bc17-5bc7f77d92e9-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-f6ab89b2-83b0-5a43-8772-cb0eafa650b5-common@15.0.2(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-7bbc9e7d-eeae-55fc-8bf9-dc2f66e0dc73-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@xterm/addon-clipboard': 0.2.0-beta.81(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-image': 0.9.0-beta.98(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-ligatures': 0.10.0-beta.98(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-progress': 0.2.0-beta.4(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-search': 0.16.0-beta.98(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-serialize': 0.14.0-beta.98(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-unicode11': 0.9.0-beta.98(@xterm/xterm@5.6.0-beta.98)
      '@xterm/addon-webgl': 0.19.0-beta.98(@xterm/xterm@5.6.0-beta.98)
    transitivePeerDependencies:
      - '@xterm/xterm'

  '@codingame/monaco-vscode-fc985c90-0334-5b62-88bc-73e2efa0b80b-common@15.0.2': {}

  '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-ff9fa663-eae3-5274-8573-c2b918871e4b-common@15.0.2': {}

  '@codingame/monaco-vscode-files-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common': 15.0.2
      '@codingame/monaco-vscode-15626ec7-b165-51e1-8caf-7bcc2ae9b95a-common': 15.0.2
      '@codingame/monaco-vscode-2e67e044-0db9-5fa6-8bd1-3737a7d586d4-common': 15.0.2
      '@codingame/monaco-vscode-2f06fe84-148e-5e6b-a7ca-c7989c5f128a-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-cea4d01f-6526-5c2f-8b09-b168fead499f-common': 15.0.2

  '@codingame/monaco-vscode-groovy-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-host-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-java-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-javascript-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-json-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-keybindings-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-2a22c7b4-b906-5914-8cd1-3ed912fb738f-common': 15.0.2
      '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common': 15.0.2
      '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d609a7d3-bf87-551a-884f-550a8b327ec5-common': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2

  '@codingame/monaco-vscode-language-pack-cs@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-de@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-es@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-fr@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-it@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-ja@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-ko@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-pl@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-pt-br@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-qps-ploc@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-ru@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-tr@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-zh-hans@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-language-pack-zh-hant@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-languages-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2

  '@codingame/monaco-vscode-layout-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2

  '@codingame/monaco-vscode-lifecycle-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-localization-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-log-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-abed5a84-8a82-5f84-9412-88a736235bae-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-cea4d01f-6526-5c2f-8b09-b168fead499f-common': 15.0.2
      '@codingame/monaco-vscode-environment-service-override': 15.0.2

  '@codingame/monaco-vscode-model-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-monarch-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-preferences-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0f5ced28-abde-558b-8652-db8e7d4d64aa-common': 15.0.2
      '@codingame/monaco-vscode-10418ae3-ee63-5700-a757-89cbe6564ee4-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common': 15.0.2
      '@codingame/monaco-vscode-52bb4d5b-ba1a-57fd-9bee-b28824214eac-common': 15.0.2
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1249c5b-1339-5278-b002-746f08105c6d-common': 15.0.2
      '@codingame/monaco-vscode-c3b3387c-7bce-5b8f-9e14-bebeb636f1c8-common': 15.0.2
      '@codingame/monaco-vscode-d609a7d3-bf87-551a-884f-550a8b327ec5-common': 15.0.2
      '@codingame/monaco-vscode-e72c94ca-257a-5b75-8b68-5a5fa3c18255-common': 15.0.2
      '@codingame/monaco-vscode-fc985c90-0334-5b62-88bc-73e2efa0b80b-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2

  '@codingame/monaco-vscode-python-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-quickaccess-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-4ead9d5f-54da-5c5a-b093-32be4a84d711-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-9a1a5840-af83-5d07-a156-ba32a36c5c4b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d609a7d3-bf87-551a-884f-550a8b327ec5-common': 15.0.2
      '@codingame/monaco-vscode-ea14e352-8f1c-5569-b79a-8a96a53e8abe-common': 15.0.2
      '@codingame/monaco-vscode-fc985c90-0334-5b62-88bc-73e2efa0b80b-common': 15.0.2

  '@codingame/monaco-vscode-remote-agent-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-1b4486de-4fe4-59c4-9e6d-34f265ff6625-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-55ed5668-e8ca-5335-a587-790bcf1200c0-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-abed5a84-8a82-5f84-9412-88a736235bae-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common': 15.0.2
      '@codingame/monaco-vscode-environment-service-override': 15.0.2

  '@codingame/monaco-vscode-search-result-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-search-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1ae7d696-d960-5ac6-97a3-9fe7c8c3a793-common': 15.0.2
      '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common': 15.0.2
      '@codingame/monaco-vscode-45a408c6-90ed-5d8b-801d-f3f69c7a97f2-common': 15.0.2
      '@codingame/monaco-vscode-4ead9d5f-54da-5c5a-b093-32be4a84d711-common': 15.0.2
      '@codingame/monaco-vscode-523730aa-81e6-55d7-9916-87ad537fe087-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-695440c8-a687-5594-b476-bbc7b36bafe9-common': 15.0.2
      '@codingame/monaco-vscode-6f9bc782-77e0-5716-93bd-b05210c768c5-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d56fc266-2991-5e70-8f69-134ad70e1700-common': 15.0.2
      '@codingame/monaco-vscode-ea14e352-8f1c-5569-b79a-8a96a53e8abe-common': 15.0.2

  '@codingame/monaco-vscode-secret-storage-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-standalone-json-language-features@15.0.2':
    dependencies:
      monaco-editor: '@codingame/monaco-vscode-editor-api@15.0.2'

  '@codingame/monaco-vscode-standalone-languages@15.0.2':
    dependencies:
      monaco-editor: '@codingame/monaco-vscode-editor-api@15.0.2'

  '@codingame/monaco-vscode-standalone-typescript-language-features@15.0.2':
    dependencies:
      monaco-editor: '@codingame/monaco-vscode-editor-api@15.0.2'

  '@codingame/monaco-vscode-storage-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-2f06fe84-148e-5e6b-a7ca-c7989c5f128a-common': 15.0.2
      '@codingame/monaco-vscode-a2719803-af40-5ae9-a29f-8a2231c33056-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-terminal-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-422642f2-7e3a-5c1c-9e1e-1d3ef1817346-common': 15.0.2
      '@codingame/monaco-vscode-42931eb9-e564-530c-bafc-fa23ab43a070-common': 15.0.2
      '@codingame/monaco-vscode-45a408c6-90ed-5d8b-801d-f3f69c7a97f2-common': 15.0.2
      '@codingame/monaco-vscode-55ed5668-e8ca-5335-a587-790bcf1200c0-common': 15.0.2
      '@codingame/monaco-vscode-625898ab-0e33-5b7f-a3ae-29c10271dc1c-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-7bbc9e7d-eeae-55fc-8bf9-dc2f66e0dc73-common': 15.0.2
      '@codingame/monaco-vscode-805e9c2f-56b6-5a43-8b5b-d2dc2d3805fc-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-9a6d8b6c-ad4c-5ea3-9558-f43d6dc4c0ad-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-f405f7ba-995b-5117-bc17-5bc7f77d92e9-common': 15.0.2
      '@codingame/monaco-vscode-f6ab89b2-83b0-5a43-8772-cb0eafa650b5-common': 15.0.2(@xterm/xterm@5.6.0-beta.98)
      '@xterm/xterm': 5.6.0-beta.98

  '@codingame/monaco-vscode-testing-service-override@15.0.2(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@codingame/monaco-vscode-0b087f42-a5a3-5eb9-9bfd-1eebc1bba163-common': 15.0.2
      '@codingame/monaco-vscode-10418ae3-ee63-5700-a757-89cbe6564ee4-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1bb39316-6fbf-572e-ab6a-818a2496c14f-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-6845754f-e617-5ed9-8aaa-6ca3653a9532-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-f6ab89b2-83b0-5a43-8772-cb0eafa650b5-common': 15.0.2(@xterm/xterm@5.6.0-beta.98)
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2
      '@codingame/monaco-vscode-terminal-service-override': 15.0.2
    transitivePeerDependencies:
      - '@xterm/xterm'

  '@codingame/monaco-vscode-textmate-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-33833ac7-3af3-5e9d-8fb9-11838d852c59-common': 15.0.2
      '@codingame/monaco-vscode-695440c8-a687-5594-b476-bbc7b36bafe9-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2
      vscode-oniguruma: 1.7.0
      vscode-textmate: 9.2.0

  '@codingame/monaco-vscode-theme-defaults-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-theme-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-9d0168a3-519b-57f3-9bcc-89efc41f951a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2

  '@codingame/monaco-vscode-typescript-basics-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-typescript-language-features-default-extension@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2

  '@codingame/monaco-vscode-view-banner-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2

  '@codingame/monaco-vscode-view-common-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0c06bfba-d24d-5c4d-90cd-b40cefb7f811-common': 15.0.2
      '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common': 15.0.2
      '@codingame/monaco-vscode-0f5ced28-abde-558b-8652-db8e7d4d64aa-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-1b4486de-4fe4-59c4-9e6d-34f265ff6625-common': 15.0.2
      '@codingame/monaco-vscode-210e86a9-a91b-5273-b05d-390c776dde1f-common': 15.0.2
      '@codingame/monaco-vscode-29bc1406-2925-5b8f-b25e-d04a7772d896-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common': 15.0.2
      '@codingame/monaco-vscode-44b92f80-48ea-5562-a8d0-18a015f8d845-common': 15.0.2
      '@codingame/monaco-vscode-4a316137-39d1-5d77-8b53-112db3547c1e-common': 15.0.2
      '@codingame/monaco-vscode-523730aa-81e6-55d7-9916-87ad537fe087-common': 15.0.2
      '@codingame/monaco-vscode-55ed5668-e8ca-5335-a587-790bcf1200c0-common': 15.0.2
      '@codingame/monaco-vscode-65619f8f-0eab-5d8b-855a-43b6353fe527-common': 15.0.2
      '@codingame/monaco-vscode-6980eeab-47bb-5a48-8e15-32caf0785565-common': 15.0.2
      '@codingame/monaco-vscode-7559b0be-bfa5-5fe6-b731-1973fe9fffa1-common': 15.0.2
      '@codingame/monaco-vscode-7ba0af96-90c2-5e11-ad7f-befdbbf246c8-common': 15.0.2
      '@codingame/monaco-vscode-7f39b6f1-3542-5430-8760-0f404d7a7cee-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-897bebad-39df-57cb-8a57-36a271d038be-common': 15.0.2
      '@codingame/monaco-vscode-89a82baf-8ded-5b2f-b8af-e5fbd72dc5ad-common': 15.0.2
      '@codingame/monaco-vscode-8ccb7637-50ea-5359-97bf-00015d7fe567-common': 15.0.2
      '@codingame/monaco-vscode-9b5a5e82-d649-5455-b4bf-ef90d6afd294-common': 15.0.2
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-a4c2011e-8775-52bd-abf0-4a3c07a9696b-common': 15.0.2
      '@codingame/monaco-vscode-a7c9ae3c-16d2-5d17-86b2-981be7094566-common': 15.0.2
      '@codingame/monaco-vscode-ad89fae6-94f0-5ac2-a185-22dea4b68ee0-common': 15.0.2
      '@codingame/monaco-vscode-aff8bc9b-c6f8-578f-9c8a-f70d14f9c13c-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-b1e8558d-1726-5299-bc75-e43ee6d1a124-common': 15.0.2
      '@codingame/monaco-vscode-bd6ad8b7-9db3-51a8-9895-0046508c029d-common': 15.0.2
      '@codingame/monaco-vscode-bulk-edit-service-override': 15.0.2
      '@codingame/monaco-vscode-c4e2825a-b5b1-5f0e-b547-068c32e06d50-common': 15.0.2
      '@codingame/monaco-vscode-d481a59e-259c-524e-bee1-76483d75d3a1-common': 15.0.2
      '@codingame/monaco-vscode-e59ecb8c-db32-5324-8fe4-cf9921fd92b8-common': 15.0.2
      '@codingame/monaco-vscode-e72c94ca-257a-5b75-8b68-5a5fa3c18255-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2
      '@codingame/monaco-vscode-ff9fa663-eae3-5274-8573-c2b918871e4b-common': 15.0.2

  '@codingame/monaco-vscode-view-status-bar-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2

  '@codingame/monaco-vscode-view-title-bar-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-38f25ab8-ea30-5ba9-8a08-ae3308c297c0-common': 15.0.2
      '@codingame/monaco-vscode-40cada32-7e9c-528a-81fc-766e4da54147-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2

  '@codingame/monaco-vscode-views-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-0cc5da60-f921-59b9-bd8c-a018e93c0a6f-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-6980eeab-47bb-5a48-8e15-32caf0785565-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2
      '@codingame/monaco-vscode-keybindings-service-override': 15.0.2
      '@codingame/monaco-vscode-layout-service-override': 15.0.2
      '@codingame/monaco-vscode-quickaccess-service-override': 15.0.2
      '@codingame/monaco-vscode-view-common-service-override': 15.0.2

  '@codingame/monaco-vscode-workbench-service-override@15.0.2':
    dependencies:
      '@codingame/monaco-vscode-1021b67c-93e5-5c78-a270-cbdb2574d980-common': 15.0.2
      '@codingame/monaco-vscode-12c5f9eb-72d3-57ca-babd-5bef7aa9de3b-common': 15.0.2
      '@codingame/monaco-vscode-256d5b78-0649-50e9-8354-2807f95f68f4-common': 15.0.2
      '@codingame/monaco-vscode-2e69e120-617a-5258-95e0-3b8902f4e014-common': 15.0.2
      '@codingame/monaco-vscode-411e0589-fa79-504b-b32c-80a88847b23a-common': 15.0.2
      '@codingame/monaco-vscode-494be54c-bd37-5b3c-af70-02f086e28768-common': 15.0.2
      '@codingame/monaco-vscode-6980eeab-47bb-5a48-8e15-32caf0785565-common': 15.0.2
      '@codingame/monaco-vscode-81f603ca-d6ea-5402-90dd-3014dffc63b4-common': 15.0.2
      '@codingame/monaco-vscode-86d65fc6-30f9-5dca-9501-e249de688591-common': 15.0.2
      '@codingame/monaco-vscode-9efc1f50-c7de-55d6-8b28-bcc88bd49b5a-common': 15.0.2
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-d8236b3b-b91a-522d-89f4-94d70a546f6a-common': 15.0.2
      '@codingame/monaco-vscode-fdf643f9-94dd-5510-b97a-408abf26ad92-common': 15.0.2
      '@codingame/monaco-vscode-keybindings-service-override': 15.0.2
      '@codingame/monaco-vscode-quickaccess-service-override': 15.0.2
      '@codingame/monaco-vscode-view-banner-service-override': 15.0.2
      '@codingame/monaco-vscode-view-common-service-override': 15.0.2
      '@codingame/monaco-vscode-view-status-bar-service-override': 15.0.2
      '@codingame/monaco-vscode-view-title-bar-service-override': 15.0.2

  '@typefox/monaco-editor-react@6.6.0':
    dependencies:
      '@codingame/monaco-vscode-editor-api': 15.0.2
      monaco-editor-wrapper: 6.6.0
      react: 19.0.0

  '@vscode/iconv-lite-umd@0.7.0': {}

  '@vscode/l10n@0.0.18': {}

  '@xterm/addon-clipboard@0.2.0-beta.81(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98
      js-base64: 3.7.7

  '@xterm/addon-image@0.9.0-beta.98(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98

  '@xterm/addon-ligatures@0.10.0-beta.98(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98
      font-finder: 1.1.0
      font-ligatures: 1.4.1

  '@xterm/addon-progress@0.2.0-beta.4(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98

  '@xterm/addon-search@0.16.0-beta.98(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98

  '@xterm/addon-serialize@0.14.0-beta.98(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98

  '@xterm/addon-unicode11@0.9.0-beta.98(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98

  '@xterm/addon-webgl@0.19.0-beta.98(@xterm/xterm@5.6.0-beta.98)':
    dependencies:
      '@xterm/xterm': 5.6.0-beta.98

  '@xterm/xterm@5.6.0-beta.98': {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  array-flatten@1.1.1: {}

  balanced-match@1.0.2: {}

  body-parser@1.20.3:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.13.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  bytes@3.1.2: {}

  call-bind-apply-helpers@1.0.1:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bound@1.0.3:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      get-intrinsic: 1.2.7

  chevrotain-allstar@0.3.1(chevrotain@11.0.3):
    dependencies:
      chevrotain: 11.0.3
      lodash-es: 4.17.21

  chevrotain@11.0.3:
    dependencies:
      '@chevrotain/cst-dts-gen': 11.0.3
      '@chevrotain/gast': 11.0.3
      '@chevrotain/regexp-to-ast': 11.0.3
      '@chevrotain/types': 11.0.3
      '@chevrotain/utils': 11.0.3
      lodash-es: 4.17.21

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  cookie-signature@1.0.6: {}

  cookie@0.7.1: {}

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  depd@2.0.0: {}

  destroy@1.2.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  ee-first@1.1.1: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  escape-html@1.0.3: {}

  etag@1.8.1: {}

  express@4.21.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.3
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.7.1
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 2.0.0
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.3.1
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.3
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.12
      proxy-addr: 2.0.7
      qs: 6.13.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.19.0
      serve-static: 1.16.2
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  finalhandler@1.3.1:
    dependencies:
      debug: 2.6.9
      encodeurl: 2.0.0
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  font-finder@1.1.0:
    dependencies:
      get-system-fonts: 2.0.2
      promise-stream-reader: 1.0.1

  font-ligatures@1.4.1:
    dependencies:
      font-finder: 1.1.0
      lru-cache: 6.0.0
      opentype.js: 0.8.0

  forwarded@0.2.0: {}

  fresh@0.5.2: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  get-intrinsic@1.2.7:
    dependencies:
      call-bind-apply-helpers: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-system-fonts@2.0.2: {}

  gopd@1.2.0: {}

  has-symbols@1.1.0: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  immediate@3.0.6: {}

  inherits@2.0.4: {}

  ipaddr.js@1.9.1: {}

  isarray@1.0.0: {}

  js-base64@3.7.7: {}

  jschardet@3.1.4: {}

  jsonc-parser@3.3.1: {}

  jszip@3.10.1:
    dependencies:
      lie: 3.3.0
      pako: 1.0.11
      readable-stream: 2.3.8
      setimmediate: 1.0.5

  langium@3.4.0:
    dependencies:
      chevrotain: 11.0.3
      chevrotain-allstar: 0.3.1(chevrotain@11.0.3)
      vscode-languageserver: 9.0.1
      vscode-languageserver-textdocument: 1.0.12
      vscode-uri: 3.0.8

  lie@3.3.0:
    dependencies:
      immediate: 3.0.6

  lodash-es@4.17.21: {}

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  marked@14.0.0: {}

  math-intrinsics@1.1.0: {}

  media-typer@0.3.0: {}

  merge-descriptors@1.0.3: {}

  methods@1.1.2: {}

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  monaco-editor-wrapper@6.6.0:
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-editor-api': 15.0.2
      '@codingame/monaco-vscode-editor-service-override': 15.0.2
      '@codingame/monaco-vscode-extension-api': 15.0.2
      '@codingame/monaco-vscode-language-pack-cs': 15.0.2
      '@codingame/monaco-vscode-language-pack-de': 15.0.2
      '@codingame/monaco-vscode-language-pack-es': 15.0.2
      '@codingame/monaco-vscode-language-pack-fr': 15.0.2
      '@codingame/monaco-vscode-language-pack-it': 15.0.2
      '@codingame/monaco-vscode-language-pack-ja': 15.0.2
      '@codingame/monaco-vscode-language-pack-ko': 15.0.2
      '@codingame/monaco-vscode-language-pack-pl': 15.0.2
      '@codingame/monaco-vscode-language-pack-pt-br': 15.0.2
      '@codingame/monaco-vscode-language-pack-qps-ploc': 15.0.2
      '@codingame/monaco-vscode-language-pack-ru': 15.0.2
      '@codingame/monaco-vscode-language-pack-tr': 15.0.2
      '@codingame/monaco-vscode-language-pack-zh-hans': 15.0.2
      '@codingame/monaco-vscode-language-pack-zh-hant': 15.0.2
      '@codingame/monaco-vscode-monarch-service-override': 15.0.2
      '@codingame/monaco-vscode-textmate-service-override': 15.0.2
      '@codingame/monaco-vscode-theme-defaults-default-extension': 15.0.2
      '@codingame/monaco-vscode-theme-service-override': 15.0.2
      '@codingame/monaco-vscode-views-service-override': 15.0.2
      '@codingame/monaco-vscode-workbench-service-override': 15.0.2
      monaco-languageclient: 9.5.0
      vscode: '@codingame/monaco-vscode-extension-api@15.0.2'
      vscode-languageclient: 9.0.1
      vscode-languageserver-protocol: 3.17.5
      vscode-ws-jsonrpc: 3.4.0

  monaco-languageclient-examples@2025.3.6(@xterm/xterm@5.6.0-beta.98):
    dependencies:
      '@codingame/monaco-vscode-configuration-service-override': 15.0.2
      '@codingame/monaco-vscode-cpp-default-extension': 15.0.2
      '@codingame/monaco-vscode-debug-service-override': 15.0.2
      '@codingame/monaco-vscode-editor-api': 15.0.2
      '@codingame/monaco-vscode-environment-service-override': 15.0.2
      '@codingame/monaco-vscode-explorer-service-override': 15.0.2
      '@codingame/monaco-vscode-files-service-override': 15.0.2
      '@codingame/monaco-vscode-groovy-default-extension': 15.0.2
      '@codingame/monaco-vscode-java-default-extension': 15.0.2
      '@codingame/monaco-vscode-javascript-default-extension': 15.0.2
      '@codingame/monaco-vscode-json-default-extension': 15.0.2
      '@codingame/monaco-vscode-keybindings-service-override': 15.0.2
      '@codingame/monaco-vscode-lifecycle-service-override': 15.0.2
      '@codingame/monaco-vscode-localization-service-override': 15.0.2
      '@codingame/monaco-vscode-preferences-service-override': 15.0.2
      '@codingame/monaco-vscode-python-default-extension': 15.0.2
      '@codingame/monaco-vscode-remote-agent-service-override': 15.0.2
      '@codingame/monaco-vscode-search-result-default-extension': 15.0.2
      '@codingame/monaco-vscode-search-service-override': 15.0.2
      '@codingame/monaco-vscode-secret-storage-service-override': 15.0.2
      '@codingame/monaco-vscode-standalone-json-language-features': 15.0.2
      '@codingame/monaco-vscode-standalone-languages': 15.0.2
      '@codingame/monaco-vscode-standalone-typescript-language-features': 15.0.2
      '@codingame/monaco-vscode-storage-service-override': 15.0.2
      '@codingame/monaco-vscode-testing-service-override': 15.0.2(@xterm/xterm@5.6.0-beta.98)
      '@codingame/monaco-vscode-textmate-service-override': 15.0.2
      '@codingame/monaco-vscode-theme-defaults-default-extension': 15.0.2
      '@codingame/monaco-vscode-theme-service-override': 15.0.2
      '@codingame/monaco-vscode-typescript-basics-default-extension': 15.0.2
      '@codingame/monaco-vscode-typescript-language-features-default-extension': 15.0.2
      '@codingame/monaco-vscode-views-service-override': 15.0.2
      '@typefox/monaco-editor-react': 6.6.0
      cors: 2.8.5
      express: 4.21.2
      jszip: 3.10.1
      langium: 3.4.0
      monaco-editor-wrapper: 6.6.0
      monaco-languageclient: 9.5.0
      pyright: 1.1.396
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      request-light: 0.8.0
      vscode: '@codingame/monaco-vscode-extension-api@15.0.2'
      vscode-json-languageservice: 5.4.3
      vscode-languageclient: 9.0.1
      vscode-languageserver: 9.0.1
      vscode-uri: 3.1.0
      vscode-ws-jsonrpc: 3.4.0
      ws: 8.18.0
      wtd-core: 4.0.1
    transitivePeerDependencies:
      - '@xterm/xterm'
      - bufferutil
      - supports-color
      - utf-8-validate

  monaco-languageclient@9.5.0:
    dependencies:
      '@codingame/monaco-vscode-api': 15.0.2
      '@codingame/monaco-vscode-configuration-service-override': 15.0.2
      '@codingame/monaco-vscode-editor-api': 15.0.2
      '@codingame/monaco-vscode-editor-service-override': 15.0.2
      '@codingame/monaco-vscode-extension-api': 15.0.2
      '@codingame/monaco-vscode-extensions-service-override': 15.0.2
      '@codingame/monaco-vscode-languages-service-override': 15.0.2
      '@codingame/monaco-vscode-localization-service-override': 15.0.2
      '@codingame/monaco-vscode-log-service-override': 15.0.2
      '@codingame/monaco-vscode-model-service-override': 15.0.2
      vscode: '@codingame/monaco-vscode-extension-api@15.0.2'
      vscode-languageclient: 9.0.1

  ms@2.0.0: {}

  ms@2.1.3: {}

  negotiator@0.6.3: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  opentype.js@0.8.0:
    dependencies:
      tiny-inflate: 1.0.3

  pako@1.0.11: {}

  parseurl@1.3.3: {}

  path-to-regexp@0.1.12: {}

  process-nextick-args@2.0.1: {}

  promise-stream-reader@1.0.1: {}

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  pyright@1.1.396:
    optionalDependencies:
      fsevents: 2.3.3

  qs@6.13.0:
    dependencies:
      side-channel: 1.1.0

  range-parser@1.2.1: {}

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  react-dom@19.0.0(react@19.0.0):
    dependencies:
      react: 19.0.0
      scheduler: 0.25.0

  react@19.0.0: {}

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  request-light@0.8.0: {}

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  scheduler@0.25.0: {}

  semver@7.6.3: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  setimmediate@1.0.5: {}

  setprototypeof@1.2.0: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.7
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  statuses@2.0.1: {}

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  tiny-inflate@1.0.3: {}

  toidentifier@1.0.1: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typescript@5.8.2: {}

  unpipe@1.0.0: {}

  util-deprecate@1.0.2: {}

  utils-merge@1.0.1: {}

  vary@1.1.2: {}

  vscode-json-languageservice@5.4.3:
    dependencies:
      '@vscode/l10n': 0.0.18
      jsonc-parser: 3.3.1
      vscode-languageserver-textdocument: 1.0.12
      vscode-languageserver-types: 3.17.5
      vscode-uri: 3.1.0

  vscode-jsonrpc@8.2.0: {}

  vscode-jsonrpc@8.2.1: {}

  vscode-languageclient@9.0.1:
    dependencies:
      minimatch: 5.1.6
      semver: 7.6.3
      vscode-languageserver-protocol: 3.17.5

  vscode-languageserver-protocol@3.17.5:
    dependencies:
      vscode-jsonrpc: 8.2.0
      vscode-languageserver-types: 3.17.5

  vscode-languageserver-textdocument@1.0.12: {}

  vscode-languageserver-types@3.17.5: {}

  vscode-languageserver@9.0.1:
    dependencies:
      vscode-languageserver-protocol: 3.17.5

  vscode-oniguruma@1.7.0: {}

  vscode-textmate@9.2.0: {}

  vscode-uri@3.0.8: {}

  vscode-uri@3.1.0: {}

  vscode-ws-jsonrpc@3.4.0:
    dependencies:
      vscode-jsonrpc: 8.2.1

  ws@8.18.0: {}

  wtd-core@4.0.1: {}

  yallist@4.0.0: {}
