// Monarch syntax highlighting for the sysml language.
export default {
    keywords: [
        'about','abstract','accept','action','actor','after','alias','all','allocate','allocation','analysis','and','as','assert','assign','assume','at','attribute','bind','binding','by','calc','case','comment','concern','connect','connection','constant','constraint','crosses','decide','def','default','defined','dependency','derived','do','doc','else','end','entry','enum','event','exhibit','exit','expose','false','filter','first','flow','for','fork','frame','from','hastype','if','implies','import','in','include','individual','inout','interface','istype','item','join','language','library','locale','loop','merge','message','meta','metadata','new','nonunique','not','null','objective','occurrence','of','or','ordered','out','package','parallel','part','perform','port','private','protected','public','redefines','ref','references','render','rendering','rep','require','requirement','return','satisfy','send','snapshot','specializes','stakeholder','standard','state','subject','subsets','succession','terminate','then','timeslice','to','transition','true','until','use','variant','variation','verification','verify','via','view','viewpoint','when','while','xor'
    ],
    operators: [
        '!=','!==','#','$','%','&','*','**','+',',','-','->','.','..','.?','/',':','::','::>',':=',':>',':>>',';','<','<=','=','==','===','=>','>','>=','?','??','@','@@','^','|','~'
    ],
    symbols: /!=|!==|#|\$|%|&|\(|\)|\*|\*\*|\+|,|-|->|\.|\.\.|\.\?|\/|:|::|::>|:=|:>|:>>|;|<|<=|=|==|===|=>|>|>=|\?|\?\?|@|@@|\[|\]|\^|\{|\||\}|~/,

    tokenizer: {
        initial: [
            { regex: /([0-9][0-9]*)/, action: {"token":"number"} },
            { regex: /((([0-9][0-9]*))(e|E)(\+|-)?(([0-9][0-9]*)))/, action: {"token":"string"} },
            { regex: /((([a-z]|[A-Z])|_)((([a-z]|[A-Z])|_)|[0-9])*)/, action: { cases: { '@keywords': {"token":"keyword"}, '@default': {"token":"string"} }} },
            { regex: /('((\\(((((((b|t)|n)|f)|r)|")|')|\\))|((?!(\\|'))[\s\S]*?))*')/, action: {"token":"string"} },
            { regex: /("((\\(((((((b|t)|n)|f)|r)|")|')|\\))|((?!(\\|"))[\s\S]*?))*")/, action: {"token":"string"} },
            { regex: /(\/\*([\s\S]*?\*\/))/, action: {"token":"string"} },
            { include: '@whitespace' },
            { regex: /@symbols/, action: { cases: { '@operators': {"token":"operator"}, '@default': {"token":""} }} },
        ],
        whitespace: [
            { regex: /\/\/\*/, action: {"token":"comment","next":"@comment"} },
            { regex: /(\/\/(((?!(\n|\r))[\s\S]*?)((?!(\n|\r))[\s\S]*?))?(\r?\n)?)/, action: {"token":"comment"} },
            { regex: /((( |	)|\r)|\n)+/, action: {"token":"white"} },
        ],
        comment: [
            { regex: /[^//\*]+/, action: {"token":"comment"} },
            { regex: /\*\//, action: {"token":"comment","next":"@pop"} },
            { regex: /[//\*]/, action: {"token":"comment"} },
        ],
    }
};
