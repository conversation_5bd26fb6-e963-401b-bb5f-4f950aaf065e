'use client';

import React, { useState, useCallback } from 'react';
import { useSysML } from '@/contexts/SysMLContext';
import { useSysMLToast } from '@/components/ui/sysml-toast';
import { EditorTab } from '@/types/sysml';
import SysMLWrapperEditor from './SysMLWrapperEditor';
import MonacoEditor from './MonacoEditor';

interface EnhancedSysMLEditorProps {
  activeTab: EditorTab | null;
}

type EditorMode = 'wrapper' | 'legacy';

const EnhancedSysMLEditor: React.FC<EnhancedSysMLEditorProps> = ({
  activeTab,
}) => {
  const { updateTabContent } = useSysML();
  const { showToast } = useSysMLToast();
  const [editorMode, setEditorMode] = useState<EditorMode>('wrapper');
  const [isLoading, setIsLoading] = useState(false);

  // 处理内容变化
  const handleContentChange = useCallback((newContent: string) => {
    if (activeTab) {
      updateTabContent(activeTab.id, newContent);
    }
  }, [activeTab, updateTabContent]);

  // 处理保存
  const handleSave = useCallback(() => {
    if (activeTab) {
      showToast(`已保存文件: ${activeTab.name}`, 'success');
    }
  }, [activeTab, showToast]);

  // 切换编辑器模式
  const toggleEditorMode = useCallback(() => {
    setIsLoading(true);
    setTimeout(() => {
      setEditorMode(prev => prev === 'wrapper' ? 'legacy' : 'wrapper');
      setIsLoading(false);
      showToast(
        `已切换到${editorMode === 'wrapper' ? '传统' : 'Langium'}编辑器模式`,
        'info'
      );
    }, 100);
  }, [editorMode, showToast]);

  if (!activeTab) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-50">
        <div className="text-center">
          <div className="text-gray-500 text-lg mb-2">没有打开的文件</div>
          <div className="text-gray-400 text-sm">请从左侧文件树中选择一个文件进行编辑</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* 编辑器工具栏 */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
        <div className="flex items-center space-x-4">
          <div className="text-sm font-medium text-gray-700">
            {activeTab.name}
          </div>
          <div className="text-xs text-gray-500">
            {activeTab.content.length} 字符
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* 编辑器模式切换 */}
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-600">编辑器模式:</span>
            <button
              onClick={toggleEditorMode}
              disabled={isLoading}
              className={`px-3 py-1 text-xs rounded-md transition-colors ${
                editorMode === 'wrapper'
                  ? 'bg-blue-100 text-blue-700 border border-blue-300'
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              } hover:bg-blue-50 disabled:opacity-50`}
            >
              {isLoading ? '切换中...' : editorMode === 'wrapper' ? 'Langium' : '传统'}
            </button>
          </div>
          
          {/* 保存按钮 */}
          <button
            onClick={handleSave}
            className="px-3 py-1 text-xs bg-green-100 text-green-700 border border-green-300 rounded-md hover:bg-green-50 transition-colors"
          >
            保存 (Ctrl+S)
          </button>
        </div>
      </div>

      {/* 编辑器内容区域 */}
      <div className="flex-1 relative">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
              <div className="text-sm text-gray-600">正在切换编辑器...</div>
            </div>
          </div>
        )}
        
        {editorMode === 'wrapper' ? (
          <SysMLWrapperEditor
            value={activeTab.content}
            onChange={handleContentChange}
            onSave={handleSave}
            height="100%"
            readOnly={false}
          />
        ) : (
          <MonacoEditor
            value={activeTab.content}
            onChange={handleContentChange}
            onSave={handleSave}
            language="sysml"
            theme="vs-dark"
            readOnly={false}
            height="100%"
          />
        )}
      </div>

      {/* 状态栏 */}
      <div className="flex items-center justify-between px-4 py-1 bg-gray-50 border-t border-gray-200 text-xs text-gray-600">
        <div className="flex items-center space-x-4">
          <span>SysML v2</span>
          <span>UTF-8</span>
          <span>LF</span>
        </div>
        <div className="flex items-center space-x-4">
          <span>
            {editorMode === 'wrapper' ? 'Langium 语言服务器' : '基础语法高亮'}
          </span>
          <span>行 1, 列 1</span>
        </div>
      </div>
    </div>
  );
};

export default EnhancedSysMLEditor;
