// 用户相关类型
export interface User {
  id: string;
  username: string;
  email?: string;
  createdAt: Date;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// 项目相关类型
export interface Project {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
  ownerId: string;
  namespaces: Namespace[];
}

export interface Namespace {
  id: string;
  name: string;
  projectId: string;
  createdAt: Date;
  diagrams: SysMLDiagram[];
}

export interface SysMLDiagram {
  id: string;
  name: string;
  namespaceId: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
}

// 编辑器相关类型
export interface EditorTab {
  id: string;
  title: string;
  content: string;
  isDirty: boolean;
  diagramId?: string;
}

export interface EditorState {
  tabs: EditorTab[];
  activeTabId: string | null;
  isLoading: boolean;
}

// 项目树节点类型
export interface TreeNode {
  id: string;
  name: string;
  type: 'project' | 'namespace' | 'diagram';
  children?: TreeNode[];
  parentId?: string;
  isExpanded?: boolean;
}

// 代码诊断类型
export interface DiagnosticMessage {
  id: string;
  severity: 'error' | 'warning' | 'info';
  message: string;
  line: number;
  column: number;
  source?: string;
}

// 上下文菜单类型
export interface ContextMenuItem {
  id: string;
  label: string;
  icon?: string;
  action: () => void;
  separator?: boolean;
  disabled?: boolean;
}

export interface ContextMenuState {
  isVisible: boolean;
  x: number;
  y: number;
  items: ContextMenuItem[];
  targetId?: string;
}

// Modal 相关类型
export interface ModalState {
  isOpen: boolean;
  type: 'create-project' | 'rename-project' | 'delete-confirm' | 'create-namespace' | 'create-diagram' | null;
  data?: Record<string, unknown>;
}

// API 响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// 表单验证类型
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState {
  isSubmitting: boolean;
  errors: ValidationError[];
}

// SVG 渲染相关类型
export interface SVGElement {
  id: string;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  properties: Record<string, unknown>;
}

export interface DiagramLayout {
  elements: SVGElement[];
  connections: Connection[];
  bounds: {
    width: number;
    height: number;
  };
}

export interface Connection {
  id: string;
  sourceId: string;
  targetId: string;
  type: string;
  points: Point[];
}

export interface Point {
  x: number;
  y: number;
}

// 应用状态类型
export interface AppState {
  auth: AuthState;
  projects: Project[];
  editor: EditorState;
  contextMenu: ContextMenuState;
  modal: ModalState;
  diagnostics: DiagnosticMessage[];
  selectedProjectId: string | null;
  isLoading: boolean;
}

// 事件类型
export interface AppEvent {
  type: string;
  payload?: unknown;
}

// 工具栏按钮类型
export interface ToolbarButton {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  disabled?: boolean;
  hotkey?: string;
}

// 状态栏信息类型
export interface StatusBarInfo {
  message: string;
  progress?: number;
  type?: 'info' | 'success' | 'warning' | 'error';
}
