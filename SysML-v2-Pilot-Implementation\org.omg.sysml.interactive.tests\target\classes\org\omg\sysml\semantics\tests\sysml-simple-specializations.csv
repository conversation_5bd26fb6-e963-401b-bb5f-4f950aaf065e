true,checkItemDefinitionSpecialization,ItemDefinition,Items::Item,Package,OwningMembership
true,checkPartDefinitionSpecialization,PartDefinition,Parts::Part,Package,OwningMembership
true,checkPortDefinitionSpecialization,PortDefinition,Ports::Port,Package,OwningMembership
true,checkConnectionDefinitionSpecialization,ConnectionDefinition,Connections::Connection,Package,OwningMembership
true,checkFlowDefinitionSpecialization,FlowDefinition,Flows::MessageAction,Package,OwningMembership
true,checkInterfaceDefinitionSpecialization,InterfaceDefinition,Interfaces::Interface,Package,OwningMembership
true,checkAllocationDefinitionSpecialization,AllocationDefinition,Allocations::Allocation,Package,OwningMembership
true,checkActionDefinitionSpecialization,ActionDefinition,Actions::Action,Package,OwningMembership
true,checkStateDefinitionSpecialization,StateDefinition,States::StateAction,Package,OwningMembership
true,checkCalculationDefinitionSpecialization,CalculationDefinition,Calculations::Calculation,Package,OwningMembership
true,checkConstraintDefinitionSpecialization,ConstraintDefinition,Constraints::ConstraintCheck,Package,OwningMembership
true,checkRequirementDefinitionSpecialization,RequirementDefinition,Requirements::RequirementCheck,Package,OwningMembership
true,checkConcernDefinitionSpecialization,ConcernDefinition,Requirements::ConcernCheck,Package,OwningMembership
true,checkCaseDefinitionSpecialization,CaseDefinition,Cases::Case,Package,OwningMembership
true,checkAnalysisCaseDefinitionSpecialization,AnalysisCaseDefinition,AnalysisCases::AnalysisCase,Package,OwningMembership
true,checkVerificationCaseDefinitionSpecialization,VerificationCaseDefinition,VerificationCases::VerificationCase,Package,OwningMembership
true,checkUseCaseDefinitionSpecialization,UseCaseDefinition,UseCases::UseCase,Package,OwningMembership
true,checkViewDefinitionSpecialization,ViewDefinition,Views::View,Package,OwningMembership
true,checkViewpointDefinitionSpecialization,ViewpointDefinition,Views::ViewpointCheck,Package,OwningMembership
true,checkRenderingDefinitionSpecialization,RenderingDefinition,Views::Rendering,Package,OwningMembership
true,checkMetadataDefinitionSpecialization,MetadataDefinition,Metadata::MetadataItem,Package,OwningMembership
true,checkAttributeUsageSpecialization,AttributeUsage,Base::dataValues,Package,OwningMembership
true,checkEventOccurrenceSpecialization,EventOccurrenceUsage,Occurrences::Occurrence::timeEnclosedOccurrences,OccurrenceDefinition,FeatureMembership
true,checkEventOccurrenceSpecialization,EventOccurrenceUsage,Occurrences::Occurrence::timeEnclosedOccurrences,OccurrenceUsage,FeatureMembership
true,checkOccurrenceUsageSpecialization,OccurrenceUsage,Occurrences::occurrences,Package,OwningMembership
true,checkOccurrenceUsageSuboccurrencesSpecialization,OccurrenceUsage,Occurrences::Occurrence::suboccurrences,OccurrenceUsage,FeatureMembership
true,checkOccurrenceUsageSuboccurrencesSpecialization,OccurrenceUsage,Occurrences::Occurrence::suboccurrences,Class,FeatureMembership
true,checkItemUsageSpecialization,ItemUsage,Items::items,Package,OwningMembership
true,checkItemUsageSubitemSpecialization,ItemUsage,Items::Item::subitems,ItemUsage,FeatureMembership
true,checkItemUsageSubitemSpecialization,ItemUsage,Items::Item::subitems,ItemDefinition,FeatureMembership
true,checkPartUsageSpecialization,PartUsage,Parts::parts,Package,OwningMembership
true,checkPartUsageSubpartSpecialization,PartUsage,Items::Item::subparts,ItemUsage,FeatureMembership
true,checkPartUsageSubpartSpecialization,PartUsage,Items::Item::subparts,ItemDefinition,FeatureMembership
true,checkPartUsageActorSpecialization,PartUsage,Requirements::RequirementCheck::actors,RequirementDefinition,ActorMembership
true,checkPartUsageActorSpecialization,PartUsage,Requirements::RequirementCheck::actors,RequirementUsage,ActorMembership
true,checkPartUsageActorSpecialization,PartUsage,Requirements::RequirementCheck::stakeholders,RequirementDefinition,StakeholderMembership
true,checkPartUsageStakeholderSpecialization,PartUsage,Requirements::RequirementCheck::stakeholders,RequirementUsage,StakeholderMembership
true,checkPartUsageActorSpecialization,PartUsage,Cases::Case::actors,CaseDefinition,ActorMembership
true,checkPartUsageActorSpecialization,PartUsage,Cases::Case::actors,CaseUsage,ActorMembership
true,checkPortUsageSpecialization,PortUsage,Ports::ports,Package,OwningMembership
true,checkPortUsageSubportSpecialization,PortUsage,Ports::Port::subports,PortUsage,FeatureMembership
true,checkPortUsageSubportSpecialization,PortUsage,Ports::Port::subports,PortDefinition,FeatureMembership
true,checkPortUsageOwnedPortSpecialization,PortUsage,Parts::Part::ownedPorts,PartUsage,FeatureMembership
true,checkPortUsageOwnedPortSpecialization,PortUsage,Parts::Part::ownedPorts,PartDefinition,FeatureMembership
true,checkConnectionUsageSpecialization,ConnectionUsage,Connections::connections,Package,OwningMembership
true,checkFlowUsageSpecialization,FlowUsage,Flows::messages,Package,OwningMembership
true,checkSuccessionFlowUsageSpecialization,SuccessionFlowUsage,Flows::successionFlows,Package,OwningMembership
true,checkInterfaceUsageSpecialization,InterfaceUsage,Interfaces::interfaces,Package,OwningMembership
true,checkAllocationUsageSpecialization,AllocationUsage,Allocations::allocations,Package,OwningMembership
true,checkActionUsageSpecialization,ActionUsage,Actions::actions,Package,OwningMembership
true,checkActionUsageSubactionSpecialization,ActionUsage,Actions::Action::subactions,ActionDefinition,FeatureMembership
true,checkActionUsageSubactionSpecialization,ActionUsage,Actions::Action::subactions,ActionUsage,FeatureMembership
true,checkActionUsageOwnedActionSpecialization,ActionUsage,Parts::Part::ownedActions,PartUsage,FeatureMembership
true,checkActionUsageOwnedActionSpecialization,ActionUsage,Parts::Part::ownedActions,PartDefinition,FeatureMembership
true,checkJoinNodeSpecialization,JoinNode,Actions::Action::joins,ActionDefinition,FeatureMembership
true,checkForkNodeSpecialization,ForkNode,Actions::Action::forks,ActionDefinition,FeatureMembership
true,checkMergeNodeSpecialization,MergeNode,Actions::Action::merges,ActionDefinition,FeatureMembership
true,checkDecisionNodeSpecialization,DecisionNode,Actions::Action::decisions,ActionDefinition,FeatureMembership
true,checkSendActionUsageSubactionSpecialization,SendActionUsage,Actions::Action::sendSubactions,ActionUsage,FeatureMembership
true,checkAcceptActionUsageSubactionSpecialization,AcceptActionUsage,Actions::Action::acceptSubactions,ActionUsage,FeatureMembership
true,checkIfActionUsageSubactionSpecialization,IfActionUsage,Actions::Action::ifSubactions,ActionUsage,FeatureMembership
true,checkWhileLoopActionUsageSubactionSpecialization,WhileLoopActionUsage,Actions::Action::whileLoops,ActionUsage,FeatureMembership
true,checkForLoopActionUsageSubactionSpecialization,ForLoopActionUsage,Actions::Action::forLoops,ActionUsage,FeatureMembership
true,checkPerformActionUsageSpecialization,PerformActionUsage,Parts::Part::performedActions,PartUsage,FeatureMembership
true,checkTerminateActionUsageSubactionSpecialization,TerminateActionUsage,Actions::Action::terminateSubactions,ActionDefinition,FeatureMembership
true,checkTerminateActionUsageSubactionSpecialization,TerminateActionUsage,Actions::Action::terminateSubactions,ActionUsage,FeatureMembership
true,checkPerformActionUsageSpecialization,PerformActionUsage,Parts::Part::performedActions,PartDefinition,FeatureMembership
true,checkStateUsageSpecialization,StateUsage,States::stateActions,Package,OwningMembership
true,checkStateUsageExclusiveStateSpecialization,StateUsage,States::StateAction::exclusiveStates,StateUsage,FeatureMembership
true,checkStateUsageOwnedStateSpecialization,StateUsage,Parts::Part::ownedStates,PartUsage,FeatureMembership
true,checkStateUsageOwnedStateSpecialization,StateUsage,Parts::Part::ownedStates,PartDefinition,FeatureMembership
true,checkTransitionUsageSpecialization,TransitionUsage,Actions::transitionActions,Package,OwningMembership
false,checkTransitionUsageStateSpecialization,TransitionUsage,States::StateAction::stateTransitions,StateDefinition,FeatureMembership
false,checkTransitionUsageStateSpecialization,TransitionUsage,States::StateAction::stateTransitions,StateUsage,FeatureMembership
true,checkTransitionUsageActionSpecialization,TransitionUsage,Actions::Action::decisionTransitions,ActionUsage,FeatureMembership
true,checkTransitionUsageActionSpecialization,TransitionUsage,Actions::Action::decisionTransitions,ActionDefinition,FeatureMembership
true,checkCalculationUsageSpecialization,CalculationUsage,Calculations::calculations,Package,OwningMembership
true,checkCalculationUsageSubcalculationSpecialization,CalculationUsage,Calculations::Calculation::subcalculations,CalculationUsage,FeatureMembership
true,checkCalculationUsageSubcalculationSpecialization,CalculationUsage,Calculations::Calculation::subcalculations,CalculationDefinition,FeatureMembership
true,checkConstraintUsageSpecialization,ConstraintUsage,Constraints::constraintChecks,Package,OwningMembership
true,checkConstraintUsageCheckedConstraintSpecialization,ConstraintUsage,Items::Item::checkedConstraints,ItemUsage,FeatureMembership
true,checkConstraintUsageCheckedConstraintSpecialization,ConstraintUsage,Items::Item::checkedConstraints,ItemDefinition,FeatureMembership
true,checkRequirementUsageSpecialization,RequirementUsage,Requirements::requirementChecks,Package,OwningMembership
true,checkRequirementUsageSubrequirementSpecialization,RequirementUsage,Requirements::RequirementCheck::subrequirements,RequirementDefinition,FeatureMembership
true,checkRequirementUsageSubrequirementSpecialization,RequirementUsage,Requirements::RequirementCheck::subrequirements,RequirementUsage,FeatureMembership
true,checkConcernUsageSpecialization,ConcernUsage,Requirements::concernChecks,Package,OwningMembership
true,checkConcernUsageFramedConcernSpecialization,ConcernUsage,Requirements::RequirementCheck::concerns,PartUsage,FramedConcernMembership
true,checkCaseUsageSpecialization,CaseUsage,Cases::cases,Package,OwningMembership
true,checkCaseUsageSubcaseSpecialization,CaseUsage,Cases::Case::subcases,CaseUsage,FeatureMembership
true,checkAnalysisCaseUsageSpecialization,AnalysisCaseUsage,AnalysisCases::analysisCases,Package,OwningMembership
true,checkAnalysisCaseUsageSubAnalysisCaseSpecialization,AnalysisCaseUsage,AnalysisCases::AnalysisCase::subAnalysisCases,AnalysisCaseUsage,FeatureMembership
true,checkAnalysisCaseUsageSubAnalysisCaseSpecialization,AnalysisCaseUsage,AnalysisCases::AnalysisCase::subAnalysisCases,AnalysisCaseDefinition,FeatureMembership
true,checkVerificationCaseUsageSpecialization,VerificationCaseUsage,VerificationCases::verificationCases,Package,OwningMembership
true,checkVerificationCaseUsageSubVerificationCaseSpecialization,VerificationCaseUsage,VerificationCases::VerificationCase::subVerificationCases,VerificationCaseUsage,FeatureMembership
true,checkVerificationCaseUsageSubVerificationCaseSpecialization,VerificationCaseUsage,VerificationCases::VerificationCase::subVerificationCases,VerificationCaseDefinition,FeatureMembership
true,checkUseCaseUsageSpecialization,UseCaseUsage,UseCases::useCases,Package,OwningMembership
true,checkUseCaseUsageSubUseCaseSpecialization,UseCaseUsage,UseCases::UseCase::subUseCases,UseCaseUsage,FeatureMembership
true,checkUseCaseUsageSubUseCaseSpecialization,UseCaseUsage,UseCases::UseCase::subUseCases,UseCaseDefinition,FeatureMembership
true,checkIncludeUseCaseUsageSpecialization,IncludeUseCaseUsage,UseCases::UseCase::includedUseCases,UseCaseUsage,FeatureMembership
true,checkIncludeUseCaseUsageSpecialization,IncludeUseCaseUsage,UseCases::UseCase::includedUseCases,UseCaseDefinition,FeatureMembership
true,checkViewUsageSpecialization,ViewUsage,Views::views,Package,OwningMembership
true,checkViewUsageSubviewSpecialization,ViewUsage,Views::View::subviews,ViewUsage,FeatureMembership
true,checkViewUsageSubviewSpecialization,ViewUsage,Views::View::subviews,ViewDefinition,FeatureMembership
true,checkViewpointUsageSpecialization,ViewpointUsage,Views::viewpointChecks,Package,OwningMembership
true,checkViewpointUsageViewpointSatisfactionSpecialization,ViewpointUsage,Views::View::viewpointSatisfactions,ViewDefinition,FeatureMembership
true,checkViewpointUsageViewpointSatisfactionSpecialization,ViewpointUsage,Views::View::viewpointSatisfactions,ViewUsage,FeatureMembership
true,checkRenderingUsageSpecialization,RenderingUsage,Views::renderings,Package,OwningMembership
true,checkRenderingUsageSubrenderingSpecialization,RenderingUsage,Views::Rendering::subrenderings,RenderingUsage,FeatureMembership
true,checkRenderingUsageSubrenderingSpecialization,RenderingUsage,Views::Rendering::subrenderings,RenderingDefinition,FeatureMembership
true,checkMetadataUsageSpecialization,MetadataUsage,Metadata::metadataItems,Package,OwningMembership
