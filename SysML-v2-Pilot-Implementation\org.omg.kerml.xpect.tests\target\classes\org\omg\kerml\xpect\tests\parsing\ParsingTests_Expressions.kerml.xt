//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/library/Links.kerml"}
		File {from ="/library/Occurrences.kerml"}
		File {from ="/library/Performances.kerml"}
		File {from ="/library/ScalarValues.kerml"}
		File {from ="/library/BaseFunctions.kerml"}
		File {from ="/library/DataFunctions.kerml"}
		File {from ="/library/ScalarFunctions.kerml"}
		File {from ="/library/NumericalFunctions.kerml"}
		File {from ="/library/ControlFunctions.kerml"}
		File {from ="/library/FeatureReferencingPerformances.kerml"}
		File {from ="/library/ControlPerformances.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/library/Links.kerml"}
				File {from ="/library/Occurrences.kerml"}
				File {from ="/library/Performances.kerml"}
				File {from ="/library/ScalarValues.kerml"}
				File {from ="/library/BaseFunctions.kerml"}
				File {from ="/library/DataFunctions.kerml"}
				File {from ="/library/ScalarFunctions.kerml"}
				File {from ="/library/NumericalFunctions.kerml"}
				File {from ="/library/ControlFunctions.kerml"}
				File {from ="/library/FeatureReferencingPerformances.kerml"}
				File {from ="/library/ControlPerformances.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package Expressions {
	public import ScalarFunctions::*;
	public import BaseFunctions::ToString;
	public import ControlFunctions::*;
	
	a: Integer;
	aa : Boolean;
	x = ToString(a * a + 3 == 4);
	y = NumericalFunctions::'+'(1,2);
	z : Boolean = aa & true xor zz | false implies z;
	zz : Boolean = aa and true xor aa or false implies z;
	grp = -x + x * y * y + a ** 3 ^ 4;
	
	b = if x > y? x-y else y-x;
	c = x->collect {in xx; xx + 1}; 
	c1 = x.{in xx; xx + 1}; 
	d = x->select {in xx; xx != null};
	d1 = x.?{in xx; xx != null};
	e = x->reduce {in s; in t; s + t}->reduce '+';
	
	behavior w { 
		inout v : Integer;
	    step : ControlPerformances::LoopPerformance {
    		in expr whileTest {v > 3}
    		in step body {
    			step decrement {
    				out v_decr : Integer = v - 1;			
    			}
    			succession decrement then update;
    			step update : FeatureReferencingPerformances::FeatureWritePerformance {
    				in onOccurrence = w::self {
    					feature redefines startingAt : w {
    						inout feature redefines accessedFeature redefines v;
    					}
    				}
    				inout replacementValues = decrement.v_decr;
    			}
    		}
		}
	}
	
	xx = if x == 1 and y == 2? a
	     else if x == 2? b
	     else if x == 3? c
	     else 0;
    
    function TotalMass { in partMass; in subparts;
		partMass + (subparts->collect {in p; totalMass(partMass, subparts)}->reduce '+' ?? 0.0)
	}
	
	expr totalMass: TotalMass { in mass; in sub; }
	
	feature f {
		expr s {
			in x;
			return : Boolean;
		}
	}
	
	bb : Boolean = f.s(1);
	
	class C {
		var count := 0;
	}
	
	feature obj1 : C;
	feature obj2 : C;
	
	test1 = obj1 === obj2;
	test2 = x !== obj2;
	
	class L {
		feature c : C[*];
		feature count : ScalarValues::Integer = c#(1).count;
	}
	
	feature l = new L();
	feature w1 = w(xx);
}
