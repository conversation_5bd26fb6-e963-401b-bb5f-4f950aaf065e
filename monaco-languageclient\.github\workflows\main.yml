name: Build, <PERSON>t and Test

on:
  push:
    branches:
      - 'main'
    tags-ignore:
      - '**'
  pull_request:
    branches:
      - main
      - dev
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: <PERSON><PERSON>
      uses: volta-cli/action@v4
      with:
        node-version: 20

    - name: Use pnpm
      uses: pnpm/action-setup@v3
      with:
        version: 9

    - name: Install
      shell: bash
      run: |
        npm ci

    - name: Versions Report
      shell: bash
      run: |
        npm run report:versions

    - name: Build
      shell: bash
      run: |
        npm run build

    - name: Lint
      shell: bash
      run: |
        npm run lint

    - name: Test
      shell: bash
      run: |
        npm run test:install
        npm run test:run
