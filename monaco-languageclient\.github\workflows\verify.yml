name: Verification Builds

on:
  push:
    branches:
      - 'verify'
  workflow_dispatch:

jobs:
  verify:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Volta
      uses: volta-cli/action@v4

    - name: Use pnpm
      uses: pnpm/action-setup@v3
      with:
        version: 9

    - name: Install
      shell: bash
      run: |
        npm ci

    - name: Versions Report
      shell: bash
      run: |
        npm run report:versions

    - name: Execute verification
      shell: bash
      run: |
        bash ./verify/buildPeers.sh
        bash ./verify/buildAll.sh
