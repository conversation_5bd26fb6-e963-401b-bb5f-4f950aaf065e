{"private": true, "scripts": {"verify": "npm install && npm run dev", "verify:ci": "npm install && npm run build -d", "dev": "next dev -p 8081", "dev:turbo": "next dev --turbopack -p 8081", "build": "next build", "start": "next start", "clean": "shx rm -fr dist .next *.tsbuildinfo"}, "type": "module", "dependencies": {"@typefox/monaco-editor-react": "~6.9.0", "monaco-languageclient-examples": "~2025.6.2", "next": "~15.2.2", "react": "~19.0.0", "react-dom": "~19.0.0"}, "devDependencies": {"@types/node": "~22.13.10", "@types/react": "~19.0.10", "@types/react-dom": "~19.0.4", "shx": "~0.3.4", "typescript": "~5.8.2"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}}