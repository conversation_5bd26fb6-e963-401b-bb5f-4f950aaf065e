//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.local.KerMLImportLocalTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package Import_QualifiedName {
	package P1 {
		classifier A;
	}
	package P2 {
		package P2a {
			public import P1::*;
		}
		//* XPECT scope at P2a::A ---
			   Import_QualifiedName.P1.A, Import_QualifiedName.P1.A.self,
			   Import_QualifiedName.P1.A.self.that, Import_QualifiedName.P2.P2a.A, Import_QualifiedName.P2.P2a.A.self,
			   Import_QualifiedName.P2.P2a.A.self.that, Import_QualifiedName.P2.x, Import_QualifiedName.P2.x.self,
			   Import_QualifiedName.P2.x.self.that, Import_QualifiedName.P2.x.that, Import_QualifiedName.P2.x.that.self, P1.A,
			   P1.A.self, P1.A.self.that, P2.P2a.A, P2.P2a.A.self, P2.P2a.A.self.that, P2.x, P2.x.self,
			   P2.x.self.that, P2.x.that, P2.x.that.self, P2a.A, P2a.A.self, P2a.A.self.that, x, x.self,
			   x.self.that, x.that, x.that.self
		--- */
		// The following should not fail.
		// A is a member of P2a because of the public import.
		x: P2a::A;
	}
}
