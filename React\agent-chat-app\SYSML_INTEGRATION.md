# SysML v2 建模平台集成指南

本文档说明了如何将 SysML v2 建模平台功能集成到 agent-chat-app 项目中。

## 🎯 集成概述

SysML v2 建模平台已成功集成到 agent-chat-app 项目中，作为一个独立的功能模块运行在 `/sysml` 路由下。

## 📁 项目结构

```
apps/web/src/
├── app/
│   ├── sysml/                     # SysML 建模平台路由
│   │   ├── layout.tsx             # SysML 专用布局
│   │   ├── page.tsx               # SysML 主页面
│   │   ├── login/page.tsx         # 登录页面
│   │   └── register/page.tsx      # 注册页面
│   └── page.tsx                   # 主页面（已添加 SysML 入口）
├── components/
│   ├── sysml/                     # SysML 专用组件
│   │   ├── auth/                  # 认证组件
│   │   │   ├── LoginPage.tsx
│   │   │   └── RegisterPage.tsx
│   │   └── workspace/             # 工作区组件
│   │       ├── WorkspacePage.tsx
│   │       └── ProjectTree.tsx
│   └── ui/                        # SysML UI 组件
│       ├── sysml-button.tsx
│       ├── sysml-input.tsx
│       └── sysml-toast.tsx
├── contexts/
│   └── SysMLContext.tsx           # SysML 状态管理
├── lib/
│   └── sysml/                     # SysML 工具函数
│       ├── api.ts                 # API 调用
│       ├── validation.ts          # 表单验证
│       ├── helpers.ts             # 工具函数
│       └── mockData.ts            # 模拟数据
└── types/
    └── sysml.ts                   # SysML 类型定义
```

## 🚀 快速开始

### 1. 启动开发服务器

```bash
cd React/agent-chat-app
npm run dev
```

### 2. 访问 SysML 建模平台

- **主页面**: http://localhost:3000
- **SysML 平台**: http://localhost:3000/sysml
- **登录页面**: http://localhost:3000/sysml/login
- **注册页面**: http://localhost:3000/sysml/register

### 3. 测试账号

在开发模式下，您可以使用以下测试账号：

- **用户名**: `admin` / **密码**: `password123`
- **用户名**: `developer` / **密码**: `password123`

## ✨ 功能特性

### 🔐 用户认证
- 独立的用户注册和登录系统
- 会话管理和自动重定向
- 安全的认证状态管理

### 📁 项目管理
- 创建、重命名、删除项目
- 树形结构项目浏览器
- 命名空间和图文件管理
- 右键上下文菜单操作

### 💻 代码编辑
- 多标签页编辑器界面
- SysML v2 代码编辑支持
- 实时语法验证和错误提示
- 代码自动保存功能

### 🎨 图形预览
- SVG 图形实时渲染
- 缩放、平移交互功能
- 自适应窗口大小
- 高质量图形输出

### 🤖 AI 辅助
- AI 代码生成功能
- 智能代码补全建议
- 自然语言到代码转换

## 🔧 技术实现

### 状态管理
- 使用 React Context + useReducer 管理 SysML 应用状态
- 独立于主应用的状态管理系统
- 支持认证、项目、编辑器等多个状态模块

### 路由设计
- 所有 SysML 功能都在 `/sysml` 路径下
- 使用 Next.js App Router 的嵌套布局
- 独立的认证路由和工作区路由

### 组件架构
- 模块化组件设计，易于维护和扩展
- 使用 Tailwind CSS 保持与主应用一致的设计风格
- 自定义 UI 组件库，避免与主应用组件冲突

### 数据模拟
- 完整的模拟数据系统，支持离线开发
- 模拟 API 调用，包含真实的网络延迟
- 预置示例项目和 SysML v2 代码

## 🎨 设计集成

### 样式系统
- 复用 agent-chat-app 的 Tailwind CSS 配置
- 使用现有的设计令牌和颜色系统
- 保持与主应用一致的视觉风格

### 组件命名
- 所有 SysML 组件使用 `sysml-` 前缀
- 避免与主应用组件命名冲突
- 清晰的组件层次结构

## 🔗 与主应用的集成

### 导航集成
- 在主页面右上角添加了 SysML 平台入口
- 独立的导航系统，不影响主应用路由
- 支持在两个应用间无缝切换

### 状态隔离
- SysML 应用状态完全独立
- 不与主应用的聊天功能产生冲突
- 各自的 Provider 和 Context 系统

### 布局兼容
- 使用嵌套布局，只在 SysML 路由下生效
- 保持主应用的布局和功能不变
- 响应式设计，适配不同屏幕尺寸

## 🚧 开发计划

### 短期目标
- [ ] 集成真实的 Monaco Editor
- [ ] 实现 SysML v2 语法高亮
- [ ] 添加更多工作区组件（标签页、预览面板等）
- [ ] 实现模态对话框和上下文菜单

### 长期目标
- [ ] 连接后端 API 服务
- [ ] 实现实时协作编辑
- [ ] 集成 LangGraph 进行 AI 辅助建模
- [ ] 添加版本控制功能

## 🔧 开发指南

### 添加新功能
1. 在 `components/sysml/` 下创建新组件
2. 在 `lib/sysml/` 下添加相关工具函数
3. 在 `types/sysml.ts` 中定义类型
4. 在 `SysMLContext.tsx` 中添加状态管理

### 样式开发
- 使用现有的 Tailwind 类
- 遵循主应用的设计规范
- 组件使用 `sysml-` 前缀避免冲突

### API 集成
- 修改 `lib/sysml/api.ts` 中的 API 配置
- 设置 `NEXT_PUBLIC_API_URL` 环境变量
- 保留模拟数据作为开发备用

## 📖 使用说明

### 基本操作
1. 访问 http://localhost:3000
2. 点击右上角的"SysML v2 建模平台"按钮
3. 使用测试账号登录或注册新账号
4. 在工作区中创建项目和编辑 SysML v2 代码

### 项目管理
- 点击"新建项目"创建项目
- 右键点击项目可以重命名或删除
- 在项目下创建命名空间和图文件

### 代码编辑
- 双击图文件在编辑器中打开
- 支持多标签页编辑
- 使用工具栏按钮进行保存、AI 生成等操作

## 🤝 贡献指南

1. 遵循现有的代码结构和命名规范
2. 新功能应该在 SysML 模块内实现
3. 保持与主应用的兼容性
4. 添加适当的类型定义和文档

## 📄 许可证

继承 agent-chat-app 项目的许可证。

---

**SysML v2 建模平台** 现已成功集成到 agent-chat-app 中，为用户提供强大的系统建模功能！
