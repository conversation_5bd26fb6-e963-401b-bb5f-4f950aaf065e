//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/

package Classes {
	// XPECT errors --> "Couldn't resolve reference to Membership 'VisibilityPackage::c_Private'." at "VisibilityPackage::c_Private"
	public import VisibilityPackage::c_Private;
	// XPECT errors --> "Couldn't resolve reference to Classifier 'c_Private::c_private'." at "c_Private::c_private"
	classifier try specializes c_Private::c_private{}
	// XPECT errors --> "Couldn't resolve reference to Type 'c_Private::c_private'." at "c_Private::c_private"
	feature f : c_Private::c_private;
}
