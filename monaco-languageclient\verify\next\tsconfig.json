{"compilerOptions": {"target": "ES2022", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "lib": ["ES2022", "DOM", "DOM.Iterable"], "rootDir": ".", "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "noEmit": true}, "include": ["dist/types/**/*.ts", "src/**/*.ts", "src/**/*.tsx", "src/next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["dist/**/*", "lib/**/*", "node_modules/**/*"]}