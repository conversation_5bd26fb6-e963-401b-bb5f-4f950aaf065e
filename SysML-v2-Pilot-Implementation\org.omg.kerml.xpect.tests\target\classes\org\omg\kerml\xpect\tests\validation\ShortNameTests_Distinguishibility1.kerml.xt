//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		File {from ="/library/Base.kerml"}
		ThisFile {}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

package Test {
	 //short names are the same
	 //XPECT warnings --> "Duplicate of other owned member name" at "one"
	 classifier <one> two;
	 //XPECT warnings --> "Duplicate of other owned member name" at "one"
	 classifier <one> three;
}

