'use client';

import React, { useState, useEffect } from 'react';

interface DiagnosticResult {
  name: string;
  status: 'success' | 'error' | 'warning' | 'loading';
  message: string;
  details?: string;
}

const EditorDiagnostics: React.FC = () => {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    const results: DiagnosticResult[] = [];

    // 测试 Monaco Editor 加载
    try {
      const monaco = await import('monaco-editor');
      results.push({
        name: 'Monaco Editor',
        status: 'success',
        message: 'Monaco Editor 加载成功',
        details: `版本: ${monaco.editor.VERSION || 'Unknown'}`
      });
    } catch (error) {
      results.push({
        name: 'Monaco Editor',
        status: 'error',
        message: 'Monaco Editor 加载失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // 测试 monaco-editor-wrapper
    try {
      const { MonacoEditorLanguageClientWrapper } = await import('monaco-editor-wrapper');
      results.push({
        name: 'Monaco Editor Wrapper',
        status: 'success',
        message: 'Monaco Editor Wrapper 加载成功',
        details: 'MonacoEditorLanguageClientWrapper 类可用'
      });
    } catch (error) {
      results.push({
        name: 'Monaco Editor Wrapper',
        status: 'error',
        message: 'Monaco Editor Wrapper 加载失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // 测试 Langium
    try {
      const { EmptyFileSystem } = await import('langium');
      const { startLanguageServer } = await import('langium/lsp');
      results.push({
        name: 'Langium Framework',
        status: 'success',
        message: 'Langium 框架加载成功',
        details: 'EmptyFileSystem 和 startLanguageServer 可用'
      });
    } catch (error) {
      results.push({
        name: 'Langium Framework',
        status: 'error',
        message: 'Langium 框架加载失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // 测试 SysML 语言模块
    try {
      const { createSysmlServices } = await import('@/language/sysml-module');
      results.push({
        name: 'SysML 语言模块',
        status: 'success',
        message: 'SysML 语言模块加载成功',
        details: 'createSysmlServices 函数可用'
      });
    } catch (error) {
      results.push({
        name: 'SysML 语言模块',
        status: 'error',
        message: 'SysML 语言模块加载失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // 测试 Web Worker 支持
    try {
      if (typeof Worker !== 'undefined') {
        results.push({
          name: 'Web Worker 支持',
          status: 'success',
          message: 'Web Worker 支持可用',
          details: '浏览器支持 Web Workers'
        });
      } else {
        results.push({
          name: 'Web Worker 支持',
          status: 'error',
          message: 'Web Worker 不支持',
          details: '当前浏览器不支持 Web Workers'
        });
      }
    } catch (error) {
      results.push({
        name: 'Web Worker 支持',
        status: 'error',
        message: 'Web Worker 检测失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    // 测试 VSCode 语言客户端
    try {
      const { BrowserMessageReader, BrowserMessageWriter } = await import('vscode-languageserver-protocol/browser');
      results.push({
        name: 'VSCode 语言客户端',
        status: 'success',
        message: 'VSCode 语言客户端加载成功',
        details: 'BrowserMessageReader 和 BrowserMessageWriter 可用'
      });
    } catch (error) {
      results.push({
        name: 'VSCode 语言客户端',
        status: 'error',
        message: 'VSCode 语言客户端加载失败',
        details: error instanceof Error ? error.message : String(error)
      });
    }

    setDiagnostics(results);
    setIsRunning(false);
  };

  useEffect(() => {
    runDiagnostics();
  }, []);

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'loading':
        return '⏳';
      default:
        return '❓';
    }
  };

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'warning':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'loading':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const successCount = diagnostics.filter(d => d.status === 'success').length;
  const errorCount = diagnostics.filter(d => d.status === 'error').length;
  const warningCount = diagnostics.filter(d => d.status === 'warning').length;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                SysML 编辑器诊断
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                检查编辑器组件的加载状态和兼容性
              </p>
            </div>
            <button
              onClick={runDiagnostics}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isRunning ? '检测中...' : '重新检测'}
            </button>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="px-6 py-4 bg-gray-50 border-b">
          <div className="flex items-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-green-600">✅</span>
              <span>成功: {successCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-red-600">❌</span>
              <span>错误: {errorCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-yellow-600">⚠️</span>
              <span>警告: {warningCount}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>总计: {diagnostics.length}</span>
            </div>
          </div>
        </div>

        {/* 诊断结果 */}
        <div className="divide-y divide-gray-200">
          {diagnostics.map((diagnostic, index) => (
            <div key={index} className="px-6 py-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 text-lg">
                  {getStatusIcon(diagnostic.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">
                      {diagnostic.name}
                    </h3>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(diagnostic.status)}`}>
                      {diagnostic.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {diagnostic.message}
                  </p>
                  {diagnostic.details && (
                    <p className="text-xs text-gray-500 mt-2 font-mono bg-gray-100 p-2 rounded">
                      {diagnostic.details}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {isRunning && (
          <div className="px-6 py-4 text-center">
            <div className="inline-flex items-center space-x-2 text-blue-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
              <span>正在运行诊断...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EditorDiagnostics;
