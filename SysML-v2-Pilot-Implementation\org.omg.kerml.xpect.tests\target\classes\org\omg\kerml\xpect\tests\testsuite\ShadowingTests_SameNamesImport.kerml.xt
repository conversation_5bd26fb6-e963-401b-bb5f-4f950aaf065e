//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencySamePackageName_A.kerml" }
		File {from ="/src/DependencySamePackageName_B.kerml" }
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencySamePackageName_A.kerml" }
				File {from ="/src/DependencySamePackageName_B.kerml" }
			}
		}
	}
END_SETUP 
*/
//Both DepedencySamePackageName_A and DependencySamePackageName_B have the same package names "SamePackage"
//Current implemention public import from the first src package(A).
package test{
	public import SamePackage::container::*;
	classifier something1 specializes A{}
	//pass junit test
	//XPECT errors -->"Couldn't resolve reference to Classifier 'B'." at "B"
	classifier something2 specializes B{}
}

