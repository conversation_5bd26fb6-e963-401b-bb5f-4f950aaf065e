<!DOCTYPE html>
<html>

<head>
    <title>JSON Language Client & Language Server (Web Socket)</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="style.css">
</head>


<body>
    <div class="exampleHeadelineDiv">
        <b class="exampleHeadeline">JSON Language Client & Language Server (Web Socket)</b> - [<a href="../../index.html">Back to Index</a>]
    </div>
	<div id="monaco-editor-root" style="width:800px;height:600px;border:1px solid grey"></div>
    <script type="module">
        import { runClient } from "./src/bare/client.ts";

        runClient();
    </script>
</body>
</html>
