# Contributing

- Contributions to catch up with latest versions of LSP and Monaco are always welcomed!
- Contributions to propose a new example should be only accepted if an author is ready to become a maintainer and help with upgrading and testing it with latest versions of LSP and Monaco.

## Pull Request 
Please do not `git merge` main branch into a feature branch. Always `git rebase` a feature or bugfix branch to the latest main if you require changes from main.

## Maintaining

The project is maintained by individuals using for its own purposes and being ready to help as it suites them.
There is no guarantee on time of response to issues and pull requests.

### Current Maintainers

- @CGNonofr - <PERSON><PERSON><PERSON>
- @kaisalmen - Kai <PERSON>

### Past Maintainers

- @AlexTugarev - <PERSON>
- @a<PERSON><PERSON><PERSON> - <PERSON>
- @zewa666 - <PERSON><PERSON><PERSON>
- @<PERSON><PERSON> - <PERSON>
- @mofux - <PERSON>
- @<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>
- @rcj<PERSON>en - <PERSON><PERSON>
- @asual - R<PERSON><PERSON>
- @john<PERSON><PERSON> - <PERSON>
- @RomanNikitenko - <PERSON>
- @azatsary<PERSON><PERSON> - <PERSON><PERSON>nny<PERSON>

### How to become a maintainer?

Please contact one of the current maintainers. Other maintainers can add you as a collaborator with `Admin` access. A new maintainer should be listed as [a current maintainer](#current-maintainers) above and as [a code owner](.github/CODEOWNERS).

### Releases

Releases are currently manually created by @kaisalmen or @CGNonofr. We have been thinking about [semantic release](https://github.com/TypeFox/monaco-languageclient/issues/365), but this is not yet decided or implementated.
