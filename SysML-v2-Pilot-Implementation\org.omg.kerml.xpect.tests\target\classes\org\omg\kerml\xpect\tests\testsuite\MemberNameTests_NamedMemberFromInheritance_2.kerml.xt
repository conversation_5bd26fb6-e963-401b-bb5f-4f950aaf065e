//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package test{
	classifier EE {}
	classifier A{
		classifier a {}
		alias aa for a;
	}
	//* XPECT scope at A ---
		A, A.a, A.aa, B, B.a, B.aa, EE, 
		test.A, test.A.a, test.A.aa, test.B, test.B.a, test.B.aa, test.EE,
				
		--- */
	classifier B specializes A{
		//XPECT linkedName at aa --> test.A.a
		feature b: aa;
		alias b_alias for b;
	}
}
