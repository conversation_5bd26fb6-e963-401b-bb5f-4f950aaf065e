//*
XPECT_SETUP org.omg.sysml.xpect.tests.expression.SysMLTests
       ResourceSet {
            ThisFile {}
            File {from ="/library.kernel/Base.kerml"}
            File {from ="/library.kernel/Links.kerml"}
            File {from ="/library.kernel/Occurrences.kerml"}
            File {from ="/library.kernel/Objects.kerml"}
            File {from ="/library.kernel/Performances.kerml"}
            File {from ="/library.kernel/ScalarValues.kerml"}
            File {from ="/library.kernel/DataFunctions.kerml"}
            File {from ="/library.kernel/ScalarFunctions.kerml"}
            File {from ="/library.systems/Items.sysml"}
            File {from ="/library.systems/Parts.sysml"}
        }
        Workspace {
            JavaProject {
                SrcFolder {
                    ThisFile {}
			            File {from ="/library.kernel/Base.kerml"}
			            File {from ="/library.kernel/Links.kerml"}
			            File {from ="/library.kernel/Occurrences.kerml"}
			            File {from ="/library.kernel/Objects.kerml"}
			            File {from ="/library.kernel/Performances.kerml"}
			            File {from ="/library.kernel/ScalarValues.kerml"}
			            File {from ="/library.kernel/DataFunctions.kerml"}
			            File {from ="/library.kernel/ScalarFunctions.kerml"}
			            File {from ="/library.systems/Items.sysml"}
			            File {from ="/library.systems/Parts.sysml"}
                }
            }
        }
END_SETUP
*/
// XPECT noErrors ---> ""
package P {
    part def Vehicle {
        public import ScalarValues::*;
        attribute a: Integer = 5;
        attribute b: Integer = 5 * a;
    }
}
