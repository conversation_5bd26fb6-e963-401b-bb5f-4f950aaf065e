//* 
XPECT_SETUP org.omg.kerml.xpect.tests.imports.recursive.KerMLImportRecursiveTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package Test {

	package Test1 {
		public import VP::**;

		classifier A specializes c_Public_Id;
		//XPECT linkedName at A --> Test.Test1.A
		classifier B specializes A;
		
		//XPECT linkedName at VP2::A --> Test.VP.VP2.A
		classifier C specializes VP2::A;

		//XPECT linkedName at A_Id --> Test.VP.VP2.A
		classifier D specializes A_Id;
	
	}

	package VP {
			
		package VP1 {	
			public classifier <'c_Public_Id'> c_Public {
				private classifier c_private{}
				public classifier c_public{}
				protected classifier c_protected{}
			}
		}
		
		package VP2 {
			public classifier <'A_Id'> A;
		}
	}
	
}

	