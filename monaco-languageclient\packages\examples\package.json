{"name": "monaco-languageclient-examples", "version": "2025.6.2", "description": "Monaco Language client examples", "author": {"name": "TypeFox GmbH", "url": "http://www.typefox.io"}, "homepage": "https://github.com/TypeFox/monaco-languageclient/blob/main/packages/examples/README.md", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/TypeFox/monaco-languageclient.git", "directory": "packages/examples"}, "bugs": {"url": "https://github.com/TypeFox/monaco-languageclient/issues"}, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./node": {"types": "./dist/node.d.ts", "default": "./dist/node.js"}, "./json-client": {"types": "./dist/json/client/wrapperWs.d.ts", "default": "./dist/json/client/wrapperWs.js"}, "./python-client": {"types": "./dist/python/client/main.d.ts", "default": "./dist/python/client/main.js"}, "./worker/langium": {"types": "./dist/langium/langium-dsl/worker/langium-server.d.ts", "default": "./dist/langium/langium-dsl/worker/langium-server.js"}, "./worker/statemachine": {"types": "./dist/langium/statemachine/worker/statemachine-server.d.ts", "default": "./dist/langium/statemachine/worker/statemachine-server.js"}}, "typesVersions": {"*": {".": ["dist/index"], "node": ["dist/node"], "json-client": ["dist/json/client/wrapperWs"], "worker/langium": ["dist/langium/langium-dsl/worker/langium-server"], "worker/statemachine": ["dist/langium/statemachine/worker/statemachine-server"]}}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "files": ["dist", "src", "*.html", "*.ts", "README.md", "CHANGELOG.md", "LICENSE"], "dependencies": {"@codingame/monaco-vscode-configuration-service-override": "~18.1.0", "@codingame/monaco-vscode-cpp-default-extension": "~18.1.0", "@codingame/monaco-vscode-debug-service-override": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-environment-service-override": "~18.1.0", "@codingame/monaco-vscode-explorer-service-override": "~18.1.0", "@codingame/monaco-vscode-files-service-override": "~18.1.0", "@codingame/monaco-vscode-groovy-default-extension": "~18.1.0", "@codingame/monaco-vscode-java-default-extension": "~18.1.0", "@codingame/monaco-vscode-javascript-default-extension": "~18.1.0", "@codingame/monaco-vscode-json-default-extension": "~18.1.0", "@codingame/monaco-vscode-keybindings-service-override": "~18.1.0", "@codingame/monaco-vscode-lifecycle-service-override": "~18.1.0", "@codingame/monaco-vscode-localization-service-override": "~18.1.0", "@codingame/monaco-vscode-preferences-service-override": "~18.1.0", "@codingame/monaco-vscode-python-default-extension": "~18.1.0", "@codingame/monaco-vscode-remote-agent-service-override": "~18.1.0", "@codingame/monaco-vscode-search-result-default-extension": "~18.1.0", "@codingame/monaco-vscode-search-service-override": "~18.1.0", "@codingame/monaco-vscode-secret-storage-service-override": "~18.1.0", "@codingame/monaco-vscode-standalone-json-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-typescript-language-features": "~18.1.0", "@codingame/monaco-vscode-testing-service-override": "~18.1.0", "@codingame/monaco-vscode-storage-service-override": "~18.1.0", "@codingame/monaco-vscode-textmate-service-override": "~18.1.0", "@codingame/monaco-vscode-theme-defaults-default-extension": "~18.1.0", "@codingame/monaco-vscode-theme-service-override": "~18.1.0", "@codingame/monaco-vscode-typescript-basics-default-extension": "~18.1.0", "@codingame/monaco-vscode-typescript-language-features-default-extension": "~18.1.0", "@codingame/monaco-vscode-views-service-override": "~18.1.0", "@codingame/monaco-vscode-outline-service-override": "~18.1.0", "@typefox/monaco-editor-react": "~6.9.0", "cors": "^2.8.5", "express": "~5.1.0", "jszip": "~3.10.1", "langium": "~3.5.0", "monaco-editor-wrapper": "~6.9.0", "monaco-languageclient": "~9.8.0", "pyright": "~1.1.402", "react": "~19.1.0", "react-dom": "~19.1.0", "request-light": "~0.8.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-json-languageservice": "~5.6.0", "vscode-languageclient": "~9.0.1", "vscode-languageserver": "~9.0.1", "vscode-uri": "~3.1.0", "vscode-ws-jsonrpc": "~3.4.0", "ws": "~8.18.2", "wtd-core": "~4.0.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/emscripten": "~1.40.1", "@types/express": "~5.0.3", "@types/ws": "~8.18.1", "langium-cli": "~3.5.0", "mini-coi": "~0.4.2", "ts-node": "~10.9.1", "vite-plugin-static-copy": "~3.0.2", "vscode-languageserver-types": "~3.17.5"}, "scripts": {"clean": "shx rm -fr dist *.tsbuildinfo ./resources/vsix", "compile": "tsc --build tsconfig.src.json", "resources:download": "tsx ./resources/scripts/downloadResources.ts", "build:msg": "echo Building main examples:", "build": "npm run build:msg && npm run clean && npm run resources:download && npm run extract:docker && npm run compile", "start:server:json": "tsx src/json/server/direct.ts", "start:server:python": "tsx src/python/server/direct.ts", "langium:generate": "langium generate --file ./src/langium/statemachine/config/langium-config.json", "extract:docker": "tsx ./resources/clangd/scripts/extractDockerFiles.ts", "production:clean": "shx rm -fr ./production", "production:copy:mini-coi": "mini-coi --service-worker production/mini-coi.js", "production:build": "npm run production:clean && vite --config vite.config.deploy.ts build && npm run production:copy:mini-coi", "production:preview:build": "npm run production:clean && vite --config vite.config.preview.ts build && npm run production:copy:mini-coi", "production:preview": "vite --config vite.config.preview.ts preview -d"}}