// SysML v2 Langium Language Server
import { 
  createDefaultModule, 
  createDefaultSharedModule, 
  inject, 
  DefaultSharedModuleContext,
  LangiumServices,
  LangiumSharedServices,
  Module
} from 'langium';
import { 
  startLanguageServer,
  BrowserMessageReader,
  BrowserMessageWriter,
  createConnection
} from 'langium/lsp';

// SysML v2 语法定义
const sysmlGrammar = `
grammar SysML

entry Model:
    (elements+=Element)*;

Element:
    Package | PartDefinition | AttributeDefinition | ConnectionDefinition | 
    RequirementDefinition | StateDefinition | ActionDefinition;

Package:
    'package' name=ID '{' 
        (elements+=Element)*
    '}';

PartDefinition:
    'part' 'def' name=ID ('specializes' specializes=[PartDefinition:ID])? '{'
        (features+=Feature)*
    '}';

Feature:
    AttributeUsage | PartUsage | PortUsage | ConnectionUsage;

AttributeUsage:
    'attribute' name=ID ':' type=TypeReference ';';

PartUsage:
    'part' name=ID ':' type=[PartDefinition:ID] ';';

PortUsage:
    'port' name=ID ':' type=TypeReference ';';

ConnectionUsage:
    'connection' name=ID 'connect' source=FeatureReference 'to' target=FeatureReference ';';

AttributeDefinition:
    'attribute' 'def' name=ID ':' type=TypeReference ';';

ConnectionDefinition:
    'connection' 'def' name=ID '{'
        'end' 'source' ':' sourceType=TypeReference ';'
        'end' 'target' ':' targetType=TypeReference ';'
    '}';

RequirementDefinition:
    'requirement' 'def' name=ID '{'
        (requirements+=RequirementElement)*
    '}';

RequirementElement:
    RequirementUsage | ConstraintUsage;

RequirementUsage:
    'requirement' name=ID '{' text=STRING '}';

ConstraintUsage:
    'constraint' name=ID '{' expression=Expression '}';

StateDefinition:
    'state' 'def' name=ID '{'
        (states+=StateElement)*
        (transitions+=Transition)*
    '}';

StateElement:
    'state' name=ID ';';

Transition:
    'transition' source=[StateElement:ID] 'to' target=[StateElement:ID] 
    ('when' trigger=Expression)? ';';

ActionDefinition:
    'action' 'def' name=ID '{'
        (parameters+=Parameter)*
        (actions+=ActionElement)*
    '}';

Parameter:
    direction=ParameterDirection name=ID ':' type=TypeReference ';';

ParameterDirection:
    'in' | 'out' | 'inout';

ActionElement:
    ActionUsage | SuccessionUsage;

ActionUsage:
    'action' name=ID ';';

SuccessionUsage:
    'succession' first=[ActionElement:ID] 'then' second=[ActionElement:ID] ';';

TypeReference:
    primitive=PrimitiveType | reference=[Element:ID];

PrimitiveType:
    'String' | 'Integer' | 'Real' | 'Boolean';

FeatureReference:
    element=[Element:ID] ('.' feature=ID)?;

Expression:
    left=Primary (operator=BinaryOperator right=Primary)*;

Primary:
    value=NUMBER | value=STRING | value=BOOLEAN | reference=FeatureReference | 
    '(' Expression ')';

BinaryOperator:
    '+' | '-' | '*' | '/' | '==' | '!=' | '<' | '>' | '<=' | '>=' | 
    'and' | 'or' | 'xor' | 'implies';

terminal ID: /[_a-zA-Z][\\w_]*/;
terminal NUMBER: /[0-9]+(\\.[0-9]+)?/;
terminal STRING: /"[^"]*"/;
terminal BOOLEAN: /true|false/;

hidden terminal WS: /\\s+/;
hidden terminal ML_COMMENT: /\\/\\*[\\s\\S]*?\\*\\//;
hidden terminal SL_COMMENT: /\\/\\/[^\\n\\r]*/;
`;

// SysML 语言模块
export interface SysMLServices extends LangiumServices {
  // 可以添加自定义服务
}

export const SysMLModule: Module<SysMLServices, LangiumServices> = {
  // 自定义服务配置
};

// 创建 SysML 语言服务
export function createSysMLServices(): {
  shared: LangiumSharedServices;
  SysML: SysMLServices;
} {
  const shared = inject(
    createDefaultSharedModule(DefaultSharedModuleContext),
    // 可以添加共享模块配置
  );
  
  const SysML = inject(
    createDefaultModule({ shared }),
    SysMLModule
  ) as SysMLServices;
  
  shared.ServiceRegistry.register(SysML);
  
  return { shared, SysML };
}

// 启动语言服务器
export function startSysMLLanguageServer() {
  const messageReader = new BrowserMessageReader(self);
  const messageWriter = new BrowserMessageWriter(self);
  
  const connection = createConnection(messageReader, messageWriter);
  const services = createSysMLServices();
  
  startLanguageServer(connection, services);
}

// 导出语法定义
export { sysmlGrammar };
