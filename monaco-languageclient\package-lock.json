{"name": "monaco-languageclient", "lockfileVersion": 3, "requires": true, "packages": {"": {"workspaces": ["packages/client", "packages/vscode-ws-jsonrpc", "packages/wrapper", "packages/wrapper-react", "packages/examples"], "dependencies": {"monaco-editor-wrapper": "^6.9.0"}, "devDependencies": {"@codingame/esbuild-import-meta-url-plugin": "~1.0.3", "@codingame/monaco-vscode-rollup-vsix-plugin": "~18.1.0", "@eslint/eslintrc": "~3.3.1", "@eslint/js": "~9.29.0", "@stylistic/eslint-plugin": "~5.0.0", "@testing-library/react": "~16.3.0", "@types/node": "~22.15.32", "@types/react": "~19.1.8", "@types/react-dom": "~19.1.6", "@typescript-eslint/eslint-plugin": "~8.35.0", "@typescript-eslint/parser": "~8.35.0", "@vitejs/plugin-react": "~4.6.0", "@vitest/browser": "~3.2.4", "editorconfig": "~3.0.0", "esbuild": "~0.25.5", "eslint": "~9.29.0", "eslint-plugin-header": "~3.1.1", "eslint-plugin-import": "~2.32.0", "eslint-plugin-unused-imports": "~4.1.4", "globals": "~16.2.0", "http-server": "~14.1.1", "minimatch": "~10.0.3", "playwright": "~1.53.1", "shx": "~0.4.0", "tsx": "~4.20.3", "typescript": "~5.8.3", "vite": "~6.3.5", "vitest": "~3.2.4"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.5", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "integrity": "sha512-KiRAp/VoJaWkkte84TvUd9qjdbZAdiqyvMxrGl1N6vzFogKmaLgoM3L1kgtLicp2HP5fBJS8JrZKLVIZGVJAVg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.4", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz", "integrity": "sha512-bXYxrXFubeYdvB0NhD/NBB3Qi6aZeV20GOWVI47t2dkecCEoneR4NPVcb7abpXDEvejgrUfFtG6vG/zxAKmg+g==", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.4", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.4", "@babel/types": "^7.27.3", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.27.5", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.5", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "integrity": "sha512-OsQd175SxWkGlzbny8J3K8TnnDD0N3lrIUtB92xwyRpzaenGZhxDvxN/JgU00U3CDZNj9tPuDJ5H0WS4Nt3vKg==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "integrity": "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.26.7", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.26.7.tgz", "integrity": "sha512-AOPI3D+a8dXnja+iwsUqGRjr1BbZIe771sXdapOtYI531gSqpi92vXivKcq2asu/DFpdl1ceFAKZyRzK2PCVcQ==", "dev": true, "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.4", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "integrity": "sha512-oNcu2QbHqts9BtOWJosOVJapWjBDSxGCpFvikNR5TGDYDQf3JwpIoMzIKrvfoti93cLfPJEG4tH9SPVeyCGgdA==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.3", "@babel/parser": "^7.27.4", "@babel/template": "^7.27.2", "@babel/types": "^7.27.3", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/@babel/types": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@chevrotain/cst-dts-gen": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/cst-dts-gen/-/cst-dts-gen-11.0.3.tgz", "integrity": "sha512-BvIKpRLeS/8UbfxXxgC33xOumsacaeCKAjAeLyOn7Pcp95HiRbrpl14S+9vaZLolnbssPIUuiUd8IvgkRyt6NQ==", "license": "Apache-2.0", "dependencies": {"@chevrotain/gast": "11.0.3", "@chevrotain/types": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/@chevrotain/gast": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/gast/-/gast-11.0.3.tgz", "integrity": "sha512-+qNfcoNk70PyS/uxmj3li5NiECO+2YKZZQMbmjTqRI3Qchu8Hig/Q9vgkHpI3alNjr7M+a2St5pw5w5F6NL5/Q==", "license": "Apache-2.0", "dependencies": {"@chevrotain/types": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/@chevrotain/regexp-to-ast": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/regexp-to-ast/-/regexp-to-ast-11.0.3.tgz", "integrity": "sha512-1fMHaBZxLFvWI067AVbGJav1eRY7N8DDvYCTwGBiE/ytKBgP8azTdgyrKyWZ9Mfh09eHWb5PgTSO8wi7U824RA==", "license": "Apache-2.0"}, "node_modules/@chevrotain/types": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/types/-/types-11.0.3.tgz", "integrity": "sha512-gsiM3G8b58kZC2HaWR50gu6Y1440cHiJ+i3JUvcp/35JchYejb2+5MVeJK0iKThYpAa/P2PYFV4hoi44HD+aHQ==", "license": "Apache-2.0"}, "node_modules/@chevrotain/utils": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/utils/-/utils-11.0.3.tgz", "integrity": "sha512-YslZMgtJUyuMbZ+aKvfF3x1f5liK4mWNxghFRv7jqRR9C3R3fAOGTTKvxXDa2Y1s9zSbcpuO0cAxDYsc9SrXoQ==", "license": "Apache-2.0"}, "node_modules/@codingame/esbuild-import-meta-url-plugin": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@codingame/esbuild-import-meta-url-plugin/-/esbuild-import-meta-url-plugin-1.0.3.tgz", "integrity": "sha512-SAIOsWZteIWYAk04BCqQ+ugu8KiJm8EplQbMvxJl905uZv3r+21+XjtGg/zzrbxlVAY1cP+hGAG7z7sBPmy63w==", "dev": true, "license": "ISC", "dependencies": {"esbuild": ">=0.19.x", "import-meta-resolve": "^4.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-FdHbSVuMRas2clqBFF94fNzJVOHABHq7KLdqdByPEvLDF+feHQxgZdjN4fqhCc/0irhamK0xkm9Hu2i6iMLGvw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-r3P/MQ7pyaN0e8K4ZK/gjDbAxupbb3+BH3fMLxU5GbZVUnYiUK4SCHLcj+thc9zUbdjP6KkuMNPmVeYvseotDQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-tD4NrIijpqICbSc9l9QRsAnFonUr3/rBYQKBuPf/Dvm7pWTPzXCzDDlN3378lYPHrfWuqkJ7yo7LQi1F3dO1Sg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-HnKgyar/3I5Qi5LND2nGpntTkHYiwTUlvR4NzBHm4gID1gxY308CaH/FhvAy2Gf/xu6zjtRjuLTxrI+HHCUJgA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-H7to0vIbqIrSCs6kq6OG+4gLyxUZXKEBtvX4r5nS8yiUKAO5uros3NNlwC2T911t+39U8XlScDY2ZxyUY9N2IA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-WUMpqjz5FFnDe7qPZnWYU1/nhm1Pb7IMkNHGB5AyFc973bxFjT7NEbfbBFp+MfKOLZwyPKjD/uXrwporEi9CEA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-PoLLtSXnmyTuEMvO1Xw4Zq+dS7ixkvr8X8J9KRQzJ2tLW7g7ygpjFp4YkSAWR5SEig5TdjAZaV/INBUsgX5hSQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-97yrhjWofC9rtXFg5h2H9lI+o88gxgdFpZ82AW2El6DAzY6z69afxXcTHvG55IpxDBMZagVrbzdIsJJXb1L9oQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-90S8fx0Cq494HSoZtH3BEr7zHNo3Q4k2ByM2IRo3ECAzvfkMP2iWYOj6AN8rcA0lvKlUsTLQW6ZjKvPHDkv0rQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-aWWjqF5kMEOQQYmNK5QKk8AS23B/0RfDSJgAg0BIHYYKNaGx57YuInO+jmkeIb5od1bq2SB3nz59SQ68G8RmSw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-ewElLVU3EF3mdKO3HBNxOnKdeu2Qx8DZAwTIlS2ZzHxSskQNsfRk/P7m36Yp0b4xksht6lCA2Zv2e2lcUuWHOg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-zuyBb/kGkg/NJ4HZvQVEHTJumj4Vq1oOMZlfEMWyHnPNaWFDJrsVVCZ9afpANFPAWqxOz6ecooU5rAf2dhW9Qw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-+Dvb3ukHVNlVigbuBpFl+FqL2hfUwIh6Mq1BfmHCAkYwyL26xyMpFoLHcNKDFJTDa6Ch+K2cxGER/Me2BHrygQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-VJD4laUkNHUrQDidsJ1Lhgf0o4bYJS6rhvoHGKovN8Oydz0vs8UsjfC/xA3n5sEjLx/rgzOz3nZubzyEslvDtA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-nzWCDz0/JM0hnkXgsyC4g4jEfUZ3M6pclfrdUrJrx78xWeeT4JkHYdHbUig48geJo2jv6Bw04BApU3neCe2jkw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-crKioYc+n87mTQGR9rjuKjjCvPNtALh43uXKOS628aHLsFXqlrCBLcSgvvUKbf6qi/DIt6FGmg7iVR1FjG6IPQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-XAPZ4aH2d8UK1H7sYpC/4BvgDUyLTu+vCLOgaU7xRKia1myEF/zhQaQDSULwxm7la/o8v8yTPtFok+gjx3Qc2Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-iCS4NhA6gYqXt8F0jasm7acyOl/WfIeX7zW2TknhSZtPiI3IHvn2O43ix7oItBlnrLiw/XkjzKRofHb+nT/R7w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-QM9doHRnXaDyR0uBEzQNjV97TgP5GaK90tK1+2W/qvf4RiL0Qfjtx+ubgy8fz6a1JnH55Ok7hSAckjgQKuziVA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-o7G50IUYD9WIJqvNp/vLNUNOHJZoyuDWSeuWEEWN7vw/ZDpYM8Tg2TwOwbq9Kl80504AUYsiI8mGFaMTLd8KkQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-a7tXKVr2GDyah5+OUqaBdDRZnfn6oX3O4xTpCiZlVJSJcQ1HdL+ntX+iCYZri6UUlDUsxVts8lAHnchKWUdSiw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-F+BrhYfHVT5VJgAHEu+FuRk0yk2EF0c38VFuFzbcLmSVbAvsVBBcptZCN+l9XXtjNJdG5oC5SdzpORAnqmv2mg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-Jqo7Z8CbGpkmJXizl/PllahyKkHS1aGQJe1OFXtMPGqMjNG0EnYme1maXQiaN4RsKNEgcf5lRwjEH0+zz/+irg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-IMn8Givog7+qmX3/mMbj64fX/28P72egKOTgiR3DCSE4ITn+aXE7B2aL+aG+zV4/op4HCVjWcQODCK6XgFzTlQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-SUgGfPiJo+LsuOqjViLnhLw+qMvU3e5vFmBUCLmdtt5VklIw9yCOXd3co6AVrKG9+3NdCJWvp6/zLMluFP2URA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-ZnG9oGfq5+b9WThF+zbBkD6kZe4yI9iwtSrUlIZ4fAqNRWFAbXomAA/AIfxGWRTbbHZdJf2iIcyttlvMz8n6Gw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-hq4uQGDsGXng4RvI9z+LnBh2lIoHU1SaORSDzP4MfJjA9+VJKiggtwlS4Du1Y0yceLlJQyigQqhv3kkbhgmgDg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-qcwRbOxu6SReNIUqld8QuZtvXD7wO4W5eAfpxTEmgmURZb7NUn1YFUV8oFJeFlEMk0CE/1uqf0N2SY0w31DiFw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-nWiMyI6j8BuqJTqMNOymxWxWqfhIAu4UsCpFcft1MQ8K1VOLGIEIImCqOsz+BXd4xL98d5y84ucFYI6Y8YO1Bw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-aDpFBC8YYraHviFcsjk51Sy/b6493DjwkTFau4tJ4cvzIn3NTL9pOYPeavd3xWlVXYTKhaEm3D13JR5GDVrFrw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-6OPOZVs3sp6/IL90qB6DMJ2PXrqHJQJYcCJVyYFpZpkePEQf10saBjnYfAcjfb/33qG73MueOFUtyd2a6kFzuw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-g5eAK2ZwIa3hVmS3O5+8PZ2muHglplSCdIe6t15rP9dPqn20O6wlhrBi3oX1u4JwytUVAnmx+SZLgsVyRD/gVg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-GMY7w+ETSl99MaVl8fmcD3bXIGY6b1En1KjVylRUdq65QOIpTiA3icITPQwVHJ6lQC0q0JDK0uY7IwZFQwLICg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-VMBF3uBKCHd6yHTdClidPj8717/1p/4l22EtFPxWxOOG05DXxw1xZEJXiIvVhD8/IfXV7C0Sii1wu8JYNjdb+g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-Y2By47jS3fyhTCtm6IBrIBpqlOF1NGktKC+8lyoNsny/k+LSOqeVNux3da5lHZrQ/0YRTGDi9nkJ715k40AqjQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-0QVpBrJJG2slyd2eVY4Gyrs99T8u0MRUQgI0v+voU3FMdzNQaugYoGLSrmSHFhiGiWNzo8K8sMC/RPXWyIiLTA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-VAIkf1HBhu+cLi/IuZvJybAXj8NslzBuyx8e6uaL6/z8ha5KLVqOX/TSpKF7NVHNtBrAXPIdv4Xo2jO34eXG/A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-sE58GZSOKovjHwW/A3uyBOlDqPY6h3zcEAxfP5oI2o2+KViitWSUqPqiplb/wBMo3D3IaKd3wx9tEjANzSzcQA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-eCf0O0K+bKiwlaJ/q6RJLHJ5d/7DE6OAI6qFD8NcQOiTKJVSPvhtMcFOnXIjs87SXXnCl7zBEOZ3Yo6DjFQCVw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-WjB0IWDCrMEjhI4Tj5W5B3pUqfLeTCcZDkw0vp5daumboHTh8NIiotjnJGMG95oG3XHXEoRJjqWh+xtMixRKqw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-qoR1WA0cziZZLTd3+OAUGvAtT+XeL75/tlnxko2Gefu0kFk85ERdRZI1TtHoRZ3BAT20NQzQFSm21dyuvIYEKQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-1QSRznvOi0kNEURP8NYztOVzJ+MhLAD648+SPWdsqvCMaGwNxHn1cUk5Gn9O5r5YMVEl+K+KfujjRkyrGj645Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-4AUr+dOIsWmcmD7CCFbF86Xew2wtS6NcVfh4jyX+NlJyLcvUo+LELMjrsKpLOm+tTXzImgkKdQwvKnwU9iMCBg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-6Ydl30+qkr4Kp+9cNuDc9F+jg4RLqrRpgwyFn+APGDYb/RCmhJgGvUoXWC18xIMjh7dcEfoxqgCSRYRSb6D3cw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-ORkQ6NMOGMFQgBM4UvPg9mLP0FfiYEhZ1zb0RBO82sCgYPyYM1xHNECQ/6ZeTRUB1He9COUXD+uKAau37unSSg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-8BC9OP+OrA1Rf7BMRyMa21fO/moq4naFAJctRJyyhJ6kmgAzDAeL8Pd2DgziTMhqzRqeGL6nVHIanuTK6OSdMQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-Pvv+/ep9pfjQQzLDzlYV5ekpu8zOsrvpOiKqGa9VHnG35KPUICIIFEeJLBM8SO6WlXRpMwWhS0UZm2vz+17XxA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-R7TeGMni/Xo7Aj5BBo+NvYslnfmrZcdxbyz3/0GqbItqROzS4YVUOaUihHx/eQstBZd9jZQ2+p3fH0ISYHuTVA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-fNBPIv8rjUCi6/yoLLrGGVfrW4w3r+SVzF7ltgTqVyJa8E7tjkhD7dH/jIh5z/uNGu5K35ymZBSCBjbOwPlVnQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-uZfZVhOTt1obZLIIhCgQwiQTaFxLaOXOii+JRa2EvKCO1x9wZLdKrwKr2M9xhH8T8kW/c5wUyxf6s2KEthStIA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-UFDS5jim/ybnYdq6/p03qkIXSl4vgoHZN+sxx23o0MMiKHOomh122cmxs/xyOVgHgpzrliK/2UXjpybDDmiqmA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-8l8fiiT3sg2SeedTEfk2I6bUpRIpQ0LQKpqLj2S74wq8g2o3WYDaNQltClWDzlGRbmTRmNfuzTIfN4dHM77Gvg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-lqaSvvTr8Z1ULr91th/TSccGEPfgRQYlGuONuHfnNg8N0vfSv6ra8mYpoY0Keh8qVaEbheDTJlWLTzyZg7yFCQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-ag1IS6Np5kLTvBZ9pXBAFEdeYAIPNHiN7sknXBQt4Py+p4FDBx3ZT3qjCl6jmg72ciY7dxkfIuUuXcuaSfU+ag==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-FgtZ6ihzAUiQRqxdu23YL20NRelQwDPHLNUi64Mos+v20t1tDtZFDgq4p9p+Tol+VuQ6BkruFxgyfVICawMTZw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-fgPk0oJZvhCDQJYt1oHDZVDPeZs7KAKNHcH6UnRGPUkBT3CP0IxGFl05auhJ1OFgVT1BMxaIMZEQuWVE2uUO/A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-eZ+HChuBLnHwaVkvPpcJQNylFpC9dDaRcJ9j61Gl0jRw6EBX3GWM+xYHzwHbbqb/9sCDJFmntYLbqRSvuSUEPw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-43+KMXFBOj8NYj0+Z3ZXqe41GhYBinlODFmuoJtb+JWGcwi/vxI+kJQATZAJ1G0KsfU673ZOWS8Y/j3qpCY1Hg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-JTaTc38XzXNzIZI41qsE9cNmEPFUwwuw/HaBzVfUgb3Wk5+ttgvqdObfcHV3Gqa5nP7asupdg/EPk87YNARkoA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-QNIaYCWtcwPFd3UA0uzLsm+2KM/0uWWkHDt75kpGLC6LuIQIM/K8GJLV4KOk0OjFKaY01l0eCqTyrYp/hz/ETg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-tvHAVCHb9q+neGM19hq+4qORnwn+aN4YFLb3tvkhIHSxbKw0d4pZ+AUlvwvtLN4YJA2Fx7W4F+sTBJQcj9WyBg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-XFJjFTLB1x72MDBr6HVgwJ00eIiQsZyF2kvwLh3Gmks6O+wPhcSCebqlXLS0VHIp3BDp14BnaUsja2ssB+wyJQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-QCGcZZzHn1xXnLNHlgmMIm5qStq3Qd8VhvJNymQ0slPx/7mN9om4/g3VUhd/8lJdHIh36vdhzH0pTyukuhNEhA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-dgz8AC+SEieWlVR2nANba1z57LbcEsGc6v3nlcB1X8NcnHL90f2/qxMCg8gAK64pYW+PKb7ICn8wxuKaGVtVeg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-fNw0zOeJQONn0TOzajJVqkwhmKoNmQQdxgv5mgt7VC0BWCmz3ZnJq3ZLscSpQWPPTmeEb4mVLtPYPnoeU2sKcg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-HenjbmFtkg1wemtMZuONsVMysD/iE6odWItlTTcfvxFnPe5ypcaPJfMUnfege3xd4bv/Cgpqf7kNjvs9wYfdQA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-mXVxZXmjibC2SBVzOClX0je0HTNqEJaIEU6Gc3wM21ha7qrWS4NgoTWikj4zoPOAs7OPAjovvXIVp3PZqtgyzg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-ty9EIggwLqh49FGcQxZOkUGJzWFWTthhwk6cD3eXPW9jMO1M7F8MyqBSs+DTSABeXLqOCKu+kyox1pvMs79KDA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-RtWPXdVP8uGxRYSgbDV/S0KBKb49VYjzy2ggr3OEtIR1sJefMOrhMlucC2w8DRMPaH9yN3UmmUqn2C+UGLIRvg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-NeES/Lgf8HpmOUy2uvU1EmcfcRqiz0JSkNlnUn5U7fvJ8maaWaX8KRBh5KUAADWkY8dFRJ5OZbDRUurkSraW9A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-xoxt8RUFWQkAgqmmLYO9OErcbqI+JCep8upB28ashRauMhPF8+aM0NxiVi7PZnu2Zm5vY7nnIlWFrqfqHRG1rA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-7gnM0DJxJIPxUppELpnP5sKK+2zKjcsVmJVAzwNgEP/+g6nMiMFk/WWBpqyPw8qU170ZIEVx0rlV1aqSIOSpRw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-xwHFp1Q9YoAD5n1KS5BVht8H4qtTwo3nUTeWCPJRnqNTGSnsP6FDFl9w4JRnr+Mez0WKE8JWnvKvytxO/ECMVw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-E8dOR4PmsseYMuUNEx6++1sIecKy8b+nvlJ5HPyYLjylKCaEcQx4uCPtidtOTm0VwEWi82f7SUM/puTg8fEoiw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-StE4<PERSON><PERSON>oiEY0sTTPVE9Rmur7ML0tvg1awgHFmLK6N1XPHQ8jf+GO42Gc5xATodRDMh0hykHaoG+Rdl3Miy66t1w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-api": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-api/-/monaco-vscode-api-18.1.0.tgz", "integrity": "sha512-FLGwjeOgVBY3C7ROsA1YY16VHFSb1+h0Md9rr/Cg2ThAqBsMdGrANsxOP9+0duGF/Fk2yP2V1M4AV9j6ltoR7A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-base-service-override": "18.1.0", "@codingame/monaco-vscode-environment-service-override": "18.1.0", "@codingame/monaco-vscode-extensions-service-override": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0", "@codingame/monaco-vscode-host-service-override": "18.1.0", "@codingame/monaco-vscode-layout-service-override": "18.1.0", "@codingame/monaco-vscode-quickaccess-service-override": "18.1.0", "@vscode/iconv-lite-umd": "0.7.0", "dompurify": "3.2.6", "jschardet": "3.1.4", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-<PERSON>z+8MlbkVD7DGhIJU7NZlQ4ylpvHZhNvfdBeJNw+AGtunTXmWvFA9lsW9GUyvSGU95U+x+MtH4Cn2wgX8hWxQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-xveofyQu7YaNMBR2rYnDIBZM+LmHIvwIH8wyvGnxTJKAbomXYG4cOLOgXy55Bl7kdwsNMoGmIDwGGN0QuMiNUg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-Jwwspla8d34y5rTi/SBZwrgsfXuHnXjpjcxLsXPWHFpXB5vMzWOu2oRCPdeY/b5RHJoDMxyYoadIOQkPqIfK+Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-base-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-base-service-override/-/monaco-vscode-base-service-override-18.1.0.tgz", "integrity": "sha512-e5LRnp6zc018D4P5ndIHpubTFe74KLa4K90b3yA8iPY0j33gjaA4RcVncvgdR0XK8DsIPtQsl1xdlsmlvn9ElA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-T1cPcjBZQe4+SFOYHte2QvMJJbar8rZevXHOWUJgFhXZgmIpb6/C2kJCAyebwwLKa3LGwacuNgPCsUw4TBEH4Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-eSTD8fgUC/Z/OBk77N5jqhoGYCuD2rnV9PjAoZkm8X+URoC0Jx7nCgsLyW1aD8gFFjBDT7yQ0tR8Db3rbwXGJQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-NzfFbZMwIWrIZLAMEK3OjC/WsR0HdjupdmOS5jJxdYDvmeMP+JOWTcRoSA/rHPk4gtHMYIaJa68XX49k2w0dKQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-bulk-edit-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-bulk-edit-service-override/-/monaco-vscode-bulk-edit-service-override-18.1.0.tgz", "integrity": "sha512-DWhxlLljIVRYPcqF+gH0kYlg77EaRrOGtbwvCRkwt0OZO1cVS8mZrc5uCVBkMn53rt2PHD1o00J/8FWDVBZYOw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-+zji/COigHt29P1+2L8lbadxdqxR7fJcA2HzhzbRkfdyFCXv+HXiyqZXWdd5xedNRXoikEuoriIpfxj9MIL0iQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-P9Z9ugyLSslxp7cD1yiogVCQhUw1MNg0cK/E0Kx2Qw8rNqZ4ltS35nm855Ieg+EGQ5PnkKDUnT5x7bORrNewmg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-Y3AN0kWchMe9fddWttfLDIAHH4h35qKIFoNWoGyIdy0cMAvObtjBs0Ia30I9UEDuYsAW/2OcrKdx6LX8tJJEyg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-GN1cpRyX7kLg0U/9UaUjX8J0wU2EvZoruppJW9dCkFJ9+Jy6ZWEYkMTmrhkkZ1xXUkVbHpIL/zzJ+9LutbY4OQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-kbyWKQ8BWrx3UoeM5SGTkb8J9AWGTcmAxMGpUZx9Zd7W38hjnnzXecYH4vZgNf/GuiFbZGaU2sBN1p9ftKwOrw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-PNNbZPgah9TEHyUhJPyQciIu7jiSrxYyodTBmg/zoU7RWwgFBhEPKsxci1Pw2vVb28vmif3n/k1asP3hEolK+A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-v0TsR5hWOWECjwpZckijT5IV5OABEUYJW8c3s747CHE4sF8CW6Bgss8sZRvpxnv/tcN7UwpbyUGj0MxGa+4tqw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-configuration-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-configuration-service-override/-/monaco-vscode-configuration-service-override-18.1.0.tgz", "integrity": "sha512-NS6if83KTZ7pR7a5mb6mK9prvb97NFX4ItNL9W3+0a4SXruS357ZoZlyDP080lz6J16dCpCF9KLy2CdYcTGdVQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-cpp-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-cpp-default-extension/-/monaco-vscode-cpp-default-extension-18.1.0.tgz", "integrity": "sha512-7Zfr0gfANw+ciOOrZo7N9P2Xz5vtUMB2Jdn0GLLCKhky9kLEuamot+xR6cLSrxhfLkTA1pjmbphcMt8hbxpNTw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-8ZubJSBN2AT8dzo2PVaPAucsJ4B4OKVZGKCi/yXIo6GXdyu2gTd3UUd4sXi264NMoRYYUjrSnkrgBGLvVnXU0Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-aaUUIjLPzNjjyWV/PZiKReJqv9PjeQAdMgQq8VSGOvpIOiHX+6h82MSXTdN4QaezfYTW347eKZAgNJdoxOXNPw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-R6/jaj63QBP50ULpglMi2nHvj/125dYFARgQMYdkam3O5J3HwqZX3fA/ay5wK1W356hOOro0Sa6pyku75w+jfw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-aWTuzemLQw3/KX/YIp1AOflWbpYD4xKT8uG39R5wHJne/jBZJ9k+eC52H/vrM/y3xorx1qbhYJ0axm+G32M4rA==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-QxfqS5Isk/kii9EvBmNK6L57CmrmuOsifOU954+iwXajd04Ll7DNiYt3B4c6+7vu+M1Ghx48ahz4NiU0zVpKmQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-debug-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-debug-service-override/-/monaco-vscode-debug-service-override-18.1.0.tgz", "integrity": "sha512-09CNTrfF1V34pMig0e8qR4Ev3NLwvaYyMkNLvhOI00D/SmG61VKTxX0g4hsXBZ8hQQuhteujjzNrol+hv4lmrQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-KvnkXeeHxqvOeRvXdetpojB7rYJx61AdNFpfjgdpbo6UESZ4szc4sZCASBsWYtQWyYITDHRzRJSZ8J7JtgvNLg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-Y9i9ltcMT4DjFH4GLscuAZcIgXVg28hZt213c+XBJ/0BOq+9/TeF6apsp3Yq2vbCMhz4kMBMqjk35uHwCdlYVA==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-QcRIDASS1xy+nSLcrGfnJJOPi6qMeoOPF+JpnAZk736b0KRjz1uGhmRAiV2N2zpkz/dWbdBzfPCeJNC6SM+6iQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-eROghw5OphwKfsDhTSDIEQWdOT536e9azdq0vuE02BsLqQ3humZzqx4YyrKsssxpGtyCUGAYp5HjkPhTTWpJSA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-U1P6NV+JpsAyWbyPOh9ro89/O1JsbZhCnayZyTMpSC0J6Mk3AqC8caYeX0i2d5VhCqr193bI7PdWOo0JVONN1w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-5gNE4ejDoV56pjmINAHMs1lNaPQCHTfgoqJWBhpBr9GQBXiqYqvvPeLiMxN3+dvoQvrxl354eeZNtw5H6CsVzg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-editor-api": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-api/-/monaco-vscode-editor-api-18.1.0.tgz", "integrity": "sha512-q4T3VCqNvS3U8g7/ZzXGITCsX9bJulDRGQIUGw196QX9qd3HBMsFfUy4XUwx/eLNE6UyE+OIbqWLrJD7UKAcsQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-editor-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-service-override/-/monaco-vscode-editor-service-override-18.1.0.tgz", "integrity": "sha512-rqC/2OTK7IukzzFO5PAiPWLd0/JI6xQSKMpSlt7KLecQvRabx+E60eM+ojq8HT+v7VODGhEWDRBbdi0vrhkMOQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-environment-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-environment-service-override/-/monaco-vscode-environment-service-override-18.1.0.tgz", "integrity": "sha512-5Y+zyTtH83MnLGMnU6Fu3vC72oEVBDNcx7TyWQYsJGmV4d62Zzg68UXYJrG1ZGEtA1BX1KJZJX8X3uVLrepRpQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-explorer-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-explorer-service-override/-/monaco-vscode-explorer-service-override-18.1.0.tgz", "integrity": "sha512-7p1vf3r4MazjFecR4KnPBKr5abusrDbY9XXwTTFMtbtGrbJchyX9j6/3elVbk+OLaxIu42DYFeQ3OO65Wjh7qg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-extension-api": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extension-api/-/monaco-vscode-extension-api-18.1.0.tgz", "integrity": "sha512-jmdCy4Dxx7GX8YNvLI8TqSOUbMrNWxdagMubuLMbS2X2ilkxTIWzBSb7800E5SPosv5Z6Z7yJ4E5+DbqTMnXDg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-extensions-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-extensions-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extensions-service-override/-/monaco-vscode-extensions-service-override-18.1.0.tgz", "integrity": "sha512-0UAKX3B73swx5VKo8iH8a4krkhiDkE33r8xGL6S1hLN9QU0w6jMoAYs+eh3CSaM70vZj+31ZHbdlayI9W807YQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-f5CXA5zUxzbFuQCgqgIH//HkgcwJduAdJ/mtBAMkENuHGTAngWYq6SkCZC48dnqSwRDT/iqnSqgJaj3Hbsuxiw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-4bBZri6iDuedw3dUvJYnEJ6aJlsvOW3WZKjzLN2grAX7xOnUo9WDVPRaqCZnpSsJrevlveAMbOFyRSdRJ1J/Mw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-X5oVi0Jk21v3Yr8+806YFhfhlUcsNsBeYsjWjmqPTWZ/imLjyPLhUMYxGE3hD+q/etiQ4Db+nqYpEcSY2cZCEA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-q+ZC50cOTt+w2S2enIvobHmFClSwCrcPYGzoOa1l3+Z0gnmPjOFSPik7PxZAaI1hyJILQsh43st5bSQt6fzOKQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-l1JY4g72bSnPKqueRMFj1yNkUhAbGLdwRUlZBT0rt+LsqkYOhpARcflhwjUs8xAkWfOyH0xw82ozAaPkv1M68Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-sHEMfXd+m1jrdg0sDPsW4A3ZaTFOrlZKylPeaNImrEAqYfd3bVDu+8CZ393DTYujl99x0hGqumRPS1qDRi1JIg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-tjP0+dxewrCJrm203ew4NY4JHbPM+ZYzZYRHXZsUT3XJ4dDKAVJRBgOMQTHRcRlDRQTnB3PaXWGM5owxmFKjMA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-IvtvfibdgH//A85LDJ/Ci4L9PctHbKuHEfGJmrZn00GNERW+8drDcrzA/eF3OFRHdukVGPCxzqd2KYA1rEYmDg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@xterm/addon-clipboard": "0.2.0-beta.90", "@xterm/addon-image": "0.9.0-beta.107", "@xterm/addon-ligatures": "0.10.0-beta.107", "@xterm/addon-progress": "0.2.0-beta.13", "@xterm/addon-search": "0.16.0-beta.107", "@xterm/addon-serialize": "0.14.0-beta.107", "@xterm/addon-unicode11": "0.9.0-beta.107", "@xterm/addon-webgl": "0.19.0-beta.107"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-LzhIAL5cF+q5aQH03gCvz4AHG8+dYR0kOFUAkLYR0Rh2qSx62+OMK4t7zkov/fDEOUyoyCM7T+qC4QwIh+yxPg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-FHVriAhaVBo8P+058Q35GEkDLcuDnQRB9Ae3v+YUIsTbeoMXGuzaeNkeBwdpIL+nJ0wg//2+wmyfNCB3lEjU4A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-XGcDUADKnRXaR3w+CGQTqMd81sZHHC/mVO5UJJj0JmAgCAkok+pK3ZgzLi6EY65iXf+ORKaVnOYqiI1raBwVaw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-18.1.0.tgz", "integrity": "sha512-5e/0XHfYvoahsnaXsdtH12AsE8qQiFAn7/6Oa0nzfbuo5qkMKaNOpFfvCt4jSvDleh1/67XYzs7BDugTqSf9jA==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-files-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-18.1.0.tgz", "integrity": "sha512-trbOEUKGMI3hYxnfzzMk3COI6sbdp9inaWP3D8sJjukehFX9BEyWbY4m+GDlZ6bIdK/xyTwTbjBDgeggUb4voA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-groovy-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-groovy-default-extension/-/monaco-vscode-groovy-default-extension-18.1.0.tgz", "integrity": "sha512-20D9DXEmzYmTEycsucs215Dsyc9TK/RjndqfVNvSz9siKvO+l+vEOiZa7doAEgB5kd6QqFdKV0Qf7s/wVKEliA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-host-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-host-service-override/-/monaco-vscode-host-service-override-18.1.0.tgz", "integrity": "sha512-vhOtX6uzNmGLMDotoJQjlaCDoZWZKizU28rBQpof0jtwwPnR6FZAEEVQG4P5jr6zHENTEZ9p7+YG30sQTcxS7A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-java-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-java-default-extension/-/monaco-vscode-java-default-extension-18.1.0.tgz", "integrity": "sha512-2SSjGHYqE53X71txU9w7P6ToKuNGlp8EbIsVlyB7tXK6a+tLr9UYkolmaOyPCTcoaxk8aRoGIwE7+B5EZGogPg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-javascript-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-javascript-default-extension/-/monaco-vscode-javascript-default-extension-18.1.0.tgz", "integrity": "sha512-k4eAyRLtG+yokUfqnkSjmnHKCYb97e8la+FvQWx4iRtXfMgCW1EPffE2MKLHbY6rtNS7xsVCudFAi5IjoAjmcg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-json-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-json-default-extension/-/monaco-vscode-json-default-extension-18.1.0.tgz", "integrity": "sha512-K8<PERSON>dJNQaQEDEkAn2b2oMhzdCSbV9DZzgxvW7GtVjPVHaia9sYX2r0j6HJOjBNv+ychxIxzD219eAJH87Iou1UA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-keybindings-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-keybindings-service-override/-/monaco-vscode-keybindings-service-override-18.1.0.tgz", "integrity": "sha512-a47H2PXi6QyzJBKrYtkuoOrVTZxn5xPQeAi1aeJScOOvo38UHmcgxxMh/nRKOhxV3Z23+b7bfyfK3XS/SaH8eA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-cs": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-cs/-/monaco-vscode-language-pack-cs-18.1.0.tgz", "integrity": "sha512-t7SZkEdYJ9X33DMKLjIrYRFLGB6MpWviKY2ubbMAKD+vrb4Mb69j4EmQPEWh6gWZQgIsPA4UzzNTccVViIcjAQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-de": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-de/-/monaco-vscode-language-pack-de-18.1.0.tgz", "integrity": "sha512-rmm+aEYbqpQihCfk6DQBIvk+GvwsGqEF1ClgVEGE3p6ysp2nMy7y9V+kw51ZhDciAle9xztTeTCmEjaMgTiTrw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-es": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-es/-/monaco-vscode-language-pack-es-18.1.0.tgz", "integrity": "sha512-a7agc4g0VQg9x9pNx+rWS02W4t2PLGANdltlof3FOaiwEt1eXG4eZaRPV5EurJyDjhIeFwqpeRsi0tXXUmeHgQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-fr": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-fr/-/monaco-vscode-language-pack-fr-18.1.0.tgz", "integrity": "sha512-0rPoCtRQweowGBt+4B93C4f3/8nWwVG5GyLP/vGPLCFhi6a1P8m+Jzl1s00J1csyyX/HGBmfR7WEThgENeoHHw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-it": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-it/-/monaco-vscode-language-pack-it-18.1.0.tgz", "integrity": "sha512-DkpIQdoziuVTXx/8JjmjiM8B5uMxZNuln1kF+Q9SDOdm2VTmyt0y1WtlIyv9m+iFRGkcBGuBzH2ez48IFaAyEw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-ja": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ja/-/monaco-vscode-language-pack-ja-18.1.0.tgz", "integrity": "sha512-9ducufCix3+OowaN8/67GoRP0QgapxKhj3KlT6O+BwyBI4Lgdijk5A3cFHugse0a5cMPBeJxut1zvfo3v1Rwaw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-ko": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ko/-/monaco-vscode-language-pack-ko-18.1.0.tgz", "integrity": "sha512-hMQ8x+hWTGPBDqJmBRkEyLnRqRO8zmQfX2TVUxOrYhgMla2K2duJ67wLUWZkvnv+um04UI/trF+050AHwds8Ew==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-pl": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-pl/-/monaco-vscode-language-pack-pl-18.1.0.tgz", "integrity": "sha512-bEmqCPc8mDDQh36Myfma40cGi0+bV3byH2Ch6/p4PJ1Q5wTu9jfoYKNNUiXYYxQ8OaoQl+e0rjvVe75SBGa75A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-pt-br": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-pt-br/-/monaco-vscode-language-pack-pt-br-18.1.0.tgz", "integrity": "sha512-2tXlC80UZIkM8h0oEyQOSC/DdJsBPgRUMQD0EDlsIJwG0rQGGCdNuOYEhkki43DUkdeX064Xgrm+7YxHypnsLw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-qps-ploc": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-qps-ploc/-/monaco-vscode-language-pack-qps-ploc-18.1.0.tgz", "integrity": "sha512-nnTwgoM8LweRBfYrB1Q6fQnsLLLmEaaYxhgzcyo3u8MBjB2jUe6hlXDKz+rqu3Uu62jxRBcdz0Boq6WPeDKxxw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-ru": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ru/-/monaco-vscode-language-pack-ru-18.1.0.tgz", "integrity": "sha512-FK65RG1MV+9q4Dvvw6WwnvCS6NHfzT06IaWyORA2+ddhCBbXPoeZHAPv/t3hVqDd/2cg6G2FtfsQKlIsriQuYg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-tr": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-tr/-/monaco-vscode-language-pack-tr-18.1.0.tgz", "integrity": "sha512-XBIlq7oveNxaxncQKJzpcmuHYkhHsOAB8XwZIgM/Dw8rBHoh6kVG5asqhQE+zAXMqtO9lVQlzLMhjYxrMifW3g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-zh-hans": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-zh-hans/-/monaco-vscode-language-pack-zh-hans-18.1.0.tgz", "integrity": "sha512-22OZazgNJ47p2vuyqYdWJ8RmRE9ZxGcVS6+3nwSsSi6EdeYsWtUbkylbj47VLU6a8a3z4FsqD3bgKQbHDBLJVg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-language-pack-zh-hant": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-zh-hant/-/monaco-vscode-language-pack-zh-hant-18.1.0.tgz", "integrity": "sha512-+qzii/hkdsflDJVn4sSjPOtexbvytgiJwxi5HHqL9YgUvLZwZ8EmbvaEHZ8HDFjI/lQuK3PtZGdEAkuO1sVyiw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-languages-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-languages-service-override/-/monaco-vscode-languages-service-override-18.1.0.tgz", "integrity": "sha512-DALhzgmIOPPz8V7B6LBCd4wWYMFiEPLiFRfyXrGLxdbTtNAinLN++w/EBPM1KT4WBoiLpkFhdXLYyJjPgAKH+g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-layout-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-layout-service-override/-/monaco-vscode-layout-service-override-18.1.0.tgz", "integrity": "sha512-9Pwxvn5vr2rYDE2CkI/GOOlTSTDbKHn6gu791tPiJhOQCkTiLcyS7DtXGnQ/UvLLBXw1BQYMjkSCOFKJ8eR3hA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-lifecycle-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-lifecycle-service-override/-/monaco-vscode-lifecycle-service-override-18.1.0.tgz", "integrity": "sha512-uJMQC3kgCTlGEamuXeXzscBxQIyGXCBs31T1pYZbhRfN8ROM8D95cQYLDQxCTFp6mJrA4J197ULJOFN4QUqQ9A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-localization-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-localization-service-override/-/monaco-vscode-localization-service-override-18.1.0.tgz", "integrity": "sha512-/s/ugoCv4mYjEFbpixsfLrTbQkzzdhPKvdluTpKWcnU05owoK5cLhr8CRJRbIWR0zAPsRR/2jVg492Wg8hQ6lA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-log-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-log-service-override/-/monaco-vscode-log-service-override-18.1.0.tgz", "integrity": "sha512-yGF0TIRbZUAxrAgrTGQIMF7TzzBBv3gACzmz6M/d88AuXJrLU00XyzqGiiyYj8JTjqdZod3EnV3z7jMQUhCBfg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-environment-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-model-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-model-service-override/-/monaco-vscode-model-service-override-18.1.0.tgz", "integrity": "sha512-wfgd857ZrQGJo3D3h2GhB9A2g91jZoS9UUi+V7SlUkGqfB8YEqUOcgBvqVxCTL8oZVMoF3P9WVXMhwk0VW92Cg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-monarch-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-monarch-service-override/-/monaco-vscode-monarch-service-override-18.1.0.tgz", "integrity": "sha512-+5AYv1NqXMS96wwPuuRZaMivJavwvIkIPpK9GdwgfuPeE+LbNDPbZajvWXZ/rTePeI+VzWq9jFoWhYw2MtqXzA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-outline-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-outline-service-override/-/monaco-vscode-outline-service-override-18.1.0.tgz", "integrity": "sha512-B5RAx0+Xaz8QkiSyxmbhSnRbbWMlwbE0CJ2Ohp4fwtSeVgbK/EwGULjOW75MG8eDfM8dzWUPQwnGDM83/uBkjg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-preferences-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-preferences-service-override/-/monaco-vscode-preferences-service-override-18.1.0.tgz", "integrity": "sha512-VOsSeT8g6C7vsbe+GLHVdNYiheMM0BMdbKUCV/cRMkwQkTYCwlhl4M9krTmoGXrN7C7cBkfZaLo+s6eoQPoKYQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-python-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-python-default-extension/-/monaco-vscode-python-default-extension-18.1.0.tgz", "integrity": "sha512-CSeQzLlSw8KzDfvrHw+J3oevfSSMBwrlIuvoHBSkyCzTomRFNmwFip3zbWuJR1q5mgn+1bH3Q5aSospOfZfA2Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-quickaccess-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-quickaccess-service-override/-/monaco-vscode-quickaccess-service-override-18.1.0.tgz", "integrity": "sha512-306jsAGsPwTZcG7yXoxD3WYNZmXFuBSq4WUoCgW2sA1/iV4NecW7N6iyE1fSgfEBHg1YMgAi1/2IxK9afThrrQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-remote-agent-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-remote-agent-service-override/-/monaco-vscode-remote-agent-service-override-18.1.0.tgz", "integrity": "sha512-NSLYFYeAL+VYibPut1QvmXKypkbNVruMC69GN9mKHifRzpP8D2w2deDL/iZ4aY4koBF2+DXLPwDN8RRNiTTe7w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-environment-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-rollup-vsix-plugin": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-rollup-vsix-plugin/-/monaco-vscode-rollup-vsix-plugin-18.1.0.tgz", "integrity": "sha512-wyXf3yAuVOcj1sd5dkkYz1qfvW1hEnyWmr1LEOltqNb5mpwC5ee3H0lF+rJs9r//Wa09y2y+mO0Yl3WtOL4pdQ==", "dev": true, "license": "MIT", "dependencies": {"@rollup/pluginutils": "^5.1.4", "css-url-parser": "^1.1.4", "memfs": "^4.17.2", "mime-types": "^3.0.1", "thenby": "^1.3.4", "yauzl": "^3.0.0"}}, "node_modules/@codingame/monaco-vscode-search-result-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-search-result-default-extension/-/monaco-vscode-search-result-default-extension-18.1.0.tgz", "integrity": "sha512-L7UQ0gzl7xLb3aFzeuTsqUefmc+nfTI6uDPiXsXC+nOc0O2FB0xPwBHTpsJrEgGNUHcBX41jk7hb5ji0/7KcXw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-search-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-search-service-override/-/monaco-vscode-search-service-override-18.1.0.tgz", "integrity": "sha512-ODOodPe0bAaNTQnQnnsYV053g6oDKnCxvXx78+z2S5yplrp5VS9l9z62sof0FD0tVgb7I55DNl5VcfNtFGDN+w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-secret-storage-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-secret-storage-service-override/-/monaco-vscode-secret-storage-service-override-18.1.0.tgz", "integrity": "sha512-RoMjsHBmLT44lCnl05D3gVVZWx9RY3LwtHbQT6h+MXjNPvz7suZwKcdkceg8Nc1NwTbFmrUppIijNcTierPBfA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-standalone-css-language-features": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-css-language-features/-/monaco-vscode-standalone-css-language-features-18.1.0.tgz", "integrity": "sha512-<PERSON>eg1ZyBRz2soi6Ux77D3UAH9OADU191UF2RSC5CdGfCQSyLZ/CUK9ZUfIqj0BQTkTTzV/ERq+kOKFdf71wvTA==", "dev": true, "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@18.1.0"}}, "node_modules/@codingame/monaco-vscode-standalone-html-language-features": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-html-language-features/-/monaco-vscode-standalone-html-language-features-18.1.0.tgz", "integrity": "sha512-+uTs9C/TSq89J7lp059FDpi+l1fCIy6XUfyiI0CNlwHVQmmIxCbGPAZxgWZ/C6r0WTeCqhB0hkJBqu+mN9ESng==", "dev": true, "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@18.1.0"}}, "node_modules/@codingame/monaco-vscode-standalone-json-language-features": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-json-language-features/-/monaco-vscode-standalone-json-language-features-18.1.0.tgz", "integrity": "sha512-uTEz5aZ6Ib1ZZzNY7+xRTiEmwEFfwoG61GMdgQKU8Sj+rnhZiPdSHUQhvFsp9+XI28jBdr0/q3lOBkiTlD7B1w==", "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@18.1.0"}}, "node_modules/@codingame/monaco-vscode-standalone-languages": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-languages/-/monaco-vscode-standalone-languages-18.1.0.tgz", "integrity": "sha512-za7uQgpeTHS6x5mVxT6z32JzotR3PNEAwCaiCnAHqhgmrIUDCOYoINhn+E06G7NwqcGl4xtaU1KSuaVqQlqj+Q==", "dev": true, "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@18.1.0"}}, "node_modules/@codingame/monaco-vscode-standalone-typescript-language-features": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-typescript-language-features/-/monaco-vscode-standalone-typescript-language-features-18.1.0.tgz", "integrity": "sha512-LISrBeCl18uTTFGqMXX8eQ8GFbxnhrCqdM1VJ3DB/tnfKVSXuTFDwcAFHpChe/iRtNutjEoPaccwCDPzyhSPkQ==", "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@18.1.0"}}, "node_modules/@codingame/monaco-vscode-storage-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-storage-service-override/-/monaco-vscode-storage-service-override-18.1.0.tgz", "integrity": "sha512-qjTqjTLr9x9fsW3n/yZ/klHxlvVIR89PDdPxMLU/jJrlEkSeCV/zj9xAlrxtD/orcPmEGV38ZKiLq76sUz9qgA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-terminal-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-terminal-service-override/-/monaco-vscode-terminal-service-override-18.1.0.tgz", "integrity": "sha512-+j/q8atnPxu9wOBNv4KgyOkEAT5en6qLjQ1+ENcfN30WAxPtWq9kdfuaHcQV8j5X9D+kOz0I9nIRqraSil+ZsA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@xterm/xterm": "5.6.0-beta.107"}}, "node_modules/@codingame/monaco-vscode-terminal-service-override/node_modules/@xterm/xterm": {"version": "5.6.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/xterm/-/xterm-5.6.0-beta.107.tgz", "integrity": "sha512-7cuJFZtc7Rv9BEpf9UsvErfXhLKkEmogI0mizgri+nNjEENnpQcFOpx84GFTjax/Zta8MAxOMUP1XU5Guju80A==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-testing-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-testing-service-override/-/monaco-vscode-testing-service-override-18.1.0.tgz", "integrity": "sha512-kfeALRVzd0joOGLbpbb/qBeS4gsMUSPWmr55aXuBasOnbB691JPztqR2lbjcrwNWkGhifN4r+wklqQ614SVLIQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-terminal-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-textmate-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-textmate-service-override/-/monaco-vscode-textmate-service-override-18.1.0.tgz", "integrity": "sha512-4lj7kynCiwz0PLW75zhmfqGhAo6IRqv1sZd2DSNGMtsw9SPJ0NUFLwTmiMq9FA/SNFr8FrLjPdZ9E8bblZHmig==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0", "vscode-oniguruma": "1.7.0", "vscode-textmate": "9.2.0"}}, "node_modules/@codingame/monaco-vscode-theme-defaults-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-theme-defaults-default-extension/-/monaco-vscode-theme-defaults-default-extension-18.1.0.tgz", "integrity": "sha512-KcVMF4STxunxO21hLji8R/sgqOp1ikJjkQLiUnqrk9m4rcP5GPV7hVAiMRPMd9FnGFP63qi3uS224kAXgD1VOw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-theme-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-theme-service-override/-/monaco-vscode-theme-service-override-18.1.0.tgz", "integrity": "sha512-ywB8NigtFhz2GLVa7MGI3Wf+dGpT140bBXzHu66C3Drpa3dl7vBrAmipsiWUeJJK1oCj0VFygqnnh022sM/HkQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-files-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-typescript-basics-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-typescript-basics-default-extension/-/monaco-vscode-typescript-basics-default-extension-18.1.0.tgz", "integrity": "sha512-KJFsJPr0CmEhSVDTXOSDd7TCQ+cmxadAaRnIa76tNkguZQKiDotq6Hcetc3mFDWhhKzrFqb4NwMEjXaBDBFEuw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-typescript-language-features-default-extension": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-typescript-language-features-default-extension/-/monaco-vscode-typescript-language-features-default-extension-18.1.0.tgz", "integrity": "sha512-D2ebxgIXBqP+TeBtWpmVyYjpX7T66XHoz3KAsmGdayHvyyOcA0LU5lu3h7OxeUDt2mvGqoCB7riK/uzo1thJMA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-view-banner-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-banner-service-override/-/monaco-vscode-view-banner-service-override-18.1.0.tgz", "integrity": "sha512-mjw7L12HIFIotIF5pXibcN4uWL+XIJyQ8REPplsueFbjcxQjT/6SEsNu4g8+/WmUJDerrQfiegQZD1kRkD4l1Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-view-common-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-common-service-override/-/monaco-vscode-view-common-service-override-18.1.0.tgz", "integrity": "sha512-BjVQGwEV1/OdAbCDwKWBUOYjMFW+3DR7wBcAh2HFvzqvRHycGOdOXuABBEv9IPA1YmhSZ3xoeVLKrQ9+V01b0g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-bulk-edit-service-override": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-view-status-bar-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-status-bar-service-override/-/monaco-vscode-view-status-bar-service-override-18.1.0.tgz", "integrity": "sha512-At4kDjbdRINU3lk2qkkyRC6kmeI5AfHwDC34HnvOIBT1s7U+T366TUp3/2MKOypl5XXiSWX0Y/UUShH1LiA4Cg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-view-title-bar-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-title-bar-service-override/-/monaco-vscode-view-title-bar-service-override-18.1.0.tgz", "integrity": "sha512-c1PR8Kumh1uMTK4utTWA4MjOGaXTqOp2dSXBj7VKUDs/xmRmk5qDu3c0njZxzS+BbCAVRYUp0gG6sKGPf4AKsA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-views-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-views-service-override/-/monaco-vscode-views-service-override-18.1.0.tgz", "integrity": "sha512-A7jRUPD0l9csY3tZlbRWTXu3xhgc5tZGrZWdh+vorcvm4lDgA6oi4LTqRjxvw6qr2ySLohkAJl8N+6oIaT5DTA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-keybindings-service-override": "18.1.0", "@codingame/monaco-vscode-layout-service-override": "18.1.0", "@codingame/monaco-vscode-quickaccess-service-override": "18.1.0", "@codingame/monaco-vscode-view-common-service-override": "18.1.0"}}, "node_modules/@codingame/monaco-vscode-workbench-service-override": {"version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-workbench-service-override/-/monaco-vscode-workbench-service-override-18.1.0.tgz", "integrity": "sha512-8qe/o2T+RcHbvculhzMutlvsVj44txGTmgZXwEk/j8ud4lx9EEhawI07vH0eVWTVCiTV0EJYRPbBZ+01k3ibGA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-keybindings-service-override": "18.1.0", "@codingame/monaco-vscode-quickaccess-service-override": "18.1.0", "@codingame/monaco-vscode-view-banner-service-override": "18.1.0", "@codingame/monaco-vscode-view-common-service-override": "18.1.0", "@codingame/monaco-vscode-view-status-bar-service-override": "18.1.0", "@codingame/monaco-vscode-view-title-bar-service-override": "18.1.0"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "integrity": "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "integrity": "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@esbuild/aix-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz", "integrity": "sha512-9o3TMmpmftaCMepOdA5k/yDw8SfInyzWWTjYTFCX3kPSDJMROQTb8jg+h9Cnwnmm1vOzvxN7gIfB5V2ewpjtGA==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["aix"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm/-/android-arm-0.25.5.tgz", "integrity": "sha512-AdJKSPeEHgi7/ZhuIPtcQKr5RQdo6OO2IL87JkianiMYMPbCtot9fxPbrMiBADOWWm3T2si9stAiVsGbTQFkbA==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz", "integrity": "sha512-VGzGhj4lJO+TVGV1v8ntCZWJktV7SGCs3Pn1GRWI1SBFtRALoomm8k5E9Pmwg3HOAal2VDc2F9+PM/rEY6oIDg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/android-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/android-x64/-/android-x64-0.25.5.tgz", "integrity": "sha512-D2GyJT1kjvO//drbRT3Hib9XPwQeWd9vZoBJn+bu/lVsOZ13cqNdDeqIF/xQ5/VmWvMduP6AmXvylO/PIc2isw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz", "integrity": "sha512-GtaBgammVvdF7aPIgH2jxMDdivezgFu6iKpmT+48+F8Hhg5J/sfnDieg0aeG/jfSvkYQU2/pceFPDKlqZzwnfQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/darwin-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz", "integrity": "sha512-1iT4FVL0dJ76/q1wd7XDsXrSW+oLoquptvh4CLR4kITDtqi2e/xwXwdCVH8hVHU43wgJdsq7Gxuzcs6Iq/7bxQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz", "integrity": "sha512-nk4tGP3JThz4La38Uy/gzyXtpkPW8zSAmoUhK9xKKXdBCzKODMc2adkB2+8om9BDYugz+uGV7sLmpTYzvmz6Sw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/freebsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz", "integrity": "sha512-PrikaNjiXdR2laW6OIjlbeuCPrPaAl0IwPIaRv+SMV8CiM8i2LqVUHFC1+8eORgWyY7yhQY+2U2fA55mBzReaw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz", "integrity": "sha512-cPzojwW2okgh7ZlRpcBEtsX7WBuqbLrNXqLU89GxWbNt6uIg78ET82qifUy3W6OVww6ZWobWub5oqZOVtwolfw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz", "integrity": "sha512-Z9kfb1v6ZlGbWj8EJk9T6czVEjjq2ntSYLY2cw6pAZl4oKtfgQuS4HOq41M/BcoLPzrUbNd+R4BXFyH//nHxVg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz", "integrity": "sha512-sQ7l00M8bSv36GLV95BVAdhJ2QsIbCuCjh/uYrWiMQSUuV+LpXwIqhgJDcvMTj+VsQmqAHL2yYaasENvJ7CDKA==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-loong64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz", "integrity": "sha512-0ur7ae16hDUC4OL5iEnDb0tZHDxYmuQyhKhsPBV8f99f6Z9KQM02g33f93rNH5A30agMS46u2HP6qTdEt6Q1kg==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-mips64el": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz", "integrity": "sha512-kB/66P1OsHO5zLz0i6X0RxlQ+3cu0mkxS3TKFvkb5lin6uwZ/ttOkP3Z8lfR9mJOBk14ZwZ9182SIIWFGNmqmg==", "cpu": ["mips64el"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-ppc64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz", "integrity": "sha512-UZCmJ7r9X2fe2D6jBmkLBMQetXPXIsZjQJCjgwpVDz+YMcS6oFR27alkgGv3Oqkv07bxdvw7fyB71/olceJhkQ==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-riscv64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz", "integrity": "sha512-kTxwu4mLyeOlsVIFPfQo+fQJAV9mh24xL+y+Bm6ej067sYANjyEw1dNHmvoqxJUCMnkBdKpvOn0Ahql6+4VyeA==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-s390x": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz", "integrity": "sha512-K2dSKTKfmdh78uJ3NcWFiqyRrimfdinS5ErLSn3vluHNeHVnBAFWC8a4X5N+7FgVE1EjXS1QDZbpqZBjfrqMTQ==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/linux-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz", "integrity": "sha512-uhj8N2obKTE6pSZ+aMUbqq+1nXxNjZIIjCjGLfsWvVpy7gKCOL6rsY1MhRh9zLtUtAI7vpgLMK6DxjO8Qm9lJw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz", "integrity": "sha512-pwHtMP9viAy1oHPvgxtOv+OkduK5ugofNTVDilIzBLpoWAM16r7b/mxBvfpuQDpRQFMfuVr5aLcn4yveGvBZvw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/netbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz", "integrity": "sha512-WOb5fKrvVTRMfWFNCroYWWklbnXH0Q5rZppjq0vQIdlsQKuw6mdSihwSo4RV/YdQ5UCKKvBy7/0ZZYLBZKIbwQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["netbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz", "integrity": "sha512-7A208+uQKgTxHd0G0uqZO8UjK2R0DDb4fDmERtARjSHWxqMTye4Erz4zZafx7Di9Cv+lNHYuncAkiGFySoD+Mw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/openbsd-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz", "integrity": "sha512-G4hE405ErTWraiZ8UiSoesH8DaCsMm0Cay4fsFWOOUcz8b8rC6uCvnagr+gnioEjWn0wC+o1/TAHt+It+MpIMg==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["openbsd"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/sunos-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz", "integrity": "sha512-l+azKShMy7FxzY0Rj4RCt5VD/q8mG/e+mDivgspo+yL8zW7qEwctQ6YqKX34DTEleFAvCIUviCFX1SDZRSyMQA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["sunos"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-arm64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz", "integrity": "sha512-O2S7SNZzdcFG7eFKgvwUEZ2VG9D/sn/eIiz8XRZ1Q/DO5a3s76Xv0mdBzVM5j5R639lXQmPmSo0iRpHqUUrsxw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-ia32": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz", "integrity": "sha512-onOJ02pqs9h1iMJ1PQphR+VZv8qBMQ77Klcsqv9CNW2w6yLqoURLcgERAIurY6QE63bbLuqgP9ATqajFLK5AMQ==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@esbuild/win32-x64": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz", "integrity": "sha512-TXv6YnJ8ZMVdX+SXWVBo/0p8LTcrUYngpWjvm91TMjjBQii7Oz11Lw5lbDV5Y0TzuhSJHwiH4hEtC1I42mMS0g==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=18"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz", "integrity": "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw==", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/eslint-utils/node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz", "integrity": "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/config-array": {"version": "0.20.1", "resolved": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.20.1.tgz", "integrity": "sha512-OL0RJzC/CBzli0DrrR31qzj6d6i6Mm3HByuhflhl4LOBiWxN+3i6/t/ZQQNii4tjksXi8r2CRW1wMpWA2ULUEw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/config-array/node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/config-helpers": {"version": "0.2.2", "resolved": "https://registry.npmjs.org/@eslint/config-helpers/-/config-helpers-0.2.2.tgz", "integrity": "sha512-+GPzk8PlG0sPpzdU5ZvIRMPidzAnZDl/s9L+y13iodqvb8leL53bTannOrQ/Im7UkpsmFU5Ily5U60LWixnmLg==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/core": {"version": "0.14.0", "resolved": "https://registry.npmjs.org/@eslint/core/-/core-0.14.0.tgz", "integrity": "sha512-qIbV0/JZr7iSDjqAc60IqbLdsj9GDt16xQtWD+B78d/HAlvysGdZZ6rpJHGAc2T0FQx1X6thsSPdnoiGKdNtdg==", "dev": true, "license": "Apache-2.0", "dependencies": {"@types/json-schema": "^7.0.15"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/eslintrc": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz", "integrity": "sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ==", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^10.0.1", "globals": "^14.0.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "14.0.0", "resolved": "https://registry.npmjs.org/globals/-/globals-14.0.0.tgz", "integrity": "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "9.29.0", "resolved": "https://registry.npmjs.org/@eslint/js/-/js-9.29.0.tgz", "integrity": "sha512-3PIF4cBw/y+1u2EazflInpV+lYsSG0aByVIQzAgb1m1MhHFSbqTyNqtBKHgWf/9Ykud+DhILS9EGkmekVhbKoQ==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}}, "node_modules/@eslint/object-schema": {"version": "2.1.6", "resolved": "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz", "integrity": "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@eslint/plugin-kit": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.1.tgz", "integrity": "sha512-0J+zgWxHN+xXONWIyPWKFMgVuJoZuGiIFu8yxk7RJjxkzpGmyja5wRFqZIVtjDVOQpV+Rw0iOAjYPE2eQyjr0w==", "dev": true, "license": "Apache-2.0", "dependencies": {"@eslint/core": "^0.14.0", "levn": "^0.4.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "node_modules/@humanfs/core": {"version": "0.19.1", "resolved": "https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz", "integrity": "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node": {"version": "0.16.6", "resolved": "https://registry.npmjs.org/@humanfs/node/-/node-0.16.6.tgz", "integrity": "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw==", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanfs/core": "^0.19.1", "@humanwhocodes/retry": "^0.3.0"}, "engines": {"node": ">=18.18.0"}}, "node_modules/@humanfs/node/node_modules/@humanwhocodes/retry": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz", "integrity": "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz", "integrity": "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/retry": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.2.tgz", "integrity": "sha512-xeO57FpIu4p1Ri3Jq/EXq4ClRm86dVF2z/+kvFnyqVYRavTZmaFaUBbWCOuuTh0o/g7DSsk6kc2vrS4Vl5oPOQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18.18"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@isaacs/balanced-match": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/@isaacs/balanced-match/-/balanced-match-4.0.1.tgz", "integrity": "sha512-yzMTt9lEb8Gv7zRioUilSglI0c0smZ9k5D65677DLWLtWJaXIS3CqcGyUFByYKlnUj6TkjLVs54fBl6+TiGQDQ==", "dev": true, "license": "MIT", "engines": {"node": "20 || >=22"}}, "node_modules/@isaacs/brace-expansion": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/@isaacs/brace-expansion/-/brace-expansion-5.0.0.tgz", "integrity": "sha512-ZT55BDLV0yv0RBm2czMiZ+SqCGO7AvmOM3G/w2xhVPH+te0aKgFjmBvGlL1dH+ql2tgGO3MVrbb3jCKyvpgnxA==", "dev": true, "license": "MIT", "dependencies": {"@isaacs/balanced-match": "^4.0.1"}, "engines": {"node": "20 || >=22"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@jsonjoy.com/base64": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/@jsonjoy.com/base64/-/base64-1.1.2.tgz", "integrity": "sha512-q6XAnWQDIMA3+FTiOYajoYqySkO+JSat0ytXGSuRdq9uXE7o92gzuQwQM14xaCRlBLGq3v5miDGC4vkVTn54xA==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "peerDependencies": {"tslib": "2"}}, "node_modules/@jsonjoy.com/json-pack": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@jsonjoy.com/json-pack/-/json-pack-1.2.0.tgz", "integrity": "sha512-io1zEbbYcElht3tdlqEOFxZ0dMTYrHz9iMf0gqn1pPjZFTCgM5R4R5IMA20Chb2UPYYsxjzs8CgZ7Nb5n2K2rA==", "dev": true, "license": "Apache-2.0", "dependencies": {"@jsonjoy.com/base64": "^1.1.1", "@jsonjoy.com/util": "^1.1.2", "hyperdyperid": "^1.2.0", "thingies": "^1.20.0"}, "engines": {"node": ">=10.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "peerDependencies": {"tslib": "2"}}, "node_modules/@jsonjoy.com/util": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/@jsonjoy.com/util/-/util-1.6.0.tgz", "integrity": "sha512-sw/RMbehRhN68WRtcKCpQOPfnH6lLP4GJfqzi3iYej8tnzpZUDr6UkZYJjcjjC0FWEJOJbyM3PTIwxucUmDG2A==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "peerDependencies": {"tslib": "2"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz", "integrity": "sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz", "integrity": "sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz", "integrity": "sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@one-ini/wasm": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/@one-ini/wasm/-/wasm-0.2.0.tgz", "integrity": "sha512-n+L/BvrwKUn7q5O3wHGo+CJZAqfewh38+37sk+eBzv/39lM9pPgPRd4sOZRvSRzo0ukLxzyXso4WlGj2oKZ5hA==", "dev": true, "license": "MIT"}, "node_modules/@polka/url": {"version": "1.0.0-next.28", "resolved": "https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.28.tgz", "integrity": "sha512-8LduaNlMZGwdZ6qWrKlfa+2M4gahzFkprZiAt2TF8uS0qQgBizKXpXURqvTJ4WtmupWxaLqjRb2UCTe72mu+Aw==", "dev": true, "license": "MIT"}, "node_modules/@rolldown/pluginutils": {"version": "1.0.0-beta.19", "resolved": "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.19.tgz", "integrity": "sha512-3FL3mnMbPu0muGOCaKAhhFEYmqv9eTfPSJRJmANrCwtgK8VuxpsZDGK+m0LYAGoyO8+0j5uRe4PeyPDK1yA/hA==", "dev": true, "license": "MIT"}, "node_modules/@rollup/pluginutils": {"version": "5.1.4", "resolved": "https://registry.npmjs.org/@rollup/pluginutils/-/pluginutils-5.1.4.tgz", "integrity": "sha512-USm05zrsFxYLPdWWq+K3STlWiT/3ELn3RcV5hJMghpeAIhxfsUIg6mt12CBJBInWMV4VneoV7SfGv8xIwo2qNQ==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0", "estree-walker": "^2.0.2", "picomatch": "^4.0.2"}, "engines": {"node": ">=14.0.0"}, "peerDependencies": {"rollup": "^1.20.0||^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}}, "node_modules/@rollup/rollup-android-arm-eabi": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.40.1.tgz", "integrity": "sha512-kxz0YeeCrRUHz3zyqvd7n+TVRlNyTifBsmnmNPtk3hQURUyG9eAB+usz6DAwagMusjx/zb3AjvDUvhFGDAexGw==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-android-arm64": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.40.1.tgz", "integrity": "sha512-PPkxTOisoNC6TpnDKatjKkjRMsdaWIhyuMkA4UsBXT9WEZY4uHezBTjs6Vl4PbqQQeu6oION1w2voYZv9yquCw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["android"]}, "node_modules/@rollup/rollup-darwin-arm64": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.40.1.tgz", "integrity": "sha512-VWXGISWFY18v/0JyNUy4A46KCFCb9NVsH+1100XP31lud+TzlezBbz24CYzbnA4x6w4hx+NYCXDfnvDVO6lcAA==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-darwin-x64": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.40.1.tgz", "integrity": "sha512-nIwkXafAI1/QCS7pxSpv/ZtFW6TXcNUEHAIA9EIyw5OzxJZQ1YDrX+CL6JAIQgZ33CInl1R6mHet9Y/UZTg2Bw==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"]}, "node_modules/@rollup/rollup-freebsd-arm64": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.40.1.tgz", "integrity": "sha512-B<PERSON>LJ2mHTrIYdaS2I99mriyJfGGenSaP+UwGi1kB9BLOCu9SR8ZpbkmmalKIALnRw24kM7qCN0IOm6L0S44iWw==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-freebsd-x64": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.40.1.tgz", "integrity": "sha512-VXeo/puqvCG8JBPNZXZf5Dqq7BzElNJzHRRw3vjBE27WujdzuOPecDPc/+1DcdcTptNBep3861jNq0mYkT8Z6Q==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["freebsd"]}, "node_modules/@rollup/rollup-linux-arm-gnueabihf": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.40.1.tgz", "integrity": "sha512-ehSKrewwsESPt1TgSE/na9nIhWCosfGSFqv7vwEtjyAqZcvbGIg4JAcV7ZEh2tfj/IlfBeZjgOXm35iOOjadcg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm-musleabihf": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.40.1.tgz", "integrity": "sha512-m39iO/aaurh5FVIu/F4/Zsl8xppd76S4qoID8E+dSRQvTyZTOI2gVk3T4oqzfq1PtcvOfAVlwLMK3KRQMaR8lg==", "cpu": ["arm"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-gnu": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.40.1.tgz", "integrity": "sha512-Y+GHnGaku4aVLSgrT0uWe2o2Rq8te9hi+MwqGF9r9ORgXhmHK5Q71N757u0F8yU1OIwUIFy6YiJtKjtyktk5hg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-arm64-musl": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.40.1.tgz", "integrity": "sha512-jEwjn3jCA+tQGswK3aEWcD09/7M5wGwc6+flhva7dsQNRZZTe30vkalgIzV4tjkopsTS9Jd7Y1Bsj6a4lzz8gQ==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-loongarch64-gnu": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.40.1.tgz", "integrity": "sha512-ySyWikVhNzv+BV/IDCsrraOAZ3UaC8SZB67FZlqVwXwnFhPihOso9rPOxzZbjp81suB1O2Topw+6Ug3JNegejQ==", "cpu": ["loong64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-powerpc64le-gnu": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.40.1.tgz", "integrity": "sha512-BvvA64QxZlh7WZWqDPPdt0GH4bznuL6uOO1pmgPnnv86rpUpc8ZxgZwcEgXvo02GRIZX1hQ0j0pAnhwkhwPqWg==", "cpu": ["ppc64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-gnu": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.40.1.tgz", "integrity": "sha512-EQSP+8+1VuSulm9RKSMKitTav89fKbHymTf25n5+Yr6gAPZxYWpj3DzAsQqoaHAk9YX2lwEyAf9S4W8F4l3VBQ==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-riscv64-musl": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.40.1.tgz", "integrity": "sha512-n/vQ4xRZXKuIpqukkMXZt9RWdl+2zgGNx7Uda8NtmLJ06NL8jiHxUawbwC+hdSq1rrw/9CghCpEONor+l1e2gA==", "cpu": ["riscv64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-s390x-gnu": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.40.1.tgz", "integrity": "sha512-h8d28xzYb98fMQKUz0w2fMc1XuGzLLjdyxVIbhbil4ELfk5/orZlSTpF/xdI9C8K0I8lCkq+1En2RJsawZekkg==", "cpu": ["s390x"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-gnu": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.40.1.tgz", "integrity": "sha512-XiK5z70PEFEFqcNj3/zRSz/qX4bp4QIraTy9QjwJAb/Z8GM7kVUsD0Uk8maIPeTyPCP03ChdI+VVmJriKYbRHQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-linux-x64-musl": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.40.1.tgz", "integrity": "sha512-2BRORitq5rQ4Da9blVovzNCMaUlyKrzMSvkVR0D4qPuOy/+pMCrh1d7o01RATwVy+6Fa1WBw+da7QPeLWU/1mQ==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["linux"]}, "node_modules/@rollup/rollup-win32-arm64-msvc": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.40.1.tgz", "integrity": "sha512-b2bcNm9Kbde03H+q+Jjw9tSfhYkzrDUf2d5MAd1bOJuVplXvFhWz7tRtWvD8/ORZi7qSCy0idW6tf2HgxSXQSg==", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-ia32-msvc": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.40.1.tgz", "integrity": "sha512-DfcogW8N7Zg7llVEfpqWMZcaErKfsj9VvmfSyRjCyo4BI3wPEfrzTtJkZG6gKP/Z92wFm6rz2aDO7/JfiR/whA==", "cpu": ["ia32"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rollup/rollup-win32-x64-msvc": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.40.1.tgz", "integrity": "sha512-ECyOuDeH3C1I8jH2MK1RtBJW+YPMvSfT0a5NN0nHfQYnDSJ6tUiZH3gzwVP5/Kfh/+Tt7tpWVF9LXNTnhTJ3kA==", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"]}, "node_modules/@rtsao/scc": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@rtsao/scc/-/scc-1.1.0.tgz", "integrity": "sha512-zt6OdqaDoOnJ1ZYsCYGt9YmWzDXl4vQdKTyJev62gFhRGKdx7mcT54V9KIjg+d2wi9EXsPvAPKe7i7WjfVWB8g==", "dev": true, "license": "MIT"}, "node_modules/@stylistic/eslint-plugin": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/@stylistic/eslint-plugin/-/eslint-plugin-5.0.0.tgz", "integrity": "sha512-nVV2FSzeTJ3oFKw+3t9gQYQcrgbopgCASSY27QOtkhEGgSfdQQjDmzZd41NeT1myQ8Wc6l+pZllST9qIu4NKzg==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/types": "^8.34.1", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "estraverse": "^5.3.0", "picomatch": "^4.0.2"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "peerDependencies": {"eslint": ">=9.0.0"}}, "node_modules/@testing-library/dom": {"version": "10.4.0", "resolved": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.4.0.tgz", "integrity": "sha512-pemlzrSESWbdAloYml3bAJMEfNh1Z7EduzqPKprCH5S341frlpYnUEW0H72dLxa6IsYr+mPno20GiSm+h9dEdQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.3.0", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}, "engines": {"node": ">=18"}}, "node_modules/@testing-library/react": {"version": "16.3.0", "resolved": "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz", "integrity": "sha512-kFSyxiEDwv1WLl2fgsq6pPBbw5aWKrsY2/noi1Id0TK0UParSF62oFQFGHXIyaG4pp2tEub/Zlel+fjjZILDsw==", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.12.5"}, "engines": {"node": ">=18"}, "peerDependencies": {"@testing-library/dom": "^10.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "node_modules/@testing-library/user-event": {"version": "14.6.1", "resolved": "https://registry.npmjs.org/@testing-library/user-event/-/user-event-14.6.1.tgz", "integrity": "sha512-vq7fv0rnt+QTXgPxr5Hjc210p6YKq2kmdziLgnsZGgLJ9e6VAShx1pACLuRjd/AS/sr7phAR58OIIpf0LlmQNw==", "dev": true, "license": "MIT", "engines": {"node": ">=12", "npm": ">=6"}, "peerDependencies": {"@testing-library/dom": ">=7.21.4"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz", "integrity": "sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz", "integrity": "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "integrity": "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz", "integrity": "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==", "dev": true, "license": "MIT"}, "node_modules/@typefox/monaco-editor-react": {"resolved": "packages/wrapper-react", "link": true}, "node_modules/@types/aria-query": {"version": "5.0.4", "resolved": "https://registry.npmjs.org/@types/aria-query/-/aria-query-5.0.4.tgz", "integrity": "sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==", "dev": true, "license": "MIT"}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.6.8", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.6.8.tgz", "integrity": "sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "dev": true, "license": "MIT", "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.6", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.6.tgz", "integrity": "sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "resolved": "https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz", "integrity": "sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/chai": {"version": "5.2.2", "resolved": "https://registry.npmjs.org/@types/chai/-/chai-5.2.2.tgz", "integrity": "sha512-8kB30R7Hwqf40JPiKhVzodJs2Qc1ZJ5zuT3uzw5Hq/dhNCl3G3l83jfpdI1e20BP348+fV7VIL/+FxaXkqBmWg==", "dev": true, "license": "MIT", "dependencies": {"@types/deep-eql": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "resolved": "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz", "integrity": "sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/cors": {"version": "2.8.19", "resolved": "https://registry.npmjs.org/@types/cors/-/cors-2.8.19.tgz", "integrity": "sha512-mFNylyeyqN93lfe/9CSxOGREz8cpzAhH+E93xJ4xWQf62V8sQ/24reV2nyzUWM6H6Xji+GGHpkbLe7pVoUEskg==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/deep-eql": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/@types/deep-eql/-/deep-eql-4.0.2.tgz", "integrity": "sha512-c9h9dVVMigMPc4bwTvC5dxqtqJZwQPePsWjPlpSOnojbor6pGqdk541lfA7AqFQr5pB1BRdq0juY9db81BwyFw==", "dev": true, "license": "MIT"}, "node_modules/@types/emscripten": {"version": "1.40.1", "resolved": "https://registry.npmjs.org/@types/emscripten/-/emscripten-1.40.1.tgz", "integrity": "sha512-sr53lnYkQNhjHNN0oJDdUm5564biioI5DuOpycufDVK7D3y+GR3oUswe2rlwY1nPNyusHbrJ9WoTyIHl4/Bpwg==", "dev": true, "license": "MIT"}, "node_modules/@types/estree": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "integrity": "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "5.0.3", "resolved": "https://registry.npmjs.org/@types/express/-/express-5.0.3.tgz", "integrity": "sha512-wGA0NX93b19/dZC1J18tKWVIYWyyF2ZjT9vin/NRu0qzzvfVzWjs04iq2rQ3H65vCTQYlRqs3YHfY7zjdV+9Kw==", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^5.0.0", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.6", "resolved": "https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-5.0.6.tgz", "integrity": "sha512-3xhRnjJPkULekpSzgtoNYYcTWgEZkp4myc+Saevii5JPnHNvHMRlBSHDbs7Bh1iPPoVTERHEZXyhyLbMEsExsA==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz", "integrity": "sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "dev": true, "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "resolved": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz", "integrity": "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "resolved": "https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz", "integrity": "sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.15.32", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.15.32.tgz", "integrity": "sha512-3jigKqgSjsH6gYZv2nEsqdXfZqIFGAV36XYYjf9KGZ3PSG+IhLecqPnI310RvjutyMwifE2hhhNEklOUrvx/wA==", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.21.0"}}, "node_modules/@types/qs": {"version": "6.9.18", "resolved": "https://registry.npmjs.org/@types/qs/-/qs-6.9.18.tgz", "integrity": "sha512-kK7dgTYDyGqS+e2Q4aK9X3D7q234CIZ1Bv0q/7Z5IwRDoADNU81xXJK/YVyLbLTZCoIwUoDoffFeF+p/eIklAA==", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz", "integrity": "sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==", "dev": true, "license": "MIT"}, "node_modules/@types/react": {"version": "19.1.8", "resolved": "https://registry.npmjs.org/@types/react/-/react-19.1.8.tgz", "integrity": "sha512-AwAfQ2Wa5bCx9WP8nZL2uMZWod7J7/JSplxbTmBQ5ms6QpqNYm672H0Vu9ZVKVngQ+ii4R/byguVEUZQyeg44g==", "dev": true, "license": "MIT", "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/react-dom": {"version": "19.1.6", "resolved": "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.1.6.tgz", "integrity": "sha512-4hOiT/dwO8Ko0gV1m/TJZYk3y0KBnY9vzDh7W+DH17b2HFSOGgdj33dhihPeuy3l0q23+4e+hoXHV6hCC4dCXw==", "dev": true, "license": "MIT", "peerDependencies": {"@types/react": "^19.0.0"}}, "node_modules/@types/send": {"version": "0.17.4", "resolved": "https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz", "integrity": "sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "resolved": "https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.7.tgz", "integrity": "sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/trusted-types": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/@types/trusted-types/-/trusted-types-2.0.7.tgz", "integrity": "sha512-ScaPdn1dQczgbl0QFTeTOmVHFULt394XJgOQNoyVhZ6r2vLnMLJfBPd53SB52T/3G36VI1/g2MZaX0cwDuXsfw==", "license": "MIT", "optional": true}, "node_modules/@types/ws": {"version": "8.18.1", "resolved": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz", "integrity": "sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.35.0.tgz", "integrity": "sha512-ijItUYaiWuce0N1SoSMrEd0b6b6lYkYt99pqCPfybd+HKVXtEvYhICfLdwp42MhiI5mp0oq7PKEL+g1cNiz/Eg==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "8.35.0", "@typescript-eslint/type-utils": "8.35.0", "@typescript-eslint/utils": "8.35.0", "@typescript-eslint/visitor-keys": "8.35.0", "graphemer": "^1.4.0", "ignore": "^7.0.0", "natural-compare": "^1.4.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^8.35.0", "eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/eslint-plugin/node_modules/ignore": {"version": "7.0.4", "resolved": "https://registry.npmjs.org/ignore/-/ignore-7.0.4.tgz", "integrity": "sha512-gJzzk+PQNznz8ysRrC0aOkBNVRBDtE1n53IqyqEf3PXrYwomFs5q4pGMizBMJF+ykh03insJ27hB8gSrD2Hn8A==", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@typescript-eslint/parser": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/parser/-/parser-8.35.0.tgz", "integrity": "sha512-6sMvZePQrnZH2/cJkwRpkT7DxoAWh+g6+GFRK6bV3YQo7ogi3SX5rgF6099r5Q53Ma5qeT7LGmOmuIutF4t3lA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/scope-manager": "8.35.0", "@typescript-eslint/types": "8.35.0", "@typescript-eslint/typescript-estree": "8.35.0", "@typescript-eslint/visitor-keys": "8.35.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/project-service": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/project-service/-/project-service-8.35.0.tgz", "integrity": "sha512-41xatqRwWZuhUMF/aZm2fcUsOFKNcG28xqRSS6ZVr9BVJtGExosLAm5A1OxTjRMagx8nJqva+P5zNIGt8RIgbQ==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/tsconfig-utils": "^8.35.0", "@typescript-eslint/types": "^8.35.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/scope-manager": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/scope-manager/-/scope-manager-8.35.0.tgz", "integrity": "sha512-+AgL5+mcoLxl1vGjwNfiWq5fLDZM1TmTPYs2UkyHfFhgERxBbqHlNjRzhThJqz+ktBqTChRYY6zwbMwy0591AA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.35.0", "@typescript-eslint/visitor-keys": "8.35.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/tsconfig-utils": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.0.tgz", "integrity": "sha512-04k/7247kZzFraweuEirmvUj+W3bJLI9fX6fbo1Qm2YykuBvEhRTPl8tcxlYO8kZZW+HIXfkZNoasVb8EV4jpA==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/type-utils": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/type-utils/-/type-utils-8.35.0.tgz", "integrity": "sha512-ceNNttjfmSEoM9PW87bWLDEIaLAyR+E6BoYJQ5PfaDau37UGca9Nyq3lBk8Bw2ad0AKvYabz6wxc7DMTO2jnNA==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "8.35.0", "@typescript-eslint/utils": "8.35.0", "debug": "^4.3.4", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/types": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/types/-/types-8.35.0.tgz", "integrity": "sha512-0mYH3emanku0vHw2aRLNGqe7EXh9WHEhi7kZzscrMDf6IIRUQ5Jk4wp1QrledE/36KtdZrVfKnE32eZCf/vaVQ==", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/typescript-estree/-/typescript-estree-8.35.0.tgz", "integrity": "sha512-F+BhnaBemgu1Qf8oHrxyw14wq6vbL8xwWKKMwTMwYIRmFFY/1n/9T/jpbobZL8vp7QyEUcC6xGrnAO4ua8Kp7w==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/project-service": "8.35.0", "@typescript-eslint/tsconfig-utils": "8.35.0", "@typescript-eslint/types": "8.35.0", "@typescript-eslint/visitor-keys": "8.35.0", "debug": "^4.3.4", "fast-glob": "^3.3.2", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^2.1.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/minimatch": {"version": "9.0.5", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "integrity": "sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/@typescript-eslint/utils": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/utils/-/utils-8.35.0.tgz", "integrity": "sha512-nqoMu7WWM7ki5tPgLVsmPM8CkqtoPUG6xXGeefM5t4x3XumOEKMoUZPdi+7F+/EotukN4R9OWdmDxN80fqoZeg==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.7.0", "@typescript-eslint/scope-manager": "8.35.0", "@typescript-eslint/types": "8.35.0", "@typescript-eslint/typescript-estree": "8.35.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <5.9.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "8.35.0", "resolved": "https://registry.npmjs.org/@typescript-eslint/visitor-keys/-/visitor-keys-8.35.0.tgz", "integrity": "sha512-zTh2+1Y8ZpmeQaQVIc/ZZxsx8UzgKJyNg1PTvjzC7WMhPSVS8bfDX34k1SrwOf016qd5RU3az2UxUNue3IfQ5g==", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "8.35.0", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@vitejs/plugin-react": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.6.0.tgz", "integrity": "sha512-5Kgff+m8e2PB+9j51eGHEpn5kUzRKH2Ry0qGoe8ItJg7pqnkPrYPkDQZGgGmTa0EGarHrkjLvOdU3b1fzI8otQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/core": "^7.27.4", "@babel/plugin-transform-react-jsx-self": "^7.27.1", "@babel/plugin-transform-react-jsx-source": "^7.27.1", "@rolldown/pluginutils": "1.0.0-beta.19", "@types/babel__core": "^7.20.5", "react-refresh": "^0.17.0"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0-beta.0"}}, "node_modules/@vitest/browser": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/browser/-/browser-3.2.4.tgz", "integrity": "sha512-tJxiPrWmzH8a+w9nLKlQMzAKX/7VjFs50MWgcAj7p9XQ7AQ9/35fByFYptgPELyLw+0aixTnC4pUWV+APcZ/kw==", "dev": true, "license": "MIT", "dependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/user-event": "^14.6.1", "@vitest/mocker": "3.2.4", "@vitest/utils": "3.2.4", "magic-string": "^0.30.17", "sirv": "^3.0.1", "tinyrainbow": "^2.0.0", "ws": "^8.18.2"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"playwright": "*", "vitest": "3.2.4", "webdriverio": "^7.0.0 || ^8.0.0 || ^9.0.0"}, "peerDependenciesMeta": {"playwright": {"optional": true}, "safaridriver": {"optional": true}, "webdriverio": {"optional": true}}}, "node_modules/@vitest/expect": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/expect/-/expect-3.2.4.tgz", "integrity": "sha512-Io0yyORnB6sikFlt8QW5K7slY4OjqNX9jmJQ02QDda8lyM6B5oNgVWoSoKPac8/kgnCUzuHQKrSLtu/uOqqrig==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/mocker": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/mocker/-/mocker-3.2.4.tgz", "integrity": "sha512-46ryTE9RZO/rfDd7pEqFl7etuyzekzEhUbTW3BvmeO/BcCMEgq59BKhek3dXDWgAj4oMK6OZi+vRr1wPW6qjEQ==", "dev": true, "license": "MIT", "dependencies": {"@vitest/spy": "3.2.4", "estree-walker": "^3.0.3", "magic-string": "^0.30.17"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"msw": "^2.4.9", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "peerDependenciesMeta": {"msw": {"optional": true}, "vite": {"optional": true}}}, "node_modules/@vitest/mocker/node_modules/estree-walker": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-3.0.3.tgz", "integrity": "sha512-7RUKfXgSMMkzt6ZuXmqapOurLGPPfgj6l9uRZ7lRGolvk0y2yocc35LdcxKC5PQZdn2DMqioAQ2NoWcrTKmm6g==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.0"}}, "node_modules/@vitest/pretty-format": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/pretty-format/-/pretty-format-3.2.4.tgz", "integrity": "sha512-IVNZik8IVRJRTr9fxlitMKeJeXFFFN0JaB9PHPGQ8NKQbGpfjlTx9zO4RefN8gp7eqjNy8nyK3NZmBzOPeIxtA==", "dev": true, "license": "MIT", "dependencies": {"tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/runner": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/runner/-/runner-3.2.4.tgz", "integrity": "sha512-oukfKT9Mk41LreEW09vt45f8wx7DordoWUZMYdY/cyAk7w5TWkTRCNZYF7sX7n2wB7jyGAl74OxgwhPgKaqDMQ==", "dev": true, "license": "MIT", "dependencies": {"@vitest/utils": "3.2.4", "pathe": "^2.0.3", "strip-literal": "^3.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/snapshot": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/snapshot/-/snapshot-3.2.4.tgz", "integrity": "sha512-dEYtS7qQP2CjU27QBC5oUOxLE/v5eLkGqPE0ZKEIDGMs4vKWe7IjgLOeauHsR0D5YuuycGRO5oSRXnwnmA78fQ==", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.4", "magic-string": "^0.30.17", "pathe": "^2.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/spy": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/spy/-/spy-3.2.4.tgz", "integrity": "sha512-vAfasCOe6AIK70iP5UD11Ac4siNUNJ9i/9PZ3NKx07sG6sUxeag1LWdNrMWeKKYBLlzuK+Gn65Yd5nyL6ds+nw==", "dev": true, "license": "MIT", "dependencies": {"tinyspy": "^4.0.3"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vitest/utils": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/@vitest/utils/-/utils-3.2.4.tgz", "integrity": "sha512-fB2V0JFrQSMsCo9HiSq3Ezpdv4iYaXRG1Sx8edX3MwxfyNn83mKiGzOcH+Fkxt4MHxr3y42fQi1oeAInqgX2QA==", "dev": true, "license": "MIT", "dependencies": {"@vitest/pretty-format": "3.2.4", "loupe": "^3.1.4", "tinyrainbow": "^2.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/@vscode/iconv-lite-umd": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/@vscode/iconv-lite-umd/-/iconv-lite-umd-0.7.0.tgz", "integrity": "sha512-bRRFxLfg5dtAyl5XyiVWz/ZBPahpOpPrNYnnHpOpUZvam4tKH35wdhP4Kj6PbM0+KdliOsPzbGWpkxcdpNB/sg==", "license": "MIT"}, "node_modules/@vscode/l10n": {"version": "0.0.18", "resolved": "https://registry.npmjs.org/@vscode/l10n/-/l10n-0.0.18.tgz", "integrity": "sha512-KYSIHVmslkaCDyw013pphY+d7x1qV8IZupYfeIfzNA+nsaWHbn5uPuQRvdRFsa9zFzGeudPuoGoZ1Op4jrJXIQ==", "license": "MIT"}, "node_modules/@xterm/addon-clipboard": {"version": "0.2.0-beta.90", "resolved": "https://registry.npmjs.org/@xterm/addon-clipboard/-/addon-clipboard-0.2.0-beta.90.tgz", "integrity": "sha512-2i7qtACRBYRTRba831ufEjQMeAvK4uuVPYPBkSXzKJ/dIAvhws5B6OOmxqZzR97OsmzUC/SrjHSujgwyD0MVVw==", "license": "MIT", "dependencies": {"js-base64": "^3.7.5"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-image": {"version": "0.9.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/addon-image/-/addon-image-0.9.0-beta.107.tgz", "integrity": "sha512-/vqb2BhVjJeCcOIDSpjWrfXbhNTCse95qkKbpmzFkQJJEh0CyaqjSUBIAM+Qcl9f/x6H//O2wADnn3Wsn5qYsg==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-ligatures": {"version": "0.10.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/addon-ligatures/-/addon-ligatures-0.10.0-beta.107.tgz", "integrity": "sha512-3qQZz6dPS8XlGGwz1p5Bqjoosah6Km1GwGeNv7YxkCVU2kpTOymFH8BMUEOFC5WHdYseDXnz8hIEleexF9Pa2g==", "license": "MIT", "dependencies": {"font-finder": "^1.1.0", "font-ligatures": "^1.4.1"}, "engines": {"node": ">8.0.0"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-progress": {"version": "0.2.0-beta.13", "resolved": "https://registry.npmjs.org/@xterm/addon-progress/-/addon-progress-0.2.0-beta.13.tgz", "integrity": "sha512-BLmX+JdA+LLOMbVLQm+gPqieeKaVMiugBFpCtQtBus3kCs/2/F8pU/XNGV31CnXxDdRjptyOGrPVRreF/rDdcg==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-search": {"version": "0.16.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/addon-search/-/addon-search-0.16.0-beta.107.tgz", "integrity": "sha512-poHjeKjnTKtS6rHyu3r5lhW/0rdLcRMSJLDmT9xo9lfTuyhfcUl8gro44iPtscKY+LEtrhZcortTlElMewy/Bw==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-serialize": {"version": "0.14.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/addon-serialize/-/addon-serialize-0.14.0-beta.107.tgz", "integrity": "sha512-MGaoO2zlNuSGofX5Xfbw+MU2wgAApEsDOPeYoEBSQyLf5BR7Z2rGVCxFyF5zNsIhNDov7ptQIVpCfonMVAgUvg==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-unicode11": {"version": "0.9.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/addon-unicode11/-/addon-unicode11-0.9.0-beta.107.tgz", "integrity": "sha512-XTfJ1G7u+8zOT6faCfEB49AbVFAX4XWUoiLtyXCfRvbrVuv5wv53T03dLtBtpwjWoFaveLKGl3ZiU7t3dpl0RA==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/addon-webgl": {"version": "0.19.0-beta.107", "resolved": "https://registry.npmjs.org/@xterm/addon-webgl/-/addon-webgl-0.19.0-beta.107.tgz", "integrity": "sha512-wdUkYiV4PnR9+7+1giJoFm5xrguCOKTH4xdQ/6lYuSbgMS0y1ykpcQjDNshwHy0TyUuh4o4z1+rd48qNjqGM6Q==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.107"}}, "node_modules/@xterm/xterm": {"version": "5.6.0-beta.114", "resolved": "https://registry.npmjs.org/@xterm/xterm/-/xterm-5.6.0-beta.114.tgz", "integrity": "sha512-OW0Pz64qQU03xCv+56mscuxBeZH5cV7u4KrRrYxfFYn6VdbuY9pkWgR0yr5IfJ0lRfazVhZyzIo9/lm2uPPk4g==", "license": "MIT", "peer": true}, "node_modules/accepts": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/accepts/-/accepts-2.0.0.tgz", "integrity": "sha512-5cvg6CtKwfgdmVqY1WIiXKc3Q1bkRqGLi+2W/6ao+6Y7gu/RCwRuAhGEzh5B4KlszSuTLgZYuqFqo5bImjNKng==", "license": "MIT", "dependencies": {"mime-types": "^3.0.0", "negotiator": "^1.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "integrity": "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/anymatch/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "integrity": "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true, "license": "Python-2.0"}, "node_modules/aria-query": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/aria-query/-/aria-query-5.3.0.tgz", "integrity": "sha512-b0P0sZPKtyu8HkeRAfCq0IfURZK+SuwMjY1UXGBU27wpAiTwQAIlq56IbIO+ytk/JjS1fMR14ee5WBBfKi5J6A==", "dev": true, "license": "Apache-2.0", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz", "integrity": "sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-includes": {"version": "3.1.9", "resolved": "https://registry.npmjs.org/array-includes/-/array-includes-3.1.9.tgz", "integrity": "sha512-FmeCCAenzH0KH381SPT5FZmiA/TmpndpcaShhfgEN9eCVjnFBqq3l1xrI42y8+PPLI6hypzou4GXw00WHmPBLQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.24.0", "es-object-atoms": "^1.1.1", "get-intrinsic": "^1.3.0", "is-string": "^1.1.1", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.findlastindex": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/array.prototype.findlastindex/-/array.prototype.findlastindex-1.2.6.tgz", "integrity": "sha512-F/TKATkzseUExPlfvmwQKGITM3DGTK+vkAsCZoDc5daVygbJBnjEUCbgkAvVFsgfXfX4YIqZ/27G3k3tdXrTxQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flat": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz", "integrity": "sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array.prototype.flatmap": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz", "integrity": "sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz", "integrity": "sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/assertion-error": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/assertion-error/-/assertion-error-2.0.1.tgz", "integrity": "sha512-Izi8RQcffqCeNVgFigKli1ssklIbpHnCYc6AknXGYoB6grJqyeby7jv12JUQgmTAnIDnbck1uxksT4dzN3PWBA==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}}, "node_modules/async": {"version": "2.6.4", "resolved": "https://registry.npmjs.org/async/-/async-2.6.4.tgz", "integrity": "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/async-function": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/async-function/-/async-function-1.0.0.tgz", "integrity": "sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/available-typed-arrays": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz", "integrity": "sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT"}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/binary-extensions": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/body-parser": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-2.2.0.tgz", "integrity": "sha512-02qvAaxv8tp7fBa/mw1ga98OGm+eCbqzJOKoRt70sLmfEEi+jyBYVTDGfCL/k06/4EMk/z01gCe7HoCH/f2LTg==", "license": "MIT", "dependencies": {"bytes": "^3.1.2", "content-type": "^1.0.5", "debug": "^4.4.0", "http-errors": "^2.0.0", "iconv-lite": "^0.6.3", "on-finished": "^2.4.1", "qs": "^6.14.0", "raw-body": "^3.0.0", "type-is": "^2.0.0"}, "engines": {"node": ">=18"}}, "node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.0", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "integrity": "sha512-PJ8gYKeS5e/whHBh8xrwYK+dAvEj7JXtz6uTucnMRB8OiGTsKccFekoRrjajPBHV8oOY+2tI4uxeceSimKwMFA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001718", "electron-to-chromium": "^1.5.160", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer-crc32": {"version": "0.2.13", "resolved": "https://registry.npmjs.org/buffer-crc32/-/buffer-crc32-0.2.13.tgz", "integrity": "sha512-VO9Ht/+p3SN7SKWqcrgEzjGbRSJYTx+Q1pTQC0wrWqHx0vpJraQ6GtHx8tvcg1rlK1byhU5gccxgOgj7B0TDkQ==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cac": {"version": "6.7.14", "resolved": "https://registry.npmjs.org/cac/-/cac-6.7.14.tgz", "integrity": "sha512-b6<PERSON>lus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/call-bind": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz", "integrity": "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz", "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001723", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz", "integrity": "sha512-1R/elMjtehrFejxwmexeXAtae5UO9iSyFn6G/I806CYC/BLyyBk1EPhrKBkWhy6wM6Xnm47dSJQec+tLJ39WHw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chai": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/chai/-/chai-5.2.0.tgz", "integrity": "sha512-mCuXncKXk5iCLhfhwTc0izo0gtEmpz5CtG2y8GiOINBlMVS6v8TMRc5TaLWKS6692m9+dVVfzgeVxR5UxWHTYw==", "dev": true, "license": "MIT", "dependencies": {"assertion-error": "^2.0.1", "check-error": "^2.1.1", "deep-eql": "^5.0.1", "loupe": "^3.1.0", "pathval": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/check-error": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/check-error/-/check-error-2.1.1.tgz", "integrity": "sha512-OAlb+T7V4Op9OwdkjmguYRqncdlx5JiofwOAUkmTF+jNdHwzTaTs4sRAGpzLF3oOz5xAyDGrPgeIDFQmDOTiJw==", "dev": true, "license": "MIT", "engines": {"node": ">= 16"}}, "node_modules/chevrotain": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/chevrotain/-/chevrotain-11.0.3.tgz", "integrity": "sha512-ci2iJH6LeIkvP9eJW6gpueU8cnZhv85ELY8w8WiFtNjMHA5ad6pQLaJo9mEly/9qUyCpvqX8/POVUTf18/HFdw==", "license": "Apache-2.0", "dependencies": {"@chevrotain/cst-dts-gen": "11.0.3", "@chevrotain/gast": "11.0.3", "@chevrotain/regexp-to-ast": "11.0.3", "@chevrotain/types": "11.0.3", "@chevrotain/utils": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/chevrotain-allstar": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/chevrotain-allstar/-/chevrotain-allstar-0.3.1.tgz", "integrity": "sha512-b7g+y9A0v4mxCW1qUhf3BSVPg+/NvGErk/dOkrDaHA0nQIQGAtrOjlX//9OQtRlSCy+x9rfB5N8yC71lH1nvMw==", "license": "MIT", "dependencies": {"lodash-es": "^4.17.21"}, "peerDependencies": {"chevrotain": "^11.0.0"}}, "node_modules/chokidar": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz", "integrity": "sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==", "dev": true, "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "14.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-14.0.0.tgz", "integrity": "sha512-2uM9rYjPvyq39NwLRqaiLtWHyDC1FvryJDa2ATTVims5YAS4PupsEQsDvP14FqhFr0P49CYDugi59xaxJlTXRA==", "dev": true, "license": "MIT", "engines": {"node": ">=20"}}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true, "license": "MIT"}, "node_modules/content-disposition": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-1.0.0.tgz", "integrity": "sha512-Au9nRL8VNUut/XSzbQA38+M78dzP4D+eqg3gfJHMIHHYa3bg067xj1KxMUWj+VULbiZMowKngFFbKczUrNJ1mg==", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-disposition/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true, "license": "MIT"}, "node_modules/cookie-signature": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.2.2.tgz", "integrity": "sha512-D76uU73ulSXrD1UXF4KE2TMxVVwhsnCgfAyTg9k8P6KGZjlXKrOLe4dJQKI3Bxi5wjesZoFXJWElNWBjPZMbhg==", "license": "MIT", "engines": {"node": ">=6.6.0"}}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/corser": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/corser/-/corser-2.0.1.tgz", "integrity": "sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/create-require": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==", "dev": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css-url-parser": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/css-url-parser/-/css-url-parser-1.1.4.tgz", "integrity": "sha512-gIpYB7ZqfIsd+/kJ8CE4pesAbIUEaZM+30Ylfl7rr0zJONslIchmi3utzY64qHIOhD/wXDrcSo7jU2VDqG7GiQ==", "dev": true, "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz", "integrity": "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==", "dev": true, "license": "MIT"}, "node_modules/data-view-buffer": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/data-view-buffer/-/data-view-buffer-1.0.2.tgz", "integrity": "sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/data-view-byte-length": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz", "integrity": "sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "node_modules/data-view-byte-offset": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz", "integrity": "sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-eql": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/deep-eql/-/deep-eql-5.0.2.tgz", "integrity": "sha512-h5k/5U50IJJFpzfL6nO9jaaumfjO/f2NjK/oYB2Djzm4p9L+3T9qWpZqZ2hAbLPuuYq9wrU08WQyBTL5GbPk5Q==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==", "dev": true, "license": "MIT"}, "node_modules/define-data-property": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz", "integrity": "sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-properties": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz", "integrity": "sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dequal": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/dequal/-/dequal-2.0.3.tgz", "integrity": "sha512-0je+qPKHEMohvfRTCEo3CrPG6cAzAYgmzKyxRiYSSDkS6eGJdyVJm7WaYA5ECaAD9wLB2T4EEeymA5aFVcYXCA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/diff": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz", "integrity": "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/doctrine": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/doctrine/-/doctrine-2.1.0.tgz", "integrity": "sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/dom-accessibility-api": {"version": "0.5.16", "resolved": "https://registry.npmjs.org/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz", "integrity": "sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg==", "dev": true, "license": "MIT"}, "node_modules/dompurify": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-3.2.6.tgz", "integrity": "sha512-/2GogDQlohXPZe6D6NOgQvXLPSYBqIWMnZ8zzOhn09REE4eyAzb+Hed3jhoM9OkuaJ8P6ZGTTVWQKAi8ieIzfQ==", "license": "(MPL-2.0 OR Apache-2.0)", "optionalDependencies": {"@types/trusted-types": "^2.0.7"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/editorconfig": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/editorconfig/-/editorconfig-3.0.0.tgz", "integrity": "sha512-cU9vLVQ1YmDmLL047KkgQ3Yv9jSI6ZlqW4XjEdG0RrFhz7kYQ5lxD7SfhQci8g1TQu9iMA+54rRLe6yvWxXxcA==", "dev": true, "license": "MIT", "dependencies": {"@one-ini/wasm": "0.2.0", "commander": "^14.0.0", "minimatch": "10.0.1", "semver": "^7.7.2"}, "bin": {"editorconfig": "bin/editorconfig"}, "engines": {"node": ">=20"}}, "node_modules/editorconfig/node_modules/minimatch": {"version": "10.0.1", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.1.tgz", "integrity": "sha512-ethXTt3SGGR+95gudmqJ1eNhRO7eGEGIgYA9vnPatK4/etz2MEVDno5GMCibdMTuBMyElzIlgxMna3K94XDIDQ==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.169", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.169.tgz", "integrity": "sha512-q7SQx6mkLy0GTJK9K9OiWeaBMV4XQtBSdf6MJUzDB/H/5tFXfIiX38Lci1Kl6SsgiEhz1SQI1ejEOU5asWEhwQ==", "dev": true, "license": "ISC"}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.4", "resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz", "integrity": "sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/es-abstract": {"version": "1.24.0", "resolved": "https://registry.npmjs.org/es-abstract/-/es-abstract-1.24.0.tgz", "integrity": "sha512-WSzPgsdLtTcQwm4CROfS5ju2Wa1QQcVeT37jFjYzdFz1r9ahadC8B8/a4qxJxM+09F18iumCdRmlr96ZYkQvEg==", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.7.0.tgz", "integrity": "sha512-jEQoCwk8hyb2AZziIOLhDqpm5+2ww5uIE6lkO/6jcOCusfk6LhMHpXXfBLXTZ7Ydyt0j4VoUQv6uGNYbdW+kBA==", "dev": true, "license": "MIT"}, "node_modules/es-object-atoms": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz", "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-set-tostringtag": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz", "integrity": "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-shim-unscopables": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz", "integrity": "sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-to-primitive/-/es-to-primitive-1.3.0.tgz", "integrity": "sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/esbuild": {"version": "0.25.5", "resolved": "https://registry.npmjs.org/esbuild/-/esbuild-0.25.5.tgz", "integrity": "sha512-P8OtKZRv/5J5hhz0cUAdu/cLuPIKXpQl1R9pZtvmHWQvrAUVd0UNIPT4IB4W3rNOqVO0rlqHmCIbSwxh/c9yUQ==", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "9.29.0", "resolved": "https://registry.npmjs.org/eslint/-/eslint-9.29.0.tgz", "integrity": "sha512-GsGizj2Y1rCWDu6XoEekL3RLilp0voSePurjZIkxL3wlm5o5EC9VpgaP7lrCvjnkuLvzFBQWB3vWB3K5KQTveQ==", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.12.1", "@eslint/config-array": "^0.20.1", "@eslint/config-helpers": "^0.2.1", "@eslint/core": "^0.14.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "9.29.0", "@eslint/plugin-kit": "^0.3.1", "@humanfs/node": "^0.16.6", "@humanwhocodes/module-importer": "^1.0.1", "@humanwhocodes/retry": "^0.4.2", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.6", "debug": "^4.3.2", "escape-string-regexp": "^4.0.0", "eslint-scope": "^8.4.0", "eslint-visitor-keys": "^4.2.1", "espree": "^10.4.0", "esquery": "^1.5.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^8.0.0", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "json-stable-stringify-without-jsonify": "^1.0.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://eslint.org/donate"}, "peerDependencies": {"jiti": "*"}, "peerDependenciesMeta": {"jiti": {"optional": true}}}, "node_modules/eslint-import-resolver-node": {"version": "0.3.9", "resolved": "https://registry.npmjs.org/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz", "integrity": "sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-module-utils": {"version": "2.12.1", "resolved": "https://registry.npmjs.org/eslint-module-utils/-/eslint-module-utils-2.12.1.tgz", "integrity": "sha512-L8jSWTze7K2mTg0vos/RuLRS5soomksDPoJLXIslC7c8Wmut3bx7CPpJijDcBZtxQ5lrbUdM+s0OlNbz0DCDNw==", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-header": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/eslint-plugin-header/-/eslint-plugin-header-3.1.1.tgz", "integrity": "sha512-9vlKxuJ4qf793CmeeSrZUvVClw6amtpghq3CuWcB5cUNnWHQhgcqy5eF8oVKFk1G3Y/CbchGfEaw3wiIJaNmVg==", "dev": true, "license": "MIT", "peerDependencies": {"eslint": ">=7.7.0"}}, "node_modules/eslint-plugin-import": {"version": "2.32.0", "resolved": "https://registry.npmjs.org/eslint-plugin-import/-/eslint-plugin-import-2.32.0.tgz", "integrity": "sha512-whOE1HFo/qJDyX4SnXzP4N6zOWn79WhnCUY/iDR0mPfQZO8wcYE4JClzI2oZrhBnnMUCBCHZhO6VQyoBU95mZA==", "dev": true, "license": "MIT", "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.9", "array.prototype.findlastindex": "^1.2.6", "array.prototype.flat": "^1.3.3", "array.prototype.flatmap": "^1.3.3", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.1", "hasown": "^2.0.2", "is-core-module": "^2.16.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.1", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.9", "tsconfig-paths": "^3.15.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "node_modules/eslint-plugin-import/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/eslint-plugin-import/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/eslint-plugin-import/node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/eslint-plugin-unused-imports": {"version": "4.1.4", "resolved": "https://registry.npmjs.org/eslint-plugin-unused-imports/-/eslint-plugin-unused-imports-4.1.4.tgz", "integrity": "sha512-YptD6IzQjDardkl0POxnnRBhU1OEePMV0nd6siHaRBbd+lyh6NAhFEobiznKU7kTsSsDeSD62Pe7kAM1b7dAZQ==", "dev": true, "license": "MIT", "peerDependencies": {"@typescript-eslint/eslint-plugin": "^8.0.0-0 || ^7.0.0 || ^6.0.0 || ^5.0.0", "eslint": "^9.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"@typescript-eslint/eslint-plugin": {"optional": true}}}, "node_modules/eslint-scope": {"version": "8.4.0", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.4.0.tgz", "integrity": "sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "4.2.1", "resolved": "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz", "integrity": "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ==", "dev": true, "license": "Apache-2.0", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/espree": {"version": "10.4.0", "resolved": "https://registry.npmjs.org/espree/-/espree-10.4.0.tgz", "integrity": "sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.15.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^4.2.1"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz", "integrity": "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estree-walker": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz", "integrity": "sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==", "dev": true, "license": "MIT"}, "node_modules/esutils": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "dev": true, "license": "MIT"}, "node_modules/execa": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/execa/-/execa-1.0.0.tgz", "integrity": "sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/execa/node_modules/cross-spawn": {"version": "6.0.6", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.6.tgz", "integrity": "sha512-VqCUuhcd1iB+dsv8gxPttb5iZh/D0iubSP21g36KXdEuf6I5JiioesUVjpCdHV9MZRUfVFlvwtIUyPfxo5trtw==", "dev": true, "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/execa/node_modules/path-key": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/execa/node_modules/semver": {"version": "5.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-5.7.2.tgz", "integrity": "sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/execa/node_modules/shebang-command": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz", "integrity": "sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/execa/node_modules/shebang-regex": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz", "integrity": "sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/execa/node_modules/which": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/which/-/which-1.3.1.tgz", "integrity": "sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/expect-type": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/expect-type/-/expect-type-1.2.1.tgz", "integrity": "sha512-/kP8CAwxzLVEeFrMm4kMmy4CCDlpipyA7MYLVrdJIkV0fYF0UaigQHRsxHiuY/GEea+bh4KSv3TIlgr+2UL6bw==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.0.0"}}, "node_modules/express": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/express/-/express-5.1.0.tgz", "integrity": "sha512-DT9ck5YIRU+8GYzzU5kT3eHGA5iL+1Zd0EutOmTE9Dtk+Tvuzd23VBU+ec7HPNSTxXYO55gPV/hq4pSBJDjFpA==", "license": "MIT", "dependencies": {"accepts": "^2.0.0", "body-parser": "^2.2.0", "content-disposition": "^1.0.0", "content-type": "^1.0.5", "cookie": "^0.7.1", "cookie-signature": "^1.2.1", "debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "finalhandler": "^2.1.0", "fresh": "^2.0.0", "http-errors": "^2.0.0", "merge-descriptors": "^2.0.0", "mime-types": "^3.0.0", "on-finished": "^2.4.1", "once": "^1.4.0", "parseurl": "^1.3.3", "proxy-addr": "^2.0.7", "qs": "^6.14.0", "range-parser": "^1.2.1", "router": "^2.2.0", "send": "^1.1.0", "serve-static": "^2.2.0", "statuses": "^2.0.1", "type-is": "^2.0.1", "vary": "^1.1.2"}, "engines": {"node": ">= 18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/cookie": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.3.tgz", "integrity": "sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.18.0", "resolved": "https://registry.npmjs.org/fastq/-/fastq-1.18.0.tgz", "integrity": "sha512-QKHXPW0hD8g4UET03SdOdunzSouc9N4AuHdsX8XNcTsuz+yYFILVNIX4l9yHABMhiEI9Db0JTTIpu0wB+Y1QQw==", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/fdir": {"version": "6.4.6", "resolved": "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz", "integrity": "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w==", "dev": true, "license": "MIT", "peerDependencies": {"picomatch": "^3 || ^4"}, "peerDependenciesMeta": {"picomatch": {"optional": true}}}, "node_modules/file-entry-cache": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz", "integrity": "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ==", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^4.0.0"}, "engines": {"node": ">=16.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-2.1.0.tgz", "integrity": "sha512-/t88Ty3d5JWQbWYgaOGCCYfXRwV1+be02WqYYlL6h0lEiUAMPM8o8qKGO01YIkOHzka2up08wvgYD0mDiI+q3Q==", "license": "MIT", "dependencies": {"debug": "^4.4.0", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "on-finished": "^2.4.1", "parseurl": "^1.3.3", "statuses": "^2.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "integrity": "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz", "integrity": "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw==", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.4"}, "engines": {"node": ">=16"}}, "node_modules/flatted": {"version": "3.3.2", "resolved": "https://registry.npmjs.org/flatted/-/flatted-3.3.2.tgz", "integrity": "sha512-AiwGJM8YcNOaobumgtng+6NHuOqC3A7MixFeDafM3X9cIUM+xUXoS5Vfgf+OihAYe20fxqNM9yPBXJzRtZ/4eA==", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/font-finder": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/font-finder/-/font-finder-1.1.0.tgz", "integrity": "sha512-wpCL2uIbi6GurJbU7ZlQ3nGd61Ho+dSU6U83/xJT5UPFfN35EeCW/rOtS+5k+IuEZu2SYmHzDIPL9eA5tSYRAw==", "license": "MIT", "dependencies": {"get-system-fonts": "^2.0.0", "promise-stream-reader": "^1.0.1"}, "engines": {"node": ">8.0.0"}}, "node_modules/font-ligatures": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/font-ligatures/-/font-ligatures-1.4.1.tgz", "integrity": "sha512-7W6zlfyhvCqShZ5ReUWqmSd9vBaUudW0Hxis+tqUjtHhsPU+L3Grf8mcZAtCiXHTzorhwdRTId2WeH/88gdFkw==", "license": "MIT", "dependencies": {"font-finder": "^1.0.3", "lru-cache": "^6.0.0", "opentype.js": "^0.8.0"}, "engines": {"node": ">8.0.0"}}, "node_modules/font-ligatures/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/font-ligatures/node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "license": "ISC"}, "node_modules/for-each": {"version": "0.3.5", "resolved": "https://registry.npmjs.org/for-each/-/for-each-0.3.5.tgz", "integrity": "sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/fresh/-/fresh-2.0.0.tgz", "integrity": "sha512-Rx/WycZ60HOaqLKAi6cHRKKI7zxWbJ31MhntmtwMoaTeF7XFH9hhBp8vITaMidfljRQ6eYWCKkaTK+ykVJHP2A==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/fs-extra": {"version": "11.1.1", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-11.1.1.tgz", "integrity": "sha512-MGIE4HOvQCeUCzmlHs0vXpih4ysz4wg9qiSAu6cd42lVwPbTM1TjV7RusoyQqMmk/95gdQZX72u+YW+c3eEpFQ==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/function.prototype.name": {"version": "1.1.8", "resolved": "https://registry.npmjs.org/function.prototype.name/-/function.prototype.name-1.1.8.tgz", "integrity": "sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/functions-have-names/-/functions-have-names-1.2.3.tgz", "integrity": "sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-intrinsic": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz", "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz", "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz", "integrity": "sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/get-symbol-description": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/get-symbol-description/-/get-symbol-description-1.1.0.tgz", "integrity": "sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-system-fonts": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/get-system-fonts/-/get-system-fonts-2.0.2.tgz", "integrity": "sha512-zzlgaYnHMIEgHRrfC7x0Qp0Ylhw/sHpM6MHXeVBTYIsvGf5GpbnClB+Q6rAPdn+0gd2oZZIo6Tj3EaWrt4VhDQ==", "license": "MIT", "engines": {"node": ">8.0.0"}}, "node_modules/get-tsconfig": {"version": "4.10.0", "resolved": "https://registry.npmjs.org/get-tsconfig/-/get-tsconfig-4.10.0.tgz", "integrity": "sha512-kGzZ3LWWQcGIAmg6iWvXn0ei6WDtV26wzHRMwDSzmAbcXrTEXxHy6IehI6/4eT6VRKyMP1eF1VqwrVUmE/LR7A==", "dev": true, "license": "MIT", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "funding": {"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz", "integrity": "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/globals": {"version": "16.2.0", "resolved": "https://registry.npmjs.org/globals/-/globals-16.2.0.tgz", "integrity": "sha512-O+7l9tPdHCU320IigZZPj5zmRCFG9xHmx9cU8FqU2Rp+JN714seHV+2S9+JslCpY4gJwU2vOGox0wzgae/MCEg==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globalthis": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/globalthis/-/globalthis-1.0.4.tgz", "integrity": "sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/graphemer/-/graphemer-1.4.0.tgz", "integrity": "sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==", "dev": true, "license": "MIT"}, "node_modules/has-bigints": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-bigints/-/has-bigints-1.1.0.tgz", "integrity": "sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz", "integrity": "sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/has-proto/-/has-proto-1.2.0.tgz", "integrity": "sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz", "integrity": "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/html-encoding-sniffer": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "integrity": "sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-server": {"version": "14.1.1", "resolved": "https://registry.npmjs.org/http-server/-/http-server-14.1.1.tgz", "integrity": "sha512-+cbxadF40UXd9T01zUHgA+rlo2Bg1Srer4+B4NwIHdaGxAGGv59nYRnGGDJ9LBk7alpS0US+J+bLLdQOOkJq4A==", "dev": true, "license": "MIT", "dependencies": {"basic-auth": "^2.0.1", "chalk": "^4.1.2", "corser": "^2.0.1", "he": "^1.2.0", "html-encoding-sniffer": "^3.0.0", "http-proxy": "^1.18.1", "mime": "^1.6.0", "minimist": "^1.2.6", "opener": "^1.5.1", "portfinder": "^1.0.28", "secure-compare": "3.0.1", "union": "~0.5.0", "url-join": "^4.0.1"}, "bin": {"http-server": "bin/http-server"}, "engines": {"node": ">=12"}}, "node_modules/hyperdyperid": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/hyperdyperid/-/hyperdyperid-1.2.0.tgz", "integrity": "sha512-Y93lCzHYgGWdrJ66yIktxiaGULYc6oGiABxhcO5AufBeOyoIdZF7bIfLaOrbM0iGIOXQQgxxRrFEnb+Y6w1n4A==", "dev": true, "license": "MIT", "engines": {"node": ">=10.18"}}, "node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "integrity": "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/immediate": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz", "integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==", "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-meta-resolve": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/import-meta-resolve/-/import-meta-resolve-4.1.0.tgz", "integrity": "sha512-I6fiaX09Xivtk+THaMfAwnA3MVA5Big1WHF1Dfx9hFuvNIWpXnorlkzhcQf6ehrqQiiZECRt1poOAkPmer3ruw==", "dev": true, "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "license": "ISC"}, "node_modules/internal-slot": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/internal-slot/-/internal-slot-1.1.0.tgz", "integrity": "sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/interpret": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "integrity": "sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-array-buffer": {"version": "3.0.5", "resolved": "https://registry.npmjs.org/is-array-buffer/-/is-array-buffer-3.0.5.tgz", "integrity": "sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-async-function": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-async-function/-/is-async-function-2.1.1.tgz", "integrity": "sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==", "dev": true, "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-bigint/-/is-bigint-1.1.0.tgz", "integrity": "sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz", "integrity": "sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/is-boolean-object/-/is-boolean-object-1.2.1.tgz", "integrity": "sha512-l9qO6eFlUETHtuihLcYOaLKByJ1f+N4kthcU9YjHy3N+B3hWv0y/2Nd0mu/7lTFnRQHTrSdXF50HQ3bl5fEnng==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://registry.npmjs.org/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-view": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/is-data-view/-/is-data-view-1.0.2.tgz", "integrity": "sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz", "integrity": "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-finalizationregistry": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz", "integrity": "sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-generator-function": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-generator-function/-/is-generator-function-1.1.0.tgz", "integrity": "sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-map": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/is-map/-/is-map-2.0.3.tgz", "integrity": "sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-negative-zero": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/is-negative-zero/-/is-negative-zero-2.0.3.tgz", "integrity": "sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-number-object": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-number-object/-/is-number-object-1.1.1.tgz", "integrity": "sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-promise": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/is-promise/-/is-promise-4.0.0.tgz", "integrity": "sha512-hvpoI6korhJMnej285dSg6nu1+e6uxs7zG3BYAm5byqDsgJNWwxzM6z6iZiAgQR4TJ30JmBTOwqZUw3WlyH3AQ==", "license": "MIT"}, "node_modules/is-regex": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz", "integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-set": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/is-set/-/is-set-2.0.3.tgz", "integrity": "sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz", "integrity": "sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz", "integrity": "sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-string": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-string/-/is-string-1.1.1.tgz", "integrity": "sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-symbol/-/is-symbol-1.1.1.tgz", "integrity": "sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.15", "resolved": "https://registry.npmjs.org/is-typed-array/-/is-typed-array-1.1.15.tgz", "integrity": "sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakmap": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/is-weakmap/-/is-weakmap-2.0.2.tgz", "integrity": "sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/is-weakref/-/is-weakref-1.1.1.tgz", "integrity": "sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakset": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-weakset/-/is-weakset-2.0.4.tgz", "integrity": "sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/isarray": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/isarray/-/isarray-2.0.5.tgz", "integrity": "sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true, "license": "ISC"}, "node_modules/js-base64": {"version": "3.7.7", "resolved": "https://registry.npmjs.org/js-base64/-/js-base64-3.7.7.tgz", "integrity": "sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true, "license": "MIT"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jschardet": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/jschardet/-/jschardet-3.1.4.tgz", "integrity": "sha512-/kmVISmrwVwtyYU40iQUOp3SUPk2dhNCMsZBQX0R1/jZ8maaXJ/oZIzUOiyOqcgtLnETFKYChbJ5iDC/eWmFHg==", "license": "LGPL-2.1+", "engines": {"node": ">=0.1.90"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz", "integrity": "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonc-parser": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.3.1.tgz", "integrity": "sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==", "license": "MIT"}, "node_modules/jsonfile": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz", "integrity": "sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonschema": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/jsonschema/-/jsonschema-1.4.1.tgz", "integrity": "sha512-S6cATIPVv1z0IlxdN+zUk5EPjkGCdnhN4wVSBlvoUO1tOLJootbo9CquNJmbIh4yikWHiUedhRYrNPn1arpEmQ==", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/jszip": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz", "integrity": "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==", "license": "(MIT OR GPL-3.0-or-later)", "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}}, "node_modules/keyv": {"version": "4.5.4", "resolved": "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz", "integrity": "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/langium": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/langium/-/langium-3.5.0.tgz", "integrity": "sha512-tnqVzWOkUcoiY0bWlyE8diFrZjmGBCF7MesC1bjUaZM+YGQSfdPC+KkhmHM0DWFG+uLcPxidKaPP1SYGtg3J0Q==", "license": "MIT", "dependencies": {"chevrotain": "~11.0.3", "chevrotain-allstar": "~0.3.0", "vscode-languageserver": "~9.0.1", "vscode-languageserver-textdocument": "~1.0.11", "vscode-uri": "~3.0.8"}, "engines": {"node": ">=18.0.0"}}, "node_modules/langium-cli": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/langium-cli/-/langium-cli-3.5.0.tgz", "integrity": "sha512-TPIzIiMAQwTPPphtHGSrFXo4t0orx3aRh0syg9jnOihvBkBDvsQdJP9fBo9hp5Qaosklpc2CfbH0wh/dkgZcJA==", "dev": true, "license": "MIT", "dependencies": {"chalk": "~5.3.0", "commander": "~11.0.0", "fs-extra": "~11.1.1", "jsonschema": "~1.4.1", "langium": "~3.5.0", "langium-railroad": "~3.5.0", "lodash": "~4.17.21"}, "bin": {"langium": "bin/langium.js"}, "engines": {"node": ">=18.0.0"}}, "node_modules/langium-cli/node_modules/chalk": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/chalk/-/chalk-5.3.0.tgz", "integrity": "sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/langium-cli/node_modules/commander": {"version": "11.0.0", "resolved": "https://registry.npmjs.org/commander/-/commander-11.0.0.tgz", "integrity": "sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==", "dev": true, "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/langium-railroad": {"version": "3.5.0", "resolved": "https://registry.npmjs.org/langium-railroad/-/langium-railroad-3.5.0.tgz", "integrity": "sha512-80Enc6bOR6oHZD18IQlVTdfCh07rbrM5SOsPUPc1kyh7n3zQbmLs5P2E9hV4SoWlkhOjGKL1I9Z6uORpgy+jTQ==", "dev": true, "license": "MIT", "dependencies": {"langium": "~3.5.0", "railroad-diagrams": "~1.0.0"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz", "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/lie": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz", "integrity": "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true, "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==", "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==", "dev": true, "license": "MIT"}, "node_modules/loupe": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/loupe/-/loupe-3.1.4.tgz", "integrity": "sha512-wJzkKwJrheKtknCOKNEtDK4iqg/MxmZheEMtSTYvnzRdEYaZzmgH976nenp8WdJRdx5Vc1X/9MO0Oszl6ezeXg==", "dev": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/lz-string": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/lz-string/-/lz-string-1.5.0.tgz", "integrity": "sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==", "dev": true, "license": "MIT", "bin": {"lz-string": "bin/bin.js"}}, "node_modules/magic-string": {"version": "0.30.17", "resolved": "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz", "integrity": "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "integrity": "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==", "dev": true, "license": "ISC"}, "node_modules/marked": {"version": "14.0.0", "resolved": "https://registry.npmjs.org/marked/-/marked-14.0.0.tgz", "integrity": "sha512-uIj4+faQ+MgHgwUW1l2PsPglZLOLOT1uErt06dAPtx2kjteLAkbsd/0FiYg/MGS+i7ZKLb7w2WClxHkzOOuryQ==", "license": "MIT", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 18"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-1.1.0.tgz", "integrity": "sha512-aisnrDP4GNe06UcKFnV5bfMNPBUw4jsLGaWwWfnH3v02GnBuXX2MCVn5RbrWo0j3pczUilYblq7fQ7Nw2t5XKw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/memfs": {"version": "4.17.2", "resolved": "https://registry.npmjs.org/memfs/-/memfs-4.17.2.tgz", "integrity": "sha512-NgYhCOWgovOXSzvYgUW0LQ7Qy72rWQMGGFJDoWg4G30RHd3z77VbYdtJ4fembJXBy8pMIUA31XNAupobOQlwdg==", "dev": true, "license": "Apache-2.0", "dependencies": {"@jsonjoy.com/json-pack": "^1.0.3", "@jsonjoy.com/util": "^1.3.0", "tree-dump": "^1.0.1", "tslib": "^2.0.0"}, "engines": {"node": ">= 4.0.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}}, "node_modules/merge-descriptors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-2.0.0.tgz", "integrity": "sha512-Snk314V5ayFLhp3fkUREub6WtjBfPdCPY1Ln8/8munuLuiYhsABgBVWsozAG+MWMbVEvcdcpbi9R7ww22l9Q3g==", "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge2": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz", "integrity": "sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/micromatch/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.54.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "integrity": "sha512-aU5EJuIN2WDemCcAp2vFBfp/m4EAhWJnUNSSw0ixs7/kXbd6Pg64EmwJkNdFhB8aWt1sH2CTXrLxo/iAGV3oPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-3.0.1.tgz", "integrity": "sha512-xRc4oEhT6eaBpU1XF7AjpOFD+xQmXNB5OVKwp4tqCuBpHLS/ZbBDrc07mYTDqVMg6PfxUjjNp85O6Cd2Z/5HWA==", "license": "MIT", "dependencies": {"mime-db": "^1.54.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mini-coi": {"version": "0.4.2", "resolved": "https://registry.npmjs.org/mini-coi/-/mini-coi-0.4.2.tgz", "integrity": "sha512-9HPJlEWRjdmpplnGHEV6+0PYQMTQzsRtVwWROvBck1tKkBZRVf7Y6jriVgoNcDWc2iu1QC9AozjHrqUuwMNvpg==", "dev": true, "license": "MIT", "dependencies": {"static-handler": "^0.5.3"}, "bin": {"mini-coi": "mini-cli.js"}}, "node_modules/minimatch": {"version": "10.0.3", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-10.0.3.tgz", "integrity": "sha512-IPZ167aShDZZUMdRk66cyQAW3qr0WzbHkPdMYa8bzZhlHhO3jALbKdxcaak7W9FfT2rZNpQuUu4Od7ILEpXSaw==", "dev": true, "license": "ISC", "dependencies": {"@isaacs/brace-expansion": "^5.0.0"}, "engines": {"node": "20 || >=22"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/monaco-editor": {"name": "@codingame/monaco-vscode-editor-api", "version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-api/-/monaco-vscode-editor-api-18.1.0.tgz", "integrity": "sha512-q4T3VCqNvS3U8g7/ZzXGITCsX9bJulDRGQIUGw196QX9qd3HBMsFfUy4XUwx/eLNE6UyE+OIbqWLrJD7UKAcsQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0"}}, "node_modules/monaco-editor-wrapper": {"resolved": "packages/wrapper", "link": true}, "node_modules/monaco-languageclient": {"resolved": "packages/client", "link": true}, "node_modules/monaco-languageclient-examples": {"resolved": "packages/examples", "link": true}, "node_modules/mrmime": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/mrmime/-/mrmime-2.0.1.tgz", "integrity": "sha512-Y3wQdFg2Va6etvQ5I82yUhGdsKrcYox6p7FfL1LbK2J4V01F9TGlepTIhnK24t7koZibmg82KGglhA1XK5IsLQ==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.11", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "integrity": "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "dev": true, "license": "MIT"}, "node_modules/negotiator": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-1.0.0.tgz", "integrity": "sha512-8Ofs/AUQh8MaEcrlq5xOX0CQ9ypTF5dl78mjlMNfOK08fzpgTHQRQPBxcPlEtIw0yRpws+Zo/3r+5WRby7u3Gg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/nice-try": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz", "integrity": "sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==", "dev": true, "license": "MIT"}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true, "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz", "integrity": "sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/npm-run-path/node_modules/path-key": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.4", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.4.tgz", "integrity": "sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz", "integrity": "sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object.assign": {"version": "4.1.7", "resolved": "https://registry.npmjs.org/object.assign/-/object.assign-4.1.7.tgz", "integrity": "sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.fromentries": {"version": "2.0.8", "resolved": "https://registry.npmjs.org/object.fromentries/-/object.fromentries-2.0.8.tgz", "integrity": "sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.groupby": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/object.groupby/-/object.groupby-1.0.3.tgz", "integrity": "sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.values": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/object.values/-/object.values-1.2.1.tgz", "integrity": "sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/opener": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz", "integrity": "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==", "dev": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/opentype.js": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/opentype.js/-/opentype.js-0.8.0.tgz", "integrity": "sha512-FQHR4oGP+a0m/f6yHoRpBOIbn/5ZWxKd4D/djHVJu8+KpBTYrJda0b7mLcgDEMWXE9xBCJm+qb0yv6FcvPjukg==", "license": "MIT", "dependencies": {"tiny-inflate": "^1.0.2"}, "bin": {"ot": "bin/ot"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz", "integrity": "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/own-keys": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/own-keys/-/own-keys-1.0.1.tgz", "integrity": "sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz", "integrity": "sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-map": {"version": "7.0.3", "resolved": "https://registry.npmjs.org/p-map/-/p-map-7.0.3.tgz", "integrity": "sha512-VkndIv2fIB99swvQoA65bm+fsmt6UNdGeIB0oxBs+WhAhdh08QA04JXpI7rbB9r08/nkbysKoya9rtDERYOYMA==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pako": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz", "integrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==", "license": "(MIT AND Zlib)"}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true, "license": "MIT"}, "node_modules/path-to-regexp": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-8.2.0.tgz", "integrity": "sha512-TdrF7fW9Rphjq4RjrW0Kp2AW0Ahwu9sRGTkS6bvDi0SCwZlEZYmcfDbEsTz8RVk0EHIS/Vd1bv3JhG+1xZuAyQ==", "license": "MIT", "engines": {"node": ">=16"}}, "node_modules/pathe": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/pathe/-/pathe-2.0.3.tgz", "integrity": "sha512-WUjGcAqP1gQacoQe+OBJsFA7Ld4DyXuUIjZ5cc75cLHvJ7dtNsTugphxIADwspS+AraAUePCKrSVtPLFj/F88w==", "dev": true, "license": "MIT"}, "node_modules/pathval": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/pathval/-/pathval-2.0.0.tgz", "integrity": "sha512-vE7JKRyES09KiunauX7nd2Q9/L7lhok4smP9RZTDeD4MVs72Dp2qNFVz39Nz5a0FVEW0BJR6C0DYrq6unoziZA==", "dev": true, "license": "MIT", "engines": {"node": ">= 14.16"}}, "node_modules/pend": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/pend/-/pend-1.2.0.tgz", "integrity": "sha512-F3asv42UuXchdzt+xXqfW1OGlVBe+mxa2mqI0pg5yAHZPvFmY3Y6drSf/GQ1A86WgWEN9Kzh/WrgKa6iGcHXLg==", "dev": true, "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-4.0.2.tgz", "integrity": "sha512-M7BAV6Rlcy5u+m6oPhAPFgJTzAioX/6B0DxyvDlo9l8+T3nLKbrczg2WLUyzd45L8RqfUMyGPzekbMvX2Ldkwg==", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/playwright": {"version": "1.53.1", "resolved": "https://registry.npmjs.org/playwright/-/playwright-1.53.1.tgz", "integrity": "sha512-LJ13YLr/ocweuwxyGf1XNFWIU4M2zUSo149Qbp+A4cpwDjsxRPj7k6H25LBrEHiEwxvRbD8HdwvQmRMSvquhYw==", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright-core": "1.53.1"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/playwright-core": {"version": "1.53.1", "resolved": "https://registry.npmjs.org/playwright-core/-/playwright-core-1.53.1.tgz", "integrity": "sha512-Z46<PERSON>q7tLAyT0lGoFx4DOuB1IA9D1TPj0QkYxpPVUnGDqHHvDpCftu1J2hM2PiWsNMoZh8+LQaarAWcDfPBc6zg==", "dev": true, "license": "Apache-2.0", "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=18"}}, "node_modules/portfinder": {"version": "1.0.32", "resolved": "https://registry.npmjs.org/portfinder/-/portfinder-1.0.32.tgz", "integrity": "sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/possible-typed-array-names": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/possible-typed-array-names/-/possible-typed-array-names-1.0.0.tgz", "integrity": "sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/postcss": {"version": "8.5.6", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz", "integrity": "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/pretty-format": {"version": "27.5.1", "resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-27.5.1.tgz", "integrity": "sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1", "ansi-styles": "^5.0.0", "react-is": "^17.0.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "license": "MIT"}, "node_modules/promise-stream-reader": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/promise-stream-reader/-/promise-stream-reader-1.0.1.tgz", "integrity": "sha512-Tnxit5trUjBAqqZCGWwjyxhmgMN4hGrtpW3Oc/tRI4bpm/O2+ej72BB08l6JBnGQgVDGCLvHFGjGgQS6vzhwXg==", "license": "MIT", "engines": {"node": ">8.0.0"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/pump": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/pump/-/pump-3.0.2.tgz", "integrity": "sha512-tUPXtzlGM8FE3P0ZL6DVs/3P58k9nk8/jZeQCurTJylQA8qFYzHFfhBJkuqyE0FifOsQ0uKWekiZ5g8wtr28cw==", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pyright": {"version": "1.1.402", "resolved": "https://registry.npmjs.org/pyright/-/pyright-1.1.402.tgz", "integrity": "sha512-DwzfZFTlqg9j7VDvwSIORmTsYL8awHMce8DaNV7n+M3KjD0wFEWBQrxSUX6N0pv2BJcKe1PKx+8x9yfJRQvqKQ==", "license": "MIT", "bin": {"pyright": "index.js", "pyright-langserver": "langserver.index.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"fsevents": "~2.3.3"}}, "node_modules/pyright/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/qs": {"version": "6.14.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.14.0.tgz", "integrity": "sha512-YWWTjgABSKcvs/nWBi9PycY/JiPJqOD4JA6o9Sej2AtvSGarXxKC3OQSk4pAarbdQlKAh5D4FCQkJNkW+GAn3w==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz", "integrity": "sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/railroad-diagrams": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/railroad-diagrams/-/railroad-diagrams-1.0.0.tgz", "integrity": "sha512-cz93DjNeLY0idrCNOH6PviZGRN9GJhsdm9hpn1YCS879fj4W+x5IFJhhkRZcwVgMmFF7R82UA/7Oh+R8lLZg6A==", "dev": true, "license": "CC0-1.0"}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-3.0.0.tgz", "integrity": "sha512-RmkhL8CAyCRPXCE28MMH0z2PNWQBNk2Q09ZdxM9IOOXwxwZbN+qbWaatPkdkWIKL2ZVDImrN/pK5HTRz2PcS4g==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.6.3", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/react": {"version": "19.1.0", "resolved": "https://registry.npmjs.org/react/-/react-19.1.0.tgz", "integrity": "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "19.1.0", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-19.1.0.tgz", "integrity": "sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==", "license": "MIT", "dependencies": {"scheduler": "^0.26.0"}, "peerDependencies": {"react": "^19.1.0"}}, "node_modules/react-is": {"version": "17.0.2", "resolved": "https://registry.npmjs.org/react-is/-/react-is-17.0.2.tgz", "integrity": "sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==", "dev": true, "license": "MIT"}, "node_modules/react-refresh": {"version": "0.17.0", "resolved": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz", "integrity": "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "license": "MIT"}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==", "dev": true, "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/readdirp/node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/rechoir": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==", "dev": true, "dependencies": {"resolve": "^1.1.6"}, "engines": {"node": ">= 0.10"}}, "node_modules/reflect.getprototypeof": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz", "integrity": "sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regenerator-runtime": {"version": "0.14.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz", "integrity": "sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==", "dev": true, "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz", "integrity": "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/request-light": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/request-light/-/request-light-0.8.0.tgz", "integrity": "sha512-bH6E4PMmsEXYrLX6Kr1vu+xI3HproB1vECAwaPSJeroLE1kpWE3HR27uB4icx+6YORu1ajqBJXxuedv8ZQg5Lw==", "license": "MIT"}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-pkg-maps": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz", "integrity": "sha512-seS2Tj26TBVOC2NIc2rOe2y2ZO7efxITtLZcGSOnHHNOQ7CkiUBfw0Iw2ck6xkIhPwLhKNLS8BO+hEpngQlqzw==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"}}, "node_modules/reusify": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz", "integrity": "sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rollup": {"version": "4.40.1", "resolved": "https://registry.npmjs.org/rollup/-/rollup-4.40.1.tgz", "integrity": "sha512-C5VvvgCCyfyotVITIAv+4efVytl5F7wt+/I2i9q9GZcEXW9BP52YYOXC58igUi+LFZVHukErIIqQSWwv/M3WRw==", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "1.0.7"}, "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.40.1", "@rollup/rollup-android-arm64": "4.40.1", "@rollup/rollup-darwin-arm64": "4.40.1", "@rollup/rollup-darwin-x64": "4.40.1", "@rollup/rollup-freebsd-arm64": "4.40.1", "@rollup/rollup-freebsd-x64": "4.40.1", "@rollup/rollup-linux-arm-gnueabihf": "4.40.1", "@rollup/rollup-linux-arm-musleabihf": "4.40.1", "@rollup/rollup-linux-arm64-gnu": "4.40.1", "@rollup/rollup-linux-arm64-musl": "4.40.1", "@rollup/rollup-linux-loongarch64-gnu": "4.40.1", "@rollup/rollup-linux-powerpc64le-gnu": "4.40.1", "@rollup/rollup-linux-riscv64-gnu": "4.40.1", "@rollup/rollup-linux-riscv64-musl": "4.40.1", "@rollup/rollup-linux-s390x-gnu": "4.40.1", "@rollup/rollup-linux-x64-gnu": "4.40.1", "@rollup/rollup-linux-x64-musl": "4.40.1", "@rollup/rollup-win32-arm64-msvc": "4.40.1", "@rollup/rollup-win32-ia32-msvc": "4.40.1", "@rollup/rollup-win32-x64-msvc": "4.40.1", "fsevents": "~2.3.2"}}, "node_modules/router": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/router/-/router-2.2.0.tgz", "integrity": "sha512-nLTrUKm2UyiL7rlhapu/Zl45FwNgkZGaCpZbIHajDYgwlJCOzLSk+cIPAnsEqV955GjILJnKbdQC1nVPz+gAYQ==", "license": "MIT", "dependencies": {"debug": "^4.4.0", "depd": "^2.0.0", "is-promise": "^4.0.0", "parseurl": "^1.3.3", "path-to-regexp": "^8.0.0"}, "engines": {"node": ">= 18"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz", "integrity": "sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/safe-array-concat": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/safe-array-concat/-/safe-array-concat-1.1.3.tgz", "integrity": "sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "license": "MIT"}, "node_modules/safe-push-apply": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/safe-push-apply/-/safe-push-apply-1.0.0.tgz", "integrity": "sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-regex-test": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/safe-regex-test/-/safe-regex-test-1.1.0.tgz", "integrity": "sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/scheduler": {"version": "0.26.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.26.0.tgz", "integrity": "sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA==", "license": "MIT"}, "node_modules/secure-compare": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha512-<PERSON>ckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==", "dev": true, "license": "MIT"}, "node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/send/-/send-1.2.0.tgz", "integrity": "sha512-uaW0WwXKpL9blXE2o0bRhoL2EGXIrZxQ2ZQ4mgcfoBxdFmQold+qWsD2jLrfZ0trjKL6vOw0j//eAwcALFjKSw==", "license": "MIT", "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "engines": {"node": ">= 18"}}, "node_modules/serve-static": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-2.2.0.tgz", "integrity": "sha512-61g9pCh0Vnh7IutZjtLGGpTA355+OPn2TyDv/6ivP2h/AdAVX9azsoxmg2/M6nZeQZNYBEwIcsne1mJd9oQItQ==", "license": "MIT", "dependencies": {"encodeurl": "^2.0.0", "escape-html": "^1.0.3", "parseurl": "^1.3.3", "send": "^1.2.0"}, "engines": {"node": ">= 18"}}, "node_modules/set-function-length": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz", "integrity": "sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/set-function-name/-/set-function-name-2.0.2.tgz", "integrity": "sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-proto": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/set-proto/-/set-proto-1.0.0.tgz", "integrity": "sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==", "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "license": "ISC"}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shelljs": {"version": "0.9.2", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.9.2.tgz", "integrity": "sha512-S3I64fEiKgTZzKCC46zT/Ib9meqofLrQVbpSswtjFfAVDW+AZ54WTnAM/3/yENoxz/V1Cy6u3kiiEbQ4DNphvw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"execa": "^1.0.0", "fast-glob": "^3.3.2", "interpret": "^1.0.0", "rechoir": "^0.6.2"}, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=18"}}, "node_modules/shx": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/shx/-/shx-0.4.0.tgz", "integrity": "sha512-Z0KixSIlGPpijKgcH6oCMCbltPImvaKy0sGH8AkLRXw1KyzpKtaCTizP2xen+hNDqVF4xxgvA0KXSb9o4Q6hnA==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.8", "shelljs": "^0.9.2"}, "bin": {"shx": "lib/cli.js"}, "engines": {"node": ">=18"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/siginfo": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/siginfo/-/siginfo-2.0.0.tgz", "integrity": "sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==", "dev": true, "license": "ISC"}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true, "license": "ISC"}, "node_modules/sirv": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/sirv/-/sirv-3.0.1.tgz", "integrity": "sha512-FoqMu0NCGBLCcAkS1qA+XJIQTR6/JHfQXl+uGteNCQ76T91DMUjPa9xfmeqMY3z80nLSg9yQmNjK0Px6RWsH/A==", "dev": true, "license": "MIT", "dependencies": {"@polka/url": "^1.0.0-next.24", "mrmime": "^2.0.0", "totalist": "^3.0.0"}, "engines": {"node": ">=18"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/stackback": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/stackback/-/stackback-0.0.2.tgz", "integrity": "sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==", "dev": true, "license": "MIT"}, "node_modules/static-handler": {"version": "0.5.3", "resolved": "https://registry.npmjs.org/static-handler/-/static-handler-0.5.3.tgz", "integrity": "sha512-VSg7+Dd6HkgwjdJ9nRN2mmBZkP/u6ICA7RQuLqBFi3KdzWe9BEQXhMiDw3A8TYcllshIVn5nNP5fRcicOPZhzQ==", "dev": true, "license": "ISC", "bin": {"static-handler": "static-handler.cjs"}, "engines": {"node": ">=16"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/std-env": {"version": "3.9.0", "resolved": "https://registry.npmjs.org/std-env/-/std-env-3.9.0.tgz", "integrity": "sha512-UGvjygr6F6tpH7o2qyqR6QYpwraIjKSdtzyBdyytFOHmPZY917kwdwLG0RbOjWOnKmnm3PeHjaoLLMie7kPLQw==", "dev": true, "license": "MIT"}, "node_modules/stop-iteration-iterator": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz", "integrity": "sha512-eLoXW/DHyl62zxY4SCaIgnRhuMr6ri4juEYARS8E6sCEqzKpOiE521Ucofdx+KnDZl5xmvGYaaKCk5FEOxJCoQ==", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string.prototype.trim": {"version": "1.2.10", "resolved": "https://registry.npmjs.org/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz", "integrity": "sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.9", "resolved": "https://registry.npmjs.org/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz", "integrity": "sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz", "integrity": "sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-bom": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-3.0.0.tgz", "integrity": "sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-eof": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz", "integrity": "sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strip-literal": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/strip-literal/-/strip-literal-3.0.0.tgz", "integrity": "sha512-TcccoMhJOM3OebGhSBEmp3UZ2SfDMZUEBdRA/9ynfLi8yYajyWX3JiXArcJt4Umh4vISpspkQIY8ZZoCqjbviA==", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^9.0.1"}, "funding": {"url": "https://github.com/sponsors/antfu"}}, "node_modules/strip-literal/node_modules/js-tokens": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-9.0.1.tgz", "integrity": "sha512-mxa9E9ITFOt0ban3j6L5MpjwegGz6lBQmM1IJkWeBZGcMxto50+eWdjC/52xDbS2vy0k7vIMK0Fe2wfL9OQSpQ==", "dev": true, "license": "MIT"}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/thenby": {"version": "1.3.4", "resolved": "https://registry.npmjs.org/thenby/-/thenby-1.3.4.tgz", "integrity": "sha512-89Gi5raiWA3QZ4b2ePcEwswC3me9JIg+ToSgtE0JWeCynLnLxNr/f9G+xfo9K+Oj4AFdom8YNJjibIARTJmapQ==", "dev": true, "license": "Apache-2.0"}, "node_modules/thingies": {"version": "1.21.0", "resolved": "https://registry.npmjs.org/thingies/-/thingies-1.21.0.tgz", "integrity": "sha512-hsqsJsFMsV+aD4s3CWKk85ep/3I9XzYV/IXaSouJMYIoDlgyi11cBhsqYe9/geRfB0YIikBQg6raRaM+nIMP9g==", "dev": true, "license": "Unlicense", "engines": {"node": ">=10.18"}, "peerDependencies": {"tslib": "^2"}}, "node_modules/tiny-inflate": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tiny-inflate/-/tiny-inflate-1.0.3.tgz", "integrity": "sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==", "license": "MIT"}, "node_modules/tinybench": {"version": "2.9.0", "resolved": "https://registry.npmjs.org/tinybench/-/tinybench-2.9.0.tgz", "integrity": "sha512-0+DUvqWMValLmha6lr4kD8iAMK1HzV0/aKnCtWb9v9641TnP/MFb7Pc2bxoxQjTXAErryXVgUOfv2YqNllqGeg==", "dev": true, "license": "MIT"}, "node_modules/tinyexec": {"version": "0.3.2", "resolved": "https://registry.npmjs.org/tinyexec/-/tinyexec-0.3.2.tgz", "integrity": "sha512-KQQR9yN7R5+OSwaK0XQoj22pwHoTlgYqmUscPYoknOoWCWfj/5/ABTMRi69FrKU5ffPVh5QcFikpWJI/P1ocHA==", "dev": true, "license": "MIT"}, "node_modules/tinyglobby": {"version": "0.2.14", "resolved": "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz", "integrity": "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ==", "dev": true, "license": "MIT", "dependencies": {"fdir": "^6.4.4", "picomatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "funding": {"url": "https://github.com/sponsors/SuperchupuDev"}}, "node_modules/tinypool": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/tinypool/-/tinypool-1.1.1.tgz", "integrity": "sha512-Zba82s87IFq9A9XmjiX5uZA/ARWDrB03OHlq+Vw1fSdt0I+4/Kutwy8BP4Y/y/aORMo61FQ0vIb5j44vSo5Pkg==", "dev": true, "license": "MIT", "engines": {"node": "^18.0.0 || >=20.0.0"}}, "node_modules/tinyrainbow": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/tinyrainbow/-/tinyrainbow-2.0.0.tgz", "integrity": "sha512-op4nsTR47R6p0vMUUoYl/a+ljLFVtlfaXkLQmqfLR1qHma1h/ysYk4hEXZ880bf2CYgTskvTa/e196Vd5dDQXw==", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/tinyspy": {"version": "4.0.3", "resolved": "https://registry.npmjs.org/tinyspy/-/tinyspy-4.0.3.tgz", "integrity": "sha512-t2T/WLB2WRgZ9EpE4jgPJ9w+i66UZfDc8wHh0xrwiRNN+UwH98GIJkTeZqX9rg0i0ptwzqW+uYeIF0T4F8LR7A==", "dev": true, "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/totalist": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/totalist/-/totalist-3.0.1.tgz", "integrity": "sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tree-dump": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tree-dump/-/tree-dump-1.0.3.tgz", "integrity": "sha512-il+Cv80yVHFBwokQSfd4bldvr1Md951DpgAGfmhydt04L+YzHgubm2tQ7zueWDcGENKHq0ZvGFR/hjvNXilHEg==", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.0"}, "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "peerDependencies": {"tslib": "2"}}, "node_modules/ts-api-utils": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/ts-api-utils/-/ts-api-utils-2.1.0.tgz", "integrity": "sha512-CUgTZL1irw8u29bzrOD/nH85jqyc74D6SshFgujOIA7osm2Rz7dYH77agkx7H4FBNxDq7Cjf+IjaX/8zwFW+ZQ==", "dev": true, "license": "MIT", "engines": {"node": ">=18.12"}, "peerDependencies": {"typescript": ">=4.8.4"}}, "node_modules/ts-node": {"version": "10.9.2", "resolved": "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz", "integrity": "sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "resolved": "https://registry.npmjs.org/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz", "integrity": "sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "integrity": "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/tsx": {"version": "4.20.3", "resolved": "https://registry.npmjs.org/tsx/-/tsx-4.20.3.tgz", "integrity": "sha512-qjbnuR9Tr+FJOMBqJCW5ehvIo/buZq7vH7qD7JziU98h6l3qGy0a/yPFjwO+y0/T7GFpNgNAvEcPPVfyT8rrPQ==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "~0.25.0", "get-tsconfig": "^4.7.5"}, "bin": {"tsx": "dist/cli.mjs"}, "engines": {"node": ">=18.0.0"}, "optionalDependencies": {"fsevents": "~2.3.3"}}, "node_modules/tsx/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/type-check": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz", "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-is": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/type-is/-/type-is-2.0.1.tgz", "integrity": "sha512-OZs6gsjF4vMp32qrCbiVSkrFmXtG/AZhY3t0iAMrMBiAZyV9oALtXO8hsrHbMXF9x6L3grlFuwW2oAz7cav+Gw==", "license": "MIT", "dependencies": {"content-type": "^1.0.5", "media-typer": "^1.1.0", "mime-types": "^3.0.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz", "integrity": "sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz", "integrity": "sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz", "integrity": "sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/typed-array-length/-/typed-array-length-1.0.7.tgz", "integrity": "sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.8.3.tgz", "integrity": "sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/unbox-primitive": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/unbox-primitive/-/unbox-primitive-1.1.0.tgz", "integrity": "sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/undici-types": {"version": "6.21.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "integrity": "sha512-iwDZqg0QAGrg9Rav5H4n0M64c3mkR59cJ6wQp+7C4nI0gsmExaedaYLNO44eT4AtBBwjbTiGPMlt2Md0T9H9JQ==", "dev": true, "license": "MIT"}, "node_modules/union": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/union/-/union-0.5.0.tgz", "integrity": "sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==", "dev": true, "dependencies": {"qs": "^6.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/universalify": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz", "integrity": "sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-join": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz", "integrity": "sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==", "dev": true, "license": "MIT"}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "license": "MIT"}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==", "dev": true, "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vite": {"version": "6.3.5", "resolved": "https://registry.npmjs.org/vite/-/vite-6.3.5.tgz", "integrity": "sha512-cZn6NDFE7wdTpINgs++ZJ4N49W2vRp8LCKrn3Ob1kYNtOo21vfDoaV5GzBfLU4MovSAB8uNRm4jgzVQZ+mBzPQ==", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "fdir": "^6.4.4", "picomatch": "^4.0.2", "postcss": "^8.5.3", "rollup": "^4.34.9", "tinyglobby": "^0.2.13"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.3"}, "peerDependencies": {"@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "jiti": ">=1.21.0", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "sass-embedded": "*", "stylus": "*", "sugarss": "*", "terser": "^5.16.0", "tsx": "^4.8.1", "yaml": "^2.4.2"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "jiti": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}, "tsx": {"optional": true}, "yaml": {"optional": true}}}, "node_modules/vite-node": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/vite-node/-/vite-node-3.2.4.tgz", "integrity": "sha512-EbKSKh+bh1E1IFxeO0pg1n4dvoOTt0UDiXMd/qn++r98+jPO1xtJilvXldeuQ8giIB5IkpjCgMleHMNEsGH6pg==", "dev": true, "license": "MIT", "dependencies": {"cac": "^6.7.14", "debug": "^4.4.1", "es-module-lexer": "^1.7.0", "pathe": "^2.0.3", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0"}, "bin": {"vite-node": "vite-node.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}}, "node_modules/vite-plugin-static-copy": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/vite-plugin-static-copy/-/vite-plugin-static-copy-3.0.2.tgz", "integrity": "sha512-/seLvhUg44s1oU9RhjTZZy/0NPbfNctozdysKcvPovxxXZdI5l19mGq6Ri3IaTf1Dy/qChS4BSR7ayxeu8o9aQ==", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^3.5.3", "fs-extra": "^11.3.0", "p-map": "^7.0.3", "picocolors": "^1.1.1", "tinyglobby": "^0.2.14"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "peerDependencies": {"vite": "^5.0.0 || ^6.0.0"}}, "node_modules/vite-plugin-static-copy/node_modules/fs-extra": {"version": "11.3.0", "resolved": "https://registry.npmjs.org/fs-extra/-/fs-extra-11.3.0.tgz", "integrity": "sha512-Z4XaCL6dUDHfP/jT25jJKMmtxvuwbkrD1vNSMFlo9lNLY2c5FHYSQgHPRZUjAB26TpDEoW9HCOgplrdbaPV/ew==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/vite/node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/vitest": {"version": "3.2.4", "resolved": "https://registry.npmjs.org/vitest/-/vitest-3.2.4.tgz", "integrity": "sha512-LUCP5ev3GURDysTWiP47wRRUpLKMOfPh+yKTx3kVIEiu5KOMeqzpnYNsKyOoVrULivR8tLcks4+lga33Whn90A==", "dev": true, "license": "MIT", "dependencies": {"@types/chai": "^5.2.2", "@vitest/expect": "3.2.4", "@vitest/mocker": "3.2.4", "@vitest/pretty-format": "^3.2.4", "@vitest/runner": "3.2.4", "@vitest/snapshot": "3.2.4", "@vitest/spy": "3.2.4", "@vitest/utils": "3.2.4", "chai": "^5.2.0", "debug": "^4.4.1", "expect-type": "^1.2.1", "magic-string": "^0.30.17", "pathe": "^2.0.3", "picomatch": "^4.0.2", "std-env": "^3.9.0", "tinybench": "^2.9.0", "tinyexec": "^0.3.2", "tinyglobby": "^0.2.14", "tinypool": "^1.1.1", "tinyrainbow": "^2.0.0", "vite": "^5.0.0 || ^6.0.0 || ^7.0.0-0", "vite-node": "3.2.4", "why-is-node-running": "^2.3.0"}, "bin": {"vitest": "vitest.mjs"}, "engines": {"node": "^18.0.0 || ^20.0.0 || >=22.0.0"}, "funding": {"url": "https://opencollective.com/vitest"}, "peerDependencies": {"@edge-runtime/vm": "*", "@types/debug": "^4.1.12", "@types/node": "^18.0.0 || ^20.0.0 || >=22.0.0", "@vitest/browser": "3.2.4", "@vitest/ui": "3.2.4", "happy-dom": "*", "jsdom": "*"}, "peerDependenciesMeta": {"@edge-runtime/vm": {"optional": true}, "@types/debug": {"optional": true}, "@types/node": {"optional": true}, "@vitest/browser": {"optional": true}, "@vitest/ui": {"optional": true}, "happy-dom": {"optional": true}, "jsdom": {"optional": true}}}, "node_modules/vscode": {"name": "@codingame/monaco-vscode-extension-api", "version": "18.1.0", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extension-api/-/monaco-vscode-extension-api-18.1.0.tgz", "integrity": "sha512-jmdCy4Dxx7GX8YNvLI8TqSOUbMrNWxdagMubuLMbS2X2ilkxTIWzBSb7800E5SPosv5Z6Z7yJ4E5+DbqTMnXDg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-************************************-common": "18.1.0", "@codingame/monaco-vscode-api": "18.1.0", "@codingame/monaco-vscode-extensions-service-override": "18.1.0"}}, "node_modules/vscode-json-languageservice": {"version": "5.6.0", "resolved": "https://registry.npmjs.org/vscode-json-languageservice/-/vscode-json-languageservice-5.6.0.tgz", "integrity": "sha512-w1dv0nEoFxaNDq0PlYleYnlM4sFYXtFNZxaGGYy9nsCidXqHMh4RFHqld6XkFOhxs7hRBpK1QuXlH9OFDkTyfg==", "license": "MIT", "dependencies": {"@vscode/l10n": "^0.0.18", "jsonc-parser": "^3.3.1", "vscode-languageserver-textdocument": "^1.0.12", "vscode-languageserver-types": "^3.17.5", "vscode-uri": "^3.1.0"}}, "node_modules/vscode-json-languageservice/node_modules/vscode-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.1.0.tgz", "integrity": "sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==", "license": "MIT"}, "node_modules/vscode-jsonrpc": {"version": "8.2.1", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-8.2.1.tgz", "integrity": "sha512-kdjOSJ2lLIn7r1rtrMbbNCHjyMPfRnowdKjBQ+mGq6NAW5QY2bEZC/khaC5OR8svbbjvLEaIXkOq45e2X9BIbQ==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/vscode-languageclient": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/vscode-languageclient/-/vscode-languageclient-9.0.1.tgz", "integrity": "sha512-JZiimVdvimEuHh5olxhxkht09m3JzUGwggb5eRUkzzJhZ2KjCN0nh55VfiED9oez9DyF8/fz1g1iBV3h+0Z2EA==", "license": "MIT", "dependencies": {"minimatch": "^5.1.0", "semver": "^7.3.7", "vscode-languageserver-protocol": "3.17.5"}, "engines": {"vscode": "^1.82.0"}}, "node_modules/vscode-languageclient/node_modules/minimatch": {"version": "5.1.6", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/vscode-languageserver": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/vscode-languageserver/-/vscode-languageserver-9.0.1.tgz", "integrity": "sha512-woByF3PDpkHFUreUa7Hos7+pUWdeWMXRd26+ZX2A8cFx6v/JPTtd4/uN0/jB6XQHYaOlHbio03NTHCqrgG5n7g==", "license": "MIT", "dependencies": {"vscode-languageserver-protocol": "3.17.5"}, "bin": {"installServerIntoExtension": "bin/installServerIntoExtension"}}, "node_modules/vscode-languageserver-protocol": {"version": "3.17.5", "resolved": "https://registry.npmjs.org/vscode-languageserver-protocol/-/vscode-languageserver-protocol-3.17.5.tgz", "integrity": "sha512-mb1bvRJN8SVznADSGWM9u/b07H7Ecg0I3OgXDuLdn307rl/J3A9YD6/eYOssqhecL27hK1IPZAsaqh00i/Jljg==", "license": "MIT", "dependencies": {"vscode-jsonrpc": "8.2.0", "vscode-languageserver-types": "3.17.5"}}, "node_modules/vscode-languageserver-protocol/node_modules/vscode-jsonrpc": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-8.2.0.tgz", "integrity": "sha512-C+r0eKJUIfiDIfwJhria30+TYWPtuHJXHtI7J0YlOmKAo7ogxP20T0zxB7HZQIFhIyvoBPwWskjxrvAtfjyZfA==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/vscode-languageserver-textdocument": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/vscode-languageserver-textdocument/-/vscode-languageserver-textdocument-1.0.12.tgz", "integrity": "sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==", "license": "MIT"}, "node_modules/vscode-languageserver-types": {"version": "3.17.5", "resolved": "https://registry.npmjs.org/vscode-languageserver-types/-/vscode-languageserver-types-3.17.5.tgz", "integrity": "sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==", "license": "MIT"}, "node_modules/vscode-oniguruma": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/vscode-oniguruma/-/vscode-oniguruma-1.7.0.tgz", "integrity": "sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==", "license": "MIT"}, "node_modules/vscode-textmate": {"version": "9.2.0", "resolved": "https://registry.npmjs.org/vscode-textmate/-/vscode-textmate-9.2.0.tgz", "integrity": "sha512-rkvG4SraZQaPSN/5XjwKswdU0OP9MF28QjrYzUBbhb8QyG3ljB1Ky996m++jiI7KdiAP2CkBiQZd9pqEDTClqA==", "license": "MIT"}, "node_modules/vscode-uri": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.0.8.tgz", "integrity": "sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==", "license": "MIT"}, "node_modules/vscode-ws-jsonrpc": {"resolved": "packages/vscode-ws-jsonrpc", "link": true}, "node_modules/whatwg-encoding": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "integrity": "sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=12"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/which-boxed-primitive": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz", "integrity": "sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-builtin-type": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/which-builtin-type/-/which-builtin-type-1.2.1.tgz", "integrity": "sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-collection": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/which-collection/-/which-collection-1.0.2.tgz", "integrity": "sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==", "dev": true, "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-typed-array": {"version": "1.1.19", "resolved": "https://registry.npmjs.org/which-typed-array/-/which-typed-array-1.1.19.tgz", "integrity": "sha512-rEvr90Bck4WZt9HHFC4DJMsjvu7x+r6bImz0/BrbWb7A2djJ8hnZMrWnHo9F8ssv0OMErasDhftrfROTyqSDrw==", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/why-is-node-running": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/why-is-node-running/-/why-is-node-running-2.3.0.tgz", "integrity": "sha512-hUrmaWBdVDcxvYqnyh09zunKzROWjbZTiNy8dBEjkS7ehEDQibXJ7XvlmtbwuTclUiIyN+CyXQD4Vmko8fNm8w==", "dev": true, "license": "MIT", "dependencies": {"siginfo": "^2.0.0", "stackback": "0.0.2"}, "bin": {"why-is-node-running": "cli.js"}, "engines": {"node": ">=8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz", "integrity": "sha512-B<PERSON>22B<PERSON>eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "license": "ISC"}, "node_modules/ws": {"version": "8.18.2", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "integrity": "sha512-DMricUmwGZUVr++AEAe2uiVM7UoO9MAVZMDu05UQOaUII0lp+zOzLLU4Xqh/JvTqklB1T4uELaaPBKyjE1r4fQ==", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/wtd-core": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/wtd-core/-/wtd-core-4.0.1.tgz", "integrity": "sha512-q6sV6Slw47bwlhwbztot0MklWaVzywUAi0wAKWwOuL/LTY4IpVFgoHQ+cnlhG2ZUms/OkJUhyfhsfoHNYkKjzA==", "license": "MIT"}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true, "license": "ISC"}, "node_modules/yauzl": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/yauzl/-/yauzl-3.2.0.tgz", "integrity": "sha512-Ow9nuGZE+qp1u4JIPvg+uCiUr7xGQWdff7JQSk5VGYTAZMDe2q8lxJ10ygv10qmSj031Ty/6FNJpLO4o1Sgc+w==", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "pend": "~1.2.0"}, "engines": {"node": ">=12"}}, "node_modules/yn": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz", "integrity": "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "packages/client": {"name": "monaco-languageclient", "version": "9.8.0", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-configuration-service-override": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-extensions-service-override": "~18.1.0", "@codingame/monaco-vscode-languages-service-override": "~18.1.0", "@codingame/monaco-vscode-localization-service-override": "~18.1.0", "@codingame/monaco-vscode-log-service-override": "~18.1.0", "@codingame/monaco-vscode-model-service-override": "~18.1.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "packages/examples": {"name": "monaco-languageclient-examples", "version": "2025.6.2", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-configuration-service-override": "~18.1.0", "@codingame/monaco-vscode-cpp-default-extension": "~18.1.0", "@codingame/monaco-vscode-debug-service-override": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-environment-service-override": "~18.1.0", "@codingame/monaco-vscode-explorer-service-override": "~18.1.0", "@codingame/monaco-vscode-files-service-override": "~18.1.0", "@codingame/monaco-vscode-groovy-default-extension": "~18.1.0", "@codingame/monaco-vscode-java-default-extension": "~18.1.0", "@codingame/monaco-vscode-javascript-default-extension": "~18.1.0", "@codingame/monaco-vscode-json-default-extension": "~18.1.0", "@codingame/monaco-vscode-keybindings-service-override": "~18.1.0", "@codingame/monaco-vscode-lifecycle-service-override": "~18.1.0", "@codingame/monaco-vscode-localization-service-override": "~18.1.0", "@codingame/monaco-vscode-outline-service-override": "~18.1.0", "@codingame/monaco-vscode-preferences-service-override": "~18.1.0", "@codingame/monaco-vscode-python-default-extension": "~18.1.0", "@codingame/monaco-vscode-remote-agent-service-override": "~18.1.0", "@codingame/monaco-vscode-search-result-default-extension": "~18.1.0", "@codingame/monaco-vscode-search-service-override": "~18.1.0", "@codingame/monaco-vscode-secret-storage-service-override": "~18.1.0", "@codingame/monaco-vscode-standalone-json-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-typescript-language-features": "~18.1.0", "@codingame/monaco-vscode-storage-service-override": "~18.1.0", "@codingame/monaco-vscode-testing-service-override": "~18.1.0", "@codingame/monaco-vscode-textmate-service-override": "~18.1.0", "@codingame/monaco-vscode-theme-defaults-default-extension": "~18.1.0", "@codingame/monaco-vscode-theme-service-override": "~18.1.0", "@codingame/monaco-vscode-typescript-basics-default-extension": "~18.1.0", "@codingame/monaco-vscode-typescript-language-features-default-extension": "~18.1.0", "@codingame/monaco-vscode-views-service-override": "~18.1.0", "@typefox/monaco-editor-react": "~6.9.0", "cors": "^2.8.5", "express": "~5.1.0", "jszip": "~3.10.1", "langium": "~3.5.0", "monaco-editor-wrapper": "~6.9.0", "monaco-languageclient": "~9.8.0", "pyright": "~1.1.402", "react": "~19.1.0", "react-dom": "~19.1.0", "request-light": "~0.8.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-json-languageservice": "~5.6.0", "vscode-languageclient": "~9.0.1", "vscode-languageserver": "~9.0.1", "vscode-uri": "~3.1.0", "vscode-ws-jsonrpc": "~3.4.0", "ws": "~8.18.2", "wtd-core": "~4.0.1"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/emscripten": "~1.40.1", "@types/express": "~5.0.3", "@types/ws": "~8.18.1", "langium-cli": "~3.5.0", "mini-coi": "~0.4.2", "ts-node": "~10.9.1", "vite-plugin-static-copy": "~3.0.2", "vscode-languageserver-types": "~3.17.5"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "packages/examples/node_modules/vscode-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.1.0.tgz", "integrity": "sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==", "license": "MIT"}, "packages/vscode-ws-jsonrpc": {"version": "3.4.0", "license": "MIT", "dependencies": {"vscode-jsonrpc": "~8.2.1"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "packages/wrapper": {"name": "monaco-editor-wrapper", "version": "6.9.0", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "~18.1.0", "@codingame/monaco-vscode-editor-api": "~18.1.0", "@codingame/monaco-vscode-editor-service-override": "~18.1.0", "@codingame/monaco-vscode-extension-api": "~18.1.0", "@codingame/monaco-vscode-language-pack-cs": "~18.1.0", "@codingame/monaco-vscode-language-pack-de": "~18.1.0", "@codingame/monaco-vscode-language-pack-es": "~18.1.0", "@codingame/monaco-vscode-language-pack-fr": "~18.1.0", "@codingame/monaco-vscode-language-pack-it": "~18.1.0", "@codingame/monaco-vscode-language-pack-ja": "~18.1.0", "@codingame/monaco-vscode-language-pack-ko": "~18.1.0", "@codingame/monaco-vscode-language-pack-pl": "~18.1.0", "@codingame/monaco-vscode-language-pack-pt-br": "~18.1.0", "@codingame/monaco-vscode-language-pack-qps-ploc": "~18.1.0", "@codingame/monaco-vscode-language-pack-ru": "~18.1.0", "@codingame/monaco-vscode-language-pack-tr": "~18.1.0", "@codingame/monaco-vscode-language-pack-zh-hans": "~18.1.0", "@codingame/monaco-vscode-language-pack-zh-hant": "~18.1.0", "@codingame/monaco-vscode-monarch-service-override": "~18.1.0", "@codingame/monaco-vscode-textmate-service-override": "~18.1.0", "@codingame/monaco-vscode-theme-defaults-default-extension": "~18.1.0", "@codingame/monaco-vscode-theme-service-override": "~18.1.0", "@codingame/monaco-vscode-views-service-override": "~18.1.0", "@codingame/monaco-vscode-workbench-service-override": "~18.1.0", "monaco-languageclient": "~9.8.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~18.1.0", "vscode-languageclient": "~9.0.1", "vscode-languageserver-protocol": "~3.17.5", "vscode-ws-jsonrpc": "~3.4.0"}, "devDependencies": {"@codingame/monaco-vscode-standalone-css-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-html-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-json-language-features": "~18.1.0", "@codingame/monaco-vscode-standalone-languages": "~18.1.0", "@codingame/monaco-vscode-standalone-typescript-language-features": "~18.1.0", "monaco-languageclient-examples": "~2025.6.2"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}, "packages/wrapper-react": {"name": "@typefox/monaco-editor-react", "version": "6.9.0", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-editor-api": "~18.1.0", "monaco-editor-wrapper": "~6.9.0", "react": ">=18.0.0 || <20.0.0"}, "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}}}}