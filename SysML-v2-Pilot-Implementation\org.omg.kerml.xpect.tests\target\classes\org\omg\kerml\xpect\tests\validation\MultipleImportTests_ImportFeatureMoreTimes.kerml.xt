//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
		File {from ="/src/DependencyMultipleMembership.kerml"}
		File {from ="/src/DependencyMembership2.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
				File {from ="/src/DependencyMultipleMembership.kerml"}
				File {from ="/src/DependencyMembership2.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	public import OuterPackage3::D;
	// XPECT errors ---> "Couldn't resolve reference to Classifier 'B'." at "B"
	classifier EE specializes B{
		// XPECT errors ---> "Couldn't resolve reference to Type 'b'." at "b"
		feature try : b;
	}
}
