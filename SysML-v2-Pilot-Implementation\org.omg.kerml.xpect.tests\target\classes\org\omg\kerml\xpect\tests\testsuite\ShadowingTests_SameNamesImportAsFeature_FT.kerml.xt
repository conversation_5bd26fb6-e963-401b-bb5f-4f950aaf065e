//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencySamePackageName_A_Feature.kerml"}
		File {from ="/src/DependencySamePackageName_B_Feature.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencySamePackageName_A_Feature.kerml"}
				File {from ="/src/DependencySamePackageName_B_Feature.kerml"}
			}
		}
	}
END_SETUP 
*/
//Both DepedencySamePackageName_A and DependencySamePackageName_B have the same package names "SamePackage"
//Current implemention public import from the first src package(A).
//XPECT noErrors --> ""
package test{
	public import SamePackage::container;
	feature something1 : container::A{}
	//* XPECT errors ---
	"Couldn't resolve reference to Type 'container::B'." at "container::B"
	--- */
	feature something2 : container::B{}
}
