// LangGraph 工作流定义
import { StateGraph, END, START } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { ChatOpenAI } from "@langchain/openai";

// 工作流状态接口
export interface WorkflowState {
  messages: BaseMessage[];
  sysmlCode?: string;
  analysisResult?: string;
  generatedCode?: string;
  validationResult?: string;
  currentStep?: string;
  error?: string;
  metadata?: Record<string, any>;
}

// 工作流节点类型
export type WorkflowNode = (state: WorkflowState) => Promise<Partial<WorkflowState>>;

// 工作流定义接口
export interface WorkflowDefinition {
  id: string;
  name: string;
  description: string;
  nodes: Record<string, WorkflowNode>;
  edges: Record<string, string | string[]>;
  entryPoint: string;
  category: 'generation' | 'analysis' | 'validation' | 'transformation';
  dependencies?: string[]; // 依赖的其他工作流
}

// SysML 代码生成工作流
export const sysmlCodeGenerationWorkflow: WorkflowDefinition = {
  id: 'sysml-code-generation',
  name: 'SysML 代码生成',
  description: '基于自然语言描述生成 SysML v2 代码',
  category: 'generation',
  entryPoint: 'analyze_requirements',
  nodes: {
    analyze_requirements: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.1 
      });
      
      const lastMessage = state.messages[state.messages.length - 1];
      const userInput = lastMessage.content as string;
      
      const analysisPrompt = `
分析以下需求描述，提取关键的系统元素、属性和关系：

用户需求: ${userInput}

请识别：
1. 主要的系统组件 (parts)
2. 组件的属性 (attributes)
3. 组件之间的连接 (connections)
4. 约束条件 (constraints)
5. 需求 (requirements)

以结构化的方式返回分析结果。
      `;
      
      const response = await llm.invoke([new HumanMessage(analysisPrompt)]);
      
      return {
        analysisResult: response.content as string,
        currentStep: 'generate_structure',
        messages: [...state.messages, response]
      };
    },

    generate_structure: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.2 
      });
      
      const structurePrompt = `
基于以下分析结果，生成 SysML v2 代码结构：

分析结果: ${state.analysisResult}

请生成完整的 SysML v2 代码，包括：
1. package 定义
2. part def 定义
3. attribute 声明
4. connection 定义
5. constraint 定义

确保代码符合 SysML v2 语法规范。
      `;
      
      const response = await llm.invoke([new HumanMessage(structurePrompt)]);
      
      return {
        generatedCode: response.content as string,
        currentStep: 'validate_syntax',
        messages: [...state.messages, response]
      };
    },

    validate_syntax: async (state: WorkflowState) => {
      // 这里可以集成实际的 SysML 语法验证
      const code = state.generatedCode || '';
      
      // 简单的语法检查
      const hasPackage = code.includes('package');
      const hasPartDef = code.includes('part def');
      const hasBraces = code.includes('{') && code.includes('}');
      
      const isValid = hasPackage && hasPartDef && hasBraces;
      
      return {
        validationResult: isValid ? 'valid' : 'invalid',
        currentStep: isValid ? 'complete' : 'fix_syntax',
        sysmlCode: isValid ? code : undefined
      };
    },

    fix_syntax: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.1 
      });
      
      const fixPrompt = `
以下 SysML v2 代码存在语法错误，请修复：

${state.generatedCode}

请确保：
1. 正确的 package 语法
2. 正确的 part def 语法
3. 正确的大括号匹配
4. 正确的分号使用
5. 符合 SysML v2 规范

返回修复后的代码。
      `;
      
      const response = await llm.invoke([new HumanMessage(fixPrompt)]);
      
      return {
        generatedCode: response.content as string,
        sysmlCode: response.content as string,
        currentStep: 'complete',
        messages: [...state.messages, response]
      };
    }
  },
  edges: {
    analyze_requirements: 'generate_structure',
    generate_structure: 'validate_syntax',
    validate_syntax: ['complete', 'fix_syntax'],
    fix_syntax: 'complete',
    complete: END
  }
};

// SysML 代码分析工作流
export const sysmlCodeAnalysisWorkflow: WorkflowDefinition = {
  id: 'sysml-code-analysis',
  name: 'SysML 代码分析',
  description: '分析现有 SysML v2 代码的结构和质量',
  category: 'analysis',
  entryPoint: 'parse_code',
  nodes: {
    parse_code: async (state: WorkflowState) => {
      const code = state.sysmlCode || '';
      
      // 解析代码结构
      const packages = (code.match(/package\s+\w+/g) || []).length;
      const partDefs = (code.match(/part\s+def\s+\w+/g) || []).length;
      const attributes = (code.match(/attribute\s+\w+/g) || []).length;
      const connections = (code.match(/connection\s+\w+/g) || []).length;
      
      const analysis = {
        packages,
        partDefs,
        attributes,
        connections,
        complexity: packages + partDefs + attributes + connections
      };
      
      return {
        analysisResult: JSON.stringify(analysis, null, 2),
        currentStep: 'generate_report',
        metadata: analysis
      };
    },

    generate_report: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.3 
      });
      
      const reportPrompt = `
基于以下 SysML v2 代码分析结果，生成详细的分析报告：

代码:
${state.sysmlCode}

分析结果:
${state.analysisResult}

请生成包含以下内容的报告：
1. 代码结构概述
2. 复杂度分析
3. 设计模式识别
4. 改进建议
5. 潜在问题
      `;
      
      const response = await llm.invoke([new HumanMessage(reportPrompt)]);
      
      return {
        analysisResult: response.content as string,
        currentStep: 'complete',
        messages: [...state.messages, response]
      };
    }
  },
  edges: {
    parse_code: 'generate_report',
    generate_report: 'complete',
    complete: END
  }
};

// 需求验证工作流
export const requirementValidationWorkflow: WorkflowDefinition = {
  id: 'requirement-validation',
  name: '需求验证',
  description: '验证 SysML 模型是否满足指定需求',
  category: 'validation',
  entryPoint: 'extract_requirements',
  dependencies: ['sysml-code-analysis'],
  nodes: {
    extract_requirements: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.1 
      });
      
      const extractPrompt = `
从以下 SysML v2 代码中提取所有需求和约束：

${state.sysmlCode}

请识别：
1. requirement 定义
2. constraint 定义
3. 隐含的需求
4. 性能要求
5. 安全要求

以结构化格式返回。
      `;
      
      const response = await llm.invoke([new HumanMessage(extractPrompt)]);
      
      return {
        analysisResult: response.content as string,
        currentStep: 'validate_completeness',
        messages: [...state.messages, response]
      };
    },

    validate_completeness: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.2 
      });
      
      const validationPrompt = `
验证以下需求的完整性和一致性：

提取的需求:
${state.analysisResult}

SysML 代码:
${state.sysmlCode}

请检查：
1. 需求是否完整覆盖
2. 需求之间是否一致
3. 是否存在冲突
4. 是否缺少关键需求
5. 可追溯性

生成验证报告。
      `;
      
      const response = await llm.invoke([new HumanMessage(validationPrompt)]);
      
      return {
        validationResult: response.content as string,
        currentStep: 'complete',
        messages: [...state.messages, response]
      };
    }
  },
  edges: {
    extract_requirements: 'validate_completeness',
    validate_completeness: 'complete',
    complete: END
  }
};

// 代码重构工作流
export const codeRefactoringWorkflow: WorkflowDefinition = {
  id: 'code-refactoring',
  name: '代码重构',
  description: '重构和优化 SysML v2 代码',
  category: 'transformation',
  entryPoint: 'analyze_structure',
  dependencies: ['sysml-code-analysis'],
  nodes: {
    analyze_structure: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.1 
      });
      
      const analysisPrompt = `
分析以下 SysML v2 代码的结构，识别重构机会：

${state.sysmlCode}

请识别：
1. 重复的代码模式
2. 可以抽象的通用组件
3. 命名不一致的地方
4. 结构可以优化的地方
5. 可以模块化的部分

提供重构建议。
      `;
      
      const response = await llm.invoke([new HumanMessage(analysisPrompt)]);
      
      return {
        analysisResult: response.content as string,
        currentStep: 'apply_refactoring',
        messages: [...state.messages, response]
      };
    },

    apply_refactoring: async (state: WorkflowState) => {
      const llm = new ChatOpenAI({ 
        modelName: "gpt-4", 
        temperature: 0.2 
      });
      
      const refactorPrompt = `
基于以下分析结果，重构 SysML v2 代码：

原始代码:
${state.sysmlCode}

重构建议:
${state.analysisResult}

请生成重构后的代码，确保：
1. 保持功能不变
2. 提高代码可读性
3. 减少重复
4. 改善结构
5. 遵循最佳实践
      `;
      
      const response = await llm.invoke([new HumanMessage(refactorPrompt)]);
      
      return {
        generatedCode: response.content as string,
        currentStep: 'complete',
        messages: [...state.messages, response]
      };
    }
  },
  edges: {
    analyze_structure: 'apply_refactoring',
    apply_refactoring: 'complete',
    complete: END
  }
};

// 导出所有工作流
export const allWorkflows: WorkflowDefinition[] = [
  sysmlCodeGenerationWorkflow,
  sysmlCodeAnalysisWorkflow,
  requirementValidationWorkflow,
  codeRefactoringWorkflow
];

// 工作流注册表
export const workflowRegistry = new Map<string, WorkflowDefinition>();
allWorkflows.forEach(workflow => {
  workflowRegistry.set(workflow.id, workflow);
});
