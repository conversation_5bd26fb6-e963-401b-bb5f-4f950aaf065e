'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSysML } from '@/contexts/SysMLContext';
import WorkspacePage from '@/components/sysml/workspace/WorkspacePage';

export default function SysMLPage() {
  const router = useRouter();
  const { state } = useSysML();

  useEffect(() => {
    // 如果未认证，跳转到登录页
    if (!state.auth.isLoading && !state.auth.isAuthenticated) {
      router.push('/sysml/login');
    }
  }, [state.auth.isAuthenticated, state.auth.isLoading, router]);

  // 如果正在加载认证状态，显示加载界面
  if (state.auth.isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">正在加载...</p>
        </div>
      </div>
    );
  }

  // 如果已认证，显示工作区
  if (state.auth.isAuthenticated) {
    return <WorkspacePage />;
  }

  // 默认返回空，等待重定向
  return null;
}
