//* 
XPECT_SETUP org.omg.kerml.xpect.tests.scoping.KerMLScopingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

//XPECT noErrors ---> ""
package Test {

	package Test0 {
		public import Test2::VP_Id::*;
		//XPECT linkedName at VVP_Id::VVVP_Id--> Test.Test2.VP.VVP.VVVP
		classifier A specializes VVP_Id::VVVP_Id;
		//XPECT linkedName at VVP::VVVP_Id--> Test.Test2.VP.VVP.VVVP
		classifier B specializes VVP::VVVP_Id;
		//XPECT linkedName at VVP_Id::VVVP--> Test.Test2.VP.VVP.VVVP
		classifier C specializes VVP_Id::VVVP;
		//XPECT linkedName at VVP_Id::VVVP_Id::VVVVP_Id_alias--> Test.Test2.VP.VVP.VVVP.VVVVP
		classifier D specializes VVP_Id::VVVP_Id::VVVVP_Id_alias;
		
	}
	package Test1 {
		public import Test2::VP_Id::*;
		//XPECT linkedName at VVP_Id --> Test.Test2.VP.VVP
		classifier A specializes VVP_Id;
	}
	
	package Test2 {
		public classifier <'VP_Id'> VP {	
			public classifier <'VVP_Id'> VVP {
				public classifier <'VVVP_Id'> VVVP{
					public classifier <'VVVVP_Id'> VVVVP;
					alias VVVVP_Id_alias for VVVVP_Id;
				}
			}
		}
		
	}
	
}

	