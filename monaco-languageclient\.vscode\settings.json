{
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.guides.bracketPairs": true,
  "editor.formatOnSave": false,
  "workbench.editor.revealIfOpen": true,
  "[javascript]": {
    "editor.formatOnSave": true,
  },
  "[typescript]": {
    "editor.formatOnSave": true,
  },
  "eslint.codeAction.showDocumentation": {
    "enable": true
  },
  "eslint.format.enable": false,
  "eslint.validate": [
    "javascript",
    "typescript"
  ],
  "[json]": {
    "editor.defaultFormatter": "vscode.json-language-features"
  },
  "terminal.integrated.scrollback": 10000
}
