//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package test{
	classifier A {
		public import test::*;
		//XPECT linkedName at A --> test.A
		//*XPECT scope at A ---
		   test.A.A, test.A.A.a, test.A.A.a.a,
		   a, a.A, a.A.a, a.a,
 		   A, A.a,  A.a.a, test.A, test.A.a, test.A.a.a, 
 		   test.A.a.A, test.A.a.A.a,
		   --- */
		classifier a specializes A;
	}
}

 //missing A.A, A.A.a, A.A.a.a
 //A.a.A, A.a.A.a
