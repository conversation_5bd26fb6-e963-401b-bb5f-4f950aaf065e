/* --------------------------------------------------------------------------------------------
 * SysML v2 Langium Language Server Worker
 * Based on monaco-languageclient langium-server.ts
 * ------------------------------------------------------------------------------------------ */
/// <reference lib="WebWorker" />

import { EmptyFileSystem } from 'langium';
import { type DefaultSharedModuleContext, startLanguageServer } from 'langium/lsp';
import { BrowserMessageReader, BrowserMessageWriter, createConnection } from 'vscode-languageserver/browser';
import { createSysmlServices } from '../language/sysml-module';

declare const self: DedicatedWorkerGlobalScope;

console.log('SysML Langium Language Server Worker starting...');

try {
  // 设置消息读写器
  const messageReader = new BrowserMessageReader(self);
  const messageWriter = new BrowserMessageWriter(self);

  // 监听消息（用于调试）
  messageReader.listen((message) => {
    console.log('Received message from main thread:', message);
  });

  // 创建连接
  const connection = createConnection(messageReader, messageWriter);

  // 注入共享服务和语言特定服务
  const context = {
    connection,
    ...EmptyFileSystem
  } as unknown as DefaultSharedModuleContext;

  // 创建 SysML 服务
  const { shared } = createSysmlServices(context);

  console.log('Starting SysML language server...');

  // 启动语言服务器
  startLanguageServer(shared);

  console.log('SysML Langium Language Server started successfully');

} catch (error) {
  console.error('Failed to start SysML Langium Language Server:', error);
  
  // 向主线程发送错误信息
  self.postMessage({
    type: 'error',
    data: {
      error: error instanceof Error ? error.message : String(error)
    }
  });
}
