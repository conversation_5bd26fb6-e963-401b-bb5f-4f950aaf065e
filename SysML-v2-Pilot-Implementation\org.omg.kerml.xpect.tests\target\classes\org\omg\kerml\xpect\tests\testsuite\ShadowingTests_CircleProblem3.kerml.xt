//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
			}
		}
	}
END_SETUP
*/
// XPECT noErrors ---> ""
package Test1{
	classifier A {
		public import B::*;
		//* XPECT scope at A ---
		A, A.B, A.B.B, A.B.B.aa, A.B.B.aa.self, A.B.B.aa.that, A.B.B.aa.that.self,
		A.B.B.bb, A.B.B.bb.aa, A.B.B.bb.aa.self, A.B.B.bb.aa.that, A.B.B.bb.aa.that.self,
		A.B.B.bb.bb, A.B.B.bb.bb.self, A.B.B.bb.bb.that, A.B.B.bb.bb.that.self, A.B.B.bb.self,
		A.B.B.bb.self.that, A.B.B.bb.that, A.B.B.bb.that.self, A.B.B.self, A.B.B.self.that, A.B.a,
		A.B.a.self, A.B.a.that, A.B.a.that.self, A.B.aa, A.B.aa.B, A.B.aa.B.aa, A.B.aa.B.aa.self,
		A.B.aa.B.aa.that, A.B.aa.B.aa.that.self, A.B.aa.B.bb, A.B.aa.B.bb.aa, A.B.aa.B.bb.aa.self,
		A.B.aa.B.bb.aa.that, A.B.aa.B.bb.aa.that.self, A.B.aa.B.bb.bb, A.B.aa.B.bb.bb.self,
		A.B.aa.B.bb.bb.that, A.B.aa.B.bb.bb.that.self, A.B.aa.B.bb.self, A.B.aa.B.bb.self.that,
		A.B.aa.B.bb.that, A.B.aa.B.bb.that.self, A.B.aa.B.self, A.B.aa.B.self.that, A.B.aa.a,
		A.B.aa.a.self, A.B.aa.a.that, A.B.aa.a.that.self, A.B.aa.aa, A.B.aa.aa.self, A.B.aa.aa.that,
		A.B.aa.aa.that.self, A.B.aa.b, A.B.aa.b.aa, A.B.aa.b.aa.self, A.B.aa.b.aa.that,
		A.B.aa.b.aa.that.self, A.B.aa.b.bb, A.B.aa.b.bb.self, A.B.aa.b.bb.that, A.B.aa.b.bb.that.self,
		A.B.aa.b.self, A.B.aa.b.self.that, A.B.aa.b.that, A.B.aa.b.that.self, A.B.aa.bb,
		A.B.aa.bb.self, A.B.aa.bb.that, A.B.aa.bb.that.self, A.B.aa.self, A.B.aa.self.that,
		A.B.aa.that, A.B.aa.that.self, A.B.b, A.B.b.aa, A.B.b.aa.self, A.B.b.aa.that,
		A.B.b.aa.that.self, A.B.b.bb, A.B.b.bb.self, A.B.b.bb.that, A.B.b.bb.that.self, A.B.b.self,
		A.B.b.self.that, A.B.b.that, A.B.b.that.self, A.B.bb, A.B.bb.B, A.B.bb.B.aa, A.B.bb.B.aa.self,
		A.B.bb.B.aa.that, A.B.bb.B.aa.that.self, A.B.bb.B.bb, A.B.bb.B.bb.self, A.B.bb.B.bb.that,
		A.B.bb.B.bb.that.self, A.B.bb.B.self, A.B.bb.B.self.that, A.B.bb.a, A.B.bb.a.self, A.B.bb.a.that,
		A.B.bb.a.that.self, A.B.bb.aa, A.B.bb.aa.B, A.B.bb.aa.B.aa, A.B.bb.aa.B.aa.self,
		A.B.bb.aa.B.aa.that, A.B.bb.aa.B.aa.that.self, A.B.bb.aa.B.bb, A.B.bb.aa.B.bb.self,
		A.B.bb.aa.B.bb.that, A.B.bb.aa.B.bb.that.self, A.B.bb.aa.B.self, A.B.bb.aa.B.self.that, A.B.bb.aa.a,
		A.B.bb.aa.a.self, A.B.bb.aa.a.that, A.B.bb.aa.a.that.self, A.B.bb.aa.b, A.B.bb.aa.b.self,
		A.B.bb.aa.b.that, A.B.bb.aa.b.that.self, A.B.bb.aa.self, A.B.bb.aa.self.that, A.B.bb.aa.that,
		A.B.bb.aa.that.self, A.B.bb.b, A.B.bb.b.self, A.B.bb.b.that, A.B.bb.b.that.self, A.B.bb.bb,
		A.B.bb.bb.self, A.B.bb.bb.that, A.B.bb.bb.that.self, A.B.bb.self, A.B.bb.self.that,
		A.B.bb.that, A.B.bb.that.self, A.B.self, A.B.self.that, A.a, A.a.B, A.a.B.aa, A.a.B.aa.self,
		A.a.B.aa.that, A.a.B.aa.that.self, A.a.B.bb, A.a.B.bb.aa, A.a.B.bb.aa.self, A.a.B.bb.aa.that,
		A.a.B.bb.aa.that.self, A.a.B.bb.bb, A.a.B.bb.bb.self, A.a.B.bb.bb.that, A.a.B.bb.bb.that.self,
		A.a.B.bb.self, A.a.B.bb.self.that, A.a.B.bb.that, A.a.B.bb.that.self, A.a.B.self,
		A.a.B.self.that, A.a.a, A.a.a.self, A.a.a.that, A.a.a.that.self, A.a.aa, A.a.aa.self,
		A.a.aa.that, A.a.aa.that.self, A.a.b, A.a.b.aa, A.a.b.aa.self, A.a.b.aa.that,
		A.a.b.aa.that.self, A.a.b.bb, A.a.b.bb.self, A.a.b.bb.that, A.a.b.bb.that.self, A.a.b.self,
		A.a.b.self.that, A.a.b.that, A.a.b.that.self, A.a.bb, A.a.bb.self, A.a.bb.that,
		A.a.bb.that.self, A.a.self, A.a.self.that, A.a.that, A.a.that.self, A.aa, A.aa.B, A.aa.B.aa,
		A.aa.B.aa.self, A.aa.B.aa.that, A.aa.B.aa.that.self, A.aa.B.bb, A.aa.B.bb.self, A.aa.B.bb.that,
		A.aa.B.bb.that.self, A.aa.B.self, A.aa.B.self.that, A.aa.a, A.aa.a.self, A.aa.a.that,
		A.aa.a.that.self, A.aa.b, A.aa.b.self, A.aa.b.that, A.aa.b.that.self, A.aa.self, A.aa.self.that,
		A.aa.that, A.aa.that.self, A.b, A.b.B, A.b.B.aa, A.b.B.aa.self, A.b.B.aa.that,
		A.b.B.aa.that.self, A.b.B.bb, A.b.B.bb.self, A.b.B.bb.that, A.b.B.bb.that.self, A.b.B.self,
		A.b.B.self.that, A.b.a, A.b.a.self, A.b.a.that, A.b.a.that.self, A.b.aa, A.b.aa.B, A.b.aa.B.aa,
		A.b.aa.B.aa.self, A.b.aa.B.aa.that, A.b.aa.B.aa.that.self, A.b.aa.B.bb, A.b.aa.B.bb.self,
		A.b.aa.B.bb.that, A.b.aa.B.bb.that.self, A.b.aa.B.self, A.b.aa.B.self.that, A.b.aa.a,
		A.b.aa.a.self, A.b.aa.a.that, A.b.aa.a.that.self, A.b.aa.b, A.b.aa.b.self, A.b.aa.b.that,
		A.b.aa.b.that.self, A.b.aa.self, A.b.aa.self.that, A.b.aa.that, A.b.aa.that.self, A.b.b,
		A.b.b.self, A.b.b.that, A.b.b.that.self, A.b.bb, A.b.bb.self, A.b.bb.that,
		A.b.bb.that.self, A.b.self, A.b.self.that, A.b.that, A.b.that.self, A.bb, A.bb.self, A.bb.that,
		A.bb.that.self, A.self, A.self.that, B, B.B, B.B.aa, B.B.aa.self, B.B.aa.that,
		B.B.aa.that.self, B.B.bb, B.B.bb.aa, B.B.bb.aa.self, B.B.bb.aa.that, B.B.bb.aa.that.self,
		B.B.bb.bb, B.B.bb.bb.self, B.B.bb.bb.that, B.B.bb.bb.that.self, B.B.bb.self,
		B.B.bb.self.that, B.B.bb.that, B.B.bb.that.self, B.B.self, B.B.self.that, B.a, B.a.self,
		B.a.that, B.a.that.self, B.aa, B.aa.B, B.aa.B.aa, B.aa.B.aa.self, B.aa.B.aa.that,
		B.aa.B.aa.that.self, B.aa.B.bb, B.aa.B.bb.aa, B.aa.B.bb.aa.self, B.aa.B.bb.aa.that,
		B.aa.B.bb.aa.that.self, B.aa.B.bb.bb, B.aa.B.bb.bb.self, B.aa.B.bb.bb.that, B.aa.B.bb.bb.that.self,
		B.aa.B.bb.self, B.aa.B.bb.self.that, B.aa.B.bb.that, B.aa.B.bb.that.self, B.aa.B.self,
		B.aa.B.self.that, B.aa.a, B.aa.a.self, B.aa.a.that, B.aa.a.that.self, B.aa.aa, B.aa.aa.self,
		B.aa.aa.that, B.aa.aa.that.self, B.aa.b, B.aa.b.aa, B.aa.b.aa.self, B.aa.b.aa.that,
		B.aa.b.aa.that.self, B.aa.b.bb, B.aa.b.bb.self, B.aa.b.bb.that, B.aa.b.bb.that.self, B.aa.b.self,
		B.aa.b.self.that, B.aa.b.that, B.aa.b.that.self, B.aa.bb, B.aa.bb.self, B.aa.bb.that,
		B.aa.bb.that.self, B.aa.self, B.aa.self.that, B.aa.that, B.aa.that.self, B.b, B.b.aa, B.b.aa.self,
		B.b.aa.that, B.b.aa.that.self, B.b.bb, B.b.bb.self, B.b.bb.that, B.b.bb.that.self, B.b.self,
		B.b.self.that, B.b.that, B.b.that.self, B.bb, B.bb.B, B.bb.B.aa, B.bb.B.aa.self,
		B.bb.B.aa.that, B.bb.B.aa.that.self, B.bb.B.bb, B.bb.B.bb.self, B.bb.B.bb.that,
		B.bb.B.bb.that.self, B.bb.B.self, B.bb.B.self.that, B.bb.a, B.bb.a.self, B.bb.a.that,
		B.bb.a.that.self, B.bb.aa, B.bb.aa.B, B.bb.aa.B.aa, B.bb.aa.B.aa.self, B.bb.aa.B.aa.that,
		B.bb.aa.B.aa.that.self, B.bb.aa.B.bb, B.bb.aa.B.bb.self, B.bb.aa.B.bb.that, B.bb.aa.B.bb.that.self,
		B.bb.aa.B.self, B.bb.aa.B.self.that, B.bb.aa.a, B.bb.aa.a.self, B.bb.aa.a.that,
		B.bb.aa.a.that.self, B.bb.aa.b, B.bb.aa.b.self, B.bb.aa.b.that, B.bb.aa.b.that.self, B.bb.aa.self,
		B.bb.aa.self.that, B.bb.aa.that, B.bb.aa.that.self, B.bb.b, B.bb.b.self, B.bb.b.that,
		B.bb.b.that.self, B.bb.bb, B.bb.bb.self, B.bb.bb.that, B.bb.bb.that.self, B.bb.self,
		B.bb.self.that, B.bb.that, B.bb.that.self, B.self, B.self.that, OuterPackage.A,
		OuterPackage.A.a1, OuterPackage.B, OuterPackage.B.b, OuterPackage.B.b.a1, Test1.A, Test1.A.B,
		Test1.A.B.B, Test1.A.B.B.aa, Test1.A.B.B.aa.self, Test1.A.B.B.aa.that,
		Test1.A.B.B.aa.that.self, Test1.A.B.B.bb, Test1.A.B.B.bb.aa, Test1.A.B.B.bb.aa.self,
		Test1.A.B.B.bb.aa.that, Test1.A.B.B.bb.aa.that.self, Test1.A.B.B.bb.bb, Test1.A.B.B.bb.bb.self,
		Test1.A.B.B.bb.bb.that, Test1.A.B.B.bb.bb.that.self, Test1.A.B.B.bb.self, Test1.A.B.B.bb.self.that,
		Test1.A.B.B.bb.that, Test1.A.B.B.bb.that.self, Test1.A.B.B.self, Test1.A.B.B.self.that, Test1.A.B.a,
		Test1.A.B.a.self, Test1.A.B.a.that, Test1.A.B.a.that.self, Test1.A.B.aa, Test1.A.B.aa.B,
		Test1.A.B.aa.B.aa, Test1.A.B.aa.B.aa.self, Test1.A.B.aa.B.aa.that, Test1.A.B.aa.B.aa.that.self,
		Test1.A.B.aa.B.bb, Test1.A.B.aa.B.bb.aa, Test1.A.B.aa.B.bb.aa.self, Test1.A.B.aa.B.bb.aa.that,
		Test1.A.B.aa.B.bb.aa.that.self, Test1.A.B.aa.B.bb.bb, Test1.A.B.aa.B.bb.bb.self, Test1.A.B.aa.B.bb.bb.that,
		Test1.A.B.aa.B.bb.bb.that.self, Test1.A.B.aa.B.bb.self, Test1.A.B.aa.B.bb.self.that, Test1.A.B.aa.B.bb.that,
		Test1.A.B.aa.B.bb.that.self, Test1.A.B.aa.B.self, Test1.A.B.aa.B.self.that, Test1.A.B.aa.a,
		Test1.A.B.aa.a.self, Test1.A.B.aa.a.that, Test1.A.B.aa.a.that.self, Test1.A.B.aa.aa,
		Test1.A.B.aa.aa.self, Test1.A.B.aa.aa.that, Test1.A.B.aa.aa.that.self, Test1.A.B.aa.b,
		Test1.A.B.aa.b.aa, Test1.A.B.aa.b.aa.self, Test1.A.B.aa.b.aa.that, Test1.A.B.aa.b.aa.that.self,
		Test1.A.B.aa.b.bb, Test1.A.B.aa.b.bb.self, Test1.A.B.aa.b.bb.that, Test1.A.B.aa.b.bb.that.self,
		Test1.A.B.aa.b.self, Test1.A.B.aa.b.self.that, Test1.A.B.aa.b.that, Test1.A.B.aa.b.that.self,
		Test1.A.B.aa.bb, Test1.A.B.aa.bb.self, Test1.A.B.aa.bb.that, Test1.A.B.aa.bb.that.self,
		Test1.A.B.aa.self, Test1.A.B.aa.self.that, Test1.A.B.aa.that, Test1.A.B.aa.that.self, Test1.A.B.b,
		Test1.A.B.b.aa, Test1.A.B.b.aa.self, Test1.A.B.b.aa.that, Test1.A.B.b.aa.that.self,
		Test1.A.B.b.bb, Test1.A.B.b.bb.self, Test1.A.B.b.bb.that, Test1.A.B.b.bb.that.self,
		Test1.A.B.b.self, Test1.A.B.b.self.that, Test1.A.B.b.that, Test1.A.B.b.that.self, Test1.A.B.bb,
		Test1.A.B.bb.B, Test1.A.B.bb.B.aa, Test1.A.B.bb.B.aa.self, Test1.A.B.bb.B.aa.that,
		Test1.A.B.bb.B.aa.that.self, Test1.A.B.bb.B.bb, Test1.A.B.bb.B.bb.self, Test1.A.B.bb.B.bb.that,
		Test1.A.B.bb.B.bb.that.self, Test1.A.B.bb.B.self, Test1.A.B.bb.B.self.that, Test1.A.B.bb.a,
		Test1.A.B.bb.a.self, Test1.A.B.bb.a.that, Test1.A.B.bb.a.that.self, Test1.A.B.bb.aa,
		Test1.A.B.bb.aa.B, Test1.A.B.bb.aa.B.aa, Test1.A.B.bb.aa.B.aa.self, Test1.A.B.bb.aa.B.aa.that,
		Test1.A.B.bb.aa.B.aa.that.self, Test1.A.B.bb.aa.B.bb, Test1.A.B.bb.aa.B.bb.self, Test1.A.B.bb.aa.B.bb.that,
		Test1.A.B.bb.aa.B.bb.that.self, Test1.A.B.bb.aa.B.self, Test1.A.B.bb.aa.B.self.that, Test1.A.B.bb.aa.a,
		Test1.A.B.bb.aa.a.self, Test1.A.B.bb.aa.a.that, Test1.A.B.bb.aa.a.that.self, Test1.A.B.bb.aa.b,
		Test1.A.B.bb.aa.b.self, Test1.A.B.bb.aa.b.that, Test1.A.B.bb.aa.b.that.self, Test1.A.B.bb.aa.self,
		Test1.A.B.bb.aa.self.that, Test1.A.B.bb.aa.that, Test1.A.B.bb.aa.that.self, Test1.A.B.bb.b,
		Test1.A.B.bb.b.self, Test1.A.B.bb.b.that, Test1.A.B.bb.b.that.self, Test1.A.B.bb.bb,
		Test1.A.B.bb.bb.self, Test1.A.B.bb.bb.that, Test1.A.B.bb.bb.that.self, Test1.A.B.bb.self,
		Test1.A.B.bb.self.that, Test1.A.B.bb.that, Test1.A.B.bb.that.self, Test1.A.B.self, Test1.A.B.self.that,
		Test1.A.a, Test1.A.a.B, Test1.A.a.B.aa, Test1.A.a.B.aa.self, Test1.A.a.B.aa.that,
		Test1.A.a.B.aa.that.self, Test1.A.a.B.bb, Test1.A.a.B.bb.aa, Test1.A.a.B.bb.aa.self,
		Test1.A.a.B.bb.aa.that, Test1.A.a.B.bb.aa.that.self, Test1.A.a.B.bb.bb, Test1.A.a.B.bb.bb.self,
		Test1.A.a.B.bb.bb.that, Test1.A.a.B.bb.bb.that.self, Test1.A.a.B.bb.self, Test1.A.a.B.bb.self.that,
		Test1.A.a.B.bb.that, Test1.A.a.B.bb.that.self, Test1.A.a.B.self, Test1.A.a.B.self.that, Test1.A.a.a,
		Test1.A.a.a.self, Test1.A.a.a.that, Test1.A.a.a.that.self, Test1.A.a.aa, Test1.A.a.aa.self,
		Test1.A.a.aa.that, Test1.A.a.aa.that.self, Test1.A.a.b, Test1.A.a.b.aa, Test1.A.a.b.aa.self,
		Test1.A.a.b.aa.that, Test1.A.a.b.aa.that.self, Test1.A.a.b.bb, Test1.A.a.b.bb.self,
		Test1.A.a.b.bb.that, Test1.A.a.b.bb.that.self, Test1.A.a.b.self, Test1.A.a.b.self.that,
		Test1.A.a.b.that, Test1.A.a.b.that.self, Test1.A.a.bb, Test1.A.a.bb.self, Test1.A.a.bb.that,
		Test1.A.a.bb.that.self, Test1.A.a.self, Test1.A.a.self.that, Test1.A.a.that, Test1.A.a.that.self,
		Test1.A.aa, Test1.A.aa.B, Test1.A.aa.B.aa, Test1.A.aa.B.aa.self, Test1.A.aa.B.aa.that,
		Test1.A.aa.B.aa.that.self, Test1.A.aa.B.bb, Test1.A.aa.B.bb.self, Test1.A.aa.B.bb.that,
		Test1.A.aa.B.bb.that.self, Test1.A.aa.B.self, Test1.A.aa.B.self.that, Test1.A.aa.a, Test1.A.aa.a.self,
		Test1.A.aa.a.that, Test1.A.aa.a.that.self, Test1.A.aa.b, Test1.A.aa.b.self, Test1.A.aa.b.that,
		Test1.A.aa.b.that.self, Test1.A.aa.self, Test1.A.aa.self.that, Test1.A.aa.that, Test1.A.aa.that.self,
		Test1.A.b, Test1.A.b.B, Test1.A.b.B.aa, Test1.A.b.B.aa.self, Test1.A.b.B.aa.that,
		Test1.A.b.B.aa.that.self, Test1.A.b.B.bb, Test1.A.b.B.bb.self, Test1.A.b.B.bb.that,
		Test1.A.b.B.bb.that.self, Test1.A.b.B.self, Test1.A.b.B.self.that, Test1.A.b.a, Test1.A.b.a.self,
		Test1.A.b.a.that, Test1.A.b.a.that.self, Test1.A.b.aa, Test1.A.b.aa.B, Test1.A.b.aa.B.aa,
		Test1.A.b.aa.B.aa.self, Test1.A.b.aa.B.aa.that, Test1.A.b.aa.B.aa.that.self, Test1.A.b.aa.B.bb,
		Test1.A.b.aa.B.bb.self, Test1.A.b.aa.B.bb.that, Test1.A.b.aa.B.bb.that.self, Test1.A.b.aa.B.self,
		Test1.A.b.aa.B.self.that, Test1.A.b.aa.a, Test1.A.b.aa.a.self, Test1.A.b.aa.a.that,
		Test1.A.b.aa.a.that.self, Test1.A.b.aa.b, Test1.A.b.aa.b.self, Test1.A.b.aa.b.that,
		Test1.A.b.aa.b.that.self, Test1.A.b.aa.self, Test1.A.b.aa.self.that, Test1.A.b.aa.that,
		Test1.A.b.aa.that.self, Test1.A.b.b, Test1.A.b.b.self, Test1.A.b.b.that, Test1.A.b.b.that.self,
		Test1.A.b.bb, Test1.A.b.bb.self, Test1.A.b.bb.that, Test1.A.b.bb.that.self, Test1.A.b.self,
		Test1.A.b.self.that, Test1.A.b.that, Test1.A.b.that.self, Test1.A.bb, Test1.A.bb.self,
		Test1.A.bb.that, Test1.A.bb.that.self, Test1.A.self, Test1.A.self.that, a, a.B, a.B.aa,
		a.B.aa.self, a.B.aa.that, a.B.aa.that.self, a.B.bb, a.B.bb.aa, a.B.bb.aa.self,
		a.B.bb.aa.that, a.B.bb.aa.that.self, a.B.bb.bb, a.B.bb.bb.self, a.B.bb.bb.that,
		a.B.bb.bb.that.self, a.B.bb.self, a.B.bb.self.that, a.B.bb.that, a.B.bb.that.self, a.B.self,
		a.B.self.that, a.a, a.a.self, a.a.that, a.a.that.self, a.aa, a.aa.self, a.aa.that,
		a.aa.that.self, a.b, a.b.aa, a.b.aa.self, a.b.aa.that, a.b.aa.that.self, a.b.bb, a.b.bb.self,
		a.b.bb.that, a.b.bb.that.self, a.b.self, a.b.self.that, a.b.that, a.b.that.self, a.bb,
		a.bb.self, a.bb.that, a.bb.that.self, a.self, a.self.that, a.that, a.that.self, aa, aa.B,
		aa.B.aa, aa.B.aa.self, aa.B.aa.that, aa.B.aa.that.self, aa.B.bb, aa.B.bb.self,
		aa.B.bb.that, aa.B.bb.that.self, aa.B.self, aa.B.self.that, aa.a, aa.a.self, aa.a.that,
		aa.a.that.self, aa.b, aa.b.self, aa.b.that, aa.b.that.self, aa.self, aa.self.that, aa.that,
		aa.that.self, b, b.B, b.B.aa, b.B.aa.self, b.B.aa.that, b.B.aa.that.self, b.B.bb,
		b.B.bb.self, b.B.bb.that, b.B.bb.that.self, b.B.self, b.B.self.that, b.a, b.a.self,
		b.a.that, b.a.that.self, b.aa, b.aa.B, b.aa.B.aa, b.aa.B.aa.self, b.aa.B.aa.that,
		b.aa.B.aa.that.self, b.aa.B.bb, b.aa.B.bb.self, b.aa.B.bb.that, b.aa.B.bb.that.self, b.aa.B.self,
		b.aa.B.self.that, b.aa.a, b.aa.a.self, b.aa.a.that, b.aa.a.that.self, b.aa.b, b.aa.b.self,
		b.aa.b.that, b.aa.b.that.self, b.aa.self, b.aa.self.that, b.aa.that, b.aa.that.self, b.b,
		b.b.self, b.b.that, b.b.that.self, b.bb, b.bb.self, b.bb.that, b.bb.that.self, b.self,
		b.self.that, b.that, b.that.self, bb, bb.self, bb.that, bb.that.self, self, self.that
		--- */
		feature a: A; //added 
		feature b: B; //added
		classifier B {
			public import A::*;
			feature aa: A; //added 
			//* XPECT scope at B ---
				A, A.B, A.B.B, A.B.B.aa, A.B.B.aa.self, A.B.B.aa.that, A.B.B.aa.that.self,
				A.B.B.bb, A.B.B.bb.aa, A.B.B.bb.aa.self, A.B.B.bb.aa.that, A.B.B.bb.aa.that.self,
				A.B.B.bb.bb, A.B.B.bb.bb.self, A.B.B.bb.bb.that, A.B.B.bb.bb.that.self, A.B.B.bb.self,
				A.B.B.bb.self.that, A.B.B.bb.that, A.B.B.bb.that.self, A.B.B.self, A.B.B.self.that, A.B.a,
				A.B.a.self, A.B.a.that, A.B.a.that.self, A.B.aa, A.B.aa.B, A.B.aa.B.aa, A.B.aa.B.aa.self,
				A.B.aa.B.aa.that, A.B.aa.B.aa.that.self, A.B.aa.B.bb, A.B.aa.B.bb.aa, A.B.aa.B.bb.aa.self,
				A.B.aa.B.bb.aa.that, A.B.aa.B.bb.aa.that.self, A.B.aa.B.bb.bb, A.B.aa.B.bb.bb.self,
				A.B.aa.B.bb.bb.that, A.B.aa.B.bb.bb.that.self, A.B.aa.B.bb.self, A.B.aa.B.bb.self.that,
				A.B.aa.B.bb.that, A.B.aa.B.bb.that.self, A.B.aa.B.self, A.B.aa.B.self.that, A.B.aa.a,
				A.B.aa.a.self, A.B.aa.a.that, A.B.aa.a.that.self, A.B.aa.aa, A.B.aa.aa.self, A.B.aa.aa.that,
				A.B.aa.aa.that.self, A.B.aa.b, A.B.aa.b.aa, A.B.aa.b.aa.self, A.B.aa.b.aa.that,
				A.B.aa.b.aa.that.self, A.B.aa.b.bb, A.B.aa.b.bb.self, A.B.aa.b.bb.that, A.B.aa.b.bb.that.self,
				A.B.aa.b.self, A.B.aa.b.self.that, A.B.aa.b.that, A.B.aa.b.that.self, A.B.aa.bb,
				A.B.aa.bb.self, A.B.aa.bb.that, A.B.aa.bb.that.self, A.B.aa.self, A.B.aa.self.that,
				A.B.aa.that, A.B.aa.that.self, A.B.b, A.B.b.aa, A.B.b.aa.self, A.B.b.aa.that,
				A.B.b.aa.that.self, A.B.b.bb, A.B.b.bb.self, A.B.b.bb.that, A.B.b.bb.that.self, A.B.b.self,
				A.B.b.self.that, A.B.b.that, A.B.b.that.self, A.B.bb, A.B.bb.B, A.B.bb.B.aa, A.B.bb.B.aa.self,
				A.B.bb.B.aa.that, A.B.bb.B.aa.that.self, A.B.bb.B.bb, A.B.bb.B.bb.self, A.B.bb.B.bb.that,
				A.B.bb.B.bb.that.self, A.B.bb.B.self, A.B.bb.B.self.that, A.B.bb.a, A.B.bb.a.self, A.B.bb.a.that,
				A.B.bb.a.that.self, A.B.bb.aa, A.B.bb.aa.B, A.B.bb.aa.B.aa, A.B.bb.aa.B.aa.self,
				A.B.bb.aa.B.aa.that, A.B.bb.aa.B.aa.that.self, A.B.bb.aa.B.bb, A.B.bb.aa.B.bb.self,
				A.B.bb.aa.B.bb.that, A.B.bb.aa.B.bb.that.self, A.B.bb.aa.B.self, A.B.bb.aa.B.self.that, A.B.bb.aa.a,
				A.B.bb.aa.a.self, A.B.bb.aa.a.that, A.B.bb.aa.a.that.self, A.B.bb.aa.b, A.B.bb.aa.b.self,
				A.B.bb.aa.b.that, A.B.bb.aa.b.that.self, A.B.bb.aa.self, A.B.bb.aa.self.that, A.B.bb.aa.that,
				A.B.bb.aa.that.self, A.B.bb.b, A.B.bb.b.self, A.B.bb.b.that, A.B.bb.b.that.self, A.B.bb.bb,
				A.B.bb.bb.self, A.B.bb.bb.that, A.B.bb.bb.that.self, A.B.bb.self, A.B.bb.self.that,
				A.B.bb.that, A.B.bb.that.self, A.B.self, A.B.self.that, A.a, A.a.B, A.a.B.aa, A.a.B.aa.self,
				A.a.B.aa.that, A.a.B.aa.that.self, A.a.B.bb, A.a.B.bb.aa, A.a.B.bb.aa.self, A.a.B.bb.aa.that,
				A.a.B.bb.aa.that.self, A.a.B.bb.bb, A.a.B.bb.bb.self, A.a.B.bb.bb.that, A.a.B.bb.bb.that.self,
				A.a.B.bb.self, A.a.B.bb.self.that, A.a.B.bb.that, A.a.B.bb.that.self, A.a.B.self,
				A.a.B.self.that, A.a.a, A.a.a.self, A.a.a.that, A.a.a.that.self, A.a.aa, A.a.aa.self,
				A.a.aa.that, A.a.aa.that.self, A.a.b, A.a.b.aa, A.a.b.aa.self, A.a.b.aa.that,
				A.a.b.aa.that.self, A.a.b.bb, A.a.b.bb.self, A.a.b.bb.that, A.a.b.bb.that.self, A.a.b.self,
				A.a.b.self.that, A.a.b.that, A.a.b.that.self, A.a.bb, A.a.bb.self, A.a.bb.that,
				A.a.bb.that.self, A.a.self, A.a.self.that, A.a.that, A.a.that.self, A.aa, A.aa.B, A.aa.B.aa,
				A.aa.B.aa.self, A.aa.B.aa.that, A.aa.B.aa.that.self, A.aa.B.bb, A.aa.B.bb.self, A.aa.B.bb.that,
				A.aa.B.bb.that.self, A.aa.B.self, A.aa.B.self.that, A.aa.a, A.aa.a.self, A.aa.a.that,
				A.aa.a.that.self, A.aa.b, A.aa.b.self, A.aa.b.that, A.aa.b.that.self, A.aa.self, A.aa.self.that,
				A.aa.that, A.aa.that.self, A.b, A.b.B, A.b.B.aa, A.b.B.aa.self, A.b.B.aa.that,
				A.b.B.aa.that.self, A.b.B.bb, A.b.B.bb.self, A.b.B.bb.that, A.b.B.bb.that.self, A.b.B.self,
				A.b.B.self.that, A.b.a, A.b.a.self, A.b.a.that, A.b.a.that.self, A.b.aa, A.b.aa.B, A.b.aa.B.aa,
				A.b.aa.B.aa.self, A.b.aa.B.aa.that, A.b.aa.B.aa.that.self, A.b.aa.B.bb, A.b.aa.B.bb.self,
				A.b.aa.B.bb.that, A.b.aa.B.bb.that.self, A.b.aa.B.self, A.b.aa.B.self.that, A.b.aa.a,
				A.b.aa.a.self, A.b.aa.a.that, A.b.aa.a.that.self, A.b.aa.b, A.b.aa.b.self, A.b.aa.b.that,
				A.b.aa.b.that.self, A.b.aa.self, A.b.aa.self.that, A.b.aa.that, A.b.aa.that.self, A.b.b,
				A.b.b.self, A.b.b.that, A.b.b.that.self, A.b.bb, A.b.bb.self, A.b.bb.that,
				A.b.bb.that.self, A.b.self, A.b.self.that, A.b.that, A.b.that.self, A.bb, A.bb.self, A.bb.that,
				A.bb.that.self, A.self, A.self.that, B, B.aa, B.aa.self, B.aa.that, B.aa.that.self, B.bb,
				B.bb.aa, B.bb.aa.self, B.bb.aa.that, B.bb.aa.that.self, B.bb.bb, B.bb.bb.self,
				B.bb.bb.that, B.bb.bb.that.self, B.bb.self, B.bb.self.that, B.bb.that, B.bb.that.self,
				B.self, B.self.that, OuterPackage.A, OuterPackage.A.a1, OuterPackage.B,
				OuterPackage.B.b, OuterPackage.B.b.a1, Test1.A, Test1.A.B, Test1.A.B.B, Test1.A.B.B.aa,
				Test1.A.B.B.aa.self, Test1.A.B.B.aa.that, Test1.A.B.B.aa.that.self, Test1.A.B.B.bb,
				Test1.A.B.B.bb.aa, Test1.A.B.B.bb.aa.self, Test1.A.B.B.bb.aa.that, Test1.A.B.B.bb.aa.that.self,
				Test1.A.B.B.bb.bb, Test1.A.B.B.bb.bb.self, Test1.A.B.B.bb.bb.that, Test1.A.B.B.bb.bb.that.self,
				Test1.A.B.B.bb.self, Test1.A.B.B.bb.self.that, Test1.A.B.B.bb.that, Test1.A.B.B.bb.that.self,
				Test1.A.B.B.self, Test1.A.B.B.self.that, Test1.A.B.a, Test1.A.B.a.self, Test1.A.B.a.that,
				Test1.A.B.a.that.self, Test1.A.B.aa, Test1.A.B.aa.B, Test1.A.B.aa.B.aa, Test1.A.B.aa.B.aa.self,
				Test1.A.B.aa.B.aa.that, Test1.A.B.aa.B.aa.that.self, Test1.A.B.aa.B.bb, Test1.A.B.aa.B.bb.aa,
				Test1.A.B.aa.B.bb.aa.self, Test1.A.B.aa.B.bb.aa.that, Test1.A.B.aa.B.bb.aa.that.self,
				Test1.A.B.aa.B.bb.bb, Test1.A.B.aa.B.bb.bb.self, Test1.A.B.aa.B.bb.bb.that,
				Test1.A.B.aa.B.bb.bb.that.self, Test1.A.B.aa.B.bb.self, Test1.A.B.aa.B.bb.self.that, Test1.A.B.aa.B.bb.that,
				Test1.A.B.aa.B.bb.that.self, Test1.A.B.aa.B.self, Test1.A.B.aa.B.self.that, Test1.A.B.aa.a,
				Test1.A.B.aa.a.self, Test1.A.B.aa.a.that, Test1.A.B.aa.a.that.self, Test1.A.B.aa.aa,
				Test1.A.B.aa.aa.self, Test1.A.B.aa.aa.that, Test1.A.B.aa.aa.that.self, Test1.A.B.aa.b,
				Test1.A.B.aa.b.aa, Test1.A.B.aa.b.aa.self, Test1.A.B.aa.b.aa.that, Test1.A.B.aa.b.aa.that.self,
				Test1.A.B.aa.b.bb, Test1.A.B.aa.b.bb.self, Test1.A.B.aa.b.bb.that, Test1.A.B.aa.b.bb.that.self,
				Test1.A.B.aa.b.self, Test1.A.B.aa.b.self.that, Test1.A.B.aa.b.that, Test1.A.B.aa.b.that.self,
				Test1.A.B.aa.bb, Test1.A.B.aa.bb.self, Test1.A.B.aa.bb.that, Test1.A.B.aa.bb.that.self,
				Test1.A.B.aa.self, Test1.A.B.aa.self.that, Test1.A.B.aa.that, Test1.A.B.aa.that.self, Test1.A.B.b,
				Test1.A.B.b.aa, Test1.A.B.b.aa.self, Test1.A.B.b.aa.that, Test1.A.B.b.aa.that.self,
				Test1.A.B.b.bb, Test1.A.B.b.bb.self, Test1.A.B.b.bb.that, Test1.A.B.b.bb.that.self,
				Test1.A.B.b.self, Test1.A.B.b.self.that, Test1.A.B.b.that, Test1.A.B.b.that.self, Test1.A.B.bb,
				Test1.A.B.bb.B, Test1.A.B.bb.B.aa, Test1.A.B.bb.B.aa.self, Test1.A.B.bb.B.aa.that,
				Test1.A.B.bb.B.aa.that.self, Test1.A.B.bb.B.bb, Test1.A.B.bb.B.bb.self, Test1.A.B.bb.B.bb.that,
				Test1.A.B.bb.B.bb.that.self, Test1.A.B.bb.B.self, Test1.A.B.bb.B.self.that, Test1.A.B.bb.a,
				Test1.A.B.bb.a.self, Test1.A.B.bb.a.that, Test1.A.B.bb.a.that.self, Test1.A.B.bb.aa,
				Test1.A.B.bb.aa.B, Test1.A.B.bb.aa.B.aa, Test1.A.B.bb.aa.B.aa.self, Test1.A.B.bb.aa.B.aa.that,
				Test1.A.B.bb.aa.B.aa.that.self, Test1.A.B.bb.aa.B.bb, Test1.A.B.bb.aa.B.bb.self, Test1.A.B.bb.aa.B.bb.that,
				Test1.A.B.bb.aa.B.bb.that.self, Test1.A.B.bb.aa.B.self, Test1.A.B.bb.aa.B.self.that, Test1.A.B.bb.aa.a,
				Test1.A.B.bb.aa.a.self, Test1.A.B.bb.aa.a.that, Test1.A.B.bb.aa.a.that.self, Test1.A.B.bb.aa.b,
				Test1.A.B.bb.aa.b.self, Test1.A.B.bb.aa.b.that, Test1.A.B.bb.aa.b.that.self, Test1.A.B.bb.aa.self,
				Test1.A.B.bb.aa.self.that, Test1.A.B.bb.aa.that, Test1.A.B.bb.aa.that.self, Test1.A.B.bb.b,
				Test1.A.B.bb.b.self, Test1.A.B.bb.b.that, Test1.A.B.bb.b.that.self, Test1.A.B.bb.bb,
				Test1.A.B.bb.bb.self, Test1.A.B.bb.bb.that, Test1.A.B.bb.bb.that.self, Test1.A.B.bb.self,
				Test1.A.B.bb.self.that, Test1.A.B.bb.that, Test1.A.B.bb.that.self, Test1.A.B.self, Test1.A.B.self.that,
				Test1.A.a, Test1.A.a.B, Test1.A.a.B.aa, Test1.A.a.B.aa.self, Test1.A.a.B.aa.that,
				Test1.A.a.B.aa.that.self, Test1.A.a.B.bb, Test1.A.a.B.bb.aa, Test1.A.a.B.bb.aa.self,
				Test1.A.a.B.bb.aa.that, Test1.A.a.B.bb.aa.that.self, Test1.A.a.B.bb.bb, Test1.A.a.B.bb.bb.self,
				Test1.A.a.B.bb.bb.that, Test1.A.a.B.bb.bb.that.self, Test1.A.a.B.bb.self, Test1.A.a.B.bb.self.that,
				Test1.A.a.B.bb.that, Test1.A.a.B.bb.that.self, Test1.A.a.B.self, Test1.A.a.B.self.that, Test1.A.a.a,
				Test1.A.a.a.self, Test1.A.a.a.that, Test1.A.a.a.that.self, Test1.A.a.aa, Test1.A.a.aa.self,
				Test1.A.a.aa.that, Test1.A.a.aa.that.self, Test1.A.a.b, Test1.A.a.b.aa, Test1.A.a.b.aa.self,
				Test1.A.a.b.aa.that, Test1.A.a.b.aa.that.self, Test1.A.a.b.bb, Test1.A.a.b.bb.self,
				Test1.A.a.b.bb.that, Test1.A.a.b.bb.that.self, Test1.A.a.b.self, Test1.A.a.b.self.that,
				Test1.A.a.b.that, Test1.A.a.b.that.self, Test1.A.a.bb, Test1.A.a.bb.self, Test1.A.a.bb.that,
				Test1.A.a.bb.that.self, Test1.A.a.self, Test1.A.a.self.that, Test1.A.a.that, Test1.A.a.that.self,
				Test1.A.aa, Test1.A.aa.B, Test1.A.aa.B.aa, Test1.A.aa.B.aa.self, Test1.A.aa.B.aa.that,
				Test1.A.aa.B.aa.that.self, Test1.A.aa.B.bb, Test1.A.aa.B.bb.self, Test1.A.aa.B.bb.that,
				Test1.A.aa.B.bb.that.self, Test1.A.aa.B.self, Test1.A.aa.B.self.that, Test1.A.aa.a, Test1.A.aa.a.self,
				Test1.A.aa.a.that, Test1.A.aa.a.that.self, Test1.A.aa.b, Test1.A.aa.b.self, Test1.A.aa.b.that,
				Test1.A.aa.b.that.self, Test1.A.aa.self, Test1.A.aa.self.that, Test1.A.aa.that, Test1.A.aa.that.self,
				Test1.A.b, Test1.A.b.B, Test1.A.b.B.aa, Test1.A.b.B.aa.self, Test1.A.b.B.aa.that,
				Test1.A.b.B.aa.that.self, Test1.A.b.B.bb, Test1.A.b.B.bb.self, Test1.A.b.B.bb.that,
				Test1.A.b.B.bb.that.self, Test1.A.b.B.self, Test1.A.b.B.self.that, Test1.A.b.a, Test1.A.b.a.self,
				Test1.A.b.a.that, Test1.A.b.a.that.self, Test1.A.b.aa, Test1.A.b.aa.B, Test1.A.b.aa.B.aa,
				Test1.A.b.aa.B.aa.self, Test1.A.b.aa.B.aa.that, Test1.A.b.aa.B.aa.that.self, Test1.A.b.aa.B.bb,
				Test1.A.b.aa.B.bb.self, Test1.A.b.aa.B.bb.that, Test1.A.b.aa.B.bb.that.self, Test1.A.b.aa.B.self,
				Test1.A.b.aa.B.self.that, Test1.A.b.aa.a, Test1.A.b.aa.a.self, Test1.A.b.aa.a.that,
				Test1.A.b.aa.a.that.self, Test1.A.b.aa.b, Test1.A.b.aa.b.self, Test1.A.b.aa.b.that,
				Test1.A.b.aa.b.that.self, Test1.A.b.aa.self, Test1.A.b.aa.self.that, Test1.A.b.aa.that,
				Test1.A.b.aa.that.self, Test1.A.b.b, Test1.A.b.b.self, Test1.A.b.b.that, Test1.A.b.b.that.self,
				Test1.A.b.bb, Test1.A.b.bb.self, Test1.A.b.bb.that, Test1.A.b.bb.that.self, Test1.A.b.self,
				Test1.A.b.self.that, Test1.A.b.that, Test1.A.b.that.self, Test1.A.bb, Test1.A.bb.self,
				Test1.A.bb.that, Test1.A.bb.that.self, Test1.A.self, Test1.A.self.that, a, a.self, a.that,
				a.that.self, aa, aa.B, aa.B.aa, aa.B.aa.self, aa.B.aa.that, aa.B.aa.that.self, aa.B.bb,
				aa.B.bb.aa, aa.B.bb.aa.self, aa.B.bb.aa.that, aa.B.bb.aa.that.self, aa.B.bb.bb,
				aa.B.bb.bb.self, aa.B.bb.bb.that, aa.B.bb.bb.that.self, aa.B.bb.self, aa.B.bb.self.that,
				aa.B.bb.that, aa.B.bb.that.self, aa.B.self, aa.B.self.that, aa.a, aa.a.self, aa.a.that,
				aa.a.that.self, aa.aa, aa.aa.self, aa.aa.that, aa.aa.that.self, aa.b, aa.b.aa, aa.b.aa.self,
				aa.b.aa.that, aa.b.aa.that.self, aa.b.bb, aa.b.bb.self, aa.b.bb.that, aa.b.bb.that.self,
				aa.b.self, aa.b.self.that, aa.b.that, aa.b.that.self, aa.bb, aa.bb.self, aa.bb.that,
				aa.bb.that.self, aa.self, aa.self.that, aa.that, aa.that.self, b, b.aa, b.aa.self, b.aa.that,
				b.aa.that.self, b.bb, b.bb.self, b.bb.that, b.bb.that.self, b.self, b.self.that, b.that,
				b.that.self, bb, bb.B, bb.B.aa, bb.B.aa.self, bb.B.aa.that, bb.B.aa.that.self, bb.B.bb,
				bb.B.bb.self, bb.B.bb.that, bb.B.bb.that.self, bb.B.self, bb.B.self.that, bb.a, bb.a.self,
				bb.a.that, bb.a.that.self, bb.aa, bb.aa.B, bb.aa.B.aa, bb.aa.B.aa.self, bb.aa.B.aa.that,
				bb.aa.B.aa.that.self, bb.aa.B.bb, bb.aa.B.bb.self, bb.aa.B.bb.that, bb.aa.B.bb.that.self,
				bb.aa.B.self, bb.aa.B.self.that, bb.aa.a, bb.aa.a.self, bb.aa.a.that, bb.aa.a.that.self,
				bb.aa.b, bb.aa.b.self, bb.aa.b.that, bb.aa.b.that.self, bb.aa.self, bb.aa.self.that,
				bb.aa.that, bb.aa.that.self, bb.b, bb.b.self, bb.b.that, bb.b.that.self, bb.bb, bb.bb.self,
				bb.bb.that, bb.bb.that.self, bb.self, bb.self.that, bb.that, bb.that.self, self, self.that
			--- */
			feature bb: B; //added
		}

	}
	
}
