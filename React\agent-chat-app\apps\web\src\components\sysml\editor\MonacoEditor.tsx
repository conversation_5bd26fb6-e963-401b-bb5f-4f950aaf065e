'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useSysML } from '@/contexts/SysMLContext';
import { useSysMLToast } from '@/components/ui/sysml-toast';

// Monaco Editor 类型定义
interface MonacoEditor {
  getValue(): string;
  setValue(value: string): void;
  onDidChangeModelContent(listener: () => void): { dispose(): void };
  getModel(): any;
  setModel(model: any): void;
  dispose(): void;
  focus(): void;
  layout(): void;
}

interface Monaco {
  editor: {
    create(container: HTMLElement, options: any): MonacoEditor;
    createModel(value: string, language: string, uri?: any): any;
    defineTheme(themeName: string, themeData: any): void;
    setTheme(themeName: string): void;
  };
  languages: {
    register(language: { id: string; extensions: string[] }): void;
    setMonarchTokensProvider(languageId: string, provider: any): void;
    setLanguageConfiguration(languageId: string, configuration: any): void;
    registerCompletionItemProvider(languageId: string, provider: any): void;
    registerHoverProvider(languageId: string, provider: any): void;
    registerDocumentSymbolProvider(languageId: string, provider: any): void;
  };
  Uri: {
    parse(uri: string): any;
  };
}

interface MonacoEditorProps {
  value: string;
  onChange: (value: string) => void;
  onSave?: () => void;
  language?: string;
  theme?: string;
  readOnly?: boolean;
  height?: string;
}

const MonacoEditor: React.FC<MonacoEditorProps> = ({
  value,
  onChange,
  onSave,
  language = 'sysml',
  theme = 'vs-dark',
  readOnly = false,
  height = '100%',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<MonacoEditor | null>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showToast } = useSysMLToast();

  // SysML v2 语法高亮配置
  const sysmlTokensProvider = {
    tokenizer: {
      root: [
        // 关键字
        [/\b(package|part|def|attribute|connection|interface|port|action|state|requirement|constraint|case|view|viewpoint|analysis|verification|use|concern|item|allocation|flow|binding|succession|metadata|abstract|individual|variation|snapshot|timeslice|ordered|nonunique|derived|readonly|composite|end|in|out|inout|ref|redefines|subsets|specializes|conjugates|references|typed|by|featured|by|chains|inverse|of|disjoint|from|import|private|protected|public|all|filter|alias|for|about|doc|comment|language|standard|library|then|if|else|while|for|do|entry|exit|when|at|after|trigger|guard|effect|assume|require|verify|satisfy|expose|render|as|hastype|istype|meta|null|true|false|and|or|xor|not|implies)\b/, 'keyword'],
        
        // 操作符
        [/[=!<>]=?/, 'operator'],
        [/[+\-*/%^]/, 'operator'],
        [/[&|~]/, 'operator'],
        [/[?:]/, 'operator'],
        [/\.\./, 'operator'],
        [/::/, 'operator'],
        [/:>/, 'operator'],
        [/:>>/, 'operator'],
        [/::>/, 'operator'],
        [/=>/, 'operator'],
        
        // 数字
        [/\d*\.\d+([eE][\-+]?\d+)?/, 'number.float'],
        [/\d+/, 'number'],
        
        // 字符串
        [/"([^"\\]|\\.)*$/, 'string.invalid'],
        [/"/, 'string', '@string'],
        [/'([^'\\]|\\.)*$/, 'string.invalid'],
        [/'/, 'string', '@string_single'],
        
        // 注释
        [/\/\*/, 'comment', '@comment'],
        [/\/\/.*$/, 'comment'],
        
        // 标识符
        [/[a-zA-Z_]\w*/, 'identifier'],
        
        // 分隔符
        [/[{}()\[\]]/, '@brackets'],
        [/[<>](?!@symbols)/, '@brackets'],
        [/[,.]/, 'delimiter'],
        [/;/, 'delimiter'],
      ],
      
      comment: [
        [/[^\/*]+/, 'comment'],
        [/\/\*/, 'comment', '@push'],
        [/\*\//, 'comment', '@pop'],
        [/[\/*]/, 'comment']
      ],
      
      string: [
        [/[^\\"]+/, 'string'],
        [/\\./, 'string.escape.invalid'],
        [/"/, 'string', '@pop']
      ],
      
      string_single: [
        [/[^\\']+/, 'string'],
        [/\\./, 'string.escape.invalid'],
        [/'/, 'string', '@pop']
      ],
    },
  };

  // SysML v2 语言配置
  const sysmlLanguageConfig = {
    comments: {
      lineComment: '//',
      blockComment: ['/*', '*/']
    },
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')']
    ],
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ],
    surroundingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" }
    ],
    folding: {
      markers: {
        start: new RegExp('^\\s*//\\s*#?region\\b'),
        end: new RegExp('^\\s*//\\s*#?endregion\\b')
      }
    }
  };

  // SysML v2 代码补全提供器
  const sysmlCompletionProvider = {
    provideCompletionItems: (model: any, position: any) => {
      const suggestions = [
        // 基本关键字
        {
          label: 'package',
          kind: 14, // Keyword
          insertText: 'package ${1:PackageName} {\n\t$0\n}',
          insertTextRules: 4, // InsertAsSnippet
          documentation: 'Define a package'
        },
        {
          label: 'part def',
          kind: 14,
          insertText: 'part def ${1:PartName} {\n\t$0\n}',
          insertTextRules: 4,
          documentation: 'Define a part definition'
        },
        {
          label: 'part',
          kind: 14,
          insertText: 'part ${1:partName} : ${2:PartType};',
          insertTextRules: 4,
          documentation: 'Define a part usage'
        },
        {
          label: 'attribute',
          kind: 14,
          insertText: 'attribute ${1:attributeName} : ${2:Type};',
          insertTextRules: 4,
          documentation: 'Define an attribute'
        },
        {
          label: 'connection',
          kind: 14,
          insertText: 'connection ${1:connectionName} connect ${2:source} to ${3:target};',
          insertTextRules: 4,
          documentation: 'Define a connection'
        },
        {
          label: 'port',
          kind: 14,
          insertText: 'port ${1:portName} : ${2:PortType};',
          insertTextRules: 4,
          documentation: 'Define a port'
        },
        {
          label: 'action def',
          kind: 14,
          insertText: 'action def ${1:ActionName} {\n\t$0\n}',
          insertTextRules: 4,
          documentation: 'Define an action definition'
        },
        {
          label: 'state def',
          kind: 14,
          insertText: 'state def ${1:StateName} {\n\t$0\n}',
          insertTextRules: 4,
          documentation: 'Define a state definition'
        },
        {
          label: 'requirement def',
          kind: 14,
          insertText: 'requirement def ${1:RequirementName} {\n\t$0\n}',
          insertTextRules: 4,
          documentation: 'Define a requirement definition'
        },
        {
          label: 'constraint',
          kind: 14,
          insertText: 'constraint ${1:constraintName} { ${2:expression} }',
          insertTextRules: 4,
          documentation: 'Define a constraint'
        }
      ];

      return { suggestions };
    }
  };

  // 初始化 Monaco Editor
  useEffect(() => {
    let mounted = true;

    const initMonaco = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 动态导入 Monaco Editor
        const monaco = await import('monaco-editor');
        
        if (!mounted) return;

        monacoRef.current = monaco as any;

        // 注册 SysML 语言
        monaco.languages.register({
          id: 'sysml',
          extensions: ['.sysml', '.sysmlv2']
        });

        // 设置语法高亮
        monaco.languages.setMonarchTokensProvider('sysml', sysmlTokensProvider);

        // 设置语言配置
        monaco.languages.setLanguageConfiguration('sysml', sysmlLanguageConfig);

        // 注册代码补全提供器
        monaco.languages.registerCompletionItemProvider('sysml', sysmlCompletionProvider);

        // 定义深色主题
        monaco.editor.defineTheme('sysml-dark', {
          base: 'vs-dark',
          inherit: true,
          rules: [
            { token: 'keyword', foreground: '569cd6' },
            { token: 'operator', foreground: 'd4d4d4' },
            { token: 'number', foreground: 'b5cea8' },
            { token: 'string', foreground: 'ce9178' },
            { token: 'comment', foreground: '6a9955' },
            { token: 'identifier', foreground: '9cdcfe' },
          ],
          colors: {
            'editor.background': '#1e1e1e',
            'editor.foreground': '#d4d4d4',
            'editorLineNumber.foreground': '#858585',
            'editorCursor.foreground': '#aeafad',
            'editor.selectionBackground': '#264f78',
            'editor.lineHighlightBackground': '#2a2d2e',
          }
        });

        // 创建编辑器实例
        if (containerRef.current) {
          const editor = monaco.editor.create(containerRef.current, {
            value: value,
            language: 'sysml',
            theme: theme === 'vs-dark' ? 'sysml-dark' : theme,
            readOnly: readOnly,
            automaticLayout: true,
            fontSize: 14,
            lineNumbers: 'on',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            folding: true,
            lineDecorationsWidth: 10,
            lineNumbersMinChars: 3,
            glyphMargin: false,
            contextmenu: true,
            mouseWheelZoom: true,
            smoothScrolling: true,
            cursorBlinking: 'blink',
            cursorSmoothCaretAnimation: 'on',
            renderLineHighlight: 'line',
            selectOnLineNumbers: true,
            roundedSelection: false,
            renderIndentGuides: true,
            colorDecorators: true,
            codeLens: true,
            suggest: {
              showKeywords: true,
              showSnippets: true,
              showFunctions: true,
              showConstructors: true,
              showFields: true,
              showVariables: true,
              showClasses: true,
              showStructs: true,
              showInterfaces: true,
              showModules: true,
              showProperties: true,
              showEvents: true,
              showOperators: true,
              showUnits: true,
              showValues: true,
              showConstants: true,
              showEnums: true,
              showEnumMembers: true,
              showColors: true,
              showFiles: true,
              showReferences: true,
              showFolders: true,
              showTypeParameters: true,
              showUsers: true,
              showIssues: true,
            }
          });

          editorRef.current = editor;

          // 监听内容变化
          const disposable = editor.onDidChangeModelContent(() => {
            const newValue = editor.getValue();
            onChange(newValue);
          });

          // 添加保存快捷键
          editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
            onSave?.();
          });

          setIsLoading(false);

          return () => {
            disposable.dispose();
            editor.dispose();
          };
        }
      } catch (err) {
        console.error('Failed to initialize Monaco Editor:', err);
        setError('Failed to load code editor');
        setIsLoading(false);
      }
    };

    initMonaco();

    return () => {
      mounted = false;
      if (editorRef.current) {
        editorRef.current.dispose();
      }
    };
  }, []);

  // 更新编辑器值
  useEffect(() => {
    if (editorRef.current && editorRef.current.getValue() !== value) {
      editorRef.current.setValue(value);
    }
  }, [value]);

  // 处理主题变化
  useEffect(() => {
    if (monacoRef.current) {
      const actualTheme = theme === 'vs-dark' ? 'sysml-dark' : theme;
      monacoRef.current.editor.setTheme(actualTheme);
    }
  }, [theme]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full bg-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">正在加载编辑器...</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={containerRef} 
      style={{ height }} 
      className="w-full border border-gray-300 rounded"
    />
  );
};

export default MonacoEditor;
