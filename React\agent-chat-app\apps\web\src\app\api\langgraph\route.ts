// LangGraph API 路由
import { NextRequest, NextResponse } from 'next/server';
import { 
  executeWorkflow, 
  executeWorkflow<PERSON>hain,
  getWorkflowExecutor 
} from '@/lib/langgraph/workflow-executor';
import { 
  allWorkflows, 
  workflowRegistry 
} from '@/lib/langgraph/workflow-definitions';
import { HumanMessage } from '@langchain/core/messages';

// GET: 获取所有工作流信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'list':
        // 返回所有可用的工作流
        const workflows = allWorkflows.map(wf => ({
          id: wf.id,
          name: wf.name,
          description: wf.description,
          category: wf.category,
          dependencies: wf.dependencies || []
        }));
        
        return NextResponse.json({
          success: true,
          workflows
        });

      case 'status':
        // 返回所有工作流的执行状态
        const executor = getWorkflowExecutor();
        const status = executor.getAllWorkflowStatus();
        
        return NextResponse.json({
          success: true,
          status
        });

      case 'history':
        // 返回指定工作流的执行历史
        const workflowId = searchParams.get('workflowId');
        if (!workflowId) {
          return NextResponse.json({
            success: false,
            error: 'workflowId parameter is required'
          }, { status: 400 });
        }
        
        const executorForHistory = getWorkflowExecutor();
        const history = executorForHistory.getExecutionHistory(workflowId);
        
        return NextResponse.json({
          success: true,
          history
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action parameter'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('LangGraph API GET error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST: 执行工作流
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, workflowId, workflowIds, input, configs } = body;

    switch (action) {
      case 'execute':
        // 执行单个工作流
        if (!workflowId) {
          return NextResponse.json({
            success: false,
            error: 'workflowId is required'
          }, { status: 400 });
        }

        if (!workflowRegistry.has(workflowId)) {
          return NextResponse.json({
            success: false,
            error: `Workflow not found: ${workflowId}`
          }, { status: 404 });
        }

        const result = await executeWorkflow(workflowId, {
          messages: input?.message ? [new HumanMessage(input.message)] : [],
          sysmlCode: input?.sysmlCode,
          ...input
        });

        return NextResponse.json({
          success: true,
          result
        });

      case 'execute-chain':
        // 执行工作流链
        if (!workflowIds || !Array.isArray(workflowIds)) {
          return NextResponse.json({
            success: false,
            error: 'workflowIds array is required'
          }, { status: 400 });
        }

        // 验证所有工作流都存在
        for (const id of workflowIds) {
          if (!workflowRegistry.has(id)) {
            return NextResponse.json({
              success: false,
              error: `Workflow not found: ${id}`
            }, { status: 404 });
          }
        }

        const chainResults = await executeWorkflowChain(workflowIds, {
          messages: input?.message ? [new HumanMessage(input.message)] : [],
          sysmlCode: input?.sysmlCode,
          ...input
        });

        return NextResponse.json({
          success: true,
          results: chainResults
        });

      case 'execute-parallel':
        // 并行执行多个工作流
        if (!configs || !Array.isArray(configs)) {
          return NextResponse.json({
            success: false,
            error: 'configs array is required'
          }, { status: 400 });
        }

        // 验证所有工作流都存在
        for (const config of configs) {
          if (!workflowRegistry.has(config.id)) {
            return NextResponse.json({
              success: false,
              error: `Workflow not found: ${config.id}`
            }, { status: 404 });
          }
        }

        const executor = getWorkflowExecutor();
        const parallelResults = await executor.executeWorkflowsParallel(
          configs.map((config: any) => ({
            id: config.id,
            input: {
              messages: config.input?.message ? [new HumanMessage(config.input.message)] : [],
              sysmlCode: config.input?.sysmlCode,
              ...config.input
            }
          }))
        );

        return NextResponse.json({
          success: true,
          results: parallelResults
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('LangGraph API POST error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE: 清除执行历史
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const workflowId = searchParams.get('workflowId');

    const executor = getWorkflowExecutor();
    executor.clearExecutionHistory(workflowId || undefined);

    return NextResponse.json({
      success: true,
      message: workflowId 
        ? `Cleared history for workflow: ${workflowId}`
        : 'Cleared all execution history'
    });
  } catch (error) {
    console.error('LangGraph API DELETE error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
