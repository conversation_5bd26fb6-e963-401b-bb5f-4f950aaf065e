'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { BrowserMessageReader, BrowserMessageWriter } from 'vscode-languageserver-protocol/browser';
import { MonacoEditorLanguageClientWrapper } from 'monaco-editor-wrapper';
import { setupSysMLWrapperConfig, SysMLDarkTheme } from '@/lib/sysml/sysml-wrapper-config';
import { useSysMLToast } from '@/components/ui/sysml-toast';

interface SysMLWrapperEditorProps {
  value?: string;
  onChange?: (value: string) => void;
  onSave?: () => void;
  height?: string;
  readOnly?: boolean;
}

const SysMLWrapperEditor: React.FC<SysMLWrapperEditorProps> = ({
  value = '',
  onChange,
  onSave,
  height = '100%',
  readOnly = false,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<MonacoEditorLanguageClientWrapper | null>(null);
  const workerRef = useRef<Worker | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showToast } = useSysMLToast();

  // 清理函数
  const cleanup = useCallback(async () => {
    if (wrapperRef.current) {
      try {
        await wrapperRef.current.dispose();
        wrapperRef.current = null;
      } catch (error) {
        console.error('Error disposing wrapper:', error);
      }
    }
    
    if (workerRef.current) {
      try {
        workerRef.current.terminate();
        workerRef.current = null;
      } catch (error) {
        console.error('Error terminating worker:', error);
      }
    }
  }, []);

  // 创建 Langium Worker
  const createLangiumWorker = useCallback(() => {
    try {
      // 使用 Next.js 的 Web Worker 支持
      const worker = new Worker(
        new URL('../../../workers/sysml-langium-server.worker.ts', import.meta.url),
        { type: 'module', name: 'SysML Langium LS' }
      );
      
      worker.onerror = (error) => {
        console.error('Worker error:', error);
        setError('语言服务器启动失败');
      };
      
      worker.onmessage = (event) => {
        const { type, data } = event.data;
        if (type === 'error') {
          console.error('Worker reported error:', data.error);
          setError(`语言服务器错误: ${data.error}`);
        }
      };
      
      return worker;
    } catch (error) {
      console.error('Failed to create worker:', error);
      setError('无法创建语言服务器');
      return null;
    }
  }, []);

  // 初始化编辑器
  const initializeEditor = useCallback(async () => {
    if (!containerRef.current) return;

    try {
      setIsLoading(true);
      setError(null);

      // 清理之前的实例
      await cleanup();

      // 创建 Worker
      const worker = createLangiumWorker();
      if (!worker) {
        throw new Error('Failed to create language server worker');
      }
      workerRef.current = worker;

      // 设置消息传输
      const reader = new BrowserMessageReader(worker);
      const writer = new BrowserMessageWriter(worker);

      // 创建配置
      const config = await setupSysMLWrapperConfig({
        worker,
        messageTransports: { reader, writer },
        htmlContainer: containerRef.current,
        initialCode: value
      });

      // 创建并启动 wrapper
      const wrapper = new MonacoEditorLanguageClientWrapper();
      wrapperRef.current = wrapper;

      await wrapper.initAndStart(config);

      // 注册自定义主题
      const monaco = await import('monaco-editor');
      monaco.editor.defineTheme('sysml-dark', SysMLDarkTheme);
      monaco.editor.setTheme('sysml-dark');

      // 监听内容变化
      const editor = wrapper.getEditor();
      if (editor && onChange) {
        editor.onDidChangeModelContent(() => {
          const currentValue = editor.getValue();
          onChange(currentValue);
        });
      }

      // 设置保存快捷键
      if (editor && onSave) {
        const monaco = await import('monaco-editor');
        editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
          onSave();
        });
      }

      setIsLoading(false);
      showToast('SysML 编辑器已就绪', 'success');

    } catch (error) {
      console.error('Failed to initialize editor:', error);
      setError(error instanceof Error ? error.message : '编辑器初始化失败');
      setIsLoading(false);
      showToast('编辑器初始化失败', 'error');
    }
  }, [value, onChange, onSave, cleanup, createLangiumWorker, showToast]);

  // 更新编辑器内容
  const updateEditorValue = useCallback(async (newValue: string) => {
    if (!wrapperRef.current) return;

    try {
      await wrapperRef.current.updateCodeResources({
        modified: {
          text: newValue,
          uri: '/workspace/current.sysml',
          enforceLanguageId: 'sysml'
        }
      });
    } catch (error) {
      console.error('Failed to update editor value:', error);
    }
  }, []);

  // 初始化效果
  useEffect(() => {
    initializeEditor();

    return () => {
      cleanup();
    };
  }, [initializeEditor, cleanup]);

  // 值变化效果
  useEffect(() => {
    if (wrapperRef.current && value !== undefined) {
      updateEditorValue(value);
    }
  }, [value, updateEditorValue]);

  // 渲染
  if (error) {
    return (
      <div 
        style={{ height }}
        className="flex items-center justify-center bg-red-50 border border-red-200 rounded-md"
      >
        <div className="text-center">
          <div className="text-red-600 font-medium mb-2">编辑器错误</div>
          <div className="text-red-500 text-sm">{error}</div>
          <button
            onClick={initializeEditor}
            className="mt-3 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative" style={{ height }}>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-75 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <div className="text-gray-600">正在加载 SysML 编辑器...</div>
          </div>
        </div>
      )}
      <div
        ref={containerRef}
        style={{ height: '100%', width: '100%' }}
        className="border border-gray-300 rounded-md overflow-hidden"
      />
    </div>
  );
};

export default SysMLWrapperEditor;
