/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer, dev }) => {
    // Monaco Editor 支持
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
        module: false,
        perf_hooks: false,
        net: false,
        tls: false,
        child_process: false,
        // 重要：为 vscode 模块提供 fallback
        vscode: '@codingame/monaco-vscode-api',
      };

      // 添加别名来解决 vscode 模块问题
      config.resolve.alias = {
        ...config.resolve.alias,
        vscode: '@codingame/monaco-vscode-api',
      };
    }

    // Web Workers 支持
    config.module.rules.push({
      test: /\.worker\.(js|ts)$/,
      use: {
        loader: 'worker-loader',
        options: {
          name: 'static/[hash].worker.js',
          publicPath: '/_next/',
        },
      },
    });

    // Langium 支持
    config.module.rules.push({
      test: /\.langium$/,
      use: 'raw-loader',
    });

    // 支持 ES modules
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };

    return config;
  },

  // 支持 ES modules
  experimental: {
    esmExternals: true,
  },

  // 外部化某些包以避免构建问题
  externals: {
    'vscode': 'commonjs vscode',
  },

  // 静态文件优化
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
