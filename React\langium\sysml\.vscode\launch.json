// A launch configuration that launches the extension inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run Extension",
            "type": "extensionHost",
            "request": "launch",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}"
            ],
            "sourceMaps": true,
            "outFiles": [
                "${workspaceFolder}/out/**/*.js"
            ]
        },
        {
            "name": "Attach to Language Server",
            "type": "node",
            "port": 6009,
            "request": "attach",
            "skipFiles": [
                "<node_internals>/**"
            ],
            "sourceMaps": true,
            "outFiles": [
                "${workspaceFolder}/out/**/*.js",
                "${workspaceFolder}/node_modules/langium"
            ]
        }
    ]
}
