//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
			}
		}
	}
END_SETUP 
*/

package 'Test' {
	package A {
		// XPECT errors --> "Must directly or indirectly specialize Base::Anything" at "classifier A;"
		classifier A;
		// XPECT errors --> "Features must have at least one type" at "feature f;"
		feature f;
	}
}