//*
XPECT_SETUP org.omg.sysml.xpect.tests.validation.invalid.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/BaseFunctions.kerml"}
		File {from ="/library.kernel/Objects.kerml"}
		File {from ="/library.kernel/Occurrences.kerml"}
		File {from ="/library.kernel/Performances.kerml"}
		File {from ="/library.systems/Constraints.sysml"}
		File {from ="/library.systems/Requirements.sysml"}
		File {from ="/library.systems/Views.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/BaseFunctions.kerml"}
				File {from ="/library.kernel/Objects.kerml"}
				File {from ="/library.kernel/Occurrences.kerml"}
				File {from ="/library.kernel/Performances.kerml"}
				File {from ="/library.systems/Constraints.sysml"}
				File {from ="/library.systems/Requirements.sysml"}
				File {from ="/library.systems/Views.sysml"}
			}
		}
	}
END_SETUP
*/
package ViewRendering_invalid {
	rendering r;
	
	view def V {
		render rendering r1;
		// XPECT errors ---> "A view definition may have at most one view rendering." at "render rendering r2;"
		render rendering r2;
		rendering r3;
	}

	view v {
		render r;
		// XPECT errors ---> "A view may have at most one view rendering." at "render rendering r4;"
		render rendering r4;
		// XPECT errors ---> "A view may have at most one view rendering." at "render rendering r5;"
		render rendering r5;
		rendering r6;
	}
}