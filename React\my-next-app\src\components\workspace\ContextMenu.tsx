'use client';

import React, { useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { useApp } from '@/contexts/AppContext';

const ContextMenu: React.FC = () => {
  const { state, dispatch } = useApp();
  const { contextMenu } = state;
  const menuRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭菜单
  useEffect(() => {
    if (!contextMenu.isVisible) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        dispatch({ type: 'HIDE_CONTEXT_MENU' });
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        dispatch({ type: 'HIDE_CONTEXT_MENU' });
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleEscape);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [contextMenu.isVisible, dispatch]);

  // 调整菜单位置，确保不超出视窗
  useEffect(() => {
    if (!contextMenu.isVisible || !menuRef.current) return;

    const menu = menuRef.current;
    const rect = menu.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let { x, y } = contextMenu;

    // 水平方向调整
    if (x + rect.width > viewportWidth) {
      x = viewportWidth - rect.width - 10;
    }
    if (x < 0) {
      x = 10;
    }

    // 垂直方向调整
    if (y + rect.height > viewportHeight) {
      y = viewportHeight - rect.height - 10;
    }
    if (y < 0) {
      y = 10;
    }

    menu.style.left = `${x}px`;
    menu.style.top = `${y}px`;
  }, [contextMenu.isVisible, contextMenu.x, contextMenu.y]);

  // 处理菜单项点击
  const handleItemClick = (item: any) => {
    if (!item.disabled) {
      item.action();
    }
    dispatch({ type: 'HIDE_CONTEXT_MENU' });
  };

  if (!contextMenu.isVisible) {
    return null;
  }

  const menuContent = (
    <div
      ref={menuRef}
      className="context-menu"
      style={{
        left: contextMenu.x,
        top: contextMenu.y,
      }}
    >
      {contextMenu.items.map((item, index) => (
        item.separator ? (
          <div key={index} className="context-menu-separator" />
        ) : (
          <div
            key={item.id}
            className={`context-menu-item ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={() => handleItemClick(item)}
          >
            <div className="flex items-center space-x-2">
              {item.icon && (
                <span className="w-4 h-4">{item.icon}</span>
              )}
              <span>{item.label}</span>
            </div>
          </div>
        )
      ))}
    </div>
  );

  return createPortal(menuContent, document.body);
};

export default ContextMenu;
