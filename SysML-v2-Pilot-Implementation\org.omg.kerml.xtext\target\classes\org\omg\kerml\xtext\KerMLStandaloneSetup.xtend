/*
 * generated by Xtext 2.18.0.M3
 */
package org.omg.kerml.xtext

import org.eclipse.emf.ecore.EStructuralFeature
import org.omg.sysml.delegate.setting.DerivedPropertySettingDelegateFactory;
import org.omg.sysml.delegate.invocation.OperationInvocationDelegateFactory;
import com.google.inject.Injector
import org.eclipse.emf.ecore.EOperation

/**
 * Initialization support for running Xtext languages without Equinox extension registry.
 */
class KerMLStandaloneSetup extends KerMLStandaloneSetupGenerated {

	def static void doSetup() {
		new KerMLStandaloneSetup().createInjectorAndDoEMFRegistration()
	}
	
	override Injector createInjectorAndDoEMFRegistration() {
		EStructuralFeature.Internal.SettingDelegate.Factory.Registry.INSTANCE.
			put(DerivedPropertySettingDelegateFactory.SYSML_ANNOTATION, new DerivedPropertySettingDelegateFactory());	
		EOperation.Internal.InvocationDelegate.Factory.Registry.INSTANCE.
			put(OperationInvocationDelegateFactory.SYSML_ANNOTATION, new OperationInvocationDelegateFactory());	
		return super.createInjectorAndDoEMFRegistration();
	}
}
