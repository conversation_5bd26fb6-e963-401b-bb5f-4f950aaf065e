'use client';

import React from 'react';
import EditorDiagnostics from '@/components/sysml/editor/EditorDiagnostics';

const DiagnosticsPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                SysML 编辑器诊断
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                检查编辑器组件的加载状态和兼容性
              </p>
            </div>
            <div className="flex space-x-3">
              <a
                href="/sysml/editor-test"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                编辑器测试
              </a>
              <a
                href="/sysml"
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
              >
                返回工作区
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="py-6">
        <EditorDiagnostics />
      </main>
    </div>
  );
};

export default DiagnosticsPage;
