//* 
XPECT_SETUP org.omg.kerml.xpect.tests.scoping.KerMLScopingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors --> ""
package Test {

	package Test2 {
		public import VP::VP2::*;
		classifier A;
		//* XPECT scope at A_Id ---
		 A,  Test2.A, Test.Test2.A,
		 AA, Test2.AA, Test.Test2.AA,
		 B, Test2.B, Test.Test2.B,
		 A_Id, Test2.A_Id, Test.Test2.A_Id,
		 VP.VP2.B, Test.VP.VP2.B,
		 VP.VP2.A_Id, Test.VP.VP2.A_Id,  
		--- */
		classifier AA specializes A_Id;
	}
	package VP {
		package VP2 {
			public classifier <'A_Id'> B;
		}
	}
	
}

	