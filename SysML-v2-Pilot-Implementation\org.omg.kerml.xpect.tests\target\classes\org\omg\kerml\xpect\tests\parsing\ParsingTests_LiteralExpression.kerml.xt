//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/library/Links.kerml"}
		File {from ="/library/Performances.kerml"}
		File {from ="/library/ScalarValues.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/library/Links.kerml"}
				File {from ="/library/Performances.kerml"}
				File {from ="/library/ScalarValues.kerml"}
			}
		}
	}
END_SETUP 
*/

// XPECT noErrors ---> ""
package P {
    i0 = 1;
    iu = *;
        
    r0 = 0.08;
    r1 = .08;
    r2 = 5.4321;
    r3 = 1.0;
    r4 = 1e-5; 
    r5 = .1e-05; 
    r6 = 3.1e+05; 
    r7 = 3e5;

    b0 = true;
    b1 = false;

    s0 = "aaa";
    
    // Should not parse "1." as a real literal
    x[1..*];
}
