//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private_alias::alias_protected'." at "VisibilityPackage::c_Private_alias::alias_protected"
	public import VisibilityPackage::c_Private_alias::alias_protected::*;
	//XPECT errors --> "Couldn't resolve reference to Namespace 'VisibilityPackage::c_Private_alias::alias_public'." at "VisibilityPackage::c_Private_alias::alias_public"
	public import VisibilityPackage::c_Private_alias::alias_public::*;
}
