{"name": "mlc-verify-angular", "version": "0.0.0", "private": "true", "type": "module", "engines": {"node": ">=20.10.0", "npm": ">=10.2.3"}, "volta": {"node": "22.16.0", "npm": "10.9.2"}, "dependencies": {"@angular/compiler": "~18.2.8", "@angular/core": "~18.2.8", "@angular/platform-browser": "~18.2.8", "@angular/platform-browser-dynamic": "~18.2.8", "@codingame/monaco-vscode-editor-api": "~18.1.0", "monaco-editor-wrapper": "~6.9.0", "monaco-languageclient-examples": "~2025.6.2", "rxjs": "~7.8.1", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-builders/custom-esbuild": "~18.0.0", "@angular/cli": "~18.2.8", "@angular/compiler-cli": "~18.2.8", "@codingame/esbuild-import-meta-url-plugin": "~1.0.3", "@types/node": "~22.13.10", "css-loader": "~7.1.2", "shx": "~0.3.4", "style-loader": "~4.0.0", "typescript": "~5.5.4"}, "scripts": {"verify": "npm install && npm run start", "verify:ci": "npm install && npm run build", "clean": "shx rm -fr dist *.tsbuildinfo src/assets", "build:msg": "echo Building angular-client example:", "build": "npm run build:msg && npm run clean && ng build", "build:production": "npm run build:msg && npm run clean && ng build --configuration production", "start": "npm run clean && ng serve", "start:production": "npm run clean && ng serve --configuration production", "watch": "npm run clean && ng build --watch --configuration development", "reset:repo": "git clean -f -X -d"}}