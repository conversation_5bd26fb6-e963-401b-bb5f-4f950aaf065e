//*
XPECT_SETUP org.omg.sysml.xpect.tests.validation.invalid.SysMLTests
	ResourceSet {
		ThisFile {}
		}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
			}
		}
	}
END_SETUP 
*/

package 'Test' {
	package A {
		// XPECT errors --> "Must directly or indirectly specialize Parts::Part" at "part def B;"
		part def B;
		// XPECT errors --> "Features must have at least one type" at "ref f;"
		ref f;
	}
}
