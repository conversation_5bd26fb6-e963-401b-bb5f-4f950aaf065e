# AI 提示词功能说明

## 🚀 功能概述

在 CodeEditor.tsx 中新增了 AI 提示词输入功能，允许用户通过自然语言描述来生成或修改 SysML v2 代码。

## ✨ 新增功能

### 1. AI 提示词输入框
- 位置：编辑器工具栏下方
- 默认提示文本：「输入修改意见...」
- 支持键盘快捷键：按 Enter 键快速生成
- 智能焦点处理：点击时自动清空默认文本

### 2. 增强的 AI 生成功能
- **智能提示词处理**：根据用户输入的自然语言生成相应代码
- **上下文感知**：基于当前编辑器中的代码进行修改或扩展
- **实时状态反馈**：生成过程中显示加载状态
- **错误处理**：完善的错误提示和异常处理

### 3. 大模型集成
- **模型**：gemini-2.5-pro
- **API**：https://api.chatfire.cn/v1
- **智能对话**：支持基于上下文的代码生成和修改

## 🔧 技术实现

### API 配置
```typescript
// 大模型配置
const AI_CONFIG = {
  api_key: "sk-nuFHtM3uPjkLbXjv55inifI4wOpswe1WZu5E647rQCwOzqdc",
  base_url: "https://api.chatfire.cn/v1",
  model: "gemini-2.5-pro"
};
```

### 核心函数
```typescript
// 新增的 API 函数
sysmlApi.generateCodeWithCustomPrompt(userPrompt, currentCode)

// 提示词构建函数
buildFullPrompt(userPrompt, currentCode)

// 模拟数据函数
mockAIGenerateWithPrompt(userPrompt, currentCode)
```

## 🎯 使用方法

### 1. 基本使用
1. 在 AI 提示词输入框中输入需求描述
2. 点击「AI 生成」按钮或按 Enter 键
3. 等待 AI 生成代码并自动填入编辑器

### 2. 提示词示例

#### 创建新模型
- "创建一个车辆系统模型"
- "生成一个机器人控制系统"
- "设计一个智能家居系统"

#### 修改现有代码
- "为当前系统添加性能需求"
- "增加错误处理机制"
- "修改引擎功率限制为600马力"

#### 扩展功能
- "添加传感器组件"
- "增加状态机定义"
- "创建验证用例"

### 3. 高级功能
- **上下文感知**：AI 会分析当前代码并基于此进行修改
- **智能补全**：根据 SysML v2 语法规范生成有效代码
- **错误预防**：生成的代码符合语法规范

## 📋 UI 界面

### 工具栏增强
```
[文件名] [●]                    [保存] [AI生成] [生成图]
```

### AI 提示词区域
```
AI 提示词： [输入修改意见...                    ] 按 Enter 快速生成
💡 提示：描述您想要的代码修改或新功能，AI 将基于当前代码进行生成或修改
```

### 状态显示
- **正常状态**：「AI 生成」按钮
- **生成中**：「生成中...」按钮（禁用状态）
- **输入验证**：空输入时显示警告提示

## 🔍 功能特点

### 1. 智能输入处理
- 自动清空默认提示文本
- 失焦时恢复默认文本（如果为空）
- 键盘快捷键支持

### 2. 上下文感知生成
- 分析当前编辑器内容
- 基于现有代码进行扩展
- 保持代码结构一致性

### 3. 用户体验优化
- 实时加载状态显示
- 详细的错误提示
- 生成完成后自动清空输入框

### 4. 安全性考虑
- API 密钥安全存储
- 请求错误处理
- 网络异常处理

## 🛠️ 开发说明

### 文件修改
1. **CodeEditor.tsx** - 主要组件，添加 UI 和逻辑
2. **api.ts** - 新增 AI API 函数
3. **mockData.ts** - 添加模拟数据支持

### 状态管理
```typescript
const [aiPrompt, setAiPrompt] = useState('输入修改意见...');
const [isAiGenerating, setIsAiGenerating] = useState(false);
```

### 事件处理
```typescript
// AI 生成处理
const handleAIGenerate = async () => {
  // 验证输入
  // 调用 AI API
  // 更新编辑器内容
  // 处理错误
};
```

## 🚀 未来扩展

### 计划功能
- [ ] 提示词历史记录
- [ ] 预设提示词模板
- [ ] 多轮对话支持
- [ ] 代码差异显示
- [ ] 批量代码生成

### 优化方向
- [ ] 响应速度优化
- [ ] 生成质量提升
- [ ] 用户界面改进
- [ ] 错误处理完善

## 📚 相关文档
- [Monaco Editor 集成说明](./MONACO_EDITOR_INTEGRATION.md)
- [开发指南](./DEVELOPMENT_GUIDE.md)
- [项目结构](./PROJECT_STRUCTURE.md)
