{"name": "sysml", "description": "Please enter a brief description here", "version": "0.0.1", "files": ["bin", "out", "src"], "type": "module", "scripts": {"build": "tsc -b tsconfig.src.json && node esbuild.mjs", "watch": "concurrently -n tsc,esbuild -c blue,yellow \"tsc -b tsconfig.src.json --watch\" \"node esbuild.mjs --watch\"", "lint": "eslint src --ext ts", "langium:generate": "langium generate", "langium:generate:production": "langium generate --mode=production", "langium:watch": "langium generate --watch", "vscode:prepublish": "npm run build && npm run lint", "bundle": "vite build", "bundle:serve": "http-server ./dist --port 5175", "dev": "vite", "dev:debug": "vite --debug --force", "serve": "node ./out/web/app.js", "test": "vitest run", "esbuild-base": "esbuild ./src/extension/main.ts --bundle --outfile=out/main.js --external:vscode --format=cjs --platform=node", "build:worker": "esbuild --minify ./out/language/main-browser.js --bundle --format=iife --outfile=./public/minilogo-server-worker.js", "prepare:public": "node scripts/prepare-public.mjs", "build:web": "npm run build && npm run prepare:public && npm run build:worker && node scripts/copy-monaco-assets.mjs"}, "dependencies": {"@codingame/monaco-vscode-editor-service-override": "~3.2.3", "@codingame/monaco-vscode-keybindings-service-override": "~3.2.3", "chalk": "~5.3.0", "commander": "~11.0.0", "langium": "~3.5.0", "monaco-editor": "npm:@codingame/monaco-vscode-editor-api@~3.2.3", "monaco-editor-workers": "^0.45.0", "monaco-editor-wrapper": "^6.9.0", "monaco-languageclient": "~8.1.1", "shelljs": "^0.10.0", "vscode": "npm:@codingame/monaco-vscode-api@~3.2.3", "vscode-languageclient": "~9.0.1", "vscode-languageserver": "~9.0.1"}, "devDependencies": {"@codingame/esbuild-import-meta-url-plugin": "~1.0.2", "@types/node": "^18.0.0", "@types/vscode": "~1.67.0", "@typescript-eslint/eslint-plugin": "~7.3.1", "@typescript-eslint/parser": "~7.3.1", "concurrently": "~8.2.1", "esbuild": "^0.20.2", "eslint": "~8.57.0", "http-server": "~14.1.1", "langium-cli": "~3.5.0", "typescript": "~5.1.6", "vite": "~5.2.7", "vitest": "~1.4.0"}, "volta": {"node": "18.19.1", "npm": "10.2.4"}, "displayName": "sysml", "engines": {"vscode": "^1.67.0", "node": ">=18.0.0"}, "categories": ["Programming Languages"], "contributes": {"languages": [{"id": "sysml", "aliases": ["sysml", "sysml"], "extensions": [".sysml"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "sysml", "scopeName": "source.sysml", "path": "syntaxes/sysml.tmLanguage.json"}]}, "activationEvents": ["onLanguage:sysml"], "main": "./out/extension/main.cjs", "bin": {"sysml-cli": "./bin/cli.js"}}