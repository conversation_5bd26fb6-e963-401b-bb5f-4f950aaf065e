//*
XPECT_SETUP org.omg.sysml.xpect.tests.validation.invalid.SysMLTests
	ResourceSet {
		ThisFile {}
		File {from ="/library.kernel/Base.kerml"}
		File {from ="/library.kernel/Occurrences.kerml"}
		File {from ="/library.kernel/Objects.kerml"}
		File {from ="/library.kernel/Performances.kerml"}
		File {from ="/library.kernel/ControlPerformances.kerml"}
		File {from ="/library.kernel/TransitionPerformances.kerml"}
		File {from ="/library.systems/Items.sysml"}
		File {from ="/library.systems/Parts.sysml"}
		File {from ="/library.systems/Ports.sysml"}
		File {from ="/library.systems/Constraints.sysml"}
		File {from ="/library.systems/Requirements.sysml"}
		File {from ="/library.systems/Actions.sysml"}
		File {from ="/library.systems/Calculations.sysml"}
		File {from ="/library.systems/Cases.sysml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library.kernel/Base.kerml"}
				File {from ="/library.kernel/Occurrences.kerml"}
				File {from ="/library.kernel/Objects.kerml"}
				File {from ="/library.kernel/Performances.kerml"}
				File {from ="/library.kernel/ControlPerformances.kerml"}
				File {from ="/library.kernel/TransitionPerformances.kerml"}
				File {from ="/library.systems/Items.sysml"}
				File {from ="/library.systems/Parts.sysml"}
				File {from ="/library.systems/Ports.sysml"}
				File {from ="/library.systems/Constraints.sysml"}
				File {from ="/library.systems/Requirements.sysml"}
				File {from ="/library.systems/Actions.sysml"}
				File {from ="/library.systems/Calculations.sysml"}
				File {from ="/library.systems/Cases.sysml"}
			}
		}
	}
END_SETUP
*/
package 'Case Subjects and Objectives' {
	
	case def C {
		subject s1;
		
		// XPECT errors --> "Only one subject is allowed." at "subject s2;"
		subject s2;

		objective o1;
		
		// XPECT errors --> "Only one objective is allowed." at "objective o2;"
		objective o2;
	}
	
	case c: C {
		subject s3;
		
		// XPECT errors --> "Only one subject is allowed." at "subject s4;"
		subject s4;

		// XPECT errors --> "Only one objective is allowed." at "objective o3;"
		objective o3;
		
		// XPECT errors --> "Only one objective is allowed." at "objective o4;"
		objective o4;
	}
	
	case def C1 :> C {
		in x;
		
		// XPECT errors --> "Subject must be first parameter." at "subject s5;"
		subject s5;
		
		// XPECT errors --> "Only one objective is allowed." at "objective o5;"
		objective o5;
	}
	
	//* XPECT errors ---
		"Only one objective is allowed." at "case c1 : C1;"
		"Subject must be first parameter." at "case c1 : C1;"
	   ---
	*/
	case c1 : C1;
	
	case c2 {
		in y;
		
		// XPECT errors --> "Subject must be first parameter." at "subject s6;"
		subject s6;
	}
	
}