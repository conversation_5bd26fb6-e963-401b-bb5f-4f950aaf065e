'use client';

import React, { useState } from 'react';
import SysMLWrapperEditor from '@/components/sysml/editor/SysMLWrapperEditor';
import { sysmlExampleCode } from '@/lib/sysml/sysml-wrapper-config';

const EditorTestPage: React.FC = () => {
  const [code, setCode] = useState(sysmlExampleCode);
  const [savedCode, setSavedCode] = useState(sysmlExampleCode);

  const handleSave = () => {
    setSavedCode(code);
    alert('代码已保存！');
  };

  const handleReset = () => {
    setCode(sysmlExampleCode);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部 */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                SysML v2 编辑器测试
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                基于 Langium 框架和 Monaco Editor 的 SysML v2 代码编辑器
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                onClick={handleReset}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                重置代码
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                保存 (Ctrl+S)
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-lg shadow-sm border">
          {/* 编辑器工具栏 */}
          <div className="flex items-center justify-between px-4 py-3 border-b bg-gray-50">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-medium text-gray-900">
                SysML v2 代码编辑器
              </h2>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Langium 语言服务器
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Monaco Editor
                </span>
              </div>
            </div>
            <div className="text-sm text-gray-500">
              {code.length} 字符
            </div>
          </div>

          {/* 编辑器 */}
          <div style={{ height: '600px' }}>
            <SysMLWrapperEditor
              value={code}
              onChange={setCode}
              onSave={handleSave}
              height="100%"
              readOnly={false}
            />
          </div>

          {/* 状态栏 */}
          <div className="flex items-center justify-between px-4 py-2 border-t bg-gray-50 text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>SysML v2</span>
              <span>UTF-8</span>
              <span>LF</span>
              <span className={`${code !== savedCode ? 'text-orange-600' : 'text-green-600'}`}>
                {code !== savedCode ? '未保存' : '已保存'}
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span>Langium 语言服务器已连接</span>
              <span>行 1, 列 1</span>
            </div>
          </div>
        </div>

        {/* 功能说明 */}
        <div className="mt-6 bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">编辑器功能</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">语法高亮</h4>
                <p className="text-sm text-gray-600">完整的 SysML v2 语法高亮支持</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">智能补全</h4>
                <p className="text-sm text-gray-600">基于 Langium 的代码补全</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">错误诊断</h4>
                <p className="text-sm text-gray-600">实时语法错误检测</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">Web Worker</h4>
                <p className="text-sm text-gray-600">后台运行语言服务器</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">快捷键</h4>
                <p className="text-sm text-gray-600">Ctrl+S 保存等快捷键</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="w-2 h-2 bg-indigo-500 rounded-full mt-2"></div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-900">深色主题</h4>
                <p className="text-sm text-gray-600">专为 SysML v2 优化的主题</p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default EditorTestPage;
