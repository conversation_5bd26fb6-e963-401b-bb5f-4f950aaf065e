//* XPECT_SETUP org.omg.sysml.xpect.tests.query.failing.SysMLQueryFailingTest
        ResourceSet {
                ThisFile {}
                File {from ="/library.kernel/Base.kerml"}
                File {from ="/library.kernel/Objects.kerml"}
                File {from ="/library.kernel/Performances.kerml"}
                File {from ="/library.kernel/ScalarValues.kerml"}
                File {from ="/library.kernel/ScalarFunctions.kerml"}
                File {from ="/library.kernel/ControlFunctions.kerml"}
                File {from ="/src/Vehicle.sysml"}
        }
        Workspace {
                JavaProject {
                        SrcFolder {
                                ThisFile {}
                                File {from ="/library.kernel/Base.kerml"}
                 				File {from ="/library.kernel/Objects.kerml"}
                				File {from ="/library.kernel/Performances.kerml"}
                                File {from ="/library.kernel/ScalarValues.kerml"}
                				File {from ="/library.kernel/ScalarFunctions.kerml"}
                				File {from ="/library.kernel/ControlFunctions.kerml"}
                                File {from ="/src/Vehicle.sysml"}
                        }
                }
        }
END_SETUP
*/

// XPECT noErrors ---> ""
package P {
    public import ScalarValues::*;
    public import Vehicle::*;

    value v1_i: Integer[0..*] = .*/.*[Integer];

    value v2_r: Real[0..*] = .**[Real];

    value v1_i: Integer[0..*] = .*/.*[Integer| ./ < 3];

    value v1_i: Real[0..*] = .*/mass[./ > 3.0];
}
