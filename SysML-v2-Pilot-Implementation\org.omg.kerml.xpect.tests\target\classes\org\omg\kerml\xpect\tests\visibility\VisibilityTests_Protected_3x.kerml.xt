//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/


package Test3{
	classifier P{
		protected classifier PP {
			classifier PPP{}
		}
	}
	// XPECT errors --> "Couldn't resolve reference to Classifier 'P::PP'." at "P::PP"
	classifier C specializes P::PP {
		// XPECT errors --> "Couldn't resolve reference to Type 'PPP'." at "PPP"
		feature CC: PPP;
	}
}
