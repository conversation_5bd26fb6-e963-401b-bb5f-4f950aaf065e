// SysML v2 示例代码
// 这是一个简单的车辆系统模型

package VehicleSystem {
    
    // 定义车辆部件
    part def Vehicle {
        part engine : Engine;
        part transmission : Transmission;
        part wheels : Wheel[4];
        
        // 车辆属性
        attribute mass : Real;
        attribute maxSpeed : Real;
        
        // 端口定义
        port fuelInput : FuelPort;
        port powerOutput : PowerPort;
    }
    
    // 引擎定义
    part def Engine {
        attribute power : Real;
        attribute fuelConsumption : Real;
        
        port fuelIn : FuelPort;
        port powerOut : PowerPort;
        
        // 引擎约束
        constraint powerConstraint {
            power <= 500.0 // 最大功率限制
        }
    }
    
    // 传动系统定义
    part def Transmission {
        attribute gearRatio : Real;
        attribute efficiency : Real;
        
        port powerIn : PowerPort;
        port powerOut : PowerPort;
    }
    
    // 车轮定义
    part def Wheel {
        attribute diameter : Real;
        attribute width : Real;
        
        port powerIn : PowerPort;
    }
    
    // 接口定义
    interface def FuelPort {
        attribute fuelType : String;
        attribute flowRate : Real;
    }
    
    interface def PowerPort {
        attribute voltage : Real;
        attribute current : Real;
    }
    
    // 需求定义
    requirement def PerformanceRequirement {
        doc /* 车辆性能需求 */
        
        requirement accelerationReq {
            doc /* 0-100km/h 加速时间不超过10秒 */
            subject vehicle : Vehicle;
            
            constraint {
                // 加速性能约束
                vehicle.maxSpeed >= 100.0
            }
        }
        
        requirement fuelEfficiencyReq {
            doc /* 燃油效率要求 */
            subject engine : Engine;
            
            constraint {
                engine.fuelConsumption <= 8.0 // L/100km
            }
        }
    }
    
    // 用例定义
    use case def DrivingUseCase {
        doc /* 驾驶用例 */
        
        objective {
            doc /* 安全高效地驾驶车辆 */
        }
        
        actor driver : Driver;
        subject vehicle : Vehicle;
        
        action startEngine {
            doc /* 启动引擎 */
        }
        
        action accelerate {
            doc /* 加速 */
        }
        
        action brake {
            doc /* 刹车 */
        }
    }
    
    // 状态机定义
    state def VehicleState {
        entry action initializeVehicle;
        
        state off {
            transition startTransition
                first off
                accept StartSignal
                do startEngine
                then running;
        }
        
        state running {
            state idle;
            state moving;
            
            transition stopTransition
                first running
                accept StopSignal
                do stopEngine
                then off;
        }
    }
    
    // 分析定义
    analysis def PerformanceAnalysis {
        doc /* 车辆性能分析 */
        
        subject vehicle : Vehicle;
        
        return fuelEfficiency : Real;
        return acceleration : Real;
        return topSpeed : Real;
    }
    
    // 验证定义
    verification def PerformanceVerification {
        doc /* 性能验证 */
        
        subject vehicle : Vehicle;
        objective requirement performanceReq : PerformanceRequirement;
        
        method testAcceleration {
            doc /* 加速测试方法 */
        }
        
        method testFuelConsumption {
            doc /* 燃油消耗测试方法 */
        }
    }
}
