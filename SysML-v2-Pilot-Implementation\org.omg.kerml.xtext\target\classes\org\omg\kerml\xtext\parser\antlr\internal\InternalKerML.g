/*
 * generated by Xtext 2.34.0
 */
grammar InternalKerML;

options {
	superClass=AbstractInternalAntlrParser;
}

@lexer::header {
package org.omg.kerml.xtext.parser.antlr.internal;

// Hack: Use our own Lexer superclass by means of import. 
// Currently there is no other way to specify the superclass for the lexer.
import org.eclipse.xtext.parser.antlr.Lexer;
}

@parser::header {
package org.omg.kerml.xtext.parser.antlr.internal;

import org.eclipse.xtext.*;
import org.eclipse.xtext.parser.*;
import org.eclipse.xtext.parser.impl.*;
import org.eclipse.emf.ecore.util.EcoreUtil;
import org.eclipse.emf.ecore.EObject;
import org.eclipse.emf.common.util.Enumerator;
import org.eclipse.xtext.parser.antlr.AbstractInternalAntlrParser;
import org.eclipse.xtext.parser.antlr.XtextTokenStream;
import org.eclipse.xtext.parser.antlr.XtextTokenStream.HiddenTokens;
import org.eclipse.xtext.parser.antlr.AntlrDatatypeRuleToken;
import org.omg.kerml.xtext.services.KerMLGrammarAccess;

}

@parser::members {

 	private KerMLGrammarAccess grammarAccess;

    public InternalKerMLParser(TokenStream input, KerMLGrammarAccess grammarAccess) {
        this(input);
        this.grammarAccess = grammarAccess;
        registerRules(grammarAccess.getGrammar());
    }

    @Override
    protected String getFirstRuleName() {
    	return "RootNamespace";
   	}

   	@Override
   	protected KerMLGrammarAccess getGrammarAccess() {
   		return grammarAccess;
   	}

}

@rulecatch {
    catch (RecognitionException re) {
        recover(input,re);
        appendSkippedTokens();
    }
}

// Entry rule entryRuleRootNamespace
entryRuleRootNamespace returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getRootNamespaceRule()); }
	iv_ruleRootNamespace=ruleRootNamespace
	{ $current=$iv_ruleRootNamespace.current; }
	EOF;

// Rule RootNamespace
ruleRootNamespace returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				$current = forceCreateModelElement(
					grammarAccess.getRootNamespaceAccess().getNamespaceAction_0(),
					$current);
			}
		)
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getRootNamespaceRule());
				}
				newCompositeNode(grammarAccess.getRootNamespaceAccess().getNamespaceBodyElementParserRuleCall_1());
			}
			this_NamespaceBodyElement_1=ruleNamespaceBodyElement[$current]
			{
				$current = $this_NamespaceBodyElement_1.current;
				afterParserOrEnumRuleCall();
			}
		)*
	)
;


// Rule Identification
ruleIdentification[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='<'
			{
				newLeafNode(otherlv_0, grammarAccess.getIdentificationAccess().getLessThanSignKeyword_0_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getIdentificationAccess().getDeclaredShortNameNameParserRuleCall_0_1_0());
					}
					lv_declaredShortName_1_0=ruleName
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getIdentificationRule());
						}
						set(
							$current,
							"declaredShortName",
							lv_declaredShortName_1_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_2='>'
			{
				newLeafNode(otherlv_2, grammarAccess.getIdentificationAccess().getGreaterThanSignKeyword_0_2());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getIdentificationAccess().getDeclaredNameNameParserRuleCall_0_3_0());
					}
					lv_declaredName_3_0=ruleName
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getIdentificationRule());
						}
						set(
							$current,
							"declaredName",
							lv_declaredName_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
						afterParserOrEnumRuleCall();
					}
				)
			)?
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getIdentificationAccess().getDeclaredNameNameParserRuleCall_1_0());
				}
				lv_declaredName_4_0=ruleName
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getIdentificationRule());
					}
					set(
						$current,
						"declaredName",
						lv_declaredName_4_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule RelationshipBody
ruleRelationshipBody[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0=';'
		{
			newLeafNode(otherlv_0, grammarAccess.getRelationshipBodyAccess().getSemicolonKeyword_0());
		}
		    |
		(
			otherlv_1='{'
			{
				newLeafNode(otherlv_1, grammarAccess.getRelationshipBodyAccess().getLeftCurlyBracketKeyword_1_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getRelationshipBodyRule());
					}
					newCompositeNode(grammarAccess.getRelationshipBodyAccess().getRelationshipOwnedElementParserRuleCall_1_1());
				}
				this_RelationshipOwnedElement_2=ruleRelationshipOwnedElement[$current]
				{
					$current = $this_RelationshipOwnedElement_2.current;
					afterParserOrEnumRuleCall();
				}
			)*
			otherlv_3='}'
			{
				newLeafNode(otherlv_3, grammarAccess.getRelationshipBodyAccess().getRightCurlyBracketKeyword_1_2());
			}
		)
	)
;


// Rule RelationshipOwnedElement
ruleRelationshipOwnedElement[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getRelationshipOwnedElementAccess().getOwnedRelatedElementOwnedRelatedElementParserRuleCall_0_0());
				}
				lv_ownedRelatedElement_0_0=ruleOwnedRelatedElement
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getRelationshipOwnedElementRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_0_0,
						"org.omg.kerml.xtext.KerML.OwnedRelatedElement");
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getRelationshipOwnedElementAccess().getOwnedRelationshipOwnedAnnotationParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleOwnedAnnotation
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getRelationshipOwnedElementRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.OwnedAnnotation");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleOwnedRelatedElement
entryRuleOwnedRelatedElement returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedRelatedElementRule()); }
	iv_ruleOwnedRelatedElement=ruleOwnedRelatedElement
	{ $current=$iv_ruleOwnedRelatedElement.current; }
	EOF;

// Rule OwnedRelatedElement
ruleOwnedRelatedElement returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getOwnedRelatedElementAccess().getNonFeatureElementParserRuleCall_0());
		}
		this_NonFeatureElement_0=ruleNonFeatureElement
		{
			$current = $this_NonFeatureElement_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getOwnedRelatedElementAccess().getFeatureElementParserRuleCall_1());
		}
		this_FeatureElement_1=ruleFeatureElement
		{
			$current = $this_FeatureElement_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleDependency
entryRuleDependency returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getDependencyRule()); }
	iv_ruleDependency=ruleDependency
	{ $current=$iv_ruleDependency.current; }
	EOF;

// Rule Dependency
ruleDependency returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getDependencyAccess().getOwnedRelationshipPrefixMetadataAnnotationParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=rulePrefixMetadataAnnotation
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getDependencyRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataAnnotation");
					afterParserOrEnumRuleCall();
				}
			)
		)*
		otherlv_1='dependency'
		{
			newLeafNode(otherlv_1, grammarAccess.getDependencyAccess().getDependencyKeyword_1());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getDependencyRule());
					}
					newCompositeNode(grammarAccess.getDependencyAccess().getIdentificationParserRuleCall_2_0());
				}
				this_Identification_2=ruleIdentification[$current]
				{
					$current = $this_Identification_2.current;
					afterParserOrEnumRuleCall();
				}
			)?
			otherlv_3='from'
			{
				newLeafNode(otherlv_3, grammarAccess.getDependencyAccess().getFromKeyword_2_1());
			}
		)?
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getDependencyRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getDependencyAccess().getClientElementCrossReference_3_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_5=','
			{
				newLeafNode(otherlv_5, grammarAccess.getDependencyAccess().getCommaKeyword_4_0());
			}
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getDependencyRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getDependencyAccess().getClientElementCrossReference_4_1_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
		otherlv_7='to'
		{
			newLeafNode(otherlv_7, grammarAccess.getDependencyAccess().getToKeyword_5());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getDependencyRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getDependencyAccess().getSupplierElementCrossReference_6_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_9=','
			{
				newLeafNode(otherlv_9, grammarAccess.getDependencyAccess().getCommaKeyword_7_0());
			}
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getDependencyRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getDependencyAccess().getSupplierElementCrossReference_7_1_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getDependencyRule());
			}
			newCompositeNode(grammarAccess.getDependencyAccess().getRelationshipBodyParserRuleCall_8());
		}
		this_RelationshipBody_11=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_11.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleAnnotation
entryRuleAnnotation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAnnotationRule()); }
	iv_ruleAnnotation=ruleAnnotation
	{ $current=$iv_ruleAnnotation.current; }
	EOF;

// Rule Annotation
ruleAnnotation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getAnnotationRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getAnnotationAccess().getAnnotatedElementElementCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOwnedAnnotation
entryRuleOwnedAnnotation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedAnnotationRule()); }
	iv_ruleOwnedAnnotation=ruleOwnedAnnotation
	{ $current=$iv_ruleOwnedAnnotation.current; }
	EOF;

// Rule OwnedAnnotation
ruleOwnedAnnotation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedAnnotationAccess().getOwnedRelatedElementAnnotatingElementParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleAnnotatingElement
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedAnnotationRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.AnnotatingElement");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleAnnotatingElement
entryRuleAnnotatingElement returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAnnotatingElementRule()); }
	iv_ruleAnnotatingElement=ruleAnnotatingElement
	{ $current=$iv_ruleAnnotatingElement.current; }
	EOF;

// Rule AnnotatingElement
ruleAnnotatingElement returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getAnnotatingElementAccess().getCommentParserRuleCall_0());
		}
		this_Comment_0=ruleComment
		{
			$current = $this_Comment_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getAnnotatingElementAccess().getDocumentationParserRuleCall_1());
		}
		this_Documentation_1=ruleDocumentation
		{
			$current = $this_Documentation_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getAnnotatingElementAccess().getTextualRepresentationParserRuleCall_2());
		}
		this_TextualRepresentation_2=ruleTextualRepresentation
		{
			$current = $this_TextualRepresentation_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getAnnotatingElementAccess().getMetadataFeatureParserRuleCall_3());
		}
		this_MetadataFeature_3=ruleMetadataFeature
		{
			$current = $this_MetadataFeature_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleComment
entryRuleComment returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getCommentRule()); }
	iv_ruleComment=ruleComment
	{ $current=$iv_ruleComment.current; }
	EOF;

// Rule Comment
ruleComment returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='comment'
			{
				newLeafNode(otherlv_0, grammarAccess.getCommentAccess().getCommentKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getCommentRule());
					}
					newCompositeNode(grammarAccess.getCommentAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
			(
				otherlv_2='about'
				{
					newLeafNode(otherlv_2, grammarAccess.getCommentAccess().getAboutKeyword_0_2_0());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getCommentAccess().getOwnedRelationshipAnnotationParserRuleCall_0_2_1_0());
						}
						lv_ownedRelationship_3_0=ruleAnnotation
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getCommentRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_3_0,
								"org.omg.kerml.xtext.KerML.Annotation");
							afterParserOrEnumRuleCall();
						}
					)
				)
				(
					otherlv_4=','
					{
						newLeafNode(otherlv_4, grammarAccess.getCommentAccess().getCommaKeyword_0_2_2_0());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getCommentAccess().getOwnedRelationshipAnnotationParserRuleCall_0_2_2_1_0());
							}
							lv_ownedRelationship_5_0=ruleAnnotation
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getCommentRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_5_0,
									"org.omg.kerml.xtext.KerML.Annotation");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)*
			)?
		)?
		(
			otherlv_6='locale'
			{
				newLeafNode(otherlv_6, grammarAccess.getCommentAccess().getLocaleKeyword_1_0());
			}
			(
				(
					lv_locale_7_0=RULE_STRING_VALUE
					{
						newLeafNode(lv_locale_7_0, grammarAccess.getCommentAccess().getLocaleSTRING_VALUETerminalRuleCall_1_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getCommentRule());
						}
						setWithLastConsumed(
							$current,
							"locale",
							lv_locale_7_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.STRING_VALUE");
					}
				)
			)
		)?
		(
			(
				lv_body_8_0=RULE_REGULAR_COMMENT
				{
					newLeafNode(lv_body_8_0, grammarAccess.getCommentAccess().getBodyREGULAR_COMMENTTerminalRuleCall_2_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getCommentRule());
					}
					setWithLastConsumed(
						$current,
						"body",
						lv_body_8_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.REGULAR_COMMENT");
				}
			)
		)
	)
;

// Entry rule entryRuleDocumentation
entryRuleDocumentation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getDocumentationRule()); }
	iv_ruleDocumentation=ruleDocumentation
	{ $current=$iv_ruleDocumentation.current; }
	EOF;

// Rule Documentation
ruleDocumentation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='doc'
		{
			newLeafNode(otherlv_0, grammarAccess.getDocumentationAccess().getDocKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getDocumentationRule());
				}
				newCompositeNode(grammarAccess.getDocumentationAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			otherlv_2='locale'
			{
				newLeafNode(otherlv_2, grammarAccess.getDocumentationAccess().getLocaleKeyword_2_0());
			}
			(
				(
					lv_locale_3_0=RULE_STRING_VALUE
					{
						newLeafNode(lv_locale_3_0, grammarAccess.getDocumentationAccess().getLocaleSTRING_VALUETerminalRuleCall_2_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getDocumentationRule());
						}
						setWithLastConsumed(
							$current,
							"locale",
							lv_locale_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.STRING_VALUE");
					}
				)
			)
		)?
		(
			(
				lv_body_4_0=RULE_REGULAR_COMMENT
				{
					newLeafNode(lv_body_4_0, grammarAccess.getDocumentationAccess().getBodyREGULAR_COMMENTTerminalRuleCall_3_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getDocumentationRule());
					}
					setWithLastConsumed(
						$current,
						"body",
						lv_body_4_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.REGULAR_COMMENT");
				}
			)
		)
	)
;

// Entry rule entryRuleTextualRepresentation
entryRuleTextualRepresentation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTextualRepresentationRule()); }
	iv_ruleTextualRepresentation=ruleTextualRepresentation
	{ $current=$iv_ruleTextualRepresentation.current; }
	EOF;

// Rule TextualRepresentation
ruleTextualRepresentation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='rep'
			{
				newLeafNode(otherlv_0, grammarAccess.getTextualRepresentationAccess().getRepKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTextualRepresentationRule());
					}
					newCompositeNode(grammarAccess.getTextualRepresentationAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='language'
		{
			newLeafNode(otherlv_2, grammarAccess.getTextualRepresentationAccess().getLanguageKeyword_1());
		}
		(
			(
				lv_language_3_0=RULE_STRING_VALUE
				{
					newLeafNode(lv_language_3_0, grammarAccess.getTextualRepresentationAccess().getLanguageSTRING_VALUETerminalRuleCall_2_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTextualRepresentationRule());
					}
					setWithLastConsumed(
						$current,
						"language",
						lv_language_3_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.STRING_VALUE");
				}
			)
		)
		(
			(
				lv_body_4_0=RULE_REGULAR_COMMENT
				{
					newLeafNode(lv_body_4_0, grammarAccess.getTextualRepresentationAccess().getBodyREGULAR_COMMENTTerminalRuleCall_3_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTextualRepresentationRule());
					}
					setWithLastConsumed(
						$current,
						"body",
						lv_body_4_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.REGULAR_COMMENT");
				}
			)
		)
	)
;

// Entry rule entryRuleNamespace
entryRuleNamespace returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamespaceRule()); }
	iv_ruleNamespace=ruleNamespace
	{ $current=$iv_ruleNamespace.current; }
	EOF;

// Rule Namespace
ruleNamespace returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getNamespaceAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=rulePrefixMetadataMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamespaceRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
					afterParserOrEnumRuleCall();
				}
			)
		)*
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getNamespaceRule());
			}
			newCompositeNode(grammarAccess.getNamespaceAccess().getNamespaceDeclarationParserRuleCall_1());
		}
		this_NamespaceDeclaration_1=ruleNamespaceDeclaration[$current]
		{
			$current = $this_NamespaceDeclaration_1.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getNamespaceRule());
			}
			newCompositeNode(grammarAccess.getNamespaceAccess().getNamespaceBodyParserRuleCall_2());
		}
		this_NamespaceBody_2=ruleNamespaceBody[$current]
		{
			$current = $this_NamespaceBody_2.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule NamespaceDeclaration
ruleNamespaceDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='namespace'
		{
			newLeafNode(otherlv_0, grammarAccess.getNamespaceDeclarationAccess().getNamespaceKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getNamespaceDeclarationRule());
				}
				newCompositeNode(grammarAccess.getNamespaceDeclarationAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
	)
;


// Rule NamespaceBody
ruleNamespaceBody[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0=';'
		{
			newLeafNode(otherlv_0, grammarAccess.getNamespaceBodyAccess().getSemicolonKeyword_0());
		}
		    |
		(
			otherlv_1='{'
			{
				newLeafNode(otherlv_1, grammarAccess.getNamespaceBodyAccess().getLeftCurlyBracketKeyword_1_0());
			}
			(
				(
					(
						{
							newCompositeNode(grammarAccess.getNamespaceBodyAccess().getOwnedRelationshipNamespaceMemberParserRuleCall_1_1_0_0());
						}
						lv_ownedRelationship_2_0=ruleNamespaceMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getNamespaceBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_2_0,
								"org.omg.kerml.xtext.KerML.NamespaceMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getNamespaceBodyAccess().getOwnedRelationshipAliasMemberParserRuleCall_1_1_1_0());
						}
						lv_ownedRelationship_3_0=ruleAliasMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getNamespaceBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_3_0,
								"org.omg.kerml.xtext.KerML.AliasMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getNamespaceBodyAccess().getOwnedRelationshipImportParserRuleCall_1_1_2_0());
						}
						lv_ownedRelationship_4_0=ruleImport
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getNamespaceBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_4_0,
								"org.omg.kerml.xtext.KerML.Import");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)*
			otherlv_5='}'
			{
				newLeafNode(otherlv_5, grammarAccess.getNamespaceBodyAccess().getRightCurlyBracketKeyword_1_2());
			}
		)
	)
;


// Rule NamespaceBodyElement
ruleNamespaceBodyElement[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getNamespaceBodyElementAccess().getOwnedRelationshipNamespaceMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleNamespaceMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamespaceBodyElementRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.NamespaceMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getNamespaceBodyElementAccess().getOwnedRelationshipAliasMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleAliasMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamespaceBodyElementRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.AliasMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getNamespaceBodyElementAccess().getOwnedRelationshipImportParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleImport
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamespaceBodyElementRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.Import");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule MemberPrefix
ruleMemberPrefix[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getMemberPrefixAccess().getVisibilityVisibilityIndicatorEnumRuleCall_0());
			}
			lv_visibility_0_0=ruleVisibilityIndicator
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getMemberPrefixRule());
				}
				set(
					$current,
					"visibility",
					lv_visibility_0_0,
					"org.omg.kerml.xtext.KerML.VisibilityIndicator");
				afterParserOrEnumRuleCall();
			}
		)
	)?
;

// Entry rule entryRuleNamespaceMember
entryRuleNamespaceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamespaceMemberRule()); }
	iv_ruleNamespaceMember=ruleNamespaceMember
	{ $current=$iv_ruleNamespaceMember.current; }
	EOF;

// Rule NamespaceMember
ruleNamespaceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getNamespaceMemberAccess().getNonFeatureMemberParserRuleCall_0());
		}
		this_NonFeatureMember_0=ruleNonFeatureMember
		{
			$current = $this_NonFeatureMember_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNamespaceMemberAccess().getNamespaceFeatureMemberParserRuleCall_1());
		}
		this_NamespaceFeatureMember_1=ruleNamespaceFeatureMember
		{
			$current = $this_NamespaceFeatureMember_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleNonFeatureMember
entryRuleNonFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNonFeatureMemberRule()); }
	iv_ruleNonFeatureMember=ruleNonFeatureMember
	{ $current=$iv_ruleNonFeatureMember.current; }
	EOF;

// Rule NonFeatureMember
ruleNonFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getNonFeatureMemberRule());
			}
			newCompositeNode(grammarAccess.getNonFeatureMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getNonFeatureMemberAccess().getOwnedRelatedElementMemberElementParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleMemberElement
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNonFeatureMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.xtext.KerML.MemberElement");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleNamespaceFeatureMember
entryRuleNamespaceFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamespaceFeatureMemberRule()); }
	iv_ruleNamespaceFeatureMember=ruleNamespaceFeatureMember
	{ $current=$iv_ruleNamespaceFeatureMember.current; }
	EOF;

// Rule NamespaceFeatureMember
ruleNamespaceFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getNamespaceFeatureMemberRule());
			}
			newCompositeNode(grammarAccess.getNamespaceFeatureMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getNamespaceFeatureMemberAccess().getOwnedRelatedElementFeatureElementParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleFeatureElement
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamespaceFeatureMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.xtext.KerML.FeatureElement");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleAliasMember
entryRuleAliasMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAliasMemberRule()); }
	iv_ruleAliasMember=ruleAliasMember
	{ $current=$iv_ruleAliasMember.current; }
	EOF;

// Rule AliasMember
ruleAliasMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAliasMemberRule());
			}
			newCompositeNode(grammarAccess.getAliasMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='alias'
		{
			newLeafNode(otherlv_1, grammarAccess.getAliasMemberAccess().getAliasKeyword_1());
		}
		(
			otherlv_2='<'
			{
				newLeafNode(otherlv_2, grammarAccess.getAliasMemberAccess().getLessThanSignKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getAliasMemberAccess().getMemberShortNameNameParserRuleCall_2_1_0());
					}
					lv_memberShortName_3_0=ruleName
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getAliasMemberRule());
						}
						set(
							$current,
							"memberShortName",
							lv_memberShortName_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_4='>'
			{
				newLeafNode(otherlv_4, grammarAccess.getAliasMemberAccess().getGreaterThanSignKeyword_2_2());
			}
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getAliasMemberAccess().getMemberNameNameParserRuleCall_3_0());
				}
				lv_memberName_5_0=ruleName
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getAliasMemberRule());
					}
					set(
						$current,
						"memberName",
						lv_memberName_5_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
					afterParserOrEnumRuleCall();
				}
			)
		)?
		otherlv_6='for'
		{
			newLeafNode(otherlv_6, grammarAccess.getAliasMemberAccess().getForKeyword_4());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getAliasMemberRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getAliasMemberAccess().getMemberElementElementCrossReference_5_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAliasMemberRule());
			}
			newCompositeNode(grammarAccess.getAliasMemberAccess().getRelationshipBodyParserRuleCall_6());
		}
		this_RelationshipBody_8=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_8.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule ImportPrefix
ruleImportPrefix[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getImportPrefixAccess().getVisibilityVisibilityIndicatorEnumRuleCall_0_0());
				}
				lv_visibility_0_0=ruleVisibilityIndicator
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getImportPrefixRule());
					}
					set(
						$current,
						"visibility",
						lv_visibility_0_0,
						"org.omg.kerml.xtext.KerML.VisibilityIndicator");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_1='import'
		{
			newLeafNode(otherlv_1, grammarAccess.getImportPrefixAccess().getImportKeyword_1());
		}
		(
			(
				lv_isImportAll_2_0='all'
				{
					newLeafNode(lv_isImportAll_2_0, grammarAccess.getImportPrefixAccess().getIsImportAllAllKeyword_2_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getImportPrefixRule());
					}
					setWithLastConsumed($current, "isImportAll", lv_isImportAll_2_0 != null, "all");
				}
			)
		)?
	)
;

// Entry rule entryRuleImport
entryRuleImport returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImportRule()); }
	iv_ruleImport=ruleImport
	{ $current=$iv_ruleImport.current; }
	EOF;

// Rule Import
ruleImport returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getImportAccess().getMembershipImportParserRuleCall_0_0());
			}
			this_MembershipImport_0=ruleMembershipImport
			{
				$current = $this_MembershipImport_0.current;
				afterParserOrEnumRuleCall();
			}
			    |
			{
				newCompositeNode(grammarAccess.getImportAccess().getNamespaceImportParserRuleCall_0_1());
			}
			this_NamespaceImport_1=ruleNamespaceImport
			{
				$current = $this_NamespaceImport_1.current;
				afterParserOrEnumRuleCall();
			}
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getImportRule());
			}
			newCompositeNode(grammarAccess.getImportAccess().getRelationshipBodyParserRuleCall_1());
		}
		this_RelationshipBody_2=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_2.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleMembershipImport
entryRuleMembershipImport returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMembershipImportRule()); }
	iv_ruleMembershipImport=ruleMembershipImport
	{ $current=$iv_ruleMembershipImport.current; }
	EOF;

// Rule MembershipImport
ruleMembershipImport returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMembershipImportRule());
			}
			newCompositeNode(grammarAccess.getMembershipImportAccess().getImportPrefixParserRuleCall_0());
		}
		this_ImportPrefix_0=ruleImportPrefix[$current]
		{
			$current = $this_ImportPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMembershipImportRule());
			}
			newCompositeNode(grammarAccess.getMembershipImportAccess().getImportedMembershipParserRuleCall_1());
		}
		this_ImportedMembership_1=ruleImportedMembership[$current]
		{
			$current = $this_ImportedMembership_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule ImportedMembership
ruleImportedMembership[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getImportedMembershipRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getImportedMembershipAccess().getImportedMembershipMembershipCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1='::'
			{
				newLeafNode(otherlv_1, grammarAccess.getImportedMembershipAccess().getColonColonKeyword_1_0());
			}
			(
				(
					lv_isRecursive_2_0='**'
					{
						newLeafNode(lv_isRecursive_2_0, grammarAccess.getImportedMembershipAccess().getIsRecursiveAsteriskAsteriskKeyword_1_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getImportedMembershipRule());
						}
						setWithLastConsumed($current, "isRecursive", lv_isRecursive_2_0 != null, "**");
					}
				)
			)
		)?
	)
;

// Entry rule entryRuleNamespaceImport
entryRuleNamespaceImport returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamespaceImportRule()); }
	iv_ruleNamespaceImport=ruleNamespaceImport
	{ $current=$iv_ruleNamespaceImport.current; }
	EOF;

// Rule NamespaceImport
ruleNamespaceImport returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getNamespaceImportRule());
			}
			newCompositeNode(grammarAccess.getNamespaceImportAccess().getImportPrefixParserRuleCall_0());
		}
		this_ImportPrefix_0=ruleImportPrefix[$current]
		{
			$current = $this_ImportPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getNamespaceImportRule());
				}
				newCompositeNode(grammarAccess.getNamespaceImportAccess().getImportedNamespaceParserRuleCall_1_0());
			}
			this_ImportedNamespace_1=ruleImportedNamespace[$current]
			{
				$current = $this_ImportedNamespace_1.current;
				afterParserOrEnumRuleCall();
			}
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getNamespaceImportAccess().getOwnedRelatedElementFilterPackageParserRuleCall_1_1_0());
					}
					lv_ownedRelatedElement_2_0=ruleFilterPackage
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNamespaceImportRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_2_0,
							"org.omg.kerml.xtext.KerML.FilterPackage");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;


// Rule ImportedNamespace
ruleImportedNamespace[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getImportedNamespaceRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getImportedNamespaceAccess().getImportedNamespaceNamespaceCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_1='::'
		{
			newLeafNode(otherlv_1, grammarAccess.getImportedNamespaceAccess().getColonColonKeyword_1());
		}
		otherlv_2='*'
		{
			newLeafNode(otherlv_2, grammarAccess.getImportedNamespaceAccess().getAsteriskKeyword_2());
		}
		(
			otherlv_3='::'
			{
				newLeafNode(otherlv_3, grammarAccess.getImportedNamespaceAccess().getColonColonKeyword_3_0());
			}
			(
				(
					lv_isRecursive_4_0='**'
					{
						newLeafNode(lv_isRecursive_4_0, grammarAccess.getImportedNamespaceAccess().getIsRecursiveAsteriskAsteriskKeyword_3_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getImportedNamespaceRule());
						}
						setWithLastConsumed($current, "isRecursive", lv_isRecursive_4_0 != null, "**");
					}
				)
			)
		)?
	)
;

// Entry rule entryRuleFilterPackage
entryRuleFilterPackage returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFilterPackageRule()); }
	iv_ruleFilterPackage=ruleFilterPackage
	{ $current=$iv_ruleFilterPackage.current; }
	EOF;

// Rule FilterPackage
ruleFilterPackage returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getFilterPackageAccess().getOwnedRelationshipFilterPackageImportParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleFilterPackageImport
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFilterPackageRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.FilterPackageImport");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getFilterPackageAccess().getOwnedRelationshipFilterPackageMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleFilterPackageMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFilterPackageRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.FilterPackageMember");
					afterParserOrEnumRuleCall();
				}
			)
		)+
	)
;

// Entry rule entryRuleFilterPackageImport
entryRuleFilterPackageImport returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFilterPackageImportRule()); }
	iv_ruleFilterPackageImport=ruleFilterPackageImport
	{ $current=$iv_ruleFilterPackageImport.current; }
	EOF;

// Rule FilterPackageImport
ruleFilterPackageImport returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getFilterPackageImportAccess().getFilterPackageMembershipImportParserRuleCall_0());
		}
		this_FilterPackageMembershipImport_0=ruleFilterPackageMembershipImport
		{
			$current = $this_FilterPackageMembershipImport_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFilterPackageImportAccess().getFilterPackageNamespaceImportParserRuleCall_1());
		}
		this_FilterPackageNamespaceImport_1=ruleFilterPackageNamespaceImport
		{
			$current = $this_FilterPackageNamespaceImport_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleFilterPackageMembershipImport
entryRuleFilterPackageMembershipImport returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFilterPackageMembershipImportRule()); }
	iv_ruleFilterPackageMembershipImport=ruleFilterPackageMembershipImport
	{ $current=$iv_ruleFilterPackageMembershipImport.current; }
	EOF;

// Rule FilterPackageMembershipImport
ruleFilterPackageMembershipImport returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getFilterPackageMembershipImportRule());
		}
		newCompositeNode(grammarAccess.getFilterPackageMembershipImportAccess().getImportedMembershipParserRuleCall());
	}
	this_ImportedMembership_0=ruleImportedMembership[$current]
	{
		$current = $this_ImportedMembership_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleFilterPackageNamespaceImport
entryRuleFilterPackageNamespaceImport returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFilterPackageNamespaceImportRule()); }
	iv_ruleFilterPackageNamespaceImport=ruleFilterPackageNamespaceImport
	{ $current=$iv_ruleFilterPackageNamespaceImport.current; }
	EOF;

// Rule FilterPackageNamespaceImport
ruleFilterPackageNamespaceImport returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getFilterPackageNamespaceImportRule());
		}
		newCompositeNode(grammarAccess.getFilterPackageNamespaceImportAccess().getImportedNamespaceParserRuleCall());
	}
	this_ImportedNamespace_0=ruleImportedNamespace[$current]
	{
		$current = $this_ImportedNamespace_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleFilterPackageMember
entryRuleFilterPackageMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFilterPackageMemberRule()); }
	iv_ruleFilterPackageMember=ruleFilterPackageMember
	{ $current=$iv_ruleFilterPackageMember.current; }
	EOF;

// Rule FilterPackageMember
ruleFilterPackageMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getFilterPackageMemberAccess().getVisibilityFilterPackageMemberVisibilityEnumRuleCall_0_0());
				}
				lv_visibility_0_0=ruleFilterPackageMemberVisibility
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFilterPackageMemberRule());
					}
					set(
						$current,
						"visibility",
						lv_visibility_0_0,
						"org.omg.kerml.xtext.KerML.FilterPackageMemberVisibility");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getFilterPackageMemberAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedExpression
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFilterPackageMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_2=']'
		{
			newLeafNode(otherlv_2, grammarAccess.getFilterPackageMemberAccess().getRightSquareBracketKeyword_2());
		}
	)
;

// Entry rule entryRuleMemberElement
entryRuleMemberElement returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMemberElementRule()); }
	iv_ruleMemberElement=ruleMemberElement
	{ $current=$iv_ruleMemberElement.current; }
	EOF;

// Rule MemberElement
ruleMemberElement returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getMemberElementAccess().getAnnotatingElementParserRuleCall_0());
		}
		this_AnnotatingElement_0=ruleAnnotatingElement
		{
			$current = $this_AnnotatingElement_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getMemberElementAccess().getNonFeatureElementParserRuleCall_1());
		}
		this_NonFeatureElement_1=ruleNonFeatureElement
		{
			$current = $this_NonFeatureElement_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleNonFeatureElement
entryRuleNonFeatureElement returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNonFeatureElementRule()); }
	iv_ruleNonFeatureElement=ruleNonFeatureElement
	{ $current=$iv_ruleNonFeatureElement.current; }
	EOF;

// Rule NonFeatureElement
ruleNonFeatureElement returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getDependencyParserRuleCall_0());
		}
		this_Dependency_0=ruleDependency
		{
			$current = $this_Dependency_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getNamespaceParserRuleCall_1());
		}
		this_Namespace_1=ruleNamespace
		{
			$current = $this_Namespace_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getPackageParserRuleCall_2());
		}
		this_Package_2=rulePackage
		{
			$current = $this_Package_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getLibraryPackageParserRuleCall_3());
		}
		this_LibraryPackage_3=ruleLibraryPackage
		{
			$current = $this_LibraryPackage_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getMultiplicityParserRuleCall_4());
		}
		this_Multiplicity_4=ruleMultiplicity
		{
			$current = $this_Multiplicity_4.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getTypeParserRuleCall_5());
		}
		this_Type_5=ruleType
		{
			$current = $this_Type_5.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getClassifierParserRuleCall_6());
		}
		this_Classifier_6=ruleClassifier
		{
			$current = $this_Classifier_6.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getClassParserRuleCall_7());
		}
		this_Class_7=ruleClass
		{
			$current = $this_Class_7.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getStructureParserRuleCall_8());
		}
		this_Structure_8=ruleStructure
		{
			$current = $this_Structure_8.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getMetaclassParserRuleCall_9());
		}
		this_Metaclass_9=ruleMetaclass
		{
			$current = $this_Metaclass_9.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getDataTypeParserRuleCall_10());
		}
		this_DataType_10=ruleDataType
		{
			$current = $this_DataType_10.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getAssociationParserRuleCall_11());
		}
		this_Association_11=ruleAssociation
		{
			$current = $this_Association_11.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getAssociationStructureParserRuleCall_12());
		}
		this_AssociationStructure_12=ruleAssociationStructure
		{
			$current = $this_AssociationStructure_12.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getInteractionParserRuleCall_13());
		}
		this_Interaction_13=ruleInteraction
		{
			$current = $this_Interaction_13.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getBehaviorParserRuleCall_14());
		}
		this_Behavior_14=ruleBehavior
		{
			$current = $this_Behavior_14.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getFunctionParserRuleCall_15());
		}
		this_Function_15=ruleFunction
		{
			$current = $this_Function_15.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getPredicateParserRuleCall_16());
		}
		this_Predicate_16=rulePredicate
		{
			$current = $this_Predicate_16.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getSpecializationParserRuleCall_17());
		}
		this_Specialization_17=ruleSpecialization
		{
			$current = $this_Specialization_17.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getConjugationParserRuleCall_18());
		}
		this_Conjugation_18=ruleConjugation
		{
			$current = $this_Conjugation_18.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getFeatureTypingParserRuleCall_19());
		}
		this_FeatureTyping_19=ruleFeatureTyping
		{
			$current = $this_FeatureTyping_19.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getSubclassificationParserRuleCall_20());
		}
		this_Subclassification_20=ruleSubclassification
		{
			$current = $this_Subclassification_20.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getDisjoiningParserRuleCall_21());
		}
		this_Disjoining_21=ruleDisjoining
		{
			$current = $this_Disjoining_21.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getFeatureInvertingParserRuleCall_22());
		}
		this_FeatureInverting_22=ruleFeatureInverting
		{
			$current = $this_FeatureInverting_22.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getSubsettingParserRuleCall_23());
		}
		this_Subsetting_23=ruleSubsetting
		{
			$current = $this_Subsetting_23.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getRedefinitionParserRuleCall_24());
		}
		this_Redefinition_24=ruleRedefinition
		{
			$current = $this_Redefinition_24.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getNonFeatureElementAccess().getTypeFeaturingParserRuleCall_25());
		}
		this_TypeFeaturing_25=ruleTypeFeaturing
		{
			$current = $this_TypeFeaturing_25.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleFeatureElement
entryRuleFeatureElement returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureElementRule()); }
	iv_ruleFeatureElement=ruleFeatureElement
	{ $current=$iv_ruleFeatureElement.current; }
	EOF;

// Rule FeatureElement
ruleFeatureElement returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getFeatureParserRuleCall_0());
		}
		this_Feature_0=ruleFeature
		{
			$current = $this_Feature_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getStepParserRuleCall_1());
		}
		this_Step_1=ruleStep
		{
			$current = $this_Step_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getExpressionParserRuleCall_2());
		}
		this_Expression_2=ruleExpression
		{
			$current = $this_Expression_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getBooleanExpressionParserRuleCall_3());
		}
		this_BooleanExpression_3=ruleBooleanExpression
		{
			$current = $this_BooleanExpression_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getInvariantParserRuleCall_4());
		}
		this_Invariant_4=ruleInvariant
		{
			$current = $this_Invariant_4.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getConnectorParserRuleCall_5());
		}
		this_Connector_5=ruleConnector
		{
			$current = $this_Connector_5.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getBindingConnectorParserRuleCall_6());
		}
		this_BindingConnector_6=ruleBindingConnector
		{
			$current = $this_BindingConnector_6.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getSuccessionParserRuleCall_7());
		}
		this_Succession_7=ruleSuccession
		{
			$current = $this_Succession_7.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getFlowParserRuleCall_8());
		}
		this_Flow_8=ruleFlow
		{
			$current = $this_Flow_8.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureElementAccess().getSuccessionFlowParserRuleCall_9());
		}
		this_SuccessionFlow_9=ruleSuccessionFlow
		{
			$current = $this_SuccessionFlow_9.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRulePackage
entryRulePackage returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPackageRule()); }
	iv_rulePackage=rulePackage
	{ $current=$iv_rulePackage.current; }
	EOF;

// Rule Package
rulePackage returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getPackageAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=rulePrefixMetadataMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getPackageRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
					afterParserOrEnumRuleCall();
				}
			)
		)*
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getPackageRule());
			}
			newCompositeNode(grammarAccess.getPackageAccess().getPackageDeclarationParserRuleCall_1());
		}
		this_PackageDeclaration_1=rulePackageDeclaration[$current]
		{
			$current = $this_PackageDeclaration_1.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getPackageRule());
			}
			newCompositeNode(grammarAccess.getPackageAccess().getPackageBodyParserRuleCall_2());
		}
		this_PackageBody_2=rulePackageBody[$current]
		{
			$current = $this_PackageBody_2.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleLibraryPackage
entryRuleLibraryPackage returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLibraryPackageRule()); }
	iv_ruleLibraryPackage=ruleLibraryPackage
	{ $current=$iv_ruleLibraryPackage.current; }
	EOF;

// Rule LibraryPackage
ruleLibraryPackage returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				lv_isStandard_0_0='standard'
				{
					newLeafNode(lv_isStandard_0_0, grammarAccess.getLibraryPackageAccess().getIsStandardStandardKeyword_0_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getLibraryPackageRule());
					}
					setWithLastConsumed($current, "isStandard", lv_isStandard_0_0 != null, "standard");
				}
			)
		)?
		otherlv_1='library'
		{
			newLeafNode(otherlv_1, grammarAccess.getLibraryPackageAccess().getLibraryKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getLibraryPackageAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=rulePrefixMetadataMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getLibraryPackageRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
					afterParserOrEnumRuleCall();
				}
			)
		)*
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getLibraryPackageRule());
			}
			newCompositeNode(grammarAccess.getLibraryPackageAccess().getPackageDeclarationParserRuleCall_3());
		}
		this_PackageDeclaration_3=rulePackageDeclaration[$current]
		{
			$current = $this_PackageDeclaration_3.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getLibraryPackageRule());
			}
			newCompositeNode(grammarAccess.getLibraryPackageAccess().getPackageBodyParserRuleCall_4());
		}
		this_PackageBody_4=rulePackageBody[$current]
		{
			$current = $this_PackageBody_4.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule PackageDeclaration
rulePackageDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='package'
		{
			newLeafNode(otherlv_0, grammarAccess.getPackageDeclarationAccess().getPackageKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getPackageDeclarationRule());
				}
				newCompositeNode(grammarAccess.getPackageDeclarationAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
	)
;


// Rule PackageBody
rulePackageBody[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0=';'
		{
			newLeafNode(otherlv_0, grammarAccess.getPackageBodyAccess().getSemicolonKeyword_0());
		}
		    |
		(
			otherlv_1='{'
			{
				newLeafNode(otherlv_1, grammarAccess.getPackageBodyAccess().getLeftCurlyBracketKeyword_1_0());
			}
			(
				(
					(
						{
							newCompositeNode(grammarAccess.getPackageBodyAccess().getOwnedRelationshipNamespaceMemberParserRuleCall_1_1_0_0());
						}
						lv_ownedRelationship_2_0=ruleNamespaceMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getPackageBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_2_0,
								"org.omg.kerml.xtext.KerML.NamespaceMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getPackageBodyAccess().getOwnedRelationshipElementFilterMemberParserRuleCall_1_1_1_0());
						}
						lv_ownedRelationship_3_0=ruleElementFilterMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getPackageBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_3_0,
								"org.omg.kerml.xtext.KerML.ElementFilterMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getPackageBodyAccess().getOwnedRelationshipAliasMemberParserRuleCall_1_1_2_0());
						}
						lv_ownedRelationship_4_0=ruleAliasMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getPackageBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_4_0,
								"org.omg.kerml.xtext.KerML.AliasMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getPackageBodyAccess().getOwnedRelationshipImportParserRuleCall_1_1_3_0());
						}
						lv_ownedRelationship_5_0=ruleImport
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getPackageBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_5_0,
								"org.omg.kerml.xtext.KerML.Import");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)*
			otherlv_6='}'
			{
				newLeafNode(otherlv_6, grammarAccess.getPackageBodyAccess().getRightCurlyBracketKeyword_1_2());
			}
		)
	)
;

// Entry rule entryRuleElementFilterMember
entryRuleElementFilterMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getElementFilterMemberRule()); }
	iv_ruleElementFilterMember=ruleElementFilterMember
	{ $current=$iv_ruleElementFilterMember.current; }
	EOF;

// Rule ElementFilterMember
ruleElementFilterMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getElementFilterMemberRule());
			}
			newCompositeNode(grammarAccess.getElementFilterMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='filter'
		{
			newLeafNode(otherlv_1, grammarAccess.getElementFilterMemberAccess().getFilterKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getElementFilterMemberAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_2_0());
				}
				lv_ownedRelatedElement_2_0=ruleOwnedExpression
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getElementFilterMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_2_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_3=';'
		{
			newLeafNode(otherlv_3, grammarAccess.getElementFilterMemberAccess().getSemicolonKeyword_3());
		}
	)
;


// Rule TypePrefix
ruleTypePrefix[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				lv_isAbstract_0_0='abstract'
				{
					newLeafNode(lv_isAbstract_0_0, grammarAccess.getTypePrefixAccess().getIsAbstractAbstractKeyword_0_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTypePrefixRule());
					}
					setWithLastConsumed($current, "isAbstract", lv_isAbstract_0_0 != null, "abstract");
				}
			)
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getTypePrefixAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=rulePrefixMetadataMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getTypePrefixRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
					afterParserOrEnumRuleCall();
				}
			)
		)*
	)
;

// Entry rule entryRuleType
entryRuleType returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeRule()); }
	iv_ruleType=ruleType
	{ $current=$iv_ruleType.current; }
	EOF;

// Rule Type
ruleType returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRule());
			}
			newCompositeNode(grammarAccess.getTypeAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='type'
		{
			newLeafNode(otherlv_1, grammarAccess.getTypeAccess().getTypeKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRule());
			}
			newCompositeNode(grammarAccess.getTypeAccess().getTypeDeclarationParserRuleCall_2());
		}
		this_TypeDeclaration_2=ruleTypeDeclaration[$current]
		{
			$current = $this_TypeDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRule());
			}
			newCompositeNode(grammarAccess.getTypeAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule TypeDeclaration
ruleTypeDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				lv_isSufficient_0_0='all'
				{
					newLeafNode(lv_isSufficient_0_0, grammarAccess.getTypeDeclarationAccess().getIsSufficientAllKeyword_0_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTypeDeclarationRule());
					}
					setWithLastConsumed($current, "isSufficient", lv_isSufficient_0_0 != null, "all");
				}
			)
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getTypeDeclarationRule());
				}
				newCompositeNode(grammarAccess.getTypeDeclarationAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getTypeDeclarationAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedMultiplicity
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getTypeDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
					afterParserOrEnumRuleCall();
				}
			)
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getTypeDeclarationRule());
				}
				newCompositeNode(grammarAccess.getTypeDeclarationAccess().getSpecializationPartParserRuleCall_3_0());
			}
			this_SpecializationPart_3=ruleSpecializationPart[$current]
			{
				$current = $this_SpecializationPart_3.current;
				afterParserOrEnumRuleCall();
			}
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getTypeDeclarationRule());
				}
				newCompositeNode(grammarAccess.getTypeDeclarationAccess().getConjugationPartParserRuleCall_3_1());
			}
			this_ConjugationPart_4=ruleConjugationPart[$current]
			{
				$current = $this_ConjugationPart_4.current;
				afterParserOrEnumRuleCall();
			}
		)
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getTypeDeclarationRule());
				}
				newCompositeNode(grammarAccess.getTypeDeclarationAccess().getTypeRelationshipPartParserRuleCall_4());
			}
			this_TypeRelationshipPart_5=ruleTypeRelationshipPart[$current]
			{
				$current = $this_TypeRelationshipPart_5.current;
				afterParserOrEnumRuleCall();
			}
		)*
	)
;


// Rule SpecializationPart
ruleSpecializationPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0=':>'
			{
				newLeafNode(otherlv_0, grammarAccess.getSpecializationPartAccess().getColonGreaterThanSignKeyword_0_0());
			}
			    |
			otherlv_1='specializes'
			{
				newLeafNode(otherlv_1, grammarAccess.getSpecializationPartAccess().getSpecializesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getSpecializationPartAccess().getOwnedRelationshipOwnedSpecializationParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedSpecialization
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getSpecializationPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedSpecialization");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_3=','
			{
				newLeafNode(otherlv_3, grammarAccess.getSpecializationPartAccess().getCommaKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getSpecializationPartAccess().getOwnedRelationshipOwnedSpecializationParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_4_0=ruleOwnedSpecialization
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSpecializationPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_4_0,
							"org.omg.kerml.xtext.KerML.OwnedSpecialization");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule ConjugationPart
ruleConjugationPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='~'
			{
				newLeafNode(otherlv_0, grammarAccess.getConjugationPartAccess().getTildeKeyword_0_0());
			}
			    |
			otherlv_1='conjugates'
			{
				newLeafNode(otherlv_1, grammarAccess.getConjugationPartAccess().getConjugatesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getConjugationPartAccess().getOwnedRelationshipOwnedConjugationParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedConjugation
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConjugationPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedConjugation");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule TypeRelationshipPart
ruleTypeRelationshipPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getTypeRelationshipPartAccess().getDisjoiningPartParserRuleCall_0());
		}
		this_DisjoiningPart_0=ruleDisjoiningPart[$current]
		{
			$current = $this_DisjoiningPart_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getTypeRelationshipPartAccess().getUnioningPartParserRuleCall_1());
		}
		this_UnioningPart_1=ruleUnioningPart[$current]
		{
			$current = $this_UnioningPart_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getTypeRelationshipPartAccess().getIntersectingPartParserRuleCall_2());
		}
		this_IntersectingPart_2=ruleIntersectingPart[$current]
		{
			$current = $this_IntersectingPart_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getTypeRelationshipPartAccess().getDifferencingPartParserRuleCall_3());
		}
		this_DifferencingPart_3=ruleDifferencingPart[$current]
		{
			$current = $this_DifferencingPart_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule DisjoiningPart
ruleDisjoiningPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='disjoint'
		{
			newLeafNode(otherlv_0, grammarAccess.getDisjoiningPartAccess().getDisjointKeyword_0());
		}
		otherlv_1='from'
		{
			newLeafNode(otherlv_1, grammarAccess.getDisjoiningPartAccess().getFromKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getDisjoiningPartAccess().getOwnedRelationshipOwnedDisjoiningParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedDisjoining
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getDisjoiningPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedDisjoining");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_3=','
			{
				newLeafNode(otherlv_3, grammarAccess.getDisjoiningPartAccess().getCommaKeyword_3_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getDisjoiningPartAccess().getOwnedRelationshipOwnedDisjoiningParserRuleCall_3_1_0());
					}
					lv_ownedRelationship_4_0=ruleOwnedDisjoining
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getDisjoiningPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_4_0,
							"org.omg.kerml.xtext.KerML.OwnedDisjoining");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule UnioningPart
ruleUnioningPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='unions'
		{
			newLeafNode(otherlv_0, grammarAccess.getUnioningPartAccess().getUnionsKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getUnioningPartAccess().getOwnedRelationshipUnioningParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleUnioning
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getUnioningPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.Unioning");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_2=','
			{
				newLeafNode(otherlv_2, grammarAccess.getUnioningPartAccess().getCommaKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getUnioningPartAccess().getOwnedRelationshipUnioningParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_3_0=ruleUnioning
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getUnioningPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.xtext.KerML.Unioning");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule IntersectingPart
ruleIntersectingPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='intersects'
		{
			newLeafNode(otherlv_0, grammarAccess.getIntersectingPartAccess().getIntersectsKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getIntersectingPartAccess().getOwnedRelationshipIntersectingParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleIntersecting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getIntersectingPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.Intersecting");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_2=','
			{
				newLeafNode(otherlv_2, grammarAccess.getIntersectingPartAccess().getCommaKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getIntersectingPartAccess().getOwnedRelationshipIntersectingParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_3_0=ruleIntersecting
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getIntersectingPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.xtext.KerML.Intersecting");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule DifferencingPart
ruleDifferencingPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='differences'
		{
			newLeafNode(otherlv_0, grammarAccess.getDifferencingPartAccess().getDifferencesKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getDifferencingPartAccess().getOwnedRelationshipDifferencingParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleDifferencing
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getDifferencingPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.Differencing");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_2=','
			{
				newLeafNode(otherlv_2, grammarAccess.getDifferencingPartAccess().getCommaKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getDifferencingPartAccess().getOwnedRelationshipDifferencingParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_3_0=ruleDifferencing
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getDifferencingPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.xtext.KerML.Differencing");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule TypeBody
ruleTypeBody[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0=';'
		{
			newLeafNode(otherlv_0, grammarAccess.getTypeBodyAccess().getSemicolonKeyword_0());
		}
		    |
		(
			otherlv_1='{'
			{
				newLeafNode(otherlv_1, grammarAccess.getTypeBodyAccess().getLeftCurlyBracketKeyword_1_0());
			}
			(
				(
					(
						{
							newCompositeNode(grammarAccess.getTypeBodyAccess().getOwnedRelationshipNonFeatureMemberParserRuleCall_1_1_0_0());
						}
						lv_ownedRelationship_2_0=ruleNonFeatureMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getTypeBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_2_0,
								"org.omg.kerml.xtext.KerML.NonFeatureMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getTypeBodyAccess().getOwnedRelationshipFeatureMemberParserRuleCall_1_1_1_0());
						}
						lv_ownedRelationship_3_0=ruleFeatureMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getTypeBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_3_0,
								"org.omg.kerml.xtext.KerML.FeatureMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getTypeBodyAccess().getOwnedRelationshipAliasMemberParserRuleCall_1_1_2_0());
						}
						lv_ownedRelationship_4_0=ruleAliasMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getTypeBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_4_0,
								"org.omg.kerml.xtext.KerML.AliasMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getTypeBodyAccess().getOwnedRelationshipImportParserRuleCall_1_1_3_0());
						}
						lv_ownedRelationship_5_0=ruleImport
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getTypeBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_5_0,
								"org.omg.kerml.xtext.KerML.Import");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)*
			otherlv_6='}'
			{
				newLeafNode(otherlv_6, grammarAccess.getTypeBodyAccess().getRightCurlyBracketKeyword_1_2());
			}
		)
	)
;

// Entry rule entryRuleFeatureMember
entryRuleFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureMemberRule()); }
	iv_ruleFeatureMember=ruleFeatureMember
	{ $current=$iv_ruleFeatureMember.current; }
	EOF;

// Rule FeatureMember
ruleFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getFeatureMemberAccess().getTypeFeatureMemberParserRuleCall_0());
		}
		this_TypeFeatureMember_0=ruleTypeFeatureMember
		{
			$current = $this_TypeFeatureMember_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getFeatureMemberAccess().getOwnedFeatureMemberParserRuleCall_1());
		}
		this_OwnedFeatureMember_1=ruleOwnedFeatureMember
		{
			$current = $this_OwnedFeatureMember_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleTypeFeatureMember
entryRuleTypeFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeFeatureMemberRule()); }
	iv_ruleTypeFeatureMember=ruleTypeFeatureMember
	{ $current=$iv_ruleTypeFeatureMember.current; }
	EOF;

// Rule TypeFeatureMember
ruleTypeFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeFeatureMemberRule());
			}
			newCompositeNode(grammarAccess.getTypeFeatureMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='member'
		{
			newLeafNode(otherlv_1, grammarAccess.getTypeFeatureMemberAccess().getMemberKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getTypeFeatureMemberAccess().getOwnedRelatedElementFeatureElementParserRuleCall_2_0());
				}
				lv_ownedRelatedElement_2_0=ruleFeatureElement
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getTypeFeatureMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_2_0,
						"org.omg.kerml.xtext.KerML.FeatureElement");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleOwnedFeatureMember
entryRuleOwnedFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureMemberRule()); }
	iv_ruleOwnedFeatureMember=ruleOwnedFeatureMember
	{ $current=$iv_ruleOwnedFeatureMember.current; }
	EOF;

// Rule OwnedFeatureMember
ruleOwnedFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getOwnedFeatureMemberRule());
			}
			newCompositeNode(grammarAccess.getOwnedFeatureMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedFeatureMemberAccess().getOwnedRelatedElementFeatureElementParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleFeatureElement
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedFeatureMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.xtext.KerML.FeatureElement");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleSpecialization
entryRuleSpecialization returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSpecializationRule()); }
	iv_ruleSpecialization=ruleSpecialization
	{ $current=$iv_ruleSpecialization.current; }
	EOF;

// Rule Specialization
ruleSpecialization returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='specialization'
			{
				newLeafNode(otherlv_0, grammarAccess.getSpecializationAccess().getSpecializationKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getSpecializationRule());
					}
					newCompositeNode(grammarAccess.getSpecializationAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='subtype'
		{
			newLeafNode(otherlv_2, grammarAccess.getSpecializationAccess().getSubtypeKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getSpecializationRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getSpecializationAccess().getSpecificTypeCrossReference_2_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getSpecializationAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_2_1_0());
					}
					lv_ownedRelatedElement_4_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSpecializationRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_4_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		(
			otherlv_5=':>'
			{
				newLeafNode(otherlv_5, grammarAccess.getSpecializationAccess().getColonGreaterThanSignKeyword_3_0());
			}
			    |
			otherlv_6='specializes'
			{
				newLeafNode(otherlv_6, grammarAccess.getSpecializationAccess().getSpecializesKeyword_3_1());
			}
		)
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getSpecializationRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getSpecializationAccess().getGeneralTypeCrossReference_4_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getSpecializationAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_4_1_0());
					}
					lv_ownedRelatedElement_8_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSpecializationRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_8_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSpecializationRule());
			}
			newCompositeNode(grammarAccess.getSpecializationAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_9=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_9.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedSpecialization
entryRuleOwnedSpecialization returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedSpecializationRule()); }
	iv_ruleOwnedSpecialization=ruleOwnedSpecialization
	{ $current=$iv_ruleOwnedSpecialization.current; }
	EOF;

// Rule OwnedSpecialization
ruleOwnedSpecialization returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedSpecializationRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedSpecializationAccess().getGeneralTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedSpecializationAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedSpecializationRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleConjugation
entryRuleConjugation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConjugationRule()); }
	iv_ruleConjugation=ruleConjugation
	{ $current=$iv_ruleConjugation.current; }
	EOF;

// Rule Conjugation
ruleConjugation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='conjugation'
			{
				newLeafNode(otherlv_0, grammarAccess.getConjugationAccess().getConjugationKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getConjugationRule());
					}
					newCompositeNode(grammarAccess.getConjugationAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='conjugate'
		{
			newLeafNode(otherlv_2, grammarAccess.getConjugationAccess().getConjugateKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getConjugationRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getConjugationAccess().getConjugatedTypeTypeCrossReference_2_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getConjugationAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_2_1_0());
					}
					lv_ownedRelatedElement_4_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConjugationRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_4_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		(
			otherlv_5='~'
			{
				newLeafNode(otherlv_5, grammarAccess.getConjugationAccess().getTildeKeyword_3_0());
			}
			    |
			otherlv_6='conjugates'
			{
				newLeafNode(otherlv_6, grammarAccess.getConjugationAccess().getConjugatesKeyword_3_1());
			}
		)
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getConjugationRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getConjugationAccess().getOriginalTypeTypeCrossReference_4_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getConjugationAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_4_1_0());
					}
					lv_ownedRelatedElement_8_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConjugationRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_8_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getConjugationRule());
			}
			newCompositeNode(grammarAccess.getConjugationAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_9=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_9.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedConjugation
entryRuleOwnedConjugation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedConjugationRule()); }
	iv_ruleOwnedConjugation=ruleOwnedConjugation
	{ $current=$iv_ruleOwnedConjugation.current; }
	EOF;

// Rule OwnedConjugation
ruleOwnedConjugation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedConjugationRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedConjugationAccess().getOriginalTypeTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedConjugationAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedConjugationRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleDisjoining
entryRuleDisjoining returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getDisjoiningRule()); }
	iv_ruleDisjoining=ruleDisjoining
	{ $current=$iv_ruleDisjoining.current; }
	EOF;

// Rule Disjoining
ruleDisjoining returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='disjoining'
			{
				newLeafNode(otherlv_0, grammarAccess.getDisjoiningAccess().getDisjoiningKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getDisjoiningRule());
					}
					newCompositeNode(grammarAccess.getDisjoiningAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='disjoint'
		{
			newLeafNode(otherlv_2, grammarAccess.getDisjoiningAccess().getDisjointKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getDisjoiningRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getDisjoiningAccess().getTypeDisjoinedTypeCrossReference_2_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getDisjoiningAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_2_1_0());
					}
					lv_ownedRelatedElement_4_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getDisjoiningRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_4_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		otherlv_5='from'
		{
			newLeafNode(otherlv_5, grammarAccess.getDisjoiningAccess().getFromKeyword_3());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getDisjoiningRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getDisjoiningAccess().getDisjoiningTypeTypeCrossReference_4_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getDisjoiningAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_4_1_0());
					}
					lv_ownedRelatedElement_7_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getDisjoiningRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_7_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getDisjoiningRule());
			}
			newCompositeNode(grammarAccess.getDisjoiningAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_8=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_8.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedDisjoining
entryRuleOwnedDisjoining returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedDisjoiningRule()); }
	iv_ruleOwnedDisjoining=ruleOwnedDisjoining
	{ $current=$iv_ruleOwnedDisjoining.current; }
	EOF;

// Rule OwnedDisjoining
ruleOwnedDisjoining returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedDisjoiningRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedDisjoiningAccess().getDisjoiningTypeTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedDisjoiningAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedDisjoiningRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleUnioning
entryRuleUnioning returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getUnioningRule()); }
	iv_ruleUnioning=ruleUnioning
	{ $current=$iv_ruleUnioning.current; }
	EOF;

// Rule Unioning
ruleUnioning returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getUnioningRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getUnioningAccess().getUnioningTypeTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getUnioningAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getUnioningRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleIntersecting
entryRuleIntersecting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getIntersectingRule()); }
	iv_ruleIntersecting=ruleIntersecting
	{ $current=$iv_ruleIntersecting.current; }
	EOF;

// Rule Intersecting
ruleIntersecting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getIntersectingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getIntersectingAccess().getIntersectingTypeTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getIntersectingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getIntersectingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleDifferencing
entryRuleDifferencing returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getDifferencingRule()); }
	iv_ruleDifferencing=ruleDifferencing
	{ $current=$iv_ruleDifferencing.current; }
	EOF;

// Rule Differencing
ruleDifferencing returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getDifferencingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getDifferencingAccess().getDifferencingTypeTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getDifferencingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getDifferencingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleClassifier
entryRuleClassifier returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getClassifierRule()); }
	iv_ruleClassifier=ruleClassifier
	{ $current=$iv_ruleClassifier.current; }
	EOF;

// Rule Classifier
ruleClassifier returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getClassifierRule());
			}
			newCompositeNode(grammarAccess.getClassifierAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='classifier'
		{
			newLeafNode(otherlv_1, grammarAccess.getClassifierAccess().getClassifierKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getClassifierRule());
			}
			newCompositeNode(grammarAccess.getClassifierAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getClassifierRule());
			}
			newCompositeNode(grammarAccess.getClassifierAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule ClassifierDeclaration
ruleClassifierDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				lv_isSufficient_0_0='all'
				{
					newLeafNode(lv_isSufficient_0_0, grammarAccess.getClassifierDeclarationAccess().getIsSufficientAllKeyword_0_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getClassifierDeclarationRule());
					}
					setWithLastConsumed($current, "isSufficient", lv_isSufficient_0_0 != null, "all");
				}
			)
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getClassifierDeclarationRule());
				}
				newCompositeNode(grammarAccess.getClassifierDeclarationAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getClassifierDeclarationAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedMultiplicity
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getClassifierDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
					afterParserOrEnumRuleCall();
				}
			)
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getClassifierDeclarationRule());
				}
				newCompositeNode(grammarAccess.getClassifierDeclarationAccess().getSuperclassingPartParserRuleCall_3_0());
			}
			this_SuperclassingPart_3=ruleSuperclassingPart[$current]
			{
				$current = $this_SuperclassingPart_3.current;
				afterParserOrEnumRuleCall();
			}
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getClassifierDeclarationRule());
				}
				newCompositeNode(grammarAccess.getClassifierDeclarationAccess().getClassifierConjugationPartParserRuleCall_3_1());
			}
			this_ClassifierConjugationPart_4=ruleClassifierConjugationPart[$current]
			{
				$current = $this_ClassifierConjugationPart_4.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getClassifierDeclarationRule());
				}
				newCompositeNode(grammarAccess.getClassifierDeclarationAccess().getTypeRelationshipPartParserRuleCall_4());
			}
			this_TypeRelationshipPart_5=ruleTypeRelationshipPart[$current]
			{
				$current = $this_TypeRelationshipPart_5.current;
				afterParserOrEnumRuleCall();
			}
		)*
	)
;


// Rule SuperclassingPart
ruleSuperclassingPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0=':>'
			{
				newLeafNode(otherlv_0, grammarAccess.getSuperclassingPartAccess().getColonGreaterThanSignKeyword_0_0());
			}
			    |
			otherlv_1='specializes'
			{
				newLeafNode(otherlv_1, grammarAccess.getSuperclassingPartAccess().getSpecializesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getSuperclassingPartAccess().getOwnedRelationshipOwnedsubclassificationParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedsubclassification
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getSuperclassingPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.Ownedsubclassification");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_3=','
			{
				newLeafNode(otherlv_3, grammarAccess.getSuperclassingPartAccess().getCommaKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getSuperclassingPartAccess().getOwnedRelationshipOwnedsubclassificationParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_4_0=ruleOwnedsubclassification
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSuperclassingPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_4_0,
							"org.omg.kerml.xtext.KerML.Ownedsubclassification");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule ClassifierConjugationPart
ruleClassifierConjugationPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='~'
			{
				newLeafNode(otherlv_0, grammarAccess.getClassifierConjugationPartAccess().getTildeKeyword_0_0());
			}
			    |
			otherlv_1='conjugates'
			{
				newLeafNode(otherlv_1, grammarAccess.getClassifierConjugationPartAccess().getConjugatesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getClassifierConjugationPartAccess().getOwnedRelationshipClassifierConjugationParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleClassifierConjugation
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getClassifierConjugationPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.ClassifierConjugation");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleSubclassification
entryRuleSubclassification returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSubclassificationRule()); }
	iv_ruleSubclassification=ruleSubclassification
	{ $current=$iv_ruleSubclassification.current; }
	EOF;

// Rule Subclassification
ruleSubclassification returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='specialization'
			{
				newLeafNode(otherlv_0, grammarAccess.getSubclassificationAccess().getSpecializationKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getSubclassificationRule());
					}
					newCompositeNode(grammarAccess.getSubclassificationAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='subclassifier'
		{
			newLeafNode(otherlv_2, grammarAccess.getSubclassificationAccess().getSubclassifierKeyword_1());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getSubclassificationRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getSubclassificationAccess().getSubclassifierClassifierCrossReference_2_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_4=':>'
			{
				newLeafNode(otherlv_4, grammarAccess.getSubclassificationAccess().getColonGreaterThanSignKeyword_3_0());
			}
			    |
			otherlv_5='specializes'
			{
				newLeafNode(otherlv_5, grammarAccess.getSubclassificationAccess().getSpecializesKeyword_3_1());
			}
		)
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getSubclassificationRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getSubclassificationAccess().getSuperclassifierClassifierCrossReference_4_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSubclassificationRule());
			}
			newCompositeNode(grammarAccess.getSubclassificationAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_7=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_7.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedsubclassification
entryRuleOwnedsubclassification returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedsubclassificationRule()); }
	iv_ruleOwnedsubclassification=ruleOwnedsubclassification
	{ $current=$iv_ruleOwnedsubclassification.current; }
	EOF;

// Rule Ownedsubclassification
ruleOwnedsubclassification returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getOwnedsubclassificationRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getOwnedsubclassificationAccess().getSuperclassifierClassifierCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleClassifierConjugation
entryRuleClassifierConjugation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getClassifierConjugationRule()); }
	iv_ruleClassifierConjugation=ruleClassifierConjugation
	{ $current=$iv_ruleClassifierConjugation.current; }
	EOF;

// Rule ClassifierConjugation
ruleClassifierConjugation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getClassifierConjugationRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getClassifierConjugationAccess().getOriginalTypeClassifierCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule BasicFeaturePrefix
ruleBasicFeaturePrefix[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getBasicFeaturePrefixAccess().getDirectionFeatureDirectionEnumRuleCall_0_0());
				}
				lv_direction_0_0=ruleFeatureDirection
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getBasicFeaturePrefixRule());
					}
					set(
						$current,
						"direction",
						lv_direction_0_0,
						"org.omg.kerml.xtext.KerML.FeatureDirection");
					afterParserOrEnumRuleCall();
				}
			)
		)?
		(
			(
				lv_isDerived_1_0='derived'
				{
					newLeafNode(lv_isDerived_1_0, grammarAccess.getBasicFeaturePrefixAccess().getIsDerivedDerivedKeyword_1_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getBasicFeaturePrefixRule());
					}
					setWithLastConsumed($current, "isDerived", lv_isDerived_1_0 != null, "derived");
				}
			)
		)?
		(
			(
				lv_isAbstract_2_0='abstract'
				{
					newLeafNode(lv_isAbstract_2_0, grammarAccess.getBasicFeaturePrefixAccess().getIsAbstractAbstractKeyword_2_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getBasicFeaturePrefixRule());
					}
					setWithLastConsumed($current, "isAbstract", lv_isAbstract_2_0 != null, "abstract");
				}
			)
		)?
		(
			(
				(
					lv_isComposite_3_0='composite'
					{
						newLeafNode(lv_isComposite_3_0, grammarAccess.getBasicFeaturePrefixAccess().getIsCompositeCompositeKeyword_3_0_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getBasicFeaturePrefixRule());
						}
						setWithLastConsumed($current, "isComposite", lv_isComposite_3_0 != null, "composite");
					}
				)
			)
			    |
			(
				(
					lv_isPortion_4_0='portion'
					{
						newLeafNode(lv_isPortion_4_0, grammarAccess.getBasicFeaturePrefixAccess().getIsPortionPortionKeyword_3_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getBasicFeaturePrefixRule());
						}
						setWithLastConsumed($current, "isPortion", lv_isPortion_4_0 != null, "portion");
					}
				)
			)
		)?
		(
			(
				(
					lv_isVariable_5_0='var'
					{
						newLeafNode(lv_isVariable_5_0, grammarAccess.getBasicFeaturePrefixAccess().getIsVariableVarKeyword_4_0_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getBasicFeaturePrefixRule());
						}
						setWithLastConsumed($current, "isVariable", lv_isVariable_5_0 != null, "var");
					}
				)
			)
			    |
			(
				(
					lv_isConstant_6_0='const'
					{
						newLeafNode(lv_isConstant_6_0, grammarAccess.getBasicFeaturePrefixAccess().getIsConstantConstKeyword_4_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getBasicFeaturePrefixRule());
						}
						setWithLastConsumed($current, "isConstant", lv_isConstant_6_0 != null, "const");
					}
				)
			)
		)?
	)
;


// Rule FeaturePrefix
ruleFeaturePrefix[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(
					(
						lv_isEnd_0_0='end'
						{
							newLeafNode(lv_isEnd_0_0, grammarAccess.getFeaturePrefixAccess().getIsEndEndKeyword_0_0_0_0());
						}
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getFeaturePrefixRule());
							}
							setWithLastConsumed($current, "isEnd", lv_isEnd_0_0 != null, "end");
						}
					)
				)
				(
					(
						{
							newCompositeNode(grammarAccess.getFeaturePrefixAccess().getOwnedRelationshipOwnedCrossingFeatureMemberParserRuleCall_0_0_1_0());
						}
						lv_ownedRelationship_1_0=ruleOwnedCrossingFeatureMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getFeaturePrefixRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_1_0,
								"org.omg.kerml.xtext.KerML.OwnedCrossingFeatureMember");
							afterParserOrEnumRuleCall();
						}
					)
				)?
			)
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeaturePrefixRule());
				}
				newCompositeNode(grammarAccess.getFeaturePrefixAccess().getBasicFeaturePrefixParserRuleCall_0_1());
			}
			this_BasicFeaturePrefix_2=ruleBasicFeaturePrefix[$current]
			{
				$current = $this_BasicFeaturePrefix_2.current;
				afterParserOrEnumRuleCall();
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getFeaturePrefixAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_3_0=rulePrefixMetadataMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeaturePrefixRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_3_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
					afterParserOrEnumRuleCall();
				}
			)
		)*
	)
;

// Entry rule entryRuleOwnedCrossingFeatureMember
entryRuleOwnedCrossingFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedCrossingFeatureMemberRule()); }
	iv_ruleOwnedCrossingFeatureMember=ruleOwnedCrossingFeatureMember
	{ $current=$iv_ruleOwnedCrossingFeatureMember.current; }
	EOF;

// Rule OwnedCrossingFeatureMember
ruleOwnedCrossingFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedCrossingFeatureMemberAccess().getOwnedRelatedElementOwnedCrossingFeatureParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedCrossingFeature
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedCrossingFeatureMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.OwnedCrossingFeature");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOwnedCrossingFeature
entryRuleOwnedCrossingFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedCrossingFeatureRule()); }
	iv_ruleOwnedCrossingFeature=ruleOwnedCrossingFeature
	{ $current=$iv_ruleOwnedCrossingFeature.current; }
	EOF;

// Rule OwnedCrossingFeature
ruleOwnedCrossingFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getOwnedCrossingFeatureRule());
			}
			newCompositeNode(grammarAccess.getOwnedCrossingFeatureAccess().getBasicFeaturePrefixParserRuleCall_0());
		}
		this_BasicFeaturePrefix_0=ruleBasicFeaturePrefix[$current]
		{
			$current = $this_BasicFeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getOwnedCrossingFeatureRule());
			}
			newCompositeNode(grammarAccess.getOwnedCrossingFeatureAccess().getFeatureDeclarationParserRuleCall_1());
		}
		this_FeatureDeclaration_1=ruleFeatureDeclaration[$current]
		{
			$current = $this_FeatureDeclaration_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleFeature
entryRuleFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureRule()); }
	iv_ruleFeature=ruleFeature
	{ $current=$iv_ruleFeature.current; }
	EOF;

// Rule Feature
ruleFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureRule());
					}
					newCompositeNode(grammarAccess.getFeatureAccess().getFeaturePrefixParserRuleCall_0_0_0());
				}
				this_FeaturePrefix_0=ruleFeaturePrefix[$current]
				{
					$current = $this_FeaturePrefix_0.current;
					afterParserOrEnumRuleCall();
				}
				(
					otherlv_1='feature'
					{
						newLeafNode(otherlv_1, grammarAccess.getFeatureAccess().getFeatureKeyword_0_0_1_0());
					}
					    |
					(
						(
							{
								newCompositeNode(grammarAccess.getFeatureAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_0_0_1_1_0());
							}
							lv_ownedRelationship_2_0=rulePrefixMetadataMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getFeatureRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_2_0,
									"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureRule());
						}
						newCompositeNode(grammarAccess.getFeatureAccess().getFeatureDeclarationParserRuleCall_0_0_2());
					}
					this_FeatureDeclaration_3=ruleFeatureDeclaration[$current]
					{
						$current = $this_FeatureDeclaration_3.current;
						afterParserOrEnumRuleCall();
					}
				)?
			)
			    |
			(
				(
					(
						(
							lv_isEnd_4_0='end'
							{
								newLeafNode(lv_isEnd_4_0, grammarAccess.getFeatureAccess().getIsEndEndKeyword_0_1_0_0_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getFeatureRule());
								}
								setWithLastConsumed($current, "isEnd", lv_isEnd_4_0 != null, "end");
							}
						)
					)
					    |
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureRule());
						}
						newCompositeNode(grammarAccess.getFeatureAccess().getBasicFeaturePrefixParserRuleCall_0_1_0_1());
					}
					this_BasicFeaturePrefix_5=ruleBasicFeaturePrefix[$current]
					{
						$current = $this_BasicFeaturePrefix_5.current;
						afterParserOrEnumRuleCall();
					}
				)
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureRule());
					}
					newCompositeNode(grammarAccess.getFeatureAccess().getFeatureDeclarationParserRuleCall_0_1_1());
				}
				this_FeatureDeclaration_6=ruleFeatureDeclaration[$current]
				{
					$current = $this_FeatureDeclaration_6.current;
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureRule());
				}
				newCompositeNode(grammarAccess.getFeatureAccess().getValuePartParserRuleCall_1());
			}
			this_ValuePart_7=ruleValuePart[$current]
			{
				$current = $this_ValuePart_7.current;
				afterParserOrEnumRuleCall();
			}
		)?
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureRule());
			}
			newCompositeNode(grammarAccess.getFeatureAccess().getTypeBodyParserRuleCall_2());
		}
		this_TypeBody_8=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_8.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule FeatureDeclaration
ruleFeatureDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				lv_isSufficient_0_0='all'
				{
					newLeafNode(lv_isSufficient_0_0, grammarAccess.getFeatureDeclarationAccess().getIsSufficientAllKeyword_0_0());
				}
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
					}
					setWithLastConsumed($current, "isSufficient", lv_isSufficient_0_0 != null, "all");
				}
			)
		)?
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
					}
					newCompositeNode(grammarAccess.getFeatureDeclarationAccess().getIdentificationParserRuleCall_1_0_0());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
						}
						newCompositeNode(grammarAccess.getFeatureDeclarationAccess().getFeatureSpecializationPartParserRuleCall_1_0_1_0());
					}
					this_FeatureSpecializationPart_2=ruleFeatureSpecializationPart[$current]
					{
						$current = $this_FeatureSpecializationPart_2.current;
						afterParserOrEnumRuleCall();
					}
					    |
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
						}
						newCompositeNode(grammarAccess.getFeatureDeclarationAccess().getFeatureConjugationPartParserRuleCall_1_0_1_1());
					}
					this_FeatureConjugationPart_3=ruleFeatureConjugationPart[$current]
					{
						$current = $this_FeatureConjugationPart_3.current;
						afterParserOrEnumRuleCall();
					}
				)?
			)
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
				}
				newCompositeNode(grammarAccess.getFeatureDeclarationAccess().getFeatureSpecializationPartParserRuleCall_1_1());
			}
			this_FeatureSpecializationPart_4=ruleFeatureSpecializationPart[$current]
			{
				$current = $this_FeatureSpecializationPart_4.current;
				afterParserOrEnumRuleCall();
			}
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
				}
				newCompositeNode(grammarAccess.getFeatureDeclarationAccess().getFeatureConjugationPartParserRuleCall_1_2());
			}
			this_FeatureConjugationPart_5=ruleFeatureConjugationPart[$current]
			{
				$current = $this_FeatureConjugationPart_5.current;
				afterParserOrEnumRuleCall();
			}
		)
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureDeclarationRule());
				}
				newCompositeNode(grammarAccess.getFeatureDeclarationAccess().getFeatureRelationshipPartParserRuleCall_2());
			}
			this_FeatureRelationshipPart_6=ruleFeatureRelationshipPart[$current]
			{
				$current = $this_FeatureRelationshipPart_6.current;
				afterParserOrEnumRuleCall();
			}
		)*
	)
;


// Rule FeatureRelationshipPart
ruleFeatureRelationshipPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getFeatureRelationshipPartAccess().getTypeRelationshipPartParserRuleCall_0());
		}
		this_TypeRelationshipPart_0=ruleTypeRelationshipPart[$current]
		{
			$current = $this_TypeRelationshipPart_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getFeatureRelationshipPartAccess().getChainingPartParserRuleCall_1());
		}
		this_ChainingPart_1=ruleChainingPart[$current]
		{
			$current = $this_ChainingPart_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getFeatureRelationshipPartAccess().getInvertingPartParserRuleCall_2());
		}
		this_InvertingPart_2=ruleInvertingPart[$current]
		{
			$current = $this_InvertingPart_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureRelationshipPartRule());
			}
			newCompositeNode(grammarAccess.getFeatureRelationshipPartAccess().getTypeFeaturingPartParserRuleCall_3());
		}
		this_TypeFeaturingPart_3=ruleTypeFeaturingPart[$current]
		{
			$current = $this_TypeFeaturingPart_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule ChainingPart
ruleChainingPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='chains'
		{
			newLeafNode(otherlv_0, grammarAccess.getChainingPartAccess().getChainsKeyword_0());
		}
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getChainingPartAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_1_0_0());
					}
					lv_ownedRelationship_1_0=ruleOwnedFeatureChaining
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getChainingPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_1_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getChainingPartRule());
				}
				newCompositeNode(grammarAccess.getChainingPartAccess().getFeatureChainParserRuleCall_1_1());
			}
			this_FeatureChain_2=ruleFeatureChain[$current]
			{
				$current = $this_FeatureChain_2.current;
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule InvertingPart
ruleInvertingPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='inverse'
		{
			newLeafNode(otherlv_0, grammarAccess.getInvertingPartAccess().getInverseKeyword_0());
		}
		otherlv_1='of'
		{
			newLeafNode(otherlv_1, grammarAccess.getInvertingPartAccess().getOfKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getInvertingPartAccess().getOwnedRelationshipOwnedFeatureInvertingParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedFeatureInverting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getInvertingPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedFeatureInverting");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule TypeFeaturingPart
ruleTypeFeaturingPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='featured'
		{
			newLeafNode(otherlv_0, grammarAccess.getTypeFeaturingPartAccess().getFeaturedKeyword_0());
		}
		otherlv_1='by'
		{
			newLeafNode(otherlv_1, grammarAccess.getTypeFeaturingPartAccess().getByKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getTypeFeaturingPartAccess().getOwnedRelationshipOwnedTypeFeaturingParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedTypeFeaturing
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getTypeFeaturingPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedTypeFeaturing");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_3=','
			{
				newLeafNode(otherlv_3, grammarAccess.getTypeFeaturingPartAccess().getCommaKeyword_3_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getTypeFeaturingPartAccess().getOwnedRelationshipOwnedTypeFeaturingParserRuleCall_3_1_0());
					}
					lv_ownedRelationship_4_0=ruleOwnedTypeFeaturing
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getTypeFeaturingPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_4_0,
							"org.omg.kerml.xtext.KerML.OwnedTypeFeaturing");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule FeatureSpecializationPart
ruleFeatureSpecializationPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(':' | 'typed' | ':>' | 'subsets' | '::>' | 'references' | '=>' | 'crosses' | ':>>' | 'redefines')=>
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getFeatureSpecializationPartAccess().getFeatureSpecializationParserRuleCall_0_0());
				}
				this_FeatureSpecialization_0=ruleFeatureSpecialization[$current]
				{
					$current = $this_FeatureSpecialization_0.current;
					afterParserOrEnumRuleCall();
				}
			)+
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getFeatureSpecializationPartAccess().getMultiplicityPartParserRuleCall_0_1());
				}
				this_MultiplicityPart_1=ruleMultiplicityPart[$current]
				{
					$current = $this_MultiplicityPart_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getFeatureSpecializationPartAccess().getFeatureSpecializationParserRuleCall_0_2());
				}
				this_FeatureSpecialization_2=ruleFeatureSpecialization[$current]
				{
					$current = $this_FeatureSpecialization_2.current;
					afterParserOrEnumRuleCall();
				}
			)*
		)
		    |
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureSpecializationPartRule());
				}
				newCompositeNode(grammarAccess.getFeatureSpecializationPartAccess().getMultiplicityPartParserRuleCall_1_0());
			}
			this_MultiplicityPart_3=ruleMultiplicityPart[$current]
			{
				$current = $this_MultiplicityPart_3.current;
				afterParserOrEnumRuleCall();
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getFeatureSpecializationPartAccess().getFeatureSpecializationParserRuleCall_1_1());
				}
				this_FeatureSpecialization_4=ruleFeatureSpecialization[$current]
				{
					$current = $this_FeatureSpecialization_4.current;
					afterParserOrEnumRuleCall();
				}
			)*
		)
	)
;


// Rule MultiplicityPart
ruleMultiplicityPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getMultiplicityPartAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleOwnedMultiplicity
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMultiplicityPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getMultiplicityPartAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_1_0_0());
					}
					lv_ownedRelationship_1_0=ruleOwnedMultiplicity
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMultiplicityPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_1_0,
							"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
						afterParserOrEnumRuleCall();
					}
				)
			)?
			(
				(
					(
						(
							lv_isOrdered_2_0='ordered'
							{
								newLeafNode(lv_isOrdered_2_0, grammarAccess.getMultiplicityPartAccess().getIsOrderedOrderedKeyword_1_1_0_0_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getMultiplicityPartRule());
								}
								setWithLastConsumed($current, "isOrdered", lv_isOrdered_2_0 != null, "ordered");
							}
						)
					)
					(
						(
							lv_isNonunique_3_0='nonunique'
							{
								newLeafNode(lv_isNonunique_3_0, grammarAccess.getMultiplicityPartAccess().getIsNonuniqueNonuniqueKeyword_1_1_0_1_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getMultiplicityPartRule());
								}
								setWithLastConsumed($current, "isNonunique", lv_isNonunique_3_0 != null, "nonunique");
							}
						)
					)?
				)
				    |
				(
					(
						(
							lv_isNonunique_4_0='nonunique'
							{
								newLeafNode(lv_isNonunique_4_0, grammarAccess.getMultiplicityPartAccess().getIsNonuniqueNonuniqueKeyword_1_1_1_0_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getMultiplicityPartRule());
								}
								setWithLastConsumed($current, "isNonunique", lv_isNonunique_4_0 != null, "nonunique");
							}
						)
					)
					(
						(
							lv_isOrdered_5_0='ordered'
							{
								newLeafNode(lv_isOrdered_5_0, grammarAccess.getMultiplicityPartAccess().getIsOrderedOrderedKeyword_1_1_1_1_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getMultiplicityPartRule());
								}
								setWithLastConsumed($current, "isOrdered", lv_isOrdered_5_0 != null, "ordered");
							}
						)
					)?
				)
			)
		)
	)
;


// Rule FeatureSpecialization
ruleFeatureSpecialization[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureSpecializationRule());
			}
			newCompositeNode(grammarAccess.getFeatureSpecializationAccess().getTypingsParserRuleCall_0());
		}
		this_Typings_0=ruleTypings[$current]
		{
			$current = $this_Typings_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureSpecializationRule());
			}
			newCompositeNode(grammarAccess.getFeatureSpecializationAccess().getSubsettingsParserRuleCall_1());
		}
		this_Subsettings_1=ruleSubsettings[$current]
		{
			$current = $this_Subsettings_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureSpecializationRule());
			}
			newCompositeNode(grammarAccess.getFeatureSpecializationAccess().getReferencesParserRuleCall_2());
		}
		this_References_2=ruleReferences[$current]
		{
			$current = $this_References_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureSpecializationRule());
			}
			newCompositeNode(grammarAccess.getFeatureSpecializationAccess().getCrossingsParserRuleCall_3());
		}
		this_Crossings_3=ruleCrossings[$current]
		{
			$current = $this_Crossings_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureSpecializationRule());
			}
			newCompositeNode(grammarAccess.getFeatureSpecializationAccess().getRedefinitionsParserRuleCall_4());
		}
		this_Redefinitions_4=ruleRedefinitions[$current]
		{
			$current = $this_Redefinitions_4.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule Typings
ruleTypings[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypingsRule());
			}
			newCompositeNode(grammarAccess.getTypingsAccess().getTypedByParserRuleCall_0());
		}
		this_TypedBy_0=ruleTypedBy[$current]
		{
			$current = $this_TypedBy_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getTypingsAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getTypingsAccess().getOwnedRelationshipOwnedFeatureTypingParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleOwnedFeatureTyping
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getTypingsRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.xtext.KerML.OwnedFeatureTyping");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule TypedBy
ruleTypedBy[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0=':'
			{
				newLeafNode(otherlv_0, grammarAccess.getTypedByAccess().getColonKeyword_0_0());
			}
			    |
			(
				otherlv_1='typed'
				{
					newLeafNode(otherlv_1, grammarAccess.getTypedByAccess().getTypedKeyword_0_1_0());
				}
				otherlv_2='by'
				{
					newLeafNode(otherlv_2, grammarAccess.getTypedByAccess().getByKeyword_0_1_1());
				}
			)
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getTypedByAccess().getOwnedRelationshipOwnedFeatureTypingParserRuleCall_1_0());
				}
				lv_ownedRelationship_3_0=ruleOwnedFeatureTyping
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getTypedByRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_3_0,
						"org.omg.kerml.xtext.KerML.OwnedFeatureTyping");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule Subsettings
ruleSubsettings[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSubsettingsRule());
			}
			newCompositeNode(grammarAccess.getSubsettingsAccess().getSubsetsParserRuleCall_0());
		}
		this_Subsets_0=ruleSubsets[$current]
		{
			$current = $this_Subsets_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getSubsettingsAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getSubsettingsAccess().getOwnedRelationshipOwnedSubsettingParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleOwnedSubsetting
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSubsettingsRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.xtext.KerML.OwnedSubsetting");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule Subsets
ruleSubsets[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0=':>'
			{
				newLeafNode(otherlv_0, grammarAccess.getSubsetsAccess().getColonGreaterThanSignKeyword_0_0());
			}
			    |
			otherlv_1='subsets'
			{
				newLeafNode(otherlv_1, grammarAccess.getSubsetsAccess().getSubsetsKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getSubsetsAccess().getOwnedRelationshipOwnedSubsettingParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedSubsetting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getSubsetsRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedSubsetting");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule References
ruleReferences[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getReferencesAccess().getReferencesKeywordParserRuleCall_0());
		}
		ruleReferencesKeyword
		{
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getReferencesAccess().getOwnedRelationshipOwnedReferenceSubsettingParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleOwnedReferenceSubsetting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getReferencesRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.OwnedReferenceSubsetting");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleReferencesKeyword
entryRuleReferencesKeyword returns [String current=null]:
	{ newCompositeNode(grammarAccess.getReferencesKeywordRule()); }
	iv_ruleReferencesKeyword=ruleReferencesKeyword
	{ $current=$iv_ruleReferencesKeyword.current.getText(); }
	EOF;

// Rule ReferencesKeyword
ruleReferencesKeyword returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='::>'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getReferencesKeywordAccess().getColonColonGreaterThanSignKeyword_0());
		}
		    |
		kw='references'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getReferencesKeywordAccess().getReferencesKeyword_1());
		}
	)
;


// Rule Crossings
ruleCrossings[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='=>'
			{
				newLeafNode(otherlv_0, grammarAccess.getCrossingsAccess().getEqualsSignGreaterThanSignKeyword_0_0());
			}
			    |
			otherlv_1='crosses'
			{
				newLeafNode(otherlv_1, grammarAccess.getCrossingsAccess().getCrossesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getCrossingsAccess().getOwnedRelationshipOwnedCrossSubsettingParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedCrossSubsetting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getCrossingsRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedCrossSubsetting");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule Redefinitions
ruleRedefinitions[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getRedefinitionsRule());
			}
			newCompositeNode(grammarAccess.getRedefinitionsAccess().getRedefinesParserRuleCall_0());
		}
		this_Redefines_0=ruleRedefines[$current]
		{
			$current = $this_Redefines_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getRedefinitionsAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getRedefinitionsAccess().getOwnedRelationshipOwnedRedefinitionParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleOwnedRedefinition
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRedefinitionsRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.xtext.KerML.OwnedRedefinition");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;


// Rule Redefines
ruleRedefines[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0=':>>'
			{
				newLeafNode(otherlv_0, grammarAccess.getRedefinesAccess().getColonGreaterThanSignGreaterThanSignKeyword_0_0());
			}
			    |
			otherlv_1='redefines'
			{
				newLeafNode(otherlv_1, grammarAccess.getRedefinesAccess().getRedefinesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getRedefinesAccess().getOwnedRelationshipOwnedRedefinitionParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedRedefinition
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getRedefinesRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.OwnedRedefinition");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleFeatureInverting
entryRuleFeatureInverting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureInvertingRule()); }
	iv_ruleFeatureInverting=ruleFeatureInverting
	{ $current=$iv_ruleFeatureInverting.current; }
	EOF;

// Rule FeatureInverting
ruleFeatureInverting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='inverting'
			{
				newLeafNode(otherlv_0, grammarAccess.getFeatureInvertingAccess().getInvertingKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureInvertingRule());
					}
					newCompositeNode(grammarAccess.getFeatureInvertingAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='inverse'
		{
			newLeafNode(otherlv_2, grammarAccess.getFeatureInvertingAccess().getInverseKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureInvertingRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getFeatureInvertingAccess().getFeatureInvertedFeatureCrossReference_2_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureInvertingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_2_1_0());
					}
					lv_ownedRelatedElement_4_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureInvertingRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_4_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		otherlv_5='of'
		{
			newLeafNode(otherlv_5, grammarAccess.getFeatureInvertingAccess().getOfKeyword_3());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureInvertingRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getFeatureInvertingAccess().getInvertingFeatureFeatureCrossReference_4_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureInvertingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_4_1_0());
					}
					lv_ownedRelatedElement_7_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureInvertingRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_7_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureInvertingRule());
			}
			newCompositeNode(grammarAccess.getFeatureInvertingAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_8=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_8.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedFeatureInverting
entryRuleOwnedFeatureInverting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureInvertingRule()); }
	iv_ruleOwnedFeatureInverting=ruleOwnedFeatureInverting
	{ $current=$iv_ruleOwnedFeatureInverting.current; }
	EOF;

// Rule OwnedFeatureInverting
ruleOwnedFeatureInverting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedFeatureInvertingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedFeatureInvertingAccess().getInvertingFeatureFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedFeatureInvertingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedFeatureInvertingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleTypeFeaturing
entryRuleTypeFeaturing returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeFeaturingRule()); }
	iv_ruleTypeFeaturing=ruleTypeFeaturing
	{ $current=$iv_ruleTypeFeaturing.current; }
	EOF;

// Rule TypeFeaturing
ruleTypeFeaturing returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='featuring'
		{
			newLeafNode(otherlv_0, grammarAccess.getTypeFeaturingAccess().getFeaturingKeyword_0());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTypeFeaturingRule());
					}
					newCompositeNode(grammarAccess.getTypeFeaturingAccess().getIdentificationParserRuleCall_1_0());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
			otherlv_2='of'
			{
				newLeafNode(otherlv_2, grammarAccess.getTypeFeaturingAccess().getOfKeyword_1_1());
			}
		)?
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTypeFeaturingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getTypeFeaturingAccess().getFeatureOfTypeFeatureCrossReference_2_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_4='by'
		{
			newLeafNode(otherlv_4, grammarAccess.getTypeFeaturingAccess().getByKeyword_3());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getTypeFeaturingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getTypeFeaturingAccess().getFeaturingTypeFeatureCrossReference_4_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getTypeFeaturingRule());
			}
			newCompositeNode(grammarAccess.getTypeFeaturingAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_6=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_6.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedTypeFeaturing
entryRuleOwnedTypeFeaturing returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedTypeFeaturingRule()); }
	iv_ruleOwnedTypeFeaturing=ruleOwnedTypeFeaturing
	{ $current=$iv_ruleOwnedTypeFeaturing.current; }
	EOF;

// Rule OwnedTypeFeaturing
ruleOwnedTypeFeaturing returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getOwnedTypeFeaturingRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getOwnedTypeFeaturingAccess().getFeaturingTypeTypeCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFeatureTyping
entryRuleFeatureTyping returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureTypingRule()); }
	iv_ruleFeatureTyping=ruleFeatureTyping
	{ $current=$iv_ruleFeatureTyping.current; }
	EOF;

// Rule FeatureTyping
ruleFeatureTyping returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='specialization'
			{
				newLeafNode(otherlv_0, grammarAccess.getFeatureTypingAccess().getSpecializationKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureTypingRule());
					}
					newCompositeNode(grammarAccess.getFeatureTypingAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='typing'
		{
			newLeafNode(otherlv_2, grammarAccess.getFeatureTypingAccess().getTypingKeyword_1());
		}
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureTypingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getFeatureTypingAccess().getTypedFeatureFeatureCrossReference_2_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_4=':'
			{
				newLeafNode(otherlv_4, grammarAccess.getFeatureTypingAccess().getColonKeyword_3_0());
			}
			    |
			(
				otherlv_5='typed'
				{
					newLeafNode(otherlv_5, grammarAccess.getFeatureTypingAccess().getTypedKeyword_3_1_0());
				}
				otherlv_6='by'
				{
					newLeafNode(otherlv_6, grammarAccess.getFeatureTypingAccess().getByKeyword_3_1_1());
				}
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureTypingRule());
			}
			newCompositeNode(grammarAccess.getFeatureTypingAccess().getFeatureTypeParserRuleCall_4());
		}
		this_FeatureType_7=ruleFeatureType[$current]
		{
			$current = $this_FeatureType_7.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFeatureTypingRule());
			}
			newCompositeNode(grammarAccess.getFeatureTypingAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_8=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_8.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedFeatureTyping
entryRuleOwnedFeatureTyping returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureTypingRule()); }
	iv_ruleOwnedFeatureTyping=ruleOwnedFeatureTyping
	{ $current=$iv_ruleOwnedFeatureTyping.current; }
	EOF;

// Rule OwnedFeatureTyping
ruleOwnedFeatureTyping returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getOwnedFeatureTypingRule());
		}
		newCompositeNode(grammarAccess.getOwnedFeatureTypingAccess().getFeatureTypeParserRuleCall());
	}
	this_FeatureType_0=ruleFeatureType[$current]
	{
		$current = $this_FeatureType_0.current;
		afterParserOrEnumRuleCall();
	}
;


// Rule FeatureType
ruleFeatureType[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureTypeRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getFeatureTypeAccess().getTypeTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getFeatureTypeAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeatureTypeRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleSubsetting
entryRuleSubsetting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSubsettingRule()); }
	iv_ruleSubsetting=ruleSubsetting
	{ $current=$iv_ruleSubsetting.current; }
	EOF;

// Rule Subsetting
ruleSubsetting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='specialization'
			{
				newLeafNode(otherlv_0, grammarAccess.getSubsettingAccess().getSpecializationKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getSubsettingRule());
					}
					newCompositeNode(grammarAccess.getSubsettingAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='subset'
		{
			newLeafNode(otherlv_2, grammarAccess.getSubsettingAccess().getSubsetKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getSubsettingRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getSubsettingAccess().getSubsettingFeatureFeatureCrossReference_2_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getSubsettingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_2_1_0());
					}
					lv_ownedRelatedElement_4_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSubsettingRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_4_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		(
			otherlv_5=':>'
			{
				newLeafNode(otherlv_5, grammarAccess.getSubsettingAccess().getColonGreaterThanSignKeyword_3_0());
			}
			    |
			otherlv_6='subsets'
			{
				newLeafNode(otherlv_6, grammarAccess.getSubsettingAccess().getSubsetsKeyword_3_1());
			}
		)
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getSubsettingRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getSubsettingAccess().getSubsettedFeatureFeatureCrossReference_4_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getSubsettingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_4_1_0());
					}
					lv_ownedRelatedElement_8_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getSubsettingRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_8_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSubsettingRule());
			}
			newCompositeNode(grammarAccess.getSubsettingAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_9=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_9.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedSubsetting
entryRuleOwnedSubsetting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedSubsettingRule()); }
	iv_ruleOwnedSubsetting=ruleOwnedSubsetting
	{ $current=$iv_ruleOwnedSubsetting.current; }
	EOF;

// Rule OwnedSubsetting
ruleOwnedSubsetting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedSubsettingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedSubsettingAccess().getSubsettedFeatureFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedSubsettingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedSubsettingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleOwnedReferenceSubsetting
entryRuleOwnedReferenceSubsetting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedReferenceSubsettingRule()); }
	iv_ruleOwnedReferenceSubsetting=ruleOwnedReferenceSubsetting
	{ $current=$iv_ruleOwnedReferenceSubsetting.current; }
	EOF;

// Rule OwnedReferenceSubsetting
ruleOwnedReferenceSubsetting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedReferenceSubsettingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedReferenceSubsettingAccess().getReferencedFeatureFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedReferenceSubsettingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedReferenceSubsettingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleOwnedCrossSubsetting
entryRuleOwnedCrossSubsetting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedCrossSubsettingRule()); }
	iv_ruleOwnedCrossSubsetting=ruleOwnedCrossSubsetting
	{ $current=$iv_ruleOwnedCrossSubsetting.current; }
	EOF;

// Rule OwnedCrossSubsetting
ruleOwnedCrossSubsetting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedCrossSubsettingRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedCrossSubsettingAccess().getCrossedFeatureFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedCrossSubsettingAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedCrossSubsettingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleRedefinition
entryRuleRedefinition returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getRedefinitionRule()); }
	iv_ruleRedefinition=ruleRedefinition
	{ $current=$iv_ruleRedefinition.current; }
	EOF;

// Rule Redefinition
ruleRedefinition returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='specialization'
			{
				newLeafNode(otherlv_0, grammarAccess.getRedefinitionAccess().getSpecializationKeyword_0_0());
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getRedefinitionRule());
					}
					newCompositeNode(grammarAccess.getRedefinitionAccess().getIdentificationParserRuleCall_0_1());
				}
				this_Identification_1=ruleIdentification[$current]
				{
					$current = $this_Identification_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)?
		otherlv_2='redefinition'
		{
			newLeafNode(otherlv_2, grammarAccess.getRedefinitionAccess().getRedefinitionKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getRedefinitionRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getRedefinitionAccess().getRedefiningFeatureFeatureCrossReference_2_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getRedefinitionAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_2_1_0());
					}
					lv_ownedRelatedElement_4_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRedefinitionRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_4_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		(
			otherlv_5=':>>'
			{
				newLeafNode(otherlv_5, grammarAccess.getRedefinitionAccess().getColonGreaterThanSignGreaterThanSignKeyword_3_0());
			}
			    |
			otherlv_6='redefines'
			{
				newLeafNode(otherlv_6, grammarAccess.getRedefinitionAccess().getRedefinesKeyword_3_1());
			}
		)
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getRedefinitionRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getRedefinitionAccess().getRedefinedFeatureFeatureCrossReference_4_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getRedefinitionAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_4_1_0());
					}
					lv_ownedRelatedElement_8_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRedefinitionRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_8_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getRedefinitionRule());
			}
			newCompositeNode(grammarAccess.getRedefinitionAccess().getRelationshipBodyParserRuleCall_5());
		}
		this_RelationshipBody_9=ruleRelationshipBody[$current]
		{
			$current = $this_RelationshipBody_9.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedRedefinition
entryRuleOwnedRedefinition returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedRedefinitionRule()); }
	iv_ruleOwnedRedefinition=ruleOwnedRedefinition
	{ $current=$iv_ruleOwnedRedefinition.current; }
	EOF;

// Rule OwnedRedefinition
ruleOwnedRedefinition returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getOwnedRedefinitionRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getOwnedRedefinitionAccess().getRedefinedFeatureFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getOwnedRedefinitionAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedFeatureChain
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getOwnedRedefinitionRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule FeatureConjugationPart
ruleFeatureConjugationPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='~'
			{
				newLeafNode(otherlv_0, grammarAccess.getFeatureConjugationPartAccess().getTildeKeyword_0_0());
			}
			    |
			otherlv_1='conjugates'
			{
				newLeafNode(otherlv_1, grammarAccess.getFeatureConjugationPartAccess().getConjugatesKeyword_0_1());
			}
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getFeatureConjugationPartAccess().getOwnedRelationshipFeatureConjugationParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleFeatureConjugation
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeatureConjugationPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.FeatureConjugation");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleFeatureConjugation
entryRuleFeatureConjugation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureConjugationRule()); }
	iv_ruleFeatureConjugation=ruleFeatureConjugation
	{ $current=$iv_ruleFeatureConjugation.current; }
	EOF;

// Rule FeatureConjugation
ruleFeatureConjugation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureConjugationRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getFeatureConjugationAccess().getOriginalTypeFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule ValuePart
ruleValuePart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getValuePartAccess().getOwnedRelationshipFeatureValueParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleFeatureValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getValuePartRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.xtext.KerML.FeatureValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFeatureValue
entryRuleFeatureValue returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureValueRule()); }
	iv_ruleFeatureValue=ruleFeatureValue
	{ $current=$iv_ruleFeatureValue.current; }
	EOF;

// Rule FeatureValue
ruleFeatureValue returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='='
			{
				newLeafNode(otherlv_0, grammarAccess.getFeatureValueAccess().getEqualsSignKeyword_0_0());
			}
			    |
			(
				(
					lv_isInitial_1_0=':='
					{
						newLeafNode(lv_isInitial_1_0, grammarAccess.getFeatureValueAccess().getIsInitialColonEqualsSignKeyword_0_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFeatureValueRule());
						}
						setWithLastConsumed($current, "isInitial", lv_isInitial_1_0 != null, ":=");
					}
				)
			)
			    |
			(
				(
					(
						lv_isDefault_2_0='default'
						{
							newLeafNode(lv_isDefault_2_0, grammarAccess.getFeatureValueAccess().getIsDefaultDefaultKeyword_0_2_0_0());
						}
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getFeatureValueRule());
							}
							setWithLastConsumed($current, "isDefault", lv_isDefault_2_0 != null, "default");
						}
					)
				)
				(
					otherlv_3='='
					{
						newLeafNode(otherlv_3, grammarAccess.getFeatureValueAccess().getEqualsSignKeyword_0_2_1_0());
					}
					    |
					(
						(
							lv_isInitial_4_0=':='
							{
								newLeafNode(lv_isInitial_4_0, grammarAccess.getFeatureValueAccess().getIsInitialColonEqualsSignKeyword_0_2_1_1_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getFeatureValueRule());
								}
								setWithLastConsumed($current, "isInitial", lv_isInitial_4_0 != null, ":=");
							}
						)
					)
				)?
			)
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getFeatureValueAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_5_0=ruleOwnedExpression
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeatureValueRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_5_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleMultiplicity
entryRuleMultiplicity returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicityRule()); }
	iv_ruleMultiplicity=ruleMultiplicity
	{ $current=$iv_ruleMultiplicity.current; }
	EOF;

// Rule Multiplicity
ruleMultiplicity returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getMultiplicityAccess().getMultiplicitySubsetParserRuleCall_0());
		}
		this_MultiplicitySubset_0=ruleMultiplicitySubset
		{
			$current = $this_MultiplicitySubset_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getMultiplicityAccess().getMultiplicityRangeParserRuleCall_1());
		}
		this_MultiplicityRange_1=ruleMultiplicityRange
		{
			$current = $this_MultiplicityRange_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleMultiplicitySubset
entryRuleMultiplicitySubset returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicitySubsetRule()); }
	iv_ruleMultiplicitySubset=ruleMultiplicitySubset
	{ $current=$iv_ruleMultiplicitySubset.current; }
	EOF;

// Rule MultiplicitySubset
ruleMultiplicitySubset returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='multiplicity'
		{
			newLeafNode(otherlv_0, grammarAccess.getMultiplicitySubsetAccess().getMultiplicityKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getMultiplicitySubsetRule());
				}
				newCompositeNode(grammarAccess.getMultiplicitySubsetAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMultiplicitySubsetRule());
			}
			newCompositeNode(grammarAccess.getMultiplicitySubsetAccess().getSubsetsParserRuleCall_2());
		}
		this_Subsets_2=ruleSubsets[$current]
		{
			$current = $this_Subsets_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMultiplicitySubsetRule());
			}
			newCompositeNode(grammarAccess.getMultiplicitySubsetAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleMultiplicityRange
entryRuleMultiplicityRange returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicityRangeRule()); }
	iv_ruleMultiplicityRange=ruleMultiplicityRange
	{ $current=$iv_ruleMultiplicityRange.current; }
	EOF;

// Rule MultiplicityRange
ruleMultiplicityRange returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='multiplicity'
		{
			newLeafNode(otherlv_0, grammarAccess.getMultiplicityRangeAccess().getMultiplicityKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getMultiplicityRangeRule());
				}
				newCompositeNode(grammarAccess.getMultiplicityRangeAccess().getIdentificationParserRuleCall_1());
			}
			this_Identification_1=ruleIdentification[$current]
			{
				$current = $this_Identification_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMultiplicityRangeRule());
			}
			newCompositeNode(grammarAccess.getMultiplicityRangeAccess().getMultiplicityBoundsParserRuleCall_2());
		}
		this_MultiplicityBounds_2=ruleMultiplicityBounds[$current]
		{
			$current = $this_MultiplicityBounds_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMultiplicityRangeRule());
			}
			newCompositeNode(grammarAccess.getMultiplicityRangeAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleOwnedMultiplicity
entryRuleOwnedMultiplicity returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedMultiplicityRule()); }
	iv_ruleOwnedMultiplicity=ruleOwnedMultiplicity
	{ $current=$iv_ruleOwnedMultiplicity.current; }
	EOF;

// Rule OwnedMultiplicity
ruleOwnedMultiplicity returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedMultiplicityAccess().getOwnedRelatedElementOwnedMultiplicityRangeParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedMultiplicityRange
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedMultiplicityRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.OwnedMultiplicityRange");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOwnedMultiplicityRange
entryRuleOwnedMultiplicityRange returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedMultiplicityRangeRule()); }
	iv_ruleOwnedMultiplicityRange=ruleOwnedMultiplicityRange
	{ $current=$iv_ruleOwnedMultiplicityRange.current; }
	EOF;

// Rule OwnedMultiplicityRange
ruleOwnedMultiplicityRange returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getOwnedMultiplicityRangeRule());
		}
		newCompositeNode(grammarAccess.getOwnedMultiplicityRangeAccess().getMultiplicityBoundsParserRuleCall());
	}
	this_MultiplicityBounds_0=ruleMultiplicityBounds[$current]
	{
		$current = $this_MultiplicityBounds_0.current;
		afterParserOrEnumRuleCall();
	}
;


// Rule MultiplicityBounds
ruleMultiplicityBounds[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='['
		{
			newLeafNode(otherlv_0, grammarAccess.getMultiplicityBoundsAccess().getLeftSquareBracketKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getMultiplicityBoundsAccess().getOwnedRelationshipMultiplicityExpressionMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleMultiplicityExpressionMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMultiplicityBoundsRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.MultiplicityExpressionMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_2='..'
			{
				newLeafNode(otherlv_2, grammarAccess.getMultiplicityBoundsAccess().getFullStopFullStopKeyword_2_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getMultiplicityBoundsAccess().getOwnedRelationshipMultiplicityExpressionMemberParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_3_0=ruleMultiplicityExpressionMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMultiplicityBoundsRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.xtext.KerML.MultiplicityExpressionMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
		otherlv_4=']'
		{
			newLeafNode(otherlv_4, grammarAccess.getMultiplicityBoundsAccess().getRightSquareBracketKeyword_3());
		}
	)
;

// Entry rule entryRuleMultiplicityExpressionMember
entryRuleMultiplicityExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicityExpressionMemberRule()); }
	iv_ruleMultiplicityExpressionMember=ruleMultiplicityExpressionMember
	{ $current=$iv_ruleMultiplicityExpressionMember.current; }
	EOF;

// Rule MultiplicityExpressionMember
ruleMultiplicityExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getMultiplicityExpressionMemberAccess().getOwnedRelatedElementLiteralExpressionParserRuleCall_0_0());
				}
				lv_ownedRelatedElement_0_1=ruleLiteralExpression
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMultiplicityExpressionMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_0_1,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.LiteralExpression");
					afterParserOrEnumRuleCall();
				}
				    |
				{
					newCompositeNode(grammarAccess.getMultiplicityExpressionMemberAccess().getOwnedRelatedElementFeatureReferenceExpressionParserRuleCall_0_1());
				}
				lv_ownedRelatedElement_0_2=ruleFeatureReferenceExpression
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMultiplicityExpressionMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_0_2,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureReferenceExpression");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleDataType
entryRuleDataType returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getDataTypeRule()); }
	iv_ruleDataType=ruleDataType
	{ $current=$iv_ruleDataType.current; }
	EOF;

// Rule DataType
ruleDataType returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getDataTypeRule());
			}
			newCompositeNode(grammarAccess.getDataTypeAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='datatype'
		{
			newLeafNode(otherlv_1, grammarAccess.getDataTypeAccess().getDatatypeKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getDataTypeRule());
			}
			newCompositeNode(grammarAccess.getDataTypeAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getDataTypeRule());
			}
			newCompositeNode(grammarAccess.getDataTypeAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleClass
entryRuleClass returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getClassRule()); }
	iv_ruleClass=ruleClass
	{ $current=$iv_ruleClass.current; }
	EOF;

// Rule Class
ruleClass returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getClassRule());
			}
			newCompositeNode(grammarAccess.getClassAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='class'
		{
			newLeafNode(otherlv_1, grammarAccess.getClassAccess().getClassKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getClassRule());
			}
			newCompositeNode(grammarAccess.getClassAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getClassRule());
			}
			newCompositeNode(grammarAccess.getClassAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleStructure
entryRuleStructure returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getStructureRule()); }
	iv_ruleStructure=ruleStructure
	{ $current=$iv_ruleStructure.current; }
	EOF;

// Rule Structure
ruleStructure returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getStructureRule());
			}
			newCompositeNode(grammarAccess.getStructureAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='struct'
		{
			newLeafNode(otherlv_1, grammarAccess.getStructureAccess().getStructKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getStructureRule());
			}
			newCompositeNode(grammarAccess.getStructureAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getStructureRule());
			}
			newCompositeNode(grammarAccess.getStructureAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleAssociation
entryRuleAssociation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAssociationRule()); }
	iv_ruleAssociation=ruleAssociation
	{ $current=$iv_ruleAssociation.current; }
	EOF;

// Rule Association
ruleAssociation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAssociationRule());
			}
			newCompositeNode(grammarAccess.getAssociationAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='assoc'
		{
			newLeafNode(otherlv_1, grammarAccess.getAssociationAccess().getAssocKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAssociationRule());
			}
			newCompositeNode(grammarAccess.getAssociationAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAssociationRule());
			}
			newCompositeNode(grammarAccess.getAssociationAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleAssociationStructure
entryRuleAssociationStructure returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAssociationStructureRule()); }
	iv_ruleAssociationStructure=ruleAssociationStructure
	{ $current=$iv_ruleAssociationStructure.current; }
	EOF;

// Rule AssociationStructure
ruleAssociationStructure returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAssociationStructureRule());
			}
			newCompositeNode(grammarAccess.getAssociationStructureAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='assoc'
		{
			newLeafNode(otherlv_1, grammarAccess.getAssociationStructureAccess().getAssocKeyword_1());
		}
		otherlv_2='struct'
		{
			newLeafNode(otherlv_2, grammarAccess.getAssociationStructureAccess().getStructKeyword_2());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAssociationStructureRule());
			}
			newCompositeNode(grammarAccess.getAssociationStructureAccess().getClassifierDeclarationParserRuleCall_3());
		}
		this_ClassifierDeclaration_3=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_3.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getAssociationStructureRule());
			}
			newCompositeNode(grammarAccess.getAssociationStructureAccess().getTypeBodyParserRuleCall_4());
		}
		this_TypeBody_4=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_4.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleConnector
entryRuleConnector returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConnectorRule()); }
	iv_ruleConnector=ruleConnector
	{ $current=$iv_ruleConnector.current; }
	EOF;

// Rule Connector
ruleConnector returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getConnectorRule());
			}
			newCompositeNode(grammarAccess.getConnectorAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='connector'
		{
			newLeafNode(otherlv_1, grammarAccess.getConnectorAccess().getConnectorKeyword_1());
		}
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getConnectorRule());
						}
						newCompositeNode(grammarAccess.getConnectorAccess().getFeatureDeclarationParserRuleCall_2_0_0());
					}
					this_FeatureDeclaration_2=ruleFeatureDeclaration[$current]
					{
						$current = $this_FeatureDeclaration_2.current;
						afterParserOrEnumRuleCall();
					}
				)?
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getConnectorRule());
						}
						newCompositeNode(grammarAccess.getConnectorAccess().getValuePartParserRuleCall_2_0_1());
					}
					this_ValuePart_3=ruleValuePart[$current]
					{
						$current = $this_ValuePart_3.current;
						afterParserOrEnumRuleCall();
					}
				)?
			)
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getConnectorRule());
				}
				newCompositeNode(grammarAccess.getConnectorAccess().getConnectorDeclarationParserRuleCall_2_1());
			}
			this_ConnectorDeclaration_4=ruleConnectorDeclaration[$current]
			{
				$current = $this_ConnectorDeclaration_4.current;
				afterParserOrEnumRuleCall();
			}
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getConnectorRule());
			}
			newCompositeNode(grammarAccess.getConnectorAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_5=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_5.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule ConnectorDeclaration
ruleConnectorDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getConnectorDeclarationRule());
			}
			newCompositeNode(grammarAccess.getConnectorDeclarationAccess().getBinaryConnectorDeclarationParserRuleCall_0());
		}
		this_BinaryConnectorDeclaration_0=ruleBinaryConnectorDeclaration[$current]
		{
			$current = $this_BinaryConnectorDeclaration_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getConnectorDeclarationRule());
			}
			newCompositeNode(grammarAccess.getConnectorDeclarationAccess().getNaryConnectorDeclarationParserRuleCall_1());
		}
		this_NaryConnectorDeclaration_1=ruleNaryConnectorDeclaration[$current]
		{
			$current = $this_NaryConnectorDeclaration_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule BinaryConnectorDeclaration
ruleBinaryConnectorDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getBinaryConnectorDeclarationRule());
						}
						newCompositeNode(grammarAccess.getBinaryConnectorDeclarationAccess().getFeatureDeclarationParserRuleCall_0_0_0());
					}
					this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
					{
						$current = $this_FeatureDeclaration_0.current;
						afterParserOrEnumRuleCall();
					}
				)?
				otherlv_1='from'
				{
					newLeafNode(otherlv_1, grammarAccess.getBinaryConnectorDeclarationAccess().getFromKeyword_0_0_1());
				}
			)
			    |
			(
				(
					(
						lv_isSufficient_2_0='all'
						{
							newLeafNode(lv_isSufficient_2_0, grammarAccess.getBinaryConnectorDeclarationAccess().getIsSufficientAllKeyword_0_1_0_0());
						}
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getBinaryConnectorDeclarationRule());
							}
							setWithLastConsumed($current, "isSufficient", lv_isSufficient_2_0 != null, "all");
						}
					)
				)
				(
					otherlv_3='from'
					{
						newLeafNode(otherlv_3, grammarAccess.getBinaryConnectorDeclarationAccess().getFromKeyword_0_1_1());
					}
				)?
			)
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getBinaryConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_4_0=ruleConnectorEndMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getBinaryConnectorDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_4_0,
						"org.omg.kerml.xtext.KerML.ConnectorEndMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_5='to'
		{
			newLeafNode(otherlv_5, grammarAccess.getBinaryConnectorDeclarationAccess().getToKeyword_2());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getBinaryConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_3_0());
				}
				lv_ownedRelationship_6_0=ruleConnectorEndMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getBinaryConnectorDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_6_0,
						"org.omg.kerml.xtext.KerML.ConnectorEndMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;


// Rule NaryConnectorDeclaration
ruleNaryConnectorDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getNaryConnectorDeclarationRule());
				}
				newCompositeNode(grammarAccess.getNaryConnectorDeclarationAccess().getFeatureDeclarationParserRuleCall_0());
			}
			this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
			{
				$current = $this_FeatureDeclaration_0.current;
				afterParserOrEnumRuleCall();
			}
		)?
		otherlv_1='('
		{
			newLeafNode(otherlv_1, grammarAccess.getNaryConnectorDeclarationAccess().getLeftParenthesisKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getNaryConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleConnectorEndMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNaryConnectorDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.xtext.KerML.ConnectorEndMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_3=','
		{
			newLeafNode(otherlv_3, grammarAccess.getNaryConnectorDeclarationAccess().getCommaKeyword_3());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getNaryConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_4_0());
				}
				lv_ownedRelationship_4_0=ruleConnectorEndMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNaryConnectorDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_4_0,
						"org.omg.kerml.xtext.KerML.ConnectorEndMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_5=','
			{
				newLeafNode(otherlv_5, grammarAccess.getNaryConnectorDeclarationAccess().getCommaKeyword_5_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getNaryConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_5_1_0());
					}
					lv_ownedRelationship_6_0=ruleConnectorEndMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNaryConnectorDeclarationRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_6_0,
							"org.omg.kerml.xtext.KerML.ConnectorEndMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
		otherlv_7=')'
		{
			newLeafNode(otherlv_7, grammarAccess.getNaryConnectorDeclarationAccess().getRightParenthesisKeyword_6());
		}
	)
;

// Entry rule entryRuleConnectorEndMember
entryRuleConnectorEndMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConnectorEndMemberRule()); }
	iv_ruleConnectorEndMember=ruleConnectorEndMember
	{ $current=$iv_ruleConnectorEndMember.current; }
	EOF;

// Rule ConnectorEndMember
ruleConnectorEndMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getConnectorEndMemberAccess().getOwnedRelatedElementConnectorEndParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleConnectorEnd
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getConnectorEndMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.ConnectorEnd");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleConnectorEnd
entryRuleConnectorEnd returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConnectorEndRule()); }
	iv_ruleConnectorEnd=ruleConnectorEnd
	{ $current=$iv_ruleConnectorEnd.current; }
	EOF;

// Rule ConnectorEnd
ruleConnectorEnd returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getConnectorEndAccess().getOwnedRelationshipOwnedCrossingMultiplicityMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleOwnedCrossingMultiplicityMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConnectorEndRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.OwnedCrossingMultiplicityMember");
					afterParserOrEnumRuleCall();
				}
			)
		)?
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getConnectorEndAccess().getDeclaredNameNameParserRuleCall_1_0_0());
					}
					lv_declaredName_1_0=ruleName
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConnectorEndRule());
						}
						set(
							$current,
							"declaredName",
							lv_declaredName_1_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
						afterParserOrEnumRuleCall();
					}
				)
			)
			{
				newCompositeNode(grammarAccess.getConnectorEndAccess().getReferencesKeywordParserRuleCall_1_1());
			}
			ruleReferencesKeyword
			{
				afterParserOrEnumRuleCall();
			}
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getConnectorEndAccess().getOwnedRelationshipOwnedReferenceSubsettingParserRuleCall_2_0());
				}
				lv_ownedRelationship_3_0=ruleOwnedReferenceSubsetting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConnectorEndRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_3_0,
						"org.omg.kerml.xtext.KerML.OwnedReferenceSubsetting");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleOwnedCrossingMultiplicityMember
entryRuleOwnedCrossingMultiplicityMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedCrossingMultiplicityMemberRule()); }
	iv_ruleOwnedCrossingMultiplicityMember=ruleOwnedCrossingMultiplicityMember
	{ $current=$iv_ruleOwnedCrossingMultiplicityMember.current; }
	EOF;

// Rule OwnedCrossingMultiplicityMember
ruleOwnedCrossingMultiplicityMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedCrossingMultiplicityMemberAccess().getOwnedRelatedElementOwnedCrossingMultiplicityParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedCrossingMultiplicity
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedCrossingMultiplicityMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.OwnedCrossingMultiplicity");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOwnedCrossingMultiplicity
entryRuleOwnedCrossingMultiplicity returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedCrossingMultiplicityRule()); }
	iv_ruleOwnedCrossingMultiplicity=ruleOwnedCrossingMultiplicity
	{ $current=$iv_ruleOwnedCrossingMultiplicity.current; }
	EOF;

// Rule OwnedCrossingMultiplicity
ruleOwnedCrossingMultiplicity returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedCrossingMultiplicityAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleOwnedMultiplicity
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedCrossingMultiplicityRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleBindingConnector
entryRuleBindingConnector returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBindingConnectorRule()); }
	iv_ruleBindingConnector=ruleBindingConnector
	{ $current=$iv_ruleBindingConnector.current; }
	EOF;

// Rule BindingConnector
ruleBindingConnector returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBindingConnectorRule());
			}
			newCompositeNode(grammarAccess.getBindingConnectorAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='binding'
		{
			newLeafNode(otherlv_1, grammarAccess.getBindingConnectorAccess().getBindingKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBindingConnectorRule());
			}
			newCompositeNode(grammarAccess.getBindingConnectorAccess().getBindingConnectorDeclarationParserRuleCall_2());
		}
		this_BindingConnectorDeclaration_2=ruleBindingConnectorDeclaration[$current]
		{
			$current = $this_BindingConnectorDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBindingConnectorRule());
			}
			newCompositeNode(grammarAccess.getBindingConnectorAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule BindingConnectorDeclaration
ruleBindingConnectorDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getBindingConnectorDeclarationRule());
				}
				newCompositeNode(grammarAccess.getBindingConnectorDeclarationAccess().getFeatureDeclarationParserRuleCall_0_0());
			}
			this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
			{
				$current = $this_FeatureDeclaration_0.current;
				afterParserOrEnumRuleCall();
			}
			(
				otherlv_1='of'
				{
					newLeafNode(otherlv_1, grammarAccess.getBindingConnectorDeclarationAccess().getOfKeyword_0_1_0());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getBindingConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_0_1_1_0());
						}
						lv_ownedRelationship_2_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getBindingConnectorDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_2_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				otherlv_3='='
				{
					newLeafNode(otherlv_3, grammarAccess.getBindingConnectorDeclarationAccess().getEqualsSignKeyword_0_1_2());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getBindingConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_0_1_3_0());
						}
						lv_ownedRelationship_4_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getBindingConnectorDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_4_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)
		    |
		(
			(
				(
					lv_isSufficient_5_0='all'
					{
						newLeafNode(lv_isSufficient_5_0, grammarAccess.getBindingConnectorDeclarationAccess().getIsSufficientAllKeyword_1_0_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getBindingConnectorDeclarationRule());
						}
						setWithLastConsumed($current, "isSufficient", lv_isSufficient_5_0 != null, "all");
					}
				)
			)?
			(
				(
					otherlv_6='of'
					{
						newLeafNode(otherlv_6, grammarAccess.getBindingConnectorDeclarationAccess().getOfKeyword_1_1_0());
					}
				)?
				(
					(
						{
							newCompositeNode(grammarAccess.getBindingConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_1_1_1_0());
						}
						lv_ownedRelationship_7_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getBindingConnectorDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_7_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				otherlv_8='='
				{
					newLeafNode(otherlv_8, grammarAccess.getBindingConnectorDeclarationAccess().getEqualsSignKeyword_1_1_2());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getBindingConnectorDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_1_1_3_0());
						}
						lv_ownedRelationship_9_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getBindingConnectorDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_9_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)
	)
;

// Entry rule entryRuleSuccession
entryRuleSuccession returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSuccessionRule()); }
	iv_ruleSuccession=ruleSuccession
	{ $current=$iv_ruleSuccession.current; }
	EOF;

// Rule Succession
ruleSuccession returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSuccessionRule());
			}
			newCompositeNode(grammarAccess.getSuccessionAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='succession'
		{
			newLeafNode(otherlv_1, grammarAccess.getSuccessionAccess().getSuccessionKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSuccessionRule());
			}
			newCompositeNode(grammarAccess.getSuccessionAccess().getSuccessionDeclarationParserRuleCall_2());
		}
		this_SuccessionDeclaration_2=ruleSuccessionDeclaration[$current]
		{
			$current = $this_SuccessionDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSuccessionRule());
			}
			newCompositeNode(grammarAccess.getSuccessionAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule SuccessionDeclaration
ruleSuccessionDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getSuccessionDeclarationRule());
				}
				newCompositeNode(grammarAccess.getSuccessionDeclarationAccess().getFeatureDeclarationParserRuleCall_0_0());
			}
			this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
			{
				$current = $this_FeatureDeclaration_0.current;
				afterParserOrEnumRuleCall();
			}
			(
				otherlv_1='first'
				{
					newLeafNode(otherlv_1, grammarAccess.getSuccessionDeclarationAccess().getFirstKeyword_0_1_0());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getSuccessionDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_0_1_1_0());
						}
						lv_ownedRelationship_2_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getSuccessionDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_2_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				otherlv_3='then'
				{
					newLeafNode(otherlv_3, grammarAccess.getSuccessionDeclarationAccess().getThenKeyword_0_1_2());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getSuccessionDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_0_1_3_0());
						}
						lv_ownedRelationship_4_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getSuccessionDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_4_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)
		    |
		(
			(
				(
					lv_isSufficient_5_0='all'
					{
						newLeafNode(lv_isSufficient_5_0, grammarAccess.getSuccessionDeclarationAccess().getIsSufficientAllKeyword_1_0_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getSuccessionDeclarationRule());
						}
						setWithLastConsumed($current, "isSufficient", lv_isSufficient_5_0 != null, "all");
					}
				)
			)?
			(
				(
					otherlv_6='first'
					{
						newLeafNode(otherlv_6, grammarAccess.getSuccessionDeclarationAccess().getFirstKeyword_1_1_0());
					}
				)?
				(
					(
						{
							newCompositeNode(grammarAccess.getSuccessionDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_1_1_1_0());
						}
						lv_ownedRelationship_7_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getSuccessionDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_7_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				otherlv_8='then'
				{
					newLeafNode(otherlv_8, grammarAccess.getSuccessionDeclarationAccess().getThenKeyword_1_1_2());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getSuccessionDeclarationAccess().getOwnedRelationshipConnectorEndMemberParserRuleCall_1_1_3_0());
						}
						lv_ownedRelationship_9_0=ruleConnectorEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getSuccessionDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_9_0,
								"org.omg.kerml.xtext.KerML.ConnectorEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)
	)
;

// Entry rule entryRuleBehavior
entryRuleBehavior returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBehaviorRule()); }
	iv_ruleBehavior=ruleBehavior
	{ $current=$iv_ruleBehavior.current; }
	EOF;

// Rule Behavior
ruleBehavior returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBehaviorRule());
			}
			newCompositeNode(grammarAccess.getBehaviorAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='behavior'
		{
			newLeafNode(otherlv_1, grammarAccess.getBehaviorAccess().getBehaviorKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBehaviorRule());
			}
			newCompositeNode(grammarAccess.getBehaviorAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBehaviorRule());
			}
			newCompositeNode(grammarAccess.getBehaviorAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleStep
entryRuleStep returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getStepRule()); }
	iv_ruleStep=ruleStep
	{ $current=$iv_ruleStep.current; }
	EOF;

// Rule Step
ruleStep returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getStepRule());
			}
			newCompositeNode(grammarAccess.getStepAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='step'
		{
			newLeafNode(otherlv_1, grammarAccess.getStepAccess().getStepKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getStepRule());
			}
			newCompositeNode(grammarAccess.getStepAccess().getStepDeclarationParserRuleCall_2());
		}
		this_StepDeclaration_2=ruleStepDeclaration[$current]
		{
			$current = $this_StepDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getStepRule());
			}
			newCompositeNode(grammarAccess.getStepAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule StepDeclaration
ruleStepDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getStepDeclarationRule());
				}
				newCompositeNode(grammarAccess.getStepDeclarationAccess().getFeatureDeclarationParserRuleCall_0());
			}
			this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
			{
				$current = $this_FeatureDeclaration_0.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getStepDeclarationRule());
				}
				newCompositeNode(grammarAccess.getStepDeclarationAccess().getValuePartParserRuleCall_1());
			}
			this_ValuePart_1=ruleValuePart[$current]
			{
				$current = $this_ValuePart_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
	)
;

// Entry rule entryRuleFunction
entryRuleFunction returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionRule()); }
	iv_ruleFunction=ruleFunction
	{ $current=$iv_ruleFunction.current; }
	EOF;

// Rule Function
ruleFunction returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFunctionRule());
			}
			newCompositeNode(grammarAccess.getFunctionAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='function'
		{
			newLeafNode(otherlv_1, grammarAccess.getFunctionAccess().getFunctionKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFunctionRule());
			}
			newCompositeNode(grammarAccess.getFunctionAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFunctionRule());
			}
			newCompositeNode(grammarAccess.getFunctionAccess().getFunctionBodyParserRuleCall_3());
		}
		this_FunctionBody_3=ruleFunctionBody[$current]
		{
			$current = $this_FunctionBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule FunctionBody
ruleFunctionBody[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0=';'
		{
			newLeafNode(otherlv_0, grammarAccess.getFunctionBodyAccess().getSemicolonKeyword_0());
		}
		    |
		(
			otherlv_1='{'
			{
				newLeafNode(otherlv_1, grammarAccess.getFunctionBodyAccess().getLeftCurlyBracketKeyword_1_0());
			}
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFunctionBodyRule());
				}
				newCompositeNode(grammarAccess.getFunctionBodyAccess().getFunctionBodyPartParserRuleCall_1_1());
			}
			this_FunctionBodyPart_2=ruleFunctionBodyPart[$current]
			{
				$current = $this_FunctionBodyPart_2.current;
				afterParserOrEnumRuleCall();
			}
			otherlv_3='}'
			{
				newLeafNode(otherlv_3, grammarAccess.getFunctionBodyAccess().getRightCurlyBracketKeyword_1_2());
			}
		)
	)
;


// Rule FunctionBodyPart
ruleFunctionBodyPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getFunctionBodyPartAccess().getOwnedRelationshipNonFeatureMemberParserRuleCall_0_0_0());
					}
					lv_ownedRelationship_0_0=ruleNonFeatureMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFunctionBodyPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_0_0,
							"org.omg.kerml.xtext.KerML.NonFeatureMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getFunctionBodyPartAccess().getOwnedRelationshipFeatureMemberParserRuleCall_0_1_0());
					}
					lv_ownedRelationship_1_0=ruleFeatureMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFunctionBodyPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_1_0,
							"org.omg.kerml.xtext.KerML.FeatureMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getFunctionBodyPartAccess().getOwnedRelationshipAliasMemberParserRuleCall_0_2_0());
					}
					lv_ownedRelationship_2_0=ruleAliasMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFunctionBodyPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.xtext.KerML.AliasMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getFunctionBodyPartAccess().getOwnedRelationshipImportParserRuleCall_0_3_0());
					}
					lv_ownedRelationship_3_0=ruleImport
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFunctionBodyPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.xtext.KerML.Import");
						afterParserOrEnumRuleCall();
					}
				)
			)
			    |
			(
				(
					{
						newCompositeNode(grammarAccess.getFunctionBodyPartAccess().getOwnedRelationshipReturnFeatureMemberParserRuleCall_0_4_0());
					}
					lv_ownedRelationship_4_0=ruleReturnFeatureMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFunctionBodyPartRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_4_0,
							"org.omg.kerml.xtext.KerML.ReturnFeatureMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
		(
			(
				{
					newCompositeNode(grammarAccess.getFunctionBodyPartAccess().getOwnedRelationshipResultExpressionMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_5_0=ruleResultExpressionMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFunctionBodyPartRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_5_0,
						"org.omg.kerml.xtext.KerML.ResultExpressionMember");
					afterParserOrEnumRuleCall();
				}
			)
		)?
	)
;

// Entry rule entryRuleReturnFeatureMember
entryRuleReturnFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getReturnFeatureMemberRule()); }
	iv_ruleReturnFeatureMember=ruleReturnFeatureMember
	{ $current=$iv_ruleReturnFeatureMember.current; }
	EOF;

// Rule ReturnFeatureMember
ruleReturnFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getReturnFeatureMemberRule());
			}
			newCompositeNode(grammarAccess.getReturnFeatureMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='return'
		{
			newLeafNode(otherlv_1, grammarAccess.getReturnFeatureMemberAccess().getReturnKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getReturnFeatureMemberAccess().getOwnedRelatedElementFeatureElementParserRuleCall_2_0());
				}
				lv_ownedRelatedElement_2_0=ruleFeatureElement
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getReturnFeatureMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_2_0,
						"org.omg.kerml.xtext.KerML.FeatureElement");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleResultExpressionMember
entryRuleResultExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getResultExpressionMemberRule()); }
	iv_ruleResultExpressionMember=ruleResultExpressionMember
	{ $current=$iv_ruleResultExpressionMember.current; }
	EOF;

// Rule ResultExpressionMember
ruleResultExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getResultExpressionMemberRule());
			}
			newCompositeNode(grammarAccess.getResultExpressionMemberAccess().getMemberPrefixParserRuleCall_0());
		}
		this_MemberPrefix_0=ruleMemberPrefix[$current]
		{
			$current = $this_MemberPrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getResultExpressionMemberAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=ruleOwnedExpression
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getResultExpressionMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleExpression
entryRuleExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExpressionRule()); }
	iv_ruleExpression=ruleExpression
	{ $current=$iv_ruleExpression.current; }
	EOF;

// Rule Expression
ruleExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getExpressionRule());
			}
			newCompositeNode(grammarAccess.getExpressionAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='expr'
		{
			newLeafNode(otherlv_1, grammarAccess.getExpressionAccess().getExprKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getExpressionRule());
			}
			newCompositeNode(grammarAccess.getExpressionAccess().getExpressionDeclarationParserRuleCall_2());
		}
		this_ExpressionDeclaration_2=ruleExpressionDeclaration[$current]
		{
			$current = $this_ExpressionDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getExpressionRule());
			}
			newCompositeNode(grammarAccess.getExpressionAccess().getFunctionBodyParserRuleCall_3());
		}
		this_FunctionBody_3=ruleFunctionBody[$current]
		{
			$current = $this_FunctionBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule ExpressionDeclaration
ruleExpressionDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getExpressionDeclarationRule());
				}
				newCompositeNode(grammarAccess.getExpressionDeclarationAccess().getFeatureDeclarationParserRuleCall_0());
			}
			this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
			{
				$current = $this_FeatureDeclaration_0.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getExpressionDeclarationRule());
				}
				newCompositeNode(grammarAccess.getExpressionDeclarationAccess().getValuePartParserRuleCall_1());
			}
			this_ValuePart_1=ruleValuePart[$current]
			{
				$current = $this_ValuePart_1.current;
				afterParserOrEnumRuleCall();
			}
		)?
	)
;

// Entry rule entryRulePredicate
entryRulePredicate returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPredicateRule()); }
	iv_rulePredicate=rulePredicate
	{ $current=$iv_rulePredicate.current; }
	EOF;

// Rule Predicate
rulePredicate returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getPredicateRule());
			}
			newCompositeNode(grammarAccess.getPredicateAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='predicate'
		{
			newLeafNode(otherlv_1, grammarAccess.getPredicateAccess().getPredicateKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getPredicateRule());
			}
			newCompositeNode(grammarAccess.getPredicateAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getPredicateRule());
			}
			newCompositeNode(grammarAccess.getPredicateAccess().getFunctionBodyParserRuleCall_3());
		}
		this_FunctionBody_3=ruleFunctionBody[$current]
		{
			$current = $this_FunctionBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleBooleanExpression
entryRuleBooleanExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBooleanExpressionRule()); }
	iv_ruleBooleanExpression=ruleBooleanExpression
	{ $current=$iv_ruleBooleanExpression.current; }
	EOF;

// Rule BooleanExpression
ruleBooleanExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBooleanExpressionRule());
			}
			newCompositeNode(grammarAccess.getBooleanExpressionAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='bool'
		{
			newLeafNode(otherlv_1, grammarAccess.getBooleanExpressionAccess().getBoolKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBooleanExpressionRule());
			}
			newCompositeNode(grammarAccess.getBooleanExpressionAccess().getExpressionDeclarationParserRuleCall_2());
		}
		this_ExpressionDeclaration_2=ruleExpressionDeclaration[$current]
		{
			$current = $this_ExpressionDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getBooleanExpressionRule());
			}
			newCompositeNode(grammarAccess.getBooleanExpressionAccess().getFunctionBodyParserRuleCall_3());
		}
		this_FunctionBody_3=ruleFunctionBody[$current]
		{
			$current = $this_FunctionBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleInvariant
entryRuleInvariant returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getInvariantRule()); }
	iv_ruleInvariant=ruleInvariant
	{ $current=$iv_ruleInvariant.current; }
	EOF;

// Rule Invariant
ruleInvariant returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInvariantRule());
			}
			newCompositeNode(grammarAccess.getInvariantAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='inv'
		{
			newLeafNode(otherlv_1, grammarAccess.getInvariantAccess().getInvKeyword_1());
		}
		(
			otherlv_2='true'
			{
				newLeafNode(otherlv_2, grammarAccess.getInvariantAccess().getTrueKeyword_2_0());
			}
			    |
			(
				(
					lv_isNegated_3_0='false'
					{
						newLeafNode(lv_isNegated_3_0, grammarAccess.getInvariantAccess().getIsNegatedFalseKeyword_2_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getInvariantRule());
						}
						setWithLastConsumed($current, "isNegated", lv_isNegated_3_0 != null, "false");
					}
				)
			)
		)?
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInvariantRule());
			}
			newCompositeNode(grammarAccess.getInvariantAccess().getExpressionDeclarationParserRuleCall_3());
		}
		this_ExpressionDeclaration_4=ruleExpressionDeclaration[$current]
		{
			$current = $this_ExpressionDeclaration_4.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInvariantRule());
			}
			newCompositeNode(grammarAccess.getInvariantAccess().getFunctionBodyParserRuleCall_4());
		}
		this_FunctionBody_5=ruleFunctionBody[$current]
		{
			$current = $this_FunctionBody_5.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleInteraction
entryRuleInteraction returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getInteractionRule()); }
	iv_ruleInteraction=ruleInteraction
	{ $current=$iv_ruleInteraction.current; }
	EOF;

// Rule Interaction
ruleInteraction returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInteractionRule());
			}
			newCompositeNode(grammarAccess.getInteractionAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='interaction'
		{
			newLeafNode(otherlv_1, grammarAccess.getInteractionAccess().getInteractionKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInteractionRule());
			}
			newCompositeNode(grammarAccess.getInteractionAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInteractionRule());
			}
			newCompositeNode(grammarAccess.getInteractionAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleFlow
entryRuleFlow returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowRule()); }
	iv_ruleFlow=ruleFlow
	{ $current=$iv_ruleFlow.current; }
	EOF;

// Rule Flow
ruleFlow returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFlowRule());
			}
			newCompositeNode(grammarAccess.getFlowAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='flow'
		{
			newLeafNode(otherlv_1, grammarAccess.getFlowAccess().getFlowKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFlowRule());
			}
			newCompositeNode(grammarAccess.getFlowAccess().getFlowDeclarationParserRuleCall_2());
		}
		this_FlowDeclaration_2=ruleFlowDeclaration[$current]
		{
			$current = $this_FlowDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getFlowRule());
			}
			newCompositeNode(grammarAccess.getFlowAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleSuccessionFlow
entryRuleSuccessionFlow returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSuccessionFlowRule()); }
	iv_ruleSuccessionFlow=ruleSuccessionFlow
	{ $current=$iv_ruleSuccessionFlow.current; }
	EOF;

// Rule SuccessionFlow
ruleSuccessionFlow returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSuccessionFlowRule());
			}
			newCompositeNode(grammarAccess.getSuccessionFlowAccess().getFeaturePrefixParserRuleCall_0());
		}
		this_FeaturePrefix_0=ruleFeaturePrefix[$current]
		{
			$current = $this_FeaturePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='succession'
		{
			newLeafNode(otherlv_1, grammarAccess.getSuccessionFlowAccess().getSuccessionKeyword_1());
		}
		otherlv_2='flow'
		{
			newLeafNode(otherlv_2, grammarAccess.getSuccessionFlowAccess().getFlowKeyword_2());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSuccessionFlowRule());
			}
			newCompositeNode(grammarAccess.getSuccessionFlowAccess().getFlowDeclarationParserRuleCall_3());
		}
		this_FlowDeclaration_3=ruleFlowDeclaration[$current]
		{
			$current = $this_FlowDeclaration_3.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getSuccessionFlowRule());
			}
			newCompositeNode(grammarAccess.getSuccessionFlowAccess().getTypeBodyParserRuleCall_4());
		}
		this_TypeBody_4=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_4.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule FlowDeclaration
ruleFlowDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFlowDeclarationRule());
					}
					newCompositeNode(grammarAccess.getFlowDeclarationAccess().getFeatureDeclarationParserRuleCall_0_0());
				}
				this_FeatureDeclaration_0=ruleFeatureDeclaration[$current]
				{
					$current = $this_FeatureDeclaration_0.current;
					afterParserOrEnumRuleCall();
				}
			)?
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFlowDeclarationRule());
					}
					newCompositeNode(grammarAccess.getFlowDeclarationAccess().getValuePartParserRuleCall_0_1());
				}
				this_ValuePart_1=ruleValuePart[$current]
				{
					$current = $this_ValuePart_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
			(
				otherlv_2='of'
				{
					newLeafNode(otherlv_2, grammarAccess.getFlowDeclarationAccess().getOfKeyword_0_2_0());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getFlowDeclarationAccess().getOwnedRelationshipPayloadFeatureMemberParserRuleCall_0_2_1_0());
						}
						lv_ownedRelationship_3_0=rulePayloadFeatureMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getFlowDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_3_0,
								"org.omg.kerml.xtext.KerML.PayloadFeatureMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
			(
				otherlv_4='from'
				{
					newLeafNode(otherlv_4, grammarAccess.getFlowDeclarationAccess().getFromKeyword_0_3_0());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getFlowDeclarationAccess().getOwnedRelationshipFlowEndMemberParserRuleCall_0_3_1_0());
						}
						lv_ownedRelationship_5_0=ruleFlowEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getFlowDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_5_0,
								"org.omg.kerml.xtext.KerML.FlowEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				otherlv_6='to'
				{
					newLeafNode(otherlv_6, grammarAccess.getFlowDeclarationAccess().getToKeyword_0_3_2());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getFlowDeclarationAccess().getOwnedRelationshipFlowEndMemberParserRuleCall_0_3_3_0());
						}
						lv_ownedRelationship_7_0=ruleFlowEndMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getFlowDeclarationRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_7_0,
								"org.omg.kerml.xtext.KerML.FlowEndMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)
		    |
		(
			(
				(
					lv_isSufficient_8_0='all'
					{
						newLeafNode(lv_isSufficient_8_0, grammarAccess.getFlowDeclarationAccess().getIsSufficientAllKeyword_1_0_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFlowDeclarationRule());
						}
						setWithLastConsumed($current, "isSufficient", lv_isSufficient_8_0 != null, "all");
					}
				)
			)?
			(
				(
					{
						newCompositeNode(grammarAccess.getFlowDeclarationAccess().getOwnedRelationshipFlowEndMemberParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_9_0=ruleFlowEndMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFlowDeclarationRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_9_0,
							"org.omg.kerml.xtext.KerML.FlowEndMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_10='to'
			{
				newLeafNode(otherlv_10, grammarAccess.getFlowDeclarationAccess().getToKeyword_1_2());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getFlowDeclarationAccess().getOwnedRelationshipFlowEndMemberParserRuleCall_1_3_0());
					}
					lv_ownedRelationship_11_0=ruleFlowEndMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFlowDeclarationRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_11_0,
							"org.omg.kerml.xtext.KerML.FlowEndMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRulePayloadFeatureMember
entryRulePayloadFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPayloadFeatureMemberRule()); }
	iv_rulePayloadFeatureMember=rulePayloadFeatureMember
	{ $current=$iv_rulePayloadFeatureMember.current; }
	EOF;

// Rule PayloadFeatureMember
rulePayloadFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getPayloadFeatureMemberAccess().getOwnedRelatedElementPayloadFeatureParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=rulePayloadFeature
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getPayloadFeatureMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.PayloadFeature");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRulePayloadFeature
entryRulePayloadFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPayloadFeatureRule()); }
	iv_rulePayloadFeature=rulePayloadFeature
	{ $current=$iv_rulePayloadFeature.current; }
	EOF;

// Rule PayloadFeature
rulePayloadFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureAccess().getIdentificationParserRuleCall_0_0());
				}
				this_Identification_0=ruleIdentification[$current]
				{
					$current = $this_Identification_0.current;
					afterParserOrEnumRuleCall();
				}
			)?
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getPayloadFeatureRule());
				}
				newCompositeNode(grammarAccess.getPayloadFeatureAccess().getPayloadFeatureSpecializationPartParserRuleCall_0_1());
			}
			this_PayloadFeatureSpecializationPart_1=rulePayloadFeatureSpecializationPart[$current]
			{
				$current = $this_PayloadFeatureSpecializationPart_1.current;
				afterParserOrEnumRuleCall();
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureAccess().getValuePartParserRuleCall_0_2());
				}
				this_ValuePart_2=ruleValuePart[$current]
				{
					$current = $this_ValuePart_2.current;
					afterParserOrEnumRuleCall();
				}
			)?
		)
		    |
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureAccess().getIdentificationParserRuleCall_1_0());
				}
				this_Identification_3=ruleIdentification[$current]
				{
					$current = $this_Identification_3.current;
					afterParserOrEnumRuleCall();
				}
			)?
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getPayloadFeatureRule());
				}
				newCompositeNode(grammarAccess.getPayloadFeatureAccess().getValuePartParserRuleCall_1_1());
			}
			this_ValuePart_4=ruleValuePart[$current]
			{
				$current = $this_ValuePart_4.current;
				afterParserOrEnumRuleCall();
			}
		)
		    |
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getPayloadFeatureAccess().getOwnedRelationshipOwnedFeatureTypingParserRuleCall_2_0_0());
					}
					lv_ownedRelationship_5_0=ruleOwnedFeatureTyping
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPayloadFeatureRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_5_0,
							"org.omg.kerml.xtext.KerML.OwnedFeatureTyping");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getPayloadFeatureAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_2_1_0());
					}
					lv_ownedRelationship_6_0=ruleOwnedMultiplicity
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPayloadFeatureRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_6_0,
							"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
						afterParserOrEnumRuleCall();
					}
				)
			)?
		)
		    |
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getPayloadFeatureAccess().getOwnedRelationshipOwnedMultiplicityParserRuleCall_3_0_0());
					}
					lv_ownedRelationship_7_0=ruleOwnedMultiplicity
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPayloadFeatureRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_7_0,
							"org.omg.kerml.xtext.KerML.OwnedMultiplicity");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getPayloadFeatureAccess().getOwnedRelationshipOwnedFeatureTypingParserRuleCall_3_1_0());
					}
					lv_ownedRelationship_8_0=ruleOwnedFeatureTyping
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPayloadFeatureRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_8_0,
							"org.omg.kerml.xtext.KerML.OwnedFeatureTyping");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;


// Rule PayloadFeatureSpecializationPart
rulePayloadFeatureSpecializationPart[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(':' | 'typed' | ':>' | 'subsets' | '::>' | 'references' | '=>' | 'crosses' | ':>>' | 'redefines')=>
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureSpecializationPartAccess().getFeatureSpecializationParserRuleCall_0_0());
				}
				this_FeatureSpecialization_0=ruleFeatureSpecialization[$current]
				{
					$current = $this_FeatureSpecialization_0.current;
					afterParserOrEnumRuleCall();
				}
			)+
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureSpecializationPartAccess().getMultiplicityPartParserRuleCall_0_1());
				}
				this_MultiplicityPart_1=ruleMultiplicityPart[$current]
				{
					$current = $this_MultiplicityPart_1.current;
					afterParserOrEnumRuleCall();
				}
			)?
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureSpecializationPartAccess().getFeatureSpecializationParserRuleCall_0_2());
				}
				this_FeatureSpecialization_2=ruleFeatureSpecialization[$current]
				{
					$current = $this_FeatureSpecialization_2.current;
					afterParserOrEnumRuleCall();
				}
			)*
		)
		    |
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getPayloadFeatureSpecializationPartRule());
				}
				newCompositeNode(grammarAccess.getPayloadFeatureSpecializationPartAccess().getMultiplicityPartParserRuleCall_1_0());
			}
			this_MultiplicityPart_3=ruleMultiplicityPart[$current]
			{
				$current = $this_MultiplicityPart_3.current;
				afterParserOrEnumRuleCall();
			}
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getPayloadFeatureSpecializationPartRule());
					}
					newCompositeNode(grammarAccess.getPayloadFeatureSpecializationPartAccess().getFeatureSpecializationParserRuleCall_1_1());
				}
				this_FeatureSpecialization_4=ruleFeatureSpecialization[$current]
				{
					$current = $this_FeatureSpecialization_4.current;
					afterParserOrEnumRuleCall();
				}
			)+
		)
	)
;

// Entry rule entryRuleFlowEndMember
entryRuleFlowEndMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowEndMemberRule()); }
	iv_ruleFlowEndMember=ruleFlowEndMember
	{ $current=$iv_ruleFlowEndMember.current; }
	EOF;

// Rule FlowEndMember
ruleFlowEndMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFlowEndMemberAccess().getOwnedRelatedElementFlowEndParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleFlowEnd
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFlowEndMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.FlowEnd");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFlowEnd
entryRuleFlowEnd returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowEndRule()); }
	iv_ruleFlowEnd=ruleFlowEnd
	{ $current=$iv_ruleFlowEnd.current; }
	EOF;

// Rule FlowEnd
ruleFlowEnd returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getFlowEndAccess().getOwnedRelationshipFlowEndSubsettingParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleFlowEndSubsetting
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFlowEndRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.FlowEndSubsetting");
					afterParserOrEnumRuleCall();
				}
			)
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getFlowEndAccess().getOwnedRelationshipFlowFeatureMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleFlowFeatureMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFlowEndRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.xtext.KerML.FlowFeatureMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleFlowEndSubsetting
entryRuleFlowEndSubsetting returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowEndSubsettingRule()); }
	iv_ruleFlowEndSubsetting=ruleFlowEndSubsetting
	{ $current=$iv_ruleFlowEndSubsetting.current; }
	EOF;

// Rule FlowEndSubsetting
ruleFlowEndSubsetting returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getFlowEndSubsettingRule());
						}
					}
					{
						newCompositeNode(grammarAccess.getFlowEndSubsettingAccess().getReferencedFeatureFeatureCrossReference_0_0_0());
					}
					ruleQualifiedName
					{
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_1='.'
			{
				newLeafNode(otherlv_1, grammarAccess.getFlowEndSubsettingAccess().getFullStopKeyword_0_1());
			}
		)
		    |
		(
			(
				{
					newCompositeNode(grammarAccess.getFlowEndSubsettingAccess().getOwnedRelatedElementFeatureChainPrefixParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_2_0=ruleFeatureChainPrefix
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFlowEndSubsettingRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_2_0,
						"org.omg.kerml.xtext.KerML.FeatureChainPrefix");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleFeatureChainPrefix
entryRuleFeatureChainPrefix returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureChainPrefixRule()); }
	iv_ruleFeatureChainPrefix=ruleFeatureChainPrefix
	{ $current=$iv_ruleFeatureChainPrefix.current; }
	EOF;

// Rule FeatureChainPrefix
ruleFeatureChainPrefix returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureChainPrefixAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_0_0_0());
					}
					lv_ownedRelationship_0_0=ruleOwnedFeatureChaining
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureChainPrefixRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_0_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_1='.'
			{
				newLeafNode(otherlv_1, grammarAccess.getFeatureChainPrefixAccess().getFullStopKeyword_0_1());
			}
		)+
		(
			(
				{
					newCompositeNode(grammarAccess.getFeatureChainPrefixAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_1_0());
				}
				lv_ownedRelationship_2_0=ruleOwnedFeatureChaining
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeatureChainPrefixRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_3='.'
		{
			newLeafNode(otherlv_3, grammarAccess.getFeatureChainPrefixAccess().getFullStopKeyword_2());
		}
	)
;

// Entry rule entryRuleFlowFeatureMember
entryRuleFlowFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowFeatureMemberRule()); }
	iv_ruleFlowFeatureMember=ruleFlowFeatureMember
	{ $current=$iv_ruleFlowFeatureMember.current; }
	EOF;

// Rule FlowFeatureMember
ruleFlowFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFlowFeatureMemberAccess().getOwnedRelatedElementFlowFeatureParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleFlowFeature
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFlowFeatureMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.FlowFeature");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFlowFeature
entryRuleFlowFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowFeatureRule()); }
	iv_ruleFlowFeature=ruleFlowFeature
	{ $current=$iv_ruleFlowFeature.current; }
	EOF;

// Rule FlowFeature
ruleFlowFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFlowFeatureAccess().getOwnedRelationshipFlowRedefinitionParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleFlowRedefinition
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFlowFeatureRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.xtext.KerML.FlowRedefinition");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFlowRedefinition
entryRuleFlowRedefinition returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFlowRedefinitionRule()); }
	iv_ruleFlowRedefinition=ruleFlowRedefinition
	{ $current=$iv_ruleFlowRedefinition.current; }
	EOF;

// Rule FlowRedefinition
ruleFlowRedefinition returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFlowRedefinitionRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getFlowRedefinitionAccess().getRedefinedFeatureFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleMetaclass
entryRuleMetaclass returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetaclassRule()); }
	iv_ruleMetaclass=ruleMetaclass
	{ $current=$iv_ruleMetaclass.current; }
	EOF;

// Rule Metaclass
ruleMetaclass returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMetaclassRule());
			}
			newCompositeNode(grammarAccess.getMetaclassAccess().getTypePrefixParserRuleCall_0());
		}
		this_TypePrefix_0=ruleTypePrefix[$current]
		{
			$current = $this_TypePrefix_0.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_1='metaclass'
		{
			newLeafNode(otherlv_1, grammarAccess.getMetaclassAccess().getMetaclassKeyword_1());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMetaclassRule());
			}
			newCompositeNode(grammarAccess.getMetaclassAccess().getClassifierDeclarationParserRuleCall_2());
		}
		this_ClassifierDeclaration_2=ruleClassifierDeclaration[$current]
		{
			$current = $this_ClassifierDeclaration_2.current;
			afterParserOrEnumRuleCall();
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMetaclassRule());
			}
			newCompositeNode(grammarAccess.getMetaclassAccess().getTypeBodyParserRuleCall_3());
		}
		this_TypeBody_3=ruleTypeBody[$current]
		{
			$current = $this_TypeBody_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRulePrefixMetadataAnnotation
entryRulePrefixMetadataAnnotation returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPrefixMetadataAnnotationRule()); }
	iv_rulePrefixMetadataAnnotation=rulePrefixMetadataAnnotation
	{ $current=$iv_rulePrefixMetadataAnnotation.current; }
	EOF;

// Rule PrefixMetadataAnnotation
rulePrefixMetadataAnnotation returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='#'
		{
			newLeafNode(otherlv_0, grammarAccess.getPrefixMetadataAnnotationAccess().getNumberSignKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getPrefixMetadataAnnotationAccess().getOwnedRelatedElementPrefixMetadataFeatureParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=rulePrefixMetadataFeature
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getPrefixMetadataAnnotationRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataFeature");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRulePrefixMetadataMember
entryRulePrefixMetadataMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPrefixMetadataMemberRule()); }
	iv_rulePrefixMetadataMember=rulePrefixMetadataMember
	{ $current=$iv_rulePrefixMetadataMember.current; }
	EOF;

// Rule PrefixMetadataMember
rulePrefixMetadataMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='#'
		{
			newLeafNode(otherlv_0, grammarAccess.getPrefixMetadataMemberAccess().getNumberSignKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getPrefixMetadataMemberAccess().getOwnedRelatedElementPrefixMetadataFeatureParserRuleCall_1_0());
				}
				lv_ownedRelatedElement_1_0=rulePrefixMetadataFeature
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getPrefixMetadataMemberRule());
					}
					add(
						$current,
						"ownedRelatedElement",
						lv_ownedRelatedElement_1_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataFeature");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRulePrefixMetadataFeature
entryRulePrefixMetadataFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPrefixMetadataFeatureRule()); }
	iv_rulePrefixMetadataFeature=rulePrefixMetadataFeature
	{ $current=$iv_rulePrefixMetadataFeature.current; }
	EOF;

// Rule PrefixMetadataFeature
rulePrefixMetadataFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getPrefixMetadataFeatureAccess().getOwnedRelationshipMetadataTypingParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleMetadataTyping
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getPrefixMetadataFeatureRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.xtext.KerML.MetadataTyping");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleMetadataFeature
entryRuleMetadataFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataFeatureRule()); }
	iv_ruleMetadataFeature=ruleMetadataFeature
	{ $current=$iv_ruleMetadataFeature.current; }
	EOF;

// Rule MetadataFeature
ruleMetadataFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getMetadataFeatureAccess().getOwnedRelationshipPrefixMetadataMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=rulePrefixMetadataMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMetadataFeatureRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.xtext.KerML.PrefixMetadataMember");
					afterParserOrEnumRuleCall();
				}
			)
		)*
		(
			otherlv_1='@'
			{
				newLeafNode(otherlv_1, grammarAccess.getMetadataFeatureAccess().getCommercialAtKeyword_1_0());
			}
			    |
			otherlv_2='metadata'
			{
				newLeafNode(otherlv_2, grammarAccess.getMetadataFeatureAccess().getMetadataKeyword_1_1());
			}
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMetadataFeatureRule());
			}
			newCompositeNode(grammarAccess.getMetadataFeatureAccess().getMetadataFeatureDeclarationParserRuleCall_2());
		}
		this_MetadataFeatureDeclaration_3=ruleMetadataFeatureDeclaration[$current]
		{
			$current = $this_MetadataFeatureDeclaration_3.current;
			afterParserOrEnumRuleCall();
		}
		(
			otherlv_4='about'
			{
				newLeafNode(otherlv_4, grammarAccess.getMetadataFeatureAccess().getAboutKeyword_3_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getMetadataFeatureAccess().getOwnedRelationshipAnnotationParserRuleCall_3_1_0());
					}
					lv_ownedRelationship_5_0=ruleAnnotation
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMetadataFeatureRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_5_0,
							"org.omg.kerml.xtext.KerML.Annotation");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				otherlv_6=','
				{
					newLeafNode(otherlv_6, grammarAccess.getMetadataFeatureAccess().getCommaKeyword_3_2_0());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getMetadataFeatureAccess().getOwnedRelationshipAnnotationParserRuleCall_3_2_1_0());
						}
						lv_ownedRelationship_7_0=ruleAnnotation
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getMetadataFeatureRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_7_0,
								"org.omg.kerml.xtext.KerML.Annotation");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)*
		)?
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMetadataFeatureRule());
			}
			newCompositeNode(grammarAccess.getMetadataFeatureAccess().getMetadataBodyParserRuleCall_4());
		}
		this_MetadataBody_8=ruleMetadataBody[$current]
		{
			$current = $this_MetadataBody_8.current;
			afterParserOrEnumRuleCall();
		}
	)
;


// Rule MetadataFeatureDeclaration
ruleMetadataFeatureDeclaration[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getMetadataFeatureDeclarationRule());
				}
				newCompositeNode(grammarAccess.getMetadataFeatureDeclarationAccess().getIdentificationParserRuleCall_0_0());
			}
			this_Identification_0=ruleIdentification[$current]
			{
				$current = $this_Identification_0.current;
				afterParserOrEnumRuleCall();
			}
			(
				otherlv_1=':'
				{
					newLeafNode(otherlv_1, grammarAccess.getMetadataFeatureDeclarationAccess().getColonKeyword_0_1_0());
				}
				    |
				(
					otherlv_2='typed'
					{
						newLeafNode(otherlv_2, grammarAccess.getMetadataFeatureDeclarationAccess().getTypedKeyword_0_1_1_0());
					}
					otherlv_3='by'
					{
						newLeafNode(otherlv_3, grammarAccess.getMetadataFeatureDeclarationAccess().getByKeyword_0_1_1_1());
					}
				)
			)
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getMetadataFeatureDeclarationAccess().getOwnedRelationshipMetadataTypingParserRuleCall_1_0());
				}
				lv_ownedRelationship_4_0=ruleMetadataTyping
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMetadataFeatureDeclarationRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_4_0,
						"org.omg.kerml.xtext.KerML.MetadataTyping");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleMetadataTyping
entryRuleMetadataTyping returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataTypingRule()); }
	iv_ruleMetadataTyping=ruleMetadataTyping
	{ $current=$iv_ruleMetadataTyping.current; }
	EOF;

// Rule MetadataTyping
ruleMetadataTyping returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getMetadataTypingRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getMetadataTypingAccess().getTypeMetaclassCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule MetadataBody
ruleMetadataBody[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0=';'
		{
			newLeafNode(otherlv_0, grammarAccess.getMetadataBodyAccess().getSemicolonKeyword_0());
		}
		    |
		(
			otherlv_1='{'
			{
				newLeafNode(otherlv_1, grammarAccess.getMetadataBodyAccess().getLeftCurlyBracketKeyword_1_0());
			}
			(
				(
					(
						{
							newCompositeNode(grammarAccess.getMetadataBodyAccess().getOwnedRelationshipNonFeatureMemberParserRuleCall_1_1_0_0());
						}
						lv_ownedRelationship_2_0=ruleNonFeatureMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getMetadataBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_2_0,
								"org.omg.kerml.xtext.KerML.NonFeatureMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getMetadataBodyAccess().getOwnedRelationshipMetadataBodyFeatureMemberParserRuleCall_1_1_1_0());
						}
						lv_ownedRelationship_3_0=ruleMetadataBodyFeatureMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getMetadataBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_3_0,
								"org.omg.kerml.xtext.KerML.MetadataBodyFeatureMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getMetadataBodyAccess().getOwnedRelationshipAliasMemberParserRuleCall_1_1_2_0());
						}
						lv_ownedRelationship_4_0=ruleAliasMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getMetadataBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_4_0,
								"org.omg.kerml.xtext.KerML.AliasMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							newCompositeNode(grammarAccess.getMetadataBodyAccess().getOwnedRelationshipImportParserRuleCall_1_1_3_0());
						}
						lv_ownedRelationship_5_0=ruleImport
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getMetadataBodyRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_5_0,
								"org.omg.kerml.xtext.KerML.Import");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)*
			otherlv_6='}'
			{
				newLeafNode(otherlv_6, grammarAccess.getMetadataBodyAccess().getRightCurlyBracketKeyword_1_2());
			}
		)
	)
;

// Entry rule entryRuleMetadataBodyFeatureMember
entryRuleMetadataBodyFeatureMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataBodyFeatureMemberRule()); }
	iv_ruleMetadataBodyFeatureMember=ruleMetadataBodyFeatureMember
	{ $current=$iv_ruleMetadataBodyFeatureMember.current; }
	EOF;

// Rule MetadataBodyFeatureMember
ruleMetadataBodyFeatureMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getMetadataBodyFeatureMemberAccess().getOwnedRelatedElementMetadataBodyFeatureParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleMetadataBodyFeature
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getMetadataBodyFeatureMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.MetadataBodyFeature");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleMetadataBodyFeature
entryRuleMetadataBodyFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataBodyFeatureRule()); }
	iv_ruleMetadataBodyFeature=ruleMetadataBodyFeature
	{ $current=$iv_ruleMetadataBodyFeature.current; }
	EOF;

// Rule MetadataBodyFeature
ruleMetadataBodyFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			otherlv_0='feature'
			{
				newLeafNode(otherlv_0, grammarAccess.getMetadataBodyFeatureAccess().getFeatureKeyword_0());
			}
		)?
		(
			otherlv_1=':>>'
			{
				newLeafNode(otherlv_1, grammarAccess.getMetadataBodyFeatureAccess().getColonGreaterThanSignGreaterThanSignKeyword_1_0());
			}
			    |
			otherlv_2='redefines'
			{
				newLeafNode(otherlv_2, grammarAccess.getMetadataBodyFeatureAccess().getRedefinesKeyword_1_1());
			}
		)?
		(
			(
				{
					newCompositeNode(grammarAccess.getMetadataBodyFeatureAccess().getOwnedRelationshipOwnedRedefinitionParserRuleCall_2_0());
				}
				lv_ownedRelationship_3_0=ruleOwnedRedefinition
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMetadataBodyFeatureRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_3_0,
						"org.omg.kerml.xtext.KerML.OwnedRedefinition");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getMetadataBodyFeatureRule());
				}
				newCompositeNode(grammarAccess.getMetadataBodyFeatureAccess().getFeatureSpecializationPartParserRuleCall_3());
			}
			this_FeatureSpecializationPart_4=ruleFeatureSpecializationPart[$current]
			{
				$current = $this_FeatureSpecializationPart_4.current;
				afterParserOrEnumRuleCall();
			}
		)?
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getMetadataBodyFeatureRule());
				}
				newCompositeNode(grammarAccess.getMetadataBodyFeatureAccess().getValuePartParserRuleCall_4());
			}
			this_ValuePart_5=ruleValuePart[$current]
			{
				$current = $this_ValuePart_5.current;
				afterParserOrEnumRuleCall();
			}
		)?
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getMetadataBodyFeatureRule());
			}
			newCompositeNode(grammarAccess.getMetadataBodyFeatureAccess().getMetadataBodyParserRuleCall_5());
		}
		this_MetadataBody_6=ruleMetadataBody[$current]
		{
			$current = $this_MetadataBody_6.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleExpressionBody
entryRuleExpressionBody returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExpressionBodyRule()); }
	iv_ruleExpressionBody=ruleExpressionBody
	{ $current=$iv_ruleExpressionBody.current; }
	EOF;

// Rule ExpressionBody
ruleExpressionBody returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='{'
		{
			newLeafNode(otherlv_0, grammarAccess.getExpressionBodyAccess().getLeftCurlyBracketKeyword_0());
		}
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getExpressionBodyRule());
			}
			newCompositeNode(grammarAccess.getExpressionBodyAccess().getFunctionBodyPartParserRuleCall_1());
		}
		this_FunctionBodyPart_1=ruleFunctionBodyPart[$current]
		{
			$current = $this_FunctionBodyPart_1.current;
			afterParserOrEnumRuleCall();
		}
		otherlv_2='}'
		{
			newLeafNode(otherlv_2, grammarAccess.getExpressionBodyAccess().getRightCurlyBracketKeyword_2());
		}
	)
;

// Entry rule entryRuleOwnedExpressionMember
entryRuleOwnedExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedExpressionMemberRule()); }
	iv_ruleOwnedExpressionMember=ruleOwnedExpressionMember
	{ $current=$iv_ruleOwnedExpressionMember.current; }
	EOF;

// Rule OwnedExpressionMember
ruleOwnedExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedExpressionMemberAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOwnedExpression
entryRuleOwnedExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedExpressionRule()); }
	iv_ruleOwnedExpression=ruleOwnedExpression
	{ $current=$iv_ruleOwnedExpression.current; }
	EOF;

// Rule OwnedExpression
ruleOwnedExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		newCompositeNode(grammarAccess.getOwnedExpressionAccess().getConditionalExpressionParserRuleCall());
	}
	this_ConditionalExpression_0=ruleConditionalExpression
	{
		$current = $this_ConditionalExpression_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleOwnedExpressionReference
entryRuleOwnedExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedExpressionReferenceRule()); }
	iv_ruleOwnedExpressionReference=ruleOwnedExpressionReference
	{ $current=$iv_ruleOwnedExpressionReference.current; }
	EOF;

// Rule OwnedExpressionReference
ruleOwnedExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOwnedExpressionReferenceAccess().getOwnedRelationshipOwnedExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleOwnedExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOwnedExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleConditionalExpression
entryRuleConditionalExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConditionalExpressionRule()); }
	iv_ruleConditionalExpression=ruleConditionalExpression
	{ $current=$iv_ruleConditionalExpression.current; }
	EOF;

// Rule ConditionalExpression
ruleConditionalExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getConditionalExpressionAccess().getNullCoalescingExpressionParserRuleCall_0());
		}
		this_NullCoalescingExpression_0=ruleNullCoalescingExpression
		{
			$current = $this_NullCoalescingExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getConditionalExpressionAccess().getOperatorExpressionAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperatorConditionalOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleConditionalOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ConditionalOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperandNullCoalescingExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleNullCoalescingExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.NullCoalescingExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_4='?'
			{
				newLeafNode(otherlv_4, grammarAccess.getConditionalExpressionAccess().getQuestionMarkKeyword_1_3());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperandOwnedExpressionReferenceParserRuleCall_1_4_0());
					}
					lv_operand_5_0=ruleOwnedExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_5_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
			otherlv_6='else'
			{
				newLeafNode(otherlv_6, grammarAccess.getConditionalExpressionAccess().getElseKeyword_1_5());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getConditionalExpressionAccess().getOperandOwnedExpressionReferenceParserRuleCall_1_6_0());
					}
					lv_operand_7_0=ruleOwnedExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getConditionalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_7_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRuleConditionalOperator
entryRuleConditionalOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getConditionalOperatorRule()); }
	iv_ruleConditionalOperator=ruleConditionalOperator
	{ $current=$iv_ruleConditionalOperator.current.getText(); }
	EOF;

// Rule ConditionalOperator
ruleConditionalOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='if'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getConditionalOperatorAccess().getIfKeyword());
	}
;

// Entry rule entryRuleNullCoalescingExpression
entryRuleNullCoalescingExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNullCoalescingExpressionRule()); }
	iv_ruleNullCoalescingExpression=ruleNullCoalescingExpression
	{ $current=$iv_ruleNullCoalescingExpression.current; }
	EOF;

// Rule NullCoalescingExpression
ruleNullCoalescingExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getNullCoalescingExpressionAccess().getImpliesExpressionParserRuleCall_0());
		}
		this_ImpliesExpression_0=ruleImpliesExpression
		{
			$current = $this_ImpliesExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getNullCoalescingExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getNullCoalescingExpressionAccess().getOperatorNullCoalescingOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleNullCoalescingOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNullCoalescingExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.NullCoalescingOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getNullCoalescingExpressionAccess().getOperandImpliesExpressionReferenceParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleImpliesExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNullCoalescingExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleNullCoalescingOperator
entryRuleNullCoalescingOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getNullCoalescingOperatorRule()); }
	iv_ruleNullCoalescingOperator=ruleNullCoalescingOperator
	{ $current=$iv_ruleNullCoalescingOperator.current.getText(); }
	EOF;

// Rule NullCoalescingOperator
ruleNullCoalescingOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='??'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getNullCoalescingOperatorAccess().getQuestionMarkQuestionMarkKeyword());
	}
;

// Entry rule entryRuleImpliesExpressionReference
entryRuleImpliesExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImpliesExpressionReferenceRule()); }
	iv_ruleImpliesExpressionReference=ruleImpliesExpressionReference
	{ $current=$iv_ruleImpliesExpressionReference.current; }
	EOF;

// Rule ImpliesExpressionReference
ruleImpliesExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getImpliesExpressionReferenceAccess().getOwnedRelationshipImpliesExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleImpliesExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getImpliesExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleImpliesExpressionMember
entryRuleImpliesExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImpliesExpressionMemberRule()); }
	iv_ruleImpliesExpressionMember=ruleImpliesExpressionMember
	{ $current=$iv_ruleImpliesExpressionMember.current; }
	EOF;

// Rule ImpliesExpressionMember
ruleImpliesExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getImpliesExpressionMemberAccess().getOwnedRelatedElementImpliesExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleImpliesExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getImpliesExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleImpliesExpression
entryRuleImpliesExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getImpliesExpressionRule()); }
	iv_ruleImpliesExpression=ruleImpliesExpression
	{ $current=$iv_ruleImpliesExpression.current; }
	EOF;

// Rule ImpliesExpression
ruleImpliesExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getImpliesExpressionAccess().getOrExpressionParserRuleCall_0());
		}
		this_OrExpression_0=ruleOrExpression
		{
			$current = $this_OrExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getImpliesExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getImpliesExpressionAccess().getOperatorImpliesOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleImpliesOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getImpliesExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ImpliesOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getImpliesExpressionAccess().getOperandOrExpressionReferenceParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleOrExpressionReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getImpliesExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OrExpressionReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleImpliesOperator
entryRuleImpliesOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getImpliesOperatorRule()); }
	iv_ruleImpliesOperator=ruleImpliesOperator
	{ $current=$iv_ruleImpliesOperator.current.getText(); }
	EOF;

// Rule ImpliesOperator
ruleImpliesOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='implies'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getImpliesOperatorAccess().getImpliesKeyword());
	}
;

// Entry rule entryRuleOrExpressionReference
entryRuleOrExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOrExpressionReferenceRule()); }
	iv_ruleOrExpressionReference=ruleOrExpressionReference
	{ $current=$iv_ruleOrExpressionReference.current; }
	EOF;

// Rule OrExpressionReference
ruleOrExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOrExpressionReferenceAccess().getOwnedRelationshipOrExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleOrExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOrExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OrExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOrExpressionMember
entryRuleOrExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOrExpressionMemberRule()); }
	iv_ruleOrExpressionMember=ruleOrExpressionMember
	{ $current=$iv_ruleOrExpressionMember.current; }
	EOF;

// Rule OrExpressionMember
ruleOrExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getOrExpressionMemberAccess().getOwnedRelatedElementOrExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOrExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getOrExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OrExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleOrExpression
entryRuleOrExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOrExpressionRule()); }
	iv_ruleOrExpression=ruleOrExpression
	{ $current=$iv_ruleOrExpression.current; }
	EOF;

// Rule OrExpression
ruleOrExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getOrExpressionAccess().getXorExpressionParserRuleCall_0());
		}
		this_XorExpression_0=ruleXorExpression
		{
			$current = $this_XorExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getOrExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperatorOrOperatorParserRuleCall_1_1_0_0_0());
							}
							lv_operator_2_0=ruleOrOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_2_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.OrOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperandXorExpressionParserRuleCall_1_1_0_1_0());
							}
							lv_operand_3_0=ruleXorExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_3_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperatorConditionalOrOperatorParserRuleCall_1_1_1_0_0());
							}
							lv_operator_4_0=ruleConditionalOrOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_4_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.ConditionalOrOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getOrExpressionAccess().getOperandXorExpressionReferenceParserRuleCall_1_1_1_1_0());
							}
							lv_operand_5_0=ruleXorExpressionReference
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getOrExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_5_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpressionReference");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)
		)*
	)
;

// Entry rule entryRuleOrOperator
entryRuleOrOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getOrOperatorRule()); }
	iv_ruleOrOperator=ruleOrOperator
	{ $current=$iv_ruleOrOperator.current.getText(); }
	EOF;

// Rule OrOperator
ruleOrOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='|'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getOrOperatorAccess().getVerticalLineKeyword());
	}
;

// Entry rule entryRuleConditionalOrOperator
entryRuleConditionalOrOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getConditionalOrOperatorRule()); }
	iv_ruleConditionalOrOperator=ruleConditionalOrOperator
	{ $current=$iv_ruleConditionalOrOperator.current.getText(); }
	EOF;

// Rule ConditionalOrOperator
ruleConditionalOrOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='or'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getConditionalOrOperatorAccess().getOrKeyword());
	}
;

// Entry rule entryRuleXorExpressionReference
entryRuleXorExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getXorExpressionReferenceRule()); }
	iv_ruleXorExpressionReference=ruleXorExpressionReference
	{ $current=$iv_ruleXorExpressionReference.current; }
	EOF;

// Rule XorExpressionReference
ruleXorExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getXorExpressionReferenceAccess().getOwnedRelationshipXorExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleXorExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getXorExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleXorExpressionMember
entryRuleXorExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getXorExpressionMemberRule()); }
	iv_ruleXorExpressionMember=ruleXorExpressionMember
	{ $current=$iv_ruleXorExpressionMember.current; }
	EOF;

// Rule XorExpressionMember
ruleXorExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getXorExpressionMemberAccess().getOwnedRelatedElementXorExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleXorExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getXorExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.XorExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleXorExpression
entryRuleXorExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getXorExpressionRule()); }
	iv_ruleXorExpression=ruleXorExpression
	{ $current=$iv_ruleXorExpression.current; }
	EOF;

// Rule XorExpression
ruleXorExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getXorExpressionAccess().getAndExpressionParserRuleCall_0());
		}
		this_AndExpression_0=ruleAndExpression
		{
			$current = $this_AndExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getXorExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getXorExpressionAccess().getOperatorXorOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleXorOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getXorExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.XorOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getXorExpressionAccess().getOperandAndExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleAndExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getXorExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.AndExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleXorOperator
entryRuleXorOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getXorOperatorRule()); }
	iv_ruleXorOperator=ruleXorOperator
	{ $current=$iv_ruleXorOperator.current.getText(); }
	EOF;

// Rule XorOperator
ruleXorOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='xor'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getXorOperatorAccess().getXorKeyword());
	}
;

// Entry rule entryRuleAndExpression
entryRuleAndExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAndExpressionRule()); }
	iv_ruleAndExpression=ruleAndExpression
	{ $current=$iv_ruleAndExpression.current; }
	EOF;

// Rule AndExpression
ruleAndExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getAndExpressionAccess().getEqualityExpressionParserRuleCall_0());
		}
		this_EqualityExpression_0=ruleEqualityExpression
		{
			$current = $this_EqualityExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getAndExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperatorAndOperatorParserRuleCall_1_1_0_0_0());
							}
							lv_operator_2_0=ruleAndOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_2_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.AndOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperandEqualityExpressionParserRuleCall_1_1_0_1_0());
							}
							lv_operand_3_0=ruleEqualityExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_3_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperatorConditionalAndOperatorParserRuleCall_1_1_1_0_0());
							}
							lv_operator_4_0=ruleConditionalAndOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_4_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.ConditionalAndOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getAndExpressionAccess().getOperandEqualityExpressionReferenceParserRuleCall_1_1_1_1_0());
							}
							lv_operand_5_0=ruleEqualityExpressionReference
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getAndExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_5_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpressionReference");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)
		)*
	)
;

// Entry rule entryRuleAndOperator
entryRuleAndOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getAndOperatorRule()); }
	iv_ruleAndOperator=ruleAndOperator
	{ $current=$iv_ruleAndOperator.current.getText(); }
	EOF;

// Rule AndOperator
ruleAndOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='&'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getAndOperatorAccess().getAmpersandKeyword());
	}
;

// Entry rule entryRuleConditionalAndOperator
entryRuleConditionalAndOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getConditionalAndOperatorRule()); }
	iv_ruleConditionalAndOperator=ruleConditionalAndOperator
	{ $current=$iv_ruleConditionalAndOperator.current.getText(); }
	EOF;

// Rule ConditionalAndOperator
ruleConditionalAndOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='and'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getConditionalAndOperatorAccess().getAndKeyword());
	}
;

// Entry rule entryRuleEqualityExpressionReference
entryRuleEqualityExpressionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEqualityExpressionReferenceRule()); }
	iv_ruleEqualityExpressionReference=ruleEqualityExpressionReference
	{ $current=$iv_ruleEqualityExpressionReference.current; }
	EOF;

// Rule EqualityExpressionReference
ruleEqualityExpressionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getEqualityExpressionReferenceAccess().getOwnedRelationshipEqualityExpressionMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleEqualityExpressionMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getEqualityExpressionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpressionMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleEqualityExpressionMember
entryRuleEqualityExpressionMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEqualityExpressionMemberRule()); }
	iv_ruleEqualityExpressionMember=ruleEqualityExpressionMember
	{ $current=$iv_ruleEqualityExpressionMember.current; }
	EOF;

// Rule EqualityExpressionMember
ruleEqualityExpressionMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getEqualityExpressionMemberAccess().getOwnedRelatedElementEqualityExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleEqualityExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getEqualityExpressionMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleEqualityExpression
entryRuleEqualityExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEqualityExpressionRule()); }
	iv_ruleEqualityExpression=ruleEqualityExpression
	{ $current=$iv_ruleEqualityExpression.current; }
	EOF;

// Rule EqualityExpression
ruleEqualityExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getEqualityExpressionAccess().getClassificationExpressionParserRuleCall_0());
		}
		this_ClassificationExpression_0=ruleClassificationExpression
		{
			$current = $this_ClassificationExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getEqualityExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getEqualityExpressionAccess().getOperatorEqualityOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleEqualityOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getEqualityExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.EqualityOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getEqualityExpressionAccess().getOperandClassificationExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleClassificationExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getEqualityExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ClassificationExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleEqualityOperator
entryRuleEqualityOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getEqualityOperatorRule()); }
	iv_ruleEqualityOperator=ruleEqualityOperator
	{ $current=$iv_ruleEqualityOperator.current.getText(); }
	EOF;

// Rule EqualityOperator
ruleEqualityOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='=='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getEqualsSignEqualsSignKeyword_0());
		}
		    |
		kw='!='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getExclamationMarkEqualsSignKeyword_1());
		}
		    |
		kw='==='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getEqualsSignEqualsSignEqualsSignKeyword_2());
		}
		    |
		kw='!=='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getEqualityOperatorAccess().getExclamationMarkEqualsSignEqualsSignKeyword_3());
		}
	)
;

// Entry rule entryRuleClassificationExpression
entryRuleClassificationExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getClassificationExpressionRule()); }
	iv_ruleClassificationExpression=ruleClassificationExpression
	{ $current=$iv_ruleClassificationExpression.current; }
	EOF;

// Rule ClassificationExpression
ruleClassificationExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getClassificationExpressionAccess().getRelationalExpressionParserRuleCall_0_0());
			}
			this_RelationalExpression_0=ruleRelationalExpression
			{
				$current = $this_RelationalExpression_0.current;
				afterParserOrEnumRuleCall();
			}
			(
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getClassificationExpressionAccess().getOperatorExpressionOperandAction_0_1_0_0(),
								$current);
						}
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorClassificationTestOperatorParserRuleCall_0_1_0_1_0());
							}
							lv_operator_2_0=ruleClassificationTestOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_2_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.ClassificationTestOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeReferenceMemberParserRuleCall_0_1_0_2_0());
							}
							lv_ownedRelationship_3_0=ruleTypeReferenceMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_3_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReferenceMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getClassificationExpressionAccess().getOperatorExpressionOperandAction_0_1_1_0(),
								$current);
						}
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorCastOperatorParserRuleCall_0_1_1_1_0());
							}
							lv_operator_5_0=ruleCastOperator
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								set(
									$current,
									"operator",
									lv_operator_5_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.CastOperator");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_0_1_1_2_0());
							}
							lv_ownedRelationship_6_0=ruleTypeResultMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_6_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)?
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandSelfReferenceExpressionParserRuleCall_1_1_0());
					}
					lv_operand_8_0=ruleSelfReferenceExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_8_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.SelfReferenceExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorClassificationTestOperatorParserRuleCall_1_2_0());
					}
					lv_operator_9_0=ruleClassificationTestOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_9_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ClassificationTestOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeReferenceMemberParserRuleCall_1_3_0());
					}
					lv_ownedRelationship_10_0=ruleTypeReferenceMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_10_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReferenceMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_2_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandMetadataReferenceParserRuleCall_2_1_0());
					}
					lv_operand_12_0=ruleMetadataReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_12_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetadataReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorMetaClassificationTestOperatorParserRuleCall_2_2_0());
					}
					lv_operator_13_0=ruleMetaClassificationTestOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_13_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetaClassificationTestOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeReferenceMemberParserRuleCall_2_3_0());
					}
					lv_ownedRelationship_14_0=ruleTypeReferenceMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_14_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReferenceMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_3_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandSelfReferenceExpressionParserRuleCall_3_1_0());
					}
					lv_operand_16_0=ruleSelfReferenceExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_16_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.SelfReferenceExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorCastOperatorParserRuleCall_3_2_0());
					}
					lv_operator_17_0=ruleCastOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_17_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.CastOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_3_3_0());
					}
					lv_ownedRelationship_18_0=ruleTypeResultMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_18_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getClassificationExpressionAccess().getOperatorExpressionAction_4_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperandMetadataReferenceParserRuleCall_4_1_0());
					}
					lv_operand_20_0=ruleMetadataReference
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_20_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetadataReference");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOperatorMetaCastOperatorParserRuleCall_4_2_0());
					}
					lv_operator_21_0=ruleMetaCastOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_21_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MetaCastOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getClassificationExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_4_3_0());
					}
					lv_ownedRelationship_22_0=ruleTypeResultMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getClassificationExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_22_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRuleClassificationTestOperator
entryRuleClassificationTestOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getClassificationTestOperatorRule()); }
	iv_ruleClassificationTestOperator=ruleClassificationTestOperator
	{ $current=$iv_ruleClassificationTestOperator.current.getText(); }
	EOF;

// Rule ClassificationTestOperator
ruleClassificationTestOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='hastype'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getClassificationTestOperatorAccess().getHastypeKeyword_0());
		}
		    |
		kw='istype'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getClassificationTestOperatorAccess().getIstypeKeyword_1());
		}
		    |
		kw='@'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getClassificationTestOperatorAccess().getCommercialAtKeyword_2());
		}
	)
;

// Entry rule entryRuleMetaClassificationTestOperator
entryRuleMetaClassificationTestOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getMetaClassificationTestOperatorRule()); }
	iv_ruleMetaClassificationTestOperator=ruleMetaClassificationTestOperator
	{ $current=$iv_ruleMetaClassificationTestOperator.current.getText(); }
	EOF;

// Rule MetaClassificationTestOperator
ruleMetaClassificationTestOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='@@'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getMetaClassificationTestOperatorAccess().getCommercialAtCommercialAtKeyword());
	}
;

// Entry rule entryRuleCastOperator
entryRuleCastOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getCastOperatorRule()); }
	iv_ruleCastOperator=ruleCastOperator
	{ $current=$iv_ruleCastOperator.current.getText(); }
	EOF;

// Rule CastOperator
ruleCastOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='as'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getCastOperatorAccess().getAsKeyword());
	}
;

// Entry rule entryRuleMetaCastOperator
entryRuleMetaCastOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getMetaCastOperatorRule()); }
	iv_ruleMetaCastOperator=ruleMetaCastOperator
	{ $current=$iv_ruleMetaCastOperator.current.getText(); }
	EOF;

// Rule MetaCastOperator
ruleMetaCastOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	kw='meta'
	{
		$current.merge(kw);
		newLeafNode(kw, grammarAccess.getMetaCastOperatorAccess().getMetaKeyword());
	}
;

// Entry rule entryRuleMetadataReference
entryRuleMetadataReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataReferenceRule()); }
	iv_ruleMetadataReference=ruleMetadataReference
	{ $current=$iv_ruleMetadataReference.current; }
	EOF;

// Rule MetadataReference
ruleMetadataReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getMetadataReferenceAccess().getOwnedRelationshipElementReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleElementReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getMetadataReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ElementReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleTypeReferenceMember
entryRuleTypeReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeReferenceMemberRule()); }
	iv_ruleTypeReferenceMember=ruleTypeReferenceMember
	{ $current=$iv_ruleTypeReferenceMember.current; }
	EOF;

// Rule TypeReferenceMember
ruleTypeReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getTypeReferenceMemberAccess().getOwnedRelatedElementTypeReferenceParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleTypeReference
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getTypeReferenceMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReference");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleTypeResultMember
entryRuleTypeResultMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeResultMemberRule()); }
	iv_ruleTypeResultMember=ruleTypeResultMember
	{ $current=$iv_ruleTypeResultMember.current; }
	EOF;

// Rule TypeResultMember
ruleTypeResultMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getTypeResultMemberAccess().getOwnedRelatedElementTypeReferenceParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleTypeReference
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getTypeResultMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeReference");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleTypeReference
entryRuleTypeReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getTypeReferenceRule()); }
	iv_ruleTypeReference=ruleTypeReference
	{ $current=$iv_ruleTypeReference.current; }
	EOF;

// Rule TypeReference
ruleTypeReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getTypeReferenceAccess().getOwnedRelationshipReferenceTypingParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleReferenceTyping
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getTypeReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ReferenceTyping");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleReferenceTyping
entryRuleReferenceTyping returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getReferenceTypingRule()); }
	iv_ruleReferenceTyping=ruleReferenceTyping
	{ $current=$iv_ruleReferenceTyping.current; }
	EOF;

// Rule ReferenceTyping
ruleReferenceTyping returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getReferenceTypingRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getReferenceTypingAccess().getTypeTypeCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleSelfReferenceExpression
entryRuleSelfReferenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSelfReferenceExpressionRule()); }
	iv_ruleSelfReferenceExpression=ruleSelfReferenceExpression
	{ $current=$iv_ruleSelfReferenceExpression.current; }
	EOF;

// Rule SelfReferenceExpression
ruleSelfReferenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getSelfReferenceExpressionAccess().getOwnedRelationshipSelfReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleSelfReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getSelfReferenceExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.SelfReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleSelfReferenceMember
entryRuleSelfReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSelfReferenceMemberRule()); }
	iv_ruleSelfReferenceMember=ruleSelfReferenceMember
	{ $current=$iv_ruleSelfReferenceMember.current; }
	EOF;

// Rule SelfReferenceMember
ruleSelfReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getSelfReferenceMemberAccess().getOwnedRelatedElementEmptyFeatureParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleEmptyFeature
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getSelfReferenceMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.EmptyFeature");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleEmptyFeature
entryRuleEmptyFeature returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getEmptyFeatureRule()); }
	iv_ruleEmptyFeature=ruleEmptyFeature
	{ $current=$iv_ruleEmptyFeature.current; }
	EOF;

// Rule EmptyFeature
ruleEmptyFeature returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			$current = forceCreateModelElement(
				grammarAccess.getEmptyFeatureAccess().getFeatureAction(),
				$current);
		}
	)
;

// Entry rule entryRuleRelationalExpression
entryRuleRelationalExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getRelationalExpressionRule()); }
	iv_ruleRelationalExpression=ruleRelationalExpression
	{ $current=$iv_ruleRelationalExpression.current; }
	EOF;

// Rule RelationalExpression
ruleRelationalExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getRelationalExpressionAccess().getRangeExpressionParserRuleCall_0());
		}
		this_RangeExpression_0=ruleRangeExpression
		{
			$current = $this_RangeExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getRelationalExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getRelationalExpressionAccess().getOperatorRelationalOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleRelationalOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRelationalExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.RelationalOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getRelationalExpressionAccess().getOperandRangeExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleRangeExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRelationalExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.RangeExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleRelationalOperator
entryRuleRelationalOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getRelationalOperatorRule()); }
	iv_ruleRelationalOperator=ruleRelationalOperator
	{ $current=$iv_ruleRelationalOperator.current.getText(); }
	EOF;

// Rule RelationalOperator
ruleRelationalOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='<'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getLessThanSignKeyword_0());
		}
		    |
		kw='>'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getGreaterThanSignKeyword_1());
		}
		    |
		kw='<='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getLessThanSignEqualsSignKeyword_2());
		}
		    |
		kw='>='
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getRelationalOperatorAccess().getGreaterThanSignEqualsSignKeyword_3());
		}
	)
;

// Entry rule entryRuleRangeExpression
entryRuleRangeExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getRangeExpressionRule()); }
	iv_ruleRangeExpression=ruleRangeExpression
	{ $current=$iv_ruleRangeExpression.current; }
	EOF;

// Rule RangeExpression
ruleRangeExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getRangeExpressionAccess().getAdditiveExpressionParserRuleCall_0());
		}
		this_AdditiveExpression_0=ruleAdditiveExpression
		{
			$current = $this_AdditiveExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getRangeExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					lv_operator_2_0='..'
					{
						newLeafNode(lv_operator_2_0, grammarAccess.getRangeExpressionAccess().getOperatorFullStopFullStopKeyword_1_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getRangeExpressionRule());
						}
						setWithLastConsumed($current, "operator", lv_operator_2_0, "..");
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getRangeExpressionAccess().getOperandAdditiveExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleAdditiveExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getRangeExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.AdditiveExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
	)
;

// Entry rule entryRuleAdditiveExpression
entryRuleAdditiveExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getAdditiveExpressionRule()); }
	iv_ruleAdditiveExpression=ruleAdditiveExpression
	{ $current=$iv_ruleAdditiveExpression.current; }
	EOF;

// Rule AdditiveExpression
ruleAdditiveExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getAdditiveExpressionAccess().getMultiplicativeExpressionParserRuleCall_0());
		}
		this_MultiplicativeExpression_0=ruleMultiplicativeExpression
		{
			$current = $this_MultiplicativeExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getAdditiveExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getAdditiveExpressionAccess().getOperatorAdditiveOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleAdditiveOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getAdditiveExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.AdditiveOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getAdditiveExpressionAccess().getOperandMultiplicativeExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleMultiplicativeExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getAdditiveExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MultiplicativeExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleAdditiveOperator
entryRuleAdditiveOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getAdditiveOperatorRule()); }
	iv_ruleAdditiveOperator=ruleAdditiveOperator
	{ $current=$iv_ruleAdditiveOperator.current.getText(); }
	EOF;

// Rule AdditiveOperator
ruleAdditiveOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='+'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getAdditiveOperatorAccess().getPlusSignKeyword_0());
		}
		    |
		kw='-'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getAdditiveOperatorAccess().getHyphenMinusKeyword_1());
		}
	)
;

// Entry rule entryRuleMultiplicativeExpression
entryRuleMultiplicativeExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicativeExpressionRule()); }
	iv_ruleMultiplicativeExpression=ruleMultiplicativeExpression
	{ $current=$iv_ruleMultiplicativeExpression.current; }
	EOF;

// Rule MultiplicativeExpression
ruleMultiplicativeExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getMultiplicativeExpressionAccess().getExponentiationExpressionParserRuleCall_0());
		}
		this_ExponentiationExpression_0=ruleExponentiationExpression
		{
			$current = $this_ExponentiationExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getMultiplicativeExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getMultiplicativeExpressionAccess().getOperatorMultiplicativeOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleMultiplicativeOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMultiplicativeExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.MultiplicativeOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getMultiplicativeExpressionAccess().getOperandExponentiationExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleExponentiationExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getMultiplicativeExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExponentiationExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleMultiplicativeOperator
entryRuleMultiplicativeOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getMultiplicativeOperatorRule()); }
	iv_ruleMultiplicativeOperator=ruleMultiplicativeOperator
	{ $current=$iv_ruleMultiplicativeOperator.current.getText(); }
	EOF;

// Rule MultiplicativeOperator
ruleMultiplicativeOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='*'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getMultiplicativeOperatorAccess().getAsteriskKeyword_0());
		}
		    |
		kw='/'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getMultiplicativeOperatorAccess().getSolidusKeyword_1());
		}
		    |
		kw='%'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getMultiplicativeOperatorAccess().getPercentSignKeyword_2());
		}
	)
;

// Entry rule entryRuleExponentiationExpression
entryRuleExponentiationExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExponentiationExpressionRule()); }
	iv_ruleExponentiationExpression=ruleExponentiationExpression
	{ $current=$iv_ruleExponentiationExpression.current; }
	EOF;

// Rule ExponentiationExpression
ruleExponentiationExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getExponentiationExpressionAccess().getUnaryExpressionParserRuleCall_0());
		}
		this_UnaryExpression_0=ruleUnaryExpression
		{
			$current = $this_UnaryExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getExponentiationExpressionAccess().getOperatorExpressionOperandAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getExponentiationExpressionAccess().getOperatorExponentiationOperatorParserRuleCall_1_1_0());
					}
					lv_operator_2_0=ruleExponentiationOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExponentiationExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExponentiationOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getExponentiationExpressionAccess().getOperandExponentiationExpressionParserRuleCall_1_2_0());
					}
					lv_operand_3_0=ruleExponentiationExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExponentiationExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExponentiationExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
	)
;

// Entry rule entryRuleExponentiationOperator
entryRuleExponentiationOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getExponentiationOperatorRule()); }
	iv_ruleExponentiationOperator=ruleExponentiationOperator
	{ $current=$iv_ruleExponentiationOperator.current.getText(); }
	EOF;

// Rule ExponentiationOperator
ruleExponentiationOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='**'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getExponentiationOperatorAccess().getAsteriskAsteriskKeyword_0());
		}
		    |
		kw='^'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getExponentiationOperatorAccess().getCircumflexAccentKeyword_1());
		}
	)
;

// Entry rule entryRuleUnaryExpression
entryRuleUnaryExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getUnaryExpressionRule()); }
	iv_ruleUnaryExpression=ruleUnaryExpression
	{ $current=$iv_ruleUnaryExpression.current; }
	EOF;

// Rule UnaryExpression
ruleUnaryExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getUnaryExpressionAccess().getOperatorExpressionAction_0_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getUnaryExpressionAccess().getOperatorUnaryOperatorParserRuleCall_0_1_0());
					}
					lv_operator_1_0=ruleUnaryOperator
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getUnaryExpressionRule());
						}
						set(
							$current,
							"operator",
							lv_operator_1_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.UnaryOperator");
						afterParserOrEnumRuleCall();
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getUnaryExpressionAccess().getOperandExtentExpressionParserRuleCall_0_2_0());
					}
					lv_operand_2_0=ruleExtentExpression
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getUnaryExpressionRule());
						}
						add(
							$current,
							"operand",
							lv_operand_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ExtentExpression");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		{
			newCompositeNode(grammarAccess.getUnaryExpressionAccess().getExtentExpressionParserRuleCall_1());
		}
		this_ExtentExpression_3=ruleExtentExpression
		{
			$current = $this_ExtentExpression_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleUnaryOperator
entryRuleUnaryOperator returns [String current=null]:
	{ newCompositeNode(grammarAccess.getUnaryOperatorRule()); }
	iv_ruleUnaryOperator=ruleUnaryOperator
	{ $current=$iv_ruleUnaryOperator.current.getText(); }
	EOF;

// Rule UnaryOperator
ruleUnaryOperator returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='+'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getPlusSignKeyword_0());
		}
		    |
		kw='-'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getHyphenMinusKeyword_1());
		}
		    |
		kw='~'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getTildeKeyword_2());
		}
		    |
		kw='not'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getUnaryOperatorAccess().getNotKeyword_3());
		}
	)
;

// Entry rule entryRuleExtentExpression
entryRuleExtentExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExtentExpressionRule()); }
	iv_ruleExtentExpression=ruleExtentExpression
	{ $current=$iv_ruleExtentExpression.current; }
	EOF;

// Rule ExtentExpression
ruleExtentExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getExtentExpressionAccess().getOperatorExpressionAction_0_0(),
						$current);
				}
			)
			(
				(
					lv_operator_1_0='all'
					{
						newLeafNode(lv_operator_1_0, grammarAccess.getExtentExpressionAccess().getOperatorAllKeyword_0_1_0());
					}
					{
						if ($current==null) {
							$current = createModelElement(grammarAccess.getExtentExpressionRule());
						}
						setWithLastConsumed($current, "operator", lv_operator_1_0, "all");
					}
				)
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getExtentExpressionAccess().getOwnedRelationshipTypeResultMemberParserRuleCall_0_2_0());
					}
					lv_ownedRelationship_2_0=ruleTypeResultMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getExtentExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.TypeResultMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
		    |
		{
			newCompositeNode(grammarAccess.getExtentExpressionAccess().getPrimaryExpressionParserRuleCall_1());
		}
		this_PrimaryExpression_3=rulePrimaryExpression
		{
			$current = $this_PrimaryExpression_3.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRulePrimaryExpression
entryRulePrimaryExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getPrimaryExpressionRule()); }
	iv_rulePrimaryExpression=rulePrimaryExpression
	{ $current=$iv_rulePrimaryExpression.current; }
	EOF;

// Rule PrimaryExpression
rulePrimaryExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getBaseExpressionParserRuleCall_0());
		}
		this_BaseExpression_0=ruleBaseExpression
		{
			$current = $this_BaseExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			(
				{
					$current = forceCreateModelElementAndAdd(
						grammarAccess.getPrimaryExpressionAccess().getFeatureChainExpressionOperandAction_1_0(),
						$current);
				}
			)
			otherlv_2='.'
			{
				newLeafNode(otherlv_2, grammarAccess.getPrimaryExpressionAccess().getFullStopKeyword_1_1());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOwnedRelationshipFeatureChainMemberParserRuleCall_1_2_0());
					}
					lv_ownedRelationship_3_0=ruleFeatureChainMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_3_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureChainMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)?
		(
			(
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getIndexExpressionOperandAction_2_0_0_0(),
								$current);
						}
					)
					otherlv_5='#'
					{
						newLeafNode(otherlv_5, grammarAccess.getPrimaryExpressionAccess().getNumberSignKeyword_2_0_0_1());
					}
					otherlv_6='('
					{
						newLeafNode(otherlv_6, grammarAccess.getPrimaryExpressionAccess().getLeftParenthesisKeyword_2_0_0_2());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandSequenceExpressionParserRuleCall_2_0_0_3_0());
							}
							lv_operand_7_0=ruleSequenceExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_7_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.SequenceExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
					otherlv_8=')'
					{
						newLeafNode(otherlv_8, grammarAccess.getPrimaryExpressionAccess().getRightParenthesisKeyword_2_0_0_4());
					}
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getOperatorExpressionOperandAction_2_0_1_0(),
								$current);
						}
					)
					(
						(
							lv_operator_10_0='['
							{
								newLeafNode(lv_operator_10_0, grammarAccess.getPrimaryExpressionAccess().getOperatorLeftSquareBracketKeyword_2_0_1_1_0());
							}
							{
								if ($current==null) {
									$current = createModelElement(grammarAccess.getPrimaryExpressionRule());
								}
								setWithLastConsumed($current, "operator", lv_operator_10_0, "[");
							}
						)
					)
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandSequenceExpressionParserRuleCall_2_0_1_2_0());
							}
							lv_operand_11_0=ruleSequenceExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_11_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.SequenceExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
					otherlv_12=']'
					{
						newLeafNode(otherlv_12, grammarAccess.getPrimaryExpressionAccess().getRightSquareBracketKeyword_2_0_1_3());
					}
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getInvocationExpressionOperandAction_2_0_2_0(),
								$current);
						}
					)
					otherlv_14='->'
					{
						newLeafNode(otherlv_14, grammarAccess.getPrimaryExpressionAccess().getHyphenMinusGreaterThanSignKeyword_2_0_2_1());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOwnedRelationshipInstantiatedTypeMemberParserRuleCall_2_0_2_2_0());
							}
							lv_ownedRelationship_15_0=ruleInstantiatedTypeMember
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"ownedRelationship",
									lv_ownedRelationship_15_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.InstantiatedTypeMember");
								afterParserOrEnumRuleCall();
							}
						)
					)
					(
						(
							(
								{
									newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandBodyExpressionParserRuleCall_2_0_2_3_0_0());
								}
								lv_operand_16_0=ruleBodyExpression
								{
									if ($current==null) {
										$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
									}
									add(
										$current,
										"operand",
										lv_operand_16_0,
										"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyExpression");
									afterParserOrEnumRuleCall();
								}
							)
						)
						    |
						(
							(
								{
									newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandFunctionReferenceExpressionParserRuleCall_2_0_2_3_1_0());
								}
								lv_operand_17_0=ruleFunctionReferenceExpression
								{
									if ($current==null) {
										$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
									}
									add(
										$current,
										"operand",
										lv_operand_17_0,
										"org.omg.kerml.expressions.xtext.KerMLExpressions.FunctionReferenceExpression");
									afterParserOrEnumRuleCall();
								}
							)
						)
						    |
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getPrimaryExpressionRule());
							}
							newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getArgumentListParserRuleCall_2_0_2_3_2());
						}
						this_ArgumentList_18=ruleArgumentList[$current]
						{
							$current = $this_ArgumentList_18.current;
							afterParserOrEnumRuleCall();
						}
					)
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getCollectExpressionOperandAction_2_0_3_0(),
								$current);
						}
					)
					otherlv_20='.'
					{
						newLeafNode(otherlv_20, grammarAccess.getPrimaryExpressionAccess().getFullStopKeyword_2_0_3_1());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandBodyExpressionParserRuleCall_2_0_3_2_0());
							}
							lv_operand_21_0=ruleBodyExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_21_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
				    |
				(
					(
						{
							$current = forceCreateModelElementAndAdd(
								grammarAccess.getPrimaryExpressionAccess().getSelectExpressionOperandAction_2_0_4_0(),
								$current);
						}
					)
					otherlv_23='.?'
					{
						newLeafNode(otherlv_23, grammarAccess.getPrimaryExpressionAccess().getFullStopQuestionMarkKeyword_2_0_4_1());
					}
					(
						(
							{
								newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOperandBodyExpressionParserRuleCall_2_0_4_2_0());
							}
							lv_operand_24_0=ruleBodyExpression
							{
								if ($current==null) {
									$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
								}
								add(
									$current,
									"operand",
									lv_operand_24_0,
									"org.omg.kerml.expressions.xtext.KerMLExpressions.BodyExpression");
								afterParserOrEnumRuleCall();
							}
						)
					)
				)
			)
			(
				(
					{
						$current = forceCreateModelElementAndAdd(
							grammarAccess.getPrimaryExpressionAccess().getFeatureChainExpressionOperandAction_2_1_0(),
							$current);
					}
				)
				otherlv_26='.'
				{
					newLeafNode(otherlv_26, grammarAccess.getPrimaryExpressionAccess().getFullStopKeyword_2_1_1());
				}
				(
					(
						{
							newCompositeNode(grammarAccess.getPrimaryExpressionAccess().getOwnedRelationshipFeatureChainMemberParserRuleCall_2_1_2_0());
						}
						lv_ownedRelationship_27_0=ruleFeatureChainMember
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getPrimaryExpressionRule());
							}
							add(
								$current,
								"ownedRelationship",
								lv_ownedRelationship_27_0,
								"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureChainMember");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)?
		)*
	)
;

// Entry rule entryRuleFunctionReferenceExpression
entryRuleFunctionReferenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionReferenceExpressionRule()); }
	iv_ruleFunctionReferenceExpression=ruleFunctionReferenceExpression
	{ $current=$iv_ruleFunctionReferenceExpression.current; }
	EOF;

// Rule FunctionReferenceExpression
ruleFunctionReferenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFunctionReferenceExpressionAccess().getOwnedRelationshipFunctionReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleFunctionReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFunctionReferenceExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.FunctionReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFunctionReferenceMember
entryRuleFunctionReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionReferenceMemberRule()); }
	iv_ruleFunctionReferenceMember=ruleFunctionReferenceMember
	{ $current=$iv_ruleFunctionReferenceMember.current; }
	EOF;

// Rule FunctionReferenceMember
ruleFunctionReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFunctionReferenceMemberAccess().getOwnedRelatedElementFunctionReferenceParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleFunctionReference
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFunctionReferenceMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.FunctionReference");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFunctionReference
entryRuleFunctionReference returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFunctionReferenceRule()); }
	iv_ruleFunctionReference=ruleFunctionReference
	{ $current=$iv_ruleFunctionReference.current; }
	EOF;

// Rule FunctionReference
ruleFunctionReference returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFunctionReferenceAccess().getOwnedRelationshipReferenceTypingParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleReferenceTyping
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFunctionReferenceRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ReferenceTyping");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFeatureChainMember
entryRuleFeatureChainMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureChainMemberRule()); }
	iv_ruleFeatureChainMember=ruleFeatureChainMember
	{ $current=$iv_ruleFeatureChainMember.current; }
	EOF;

// Rule FeatureChainMember
ruleFeatureChainMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getFeatureChainMemberRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getFeatureChainMemberAccess().getMemberElementFeatureCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getFeatureChainMemberAccess().getOwningMembershipAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureChainMemberAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_1_0());
					}
					lv_ownedRelatedElement_2_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureChainMemberRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;

// Entry rule entryRuleOwnedFeatureChain
entryRuleOwnedFeatureChain returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureChainRule()); }
	iv_ruleOwnedFeatureChain=ruleOwnedFeatureChain
	{ $current=$iv_ruleOwnedFeatureChain.current; }
	EOF;

// Rule OwnedFeatureChain
ruleOwnedFeatureChain returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getOwnedFeatureChainRule());
		}
		newCompositeNode(grammarAccess.getOwnedFeatureChainAccess().getFeatureChainParserRuleCall());
	}
	this_FeatureChain_0=ruleFeatureChain[$current]
	{
		$current = $this_FeatureChain_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleBaseExpression
entryRuleBaseExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBaseExpressionRule()); }
	iv_ruleBaseExpression=ruleBaseExpression
	{ $current=$iv_ruleBaseExpression.current; }
	EOF;

// Rule BaseExpression
ruleBaseExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getNullExpressionParserRuleCall_0());
		}
		this_NullExpression_0=ruleNullExpression
		{
			$current = $this_NullExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getLiteralExpressionParserRuleCall_1());
		}
		this_LiteralExpression_1=ruleLiteralExpression
		{
			$current = $this_LiteralExpression_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getFeatureReferenceExpressionParserRuleCall_2());
		}
		this_FeatureReferenceExpression_2=ruleFeatureReferenceExpression
		{
			$current = $this_FeatureReferenceExpression_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getMetadataAccessExpressionParserRuleCall_3());
		}
		this_MetadataAccessExpression_3=ruleMetadataAccessExpression
		{
			$current = $this_MetadataAccessExpression_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getInvocationExpressionParserRuleCall_4());
		}
		this_InvocationExpression_4=ruleInvocationExpression
		{
			$current = $this_InvocationExpression_4.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getConstructorExpressionParserRuleCall_5());
		}
		this_ConstructorExpression_5=ruleConstructorExpression
		{
			$current = $this_ConstructorExpression_5.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getBaseExpressionAccess().getBodyExpressionParserRuleCall_6());
		}
		this_BodyExpression_6=ruleBodyExpression
		{
			$current = $this_BodyExpression_6.current;
			afterParserOrEnumRuleCall();
		}
		    |
		(
			otherlv_7='('
			{
				newLeafNode(otherlv_7, grammarAccess.getBaseExpressionAccess().getLeftParenthesisKeyword_7_0());
			}
			{
				newCompositeNode(grammarAccess.getBaseExpressionAccess().getSequenceExpressionParserRuleCall_7_1());
			}
			this_SequenceExpression_8=ruleSequenceExpression
			{
				$current = $this_SequenceExpression_8.current;
				afterParserOrEnumRuleCall();
			}
			otherlv_9=')'
			{
				newLeafNode(otherlv_9, grammarAccess.getBaseExpressionAccess().getRightParenthesisKeyword_7_2());
			}
		)
	)
;

// Entry rule entryRuleBodyExpression
entryRuleBodyExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBodyExpressionRule()); }
	iv_ruleBodyExpression=ruleBodyExpression
	{ $current=$iv_ruleBodyExpression.current; }
	EOF;

// Rule BodyExpression
ruleBodyExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getBodyExpressionAccess().getOwnedRelationshipExpressionBodyMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleExpressionBodyMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getBodyExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ExpressionBodyMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleExpressionBodyMember
entryRuleExpressionBodyMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getExpressionBodyMemberRule()); }
	iv_ruleExpressionBodyMember=ruleExpressionBodyMember
	{ $current=$iv_ruleExpressionBodyMember.current; }
	EOF;

// Rule ExpressionBodyMember
ruleExpressionBodyMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getExpressionBodyMemberAccess().getOwnedRelatedElementExpressionBodyParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleExpressionBody
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getExpressionBodyMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.xtext.KerML.ExpressionBody");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleBodyParameter
entryRuleBodyParameter returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getBodyParameterRule()); }
	iv_ruleBodyParameter=ruleBodyParameter
	{ $current=$iv_ruleBodyParameter.current; }
	EOF;

// Rule BodyParameter
ruleBodyParameter returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getBodyParameterAccess().getDeclaredNameNameParserRuleCall_0());
			}
			lv_declaredName_0_0=ruleName
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getBodyParameterRule());
				}
				set(
					$current,
					"declaredName",
					lv_declaredName_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.Name");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleSequenceExpression
entryRuleSequenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getSequenceExpressionRule()); }
	iv_ruleSequenceExpression=ruleSequenceExpression
	{ $current=$iv_ruleSequenceExpression.current; }
	EOF;

// Rule SequenceExpression
ruleSequenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getSequenceExpressionAccess().getOwnedExpressionParserRuleCall_0());
		}
		this_OwnedExpression_0=ruleOwnedExpression
		{
			$current = $this_OwnedExpression_0.current;
			afterParserOrEnumRuleCall();
		}
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getSequenceExpressionAccess().getCommaKeyword_1_0());
			}
			    |
			(
				(
					{
						$current = forceCreateModelElementAndAdd(
							grammarAccess.getSequenceExpressionAccess().getOperatorExpressionOperandAction_1_1_0(),
							$current);
					}
				)
				(
					(
						lv_operator_3_0=','
						{
							newLeafNode(lv_operator_3_0, grammarAccess.getSequenceExpressionAccess().getOperatorCommaKeyword_1_1_1_0());
						}
						{
							if ($current==null) {
								$current = createModelElement(grammarAccess.getSequenceExpressionRule());
							}
							setWithLastConsumed($current, "operator", lv_operator_3_0, ",");
						}
					)
				)
				(
					(
						{
							newCompositeNode(grammarAccess.getSequenceExpressionAccess().getOperandSequenceExpressionParserRuleCall_1_1_2_0());
						}
						lv_operand_4_0=ruleSequenceExpression
						{
							if ($current==null) {
								$current = createModelElementForParent(grammarAccess.getSequenceExpressionRule());
							}
							add(
								$current,
								"operand",
								lv_operand_4_0,
								"org.omg.kerml.expressions.xtext.KerMLExpressions.SequenceExpression");
							afterParserOrEnumRuleCall();
						}
					)
				)
			)
		)?
	)
;

// Entry rule entryRuleFeatureReferenceExpression
entryRuleFeatureReferenceExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureReferenceExpressionRule()); }
	iv_ruleFeatureReferenceExpression=ruleFeatureReferenceExpression
	{ $current=$iv_ruleFeatureReferenceExpression.current; }
	EOF;

// Rule FeatureReferenceExpression
ruleFeatureReferenceExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getFeatureReferenceExpressionAccess().getOwnedRelationshipFeatureReferenceMemberParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleFeatureReferenceMember
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getFeatureReferenceExpressionRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.FeatureReferenceMember");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleFeatureReferenceMember
entryRuleFeatureReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getFeatureReferenceMemberRule()); }
	iv_ruleFeatureReferenceMember=ruleFeatureReferenceMember
	{ $current=$iv_ruleFeatureReferenceMember.current; }
	EOF;

// Rule FeatureReferenceMember
ruleFeatureReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getFeatureReferenceMemberRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getFeatureReferenceMemberAccess().getMemberElementFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleMetadataAccessExpression
entryRuleMetadataAccessExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getMetadataAccessExpressionRule()); }
	iv_ruleMetadataAccessExpression=ruleMetadataAccessExpression
	{ $current=$iv_ruleMetadataAccessExpression.current; }
	EOF;

// Rule MetadataAccessExpression
ruleMetadataAccessExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getMetadataAccessExpressionAccess().getOwnedRelationshipElementReferenceMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleElementReferenceMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getMetadataAccessExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ElementReferenceMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_1='.'
		{
			newLeafNode(otherlv_1, grammarAccess.getMetadataAccessExpressionAccess().getFullStopKeyword_1());
		}
		otherlv_2='metadata'
		{
			newLeafNode(otherlv_2, grammarAccess.getMetadataAccessExpressionAccess().getMetadataKeyword_2());
		}
	)
;

// Entry rule entryRuleElementReferenceMember
entryRuleElementReferenceMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getElementReferenceMemberRule()); }
	iv_ruleElementReferenceMember=ruleElementReferenceMember
	{ $current=$iv_ruleElementReferenceMember.current; }
	EOF;

// Rule ElementReferenceMember
ruleElementReferenceMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getElementReferenceMemberRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getElementReferenceMemberAccess().getMemberElementElementCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleInvocationExpression
entryRuleInvocationExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getInvocationExpressionRule()); }
	iv_ruleInvocationExpression=ruleInvocationExpression
	{ $current=$iv_ruleInvocationExpression.current; }
	EOF;

// Rule InvocationExpression
ruleInvocationExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getInvocationExpressionAccess().getOwnedRelationshipInstantiatedTypeMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleInstantiatedTypeMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getInvocationExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.InstantiatedTypeMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		{
			if ($current==null) {
				$current = createModelElement(grammarAccess.getInvocationExpressionRule());
			}
			newCompositeNode(grammarAccess.getInvocationExpressionAccess().getArgumentListParserRuleCall_1());
		}
		this_ArgumentList_1=ruleArgumentList[$current]
		{
			$current = $this_ArgumentList_1.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleConstructorExpression
entryRuleConstructorExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConstructorExpressionRule()); }
	iv_ruleConstructorExpression=ruleConstructorExpression
	{ $current=$iv_ruleConstructorExpression.current; }
	EOF;

// Rule ConstructorExpression
ruleConstructorExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='new'
		{
			newLeafNode(otherlv_0, grammarAccess.getConstructorExpressionAccess().getNewKeyword_0());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getConstructorExpressionAccess().getOwnedRelationshipInstantiatedTypeMemberParserRuleCall_1_0());
				}
				lv_ownedRelationship_1_0=ruleInstantiatedTypeMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConstructorExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_1_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.InstantiatedTypeMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			(
				{
					newCompositeNode(grammarAccess.getConstructorExpressionAccess().getOwnedRelationshipConstructorResultMemberParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleConstructorResultMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getConstructorExpressionRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ConstructorResultMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleConstructorResultMember
entryRuleConstructorResultMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConstructorResultMemberRule()); }
	iv_ruleConstructorResultMember=ruleConstructorResultMember
	{ $current=$iv_ruleConstructorResultMember.current; }
	EOF;

// Rule ConstructorResultMember
ruleConstructorResultMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getConstructorResultMemberAccess().getOwnedRelatedElementConstructorResultParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleConstructorResult
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getConstructorResultMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ConstructorResult");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleConstructorResult
entryRuleConstructorResult returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getConstructorResultRule()); }
	iv_ruleConstructorResult=ruleConstructorResult
	{ $current=$iv_ruleConstructorResult.current; }
	EOF;

// Rule ConstructorResult
ruleConstructorResult returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	{
		if ($current==null) {
			$current = createModelElement(grammarAccess.getConstructorResultRule());
		}
		newCompositeNode(grammarAccess.getConstructorResultAccess().getArgumentListParserRuleCall());
	}
	this_ArgumentList_0=ruleArgumentList[$current]
	{
		$current = $this_ArgumentList_0.current;
		afterParserOrEnumRuleCall();
	}
;

// Entry rule entryRuleInstantiatedTypeMember
entryRuleInstantiatedTypeMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getInstantiatedTypeMemberRule()); }
	iv_ruleInstantiatedTypeMember=ruleInstantiatedTypeMember
	{ $current=$iv_ruleInstantiatedTypeMember.current; }
	EOF;

// Rule InstantiatedTypeMember
ruleInstantiatedTypeMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					if ($current==null) {
						$current = createModelElement(grammarAccess.getInstantiatedTypeMemberRule());
					}
				}
				{
					newCompositeNode(grammarAccess.getInstantiatedTypeMemberAccess().getMemberElementTypeCrossReference_0_0());
				}
				ruleQualifiedName
				{
					afterParserOrEnumRuleCall();
				}
			)
		)
		    |
		(
			(
				{
					$current = forceCreateModelElement(
						grammarAccess.getInstantiatedTypeMemberAccess().getOwningMembershipAction_1_0(),
						$current);
				}
			)
			(
				(
					{
						newCompositeNode(grammarAccess.getInstantiatedTypeMemberAccess().getOwnedRelatedElementOwnedFeatureChainParserRuleCall_1_1_0());
					}
					lv_ownedRelatedElement_2_0=ruleOwnedFeatureChain
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getInstantiatedTypeMemberRule());
						}
						add(
							$current,
							"ownedRelatedElement",
							lv_ownedRelatedElement_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChain");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)
	)
;


// Rule FeatureChain
ruleFeatureChain[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getFeatureChainAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleOwnedFeatureChaining
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getFeatureChainRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1='.'
			{
				newLeafNode(otherlv_1, grammarAccess.getFeatureChainAccess().getFullStopKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getFeatureChainAccess().getOwnedRelationshipOwnedFeatureChainingParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleOwnedFeatureChaining
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getFeatureChainRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedFeatureChaining");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)+
	)
;

// Entry rule entryRuleOwnedFeatureChaining
entryRuleOwnedFeatureChaining returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getOwnedFeatureChainingRule()); }
	iv_ruleOwnedFeatureChaining=ruleOwnedFeatureChaining
	{ $current=$iv_ruleOwnedFeatureChaining.current; }
	EOF;

// Rule OwnedFeatureChaining
ruleOwnedFeatureChaining returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getOwnedFeatureChainingRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getOwnedFeatureChainingAccess().getChainingFeatureFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule ArgumentList
ruleArgumentList[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		otherlv_0='('
		{
			newLeafNode(otherlv_0, grammarAccess.getArgumentListAccess().getLeftParenthesisKeyword_0());
		}
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getArgumentListRule());
				}
				newCompositeNode(grammarAccess.getArgumentListAccess().getPositionalArgumentListParserRuleCall_1_0());
			}
			this_PositionalArgumentList_1=rulePositionalArgumentList[$current]
			{
				$current = $this_PositionalArgumentList_1.current;
				afterParserOrEnumRuleCall();
			}
			    |
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getArgumentListRule());
				}
				newCompositeNode(grammarAccess.getArgumentListAccess().getNamedArgumentListParserRuleCall_1_1());
			}
			this_NamedArgumentList_2=ruleNamedArgumentList[$current]
			{
				$current = $this_NamedArgumentList_2.current;
				afterParserOrEnumRuleCall();
			}
		)?
		otherlv_3=')'
		{
			newLeafNode(otherlv_3, grammarAccess.getArgumentListAccess().getRightParenthesisKeyword_2());
		}
	)
;


// Rule PositionalArgumentList
rulePositionalArgumentList[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getPositionalArgumentListAccess().getOwnedRelationshipArgumentMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleArgumentMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getPositionalArgumentListRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getPositionalArgumentListAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getPositionalArgumentListAccess().getOwnedRelationshipArgumentMemberParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleArgumentMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getPositionalArgumentListRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleArgumentMember
entryRuleArgumentMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getArgumentMemberRule()); }
	iv_ruleArgumentMember=ruleArgumentMember
	{ $current=$iv_ruleArgumentMember.current; }
	EOF;

// Rule ArgumentMember
ruleArgumentMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getArgumentMemberAccess().getOwnedRelatedElementArgumentParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleArgument
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getArgumentMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.Argument");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleArgument
entryRuleArgument returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getArgumentRule()); }
	iv_ruleArgument=ruleArgument
	{ $current=$iv_ruleArgument.current; }
	EOF;

// Rule Argument
ruleArgument returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getArgumentAccess().getOwnedRelationshipArgumentValueParserRuleCall_0());
			}
			lv_ownedRelationship_0_0=ruleArgumentValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getArgumentRule());
				}
				add(
					$current,
					"ownedRelationship",
					lv_ownedRelationship_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;


// Rule NamedArgumentList
ruleNamedArgumentList[EObject in_current]  returns [EObject current=in_current]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getNamedArgumentListAccess().getOwnedRelationshipNamedArgumentMemberParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleNamedArgumentMember
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamedArgumentListRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.NamedArgumentMember");
					afterParserOrEnumRuleCall();
				}
			)
		)
		(
			otherlv_1=','
			{
				newLeafNode(otherlv_1, grammarAccess.getNamedArgumentListAccess().getCommaKeyword_1_0());
			}
			(
				(
					{
						newCompositeNode(grammarAccess.getNamedArgumentListAccess().getOwnedRelationshipNamedArgumentMemberParserRuleCall_1_1_0());
					}
					lv_ownedRelationship_2_0=ruleNamedArgumentMember
					{
						if ($current==null) {
							$current = createModelElementForParent(grammarAccess.getNamedArgumentListRule());
						}
						add(
							$current,
							"ownedRelationship",
							lv_ownedRelationship_2_0,
							"org.omg.kerml.expressions.xtext.KerMLExpressions.NamedArgumentMember");
						afterParserOrEnumRuleCall();
					}
				)
			)
		)*
	)
;

// Entry rule entryRuleNamedArgumentMember
entryRuleNamedArgumentMember returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamedArgumentMemberRule()); }
	iv_ruleNamedArgumentMember=ruleNamedArgumentMember
	{ $current=$iv_ruleNamedArgumentMember.current; }
	EOF;

// Rule NamedArgumentMember
ruleNamedArgumentMember returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getNamedArgumentMemberAccess().getOwnedRelatedElementNamedArgumentParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleNamedArgument
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getNamedArgumentMemberRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.NamedArgument");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleNamedArgument
entryRuleNamedArgument returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNamedArgumentRule()); }
	iv_ruleNamedArgument=ruleNamedArgument
	{ $current=$iv_ruleNamedArgument.current; }
	EOF;

// Rule NamedArgument
ruleNamedArgument returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				{
					newCompositeNode(grammarAccess.getNamedArgumentAccess().getOwnedRelationshipParameterRedefinitionParserRuleCall_0_0());
				}
				lv_ownedRelationship_0_0=ruleParameterRedefinition
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamedArgumentRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_0_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ParameterRedefinition");
					afterParserOrEnumRuleCall();
				}
			)
		)
		otherlv_1='='
		{
			newLeafNode(otherlv_1, grammarAccess.getNamedArgumentAccess().getEqualsSignKeyword_1());
		}
		(
			(
				{
					newCompositeNode(grammarAccess.getNamedArgumentAccess().getOwnedRelationshipArgumentValueParserRuleCall_2_0());
				}
				lv_ownedRelationship_2_0=ruleArgumentValue
				{
					if ($current==null) {
						$current = createModelElementForParent(grammarAccess.getNamedArgumentRule());
					}
					add(
						$current,
						"ownedRelationship",
						lv_ownedRelationship_2_0,
						"org.omg.kerml.expressions.xtext.KerMLExpressions.ArgumentValue");
					afterParserOrEnumRuleCall();
				}
			)
		)
	)
;

// Entry rule entryRuleParameterRedefinition
entryRuleParameterRedefinition returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getParameterRedefinitionRule()); }
	iv_ruleParameterRedefinition=ruleParameterRedefinition
	{ $current=$iv_ruleParameterRedefinition.current; }
	EOF;

// Rule ParameterRedefinition
ruleParameterRedefinition returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getParameterRedefinitionRule());
				}
			}
			{
				newCompositeNode(grammarAccess.getParameterRedefinitionAccess().getRedefinedFeatureFeatureCrossReference_0());
			}
			ruleQualifiedName
			{
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleArgumentValue
entryRuleArgumentValue returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getArgumentValueRule()); }
	iv_ruleArgumentValue=ruleArgumentValue
	{ $current=$iv_ruleArgumentValue.current; }
	EOF;

// Rule ArgumentValue
ruleArgumentValue returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getArgumentValueAccess().getOwnedRelatedElementOwnedExpressionParserRuleCall_0());
			}
			lv_ownedRelatedElement_0_0=ruleOwnedExpression
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getArgumentValueRule());
				}
				add(
					$current,
					"ownedRelatedElement",
					lv_ownedRelatedElement_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.OwnedExpression");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleNullExpression
entryRuleNullExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getNullExpressionRule()); }
	iv_ruleNullExpression=ruleNullExpression
	{ $current=$iv_ruleNullExpression.current; }
	EOF;

// Rule NullExpression
ruleNullExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				$current = forceCreateModelElement(
					grammarAccess.getNullExpressionAccess().getNullExpressionAction_0(),
					$current);
			}
		)
		(
			otherlv_1='null'
			{
				newLeafNode(otherlv_1, grammarAccess.getNullExpressionAccess().getNullKeyword_1_0());
			}
			    |
			(
				otherlv_2='('
				{
					newLeafNode(otherlv_2, grammarAccess.getNullExpressionAccess().getLeftParenthesisKeyword_1_1_0());
				}
				otherlv_3=')'
				{
					newLeafNode(otherlv_3, grammarAccess.getNullExpressionAccess().getRightParenthesisKeyword_1_1_1());
				}
			)
		)
	)
;

// Entry rule entryRuleLiteralExpression
entryRuleLiteralExpression returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralExpressionRule()); }
	iv_ruleLiteralExpression=ruleLiteralExpression
	{ $current=$iv_ruleLiteralExpression.current; }
	EOF;

// Rule LiteralExpression
ruleLiteralExpression returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralBooleanParserRuleCall_0());
		}
		this_LiteralBoolean_0=ruleLiteralBoolean
		{
			$current = $this_LiteralBoolean_0.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralStringParserRuleCall_1());
		}
		this_LiteralString_1=ruleLiteralString
		{
			$current = $this_LiteralString_1.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralIntegerParserRuleCall_2());
		}
		this_LiteralInteger_2=ruleLiteralInteger
		{
			$current = $this_LiteralInteger_2.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralRealParserRuleCall_3());
		}
		this_LiteralReal_3=ruleLiteralReal
		{
			$current = $this_LiteralReal_3.current;
			afterParserOrEnumRuleCall();
		}
		    |
		{
			newCompositeNode(grammarAccess.getLiteralExpressionAccess().getLiteralInfinityParserRuleCall_4());
		}
		this_LiteralInfinity_4=ruleLiteralInfinity
		{
			$current = $this_LiteralInfinity_4.current;
			afterParserOrEnumRuleCall();
		}
	)
;

// Entry rule entryRuleLiteralBoolean
entryRuleLiteralBoolean returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralBooleanRule()); }
	iv_ruleLiteralBoolean=ruleLiteralBoolean
	{ $current=$iv_ruleLiteralBoolean.current; }
	EOF;

// Rule LiteralBoolean
ruleLiteralBoolean returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getLiteralBooleanAccess().getValueBooleanValueParserRuleCall_0());
			}
			lv_value_0_0=ruleBooleanValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getLiteralBooleanRule());
				}
				set(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.BooleanValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleBooleanValue
entryRuleBooleanValue returns [String current=null]:
	{ newCompositeNode(grammarAccess.getBooleanValueRule()); }
	iv_ruleBooleanValue=ruleBooleanValue
	{ $current=$iv_ruleBooleanValue.current.getText(); }
	EOF;

// Rule BooleanValue
ruleBooleanValue returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='true'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getBooleanValueAccess().getTrueKeyword_0());
		}
		    |
		kw='false'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getBooleanValueAccess().getFalseKeyword_1());
		}
	)
;

// Entry rule entryRuleLiteralString
entryRuleLiteralString returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralStringRule()); }
	iv_ruleLiteralString=ruleLiteralString
	{ $current=$iv_ruleLiteralString.current; }
	EOF;

// Rule LiteralString
ruleLiteralString returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			lv_value_0_0=RULE_STRING_VALUE
			{
				newLeafNode(lv_value_0_0, grammarAccess.getLiteralStringAccess().getValueSTRING_VALUETerminalRuleCall_0());
			}
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getLiteralStringRule());
				}
				setWithLastConsumed(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.STRING_VALUE");
			}
		)
	)
;

// Entry rule entryRuleLiteralInteger
entryRuleLiteralInteger returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralIntegerRule()); }
	iv_ruleLiteralInteger=ruleLiteralInteger
	{ $current=$iv_ruleLiteralInteger.current; }
	EOF;

// Rule LiteralInteger
ruleLiteralInteger returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			lv_value_0_0=RULE_DECIMAL_VALUE
			{
				newLeafNode(lv_value_0_0, grammarAccess.getLiteralIntegerAccess().getValueDECIMAL_VALUETerminalRuleCall_0());
			}
			{
				if ($current==null) {
					$current = createModelElement(grammarAccess.getLiteralIntegerRule());
				}
				setWithLastConsumed(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.DECIMAL_VALUE");
			}
		)
	)
;

// Entry rule entryRuleLiteralReal
entryRuleLiteralReal returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralRealRule()); }
	iv_ruleLiteralReal=ruleLiteralReal
	{ $current=$iv_ruleLiteralReal.current; }
	EOF;

// Rule LiteralReal
ruleLiteralReal returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getLiteralRealAccess().getValueRealValueParserRuleCall_0());
			}
			lv_value_0_0=ruleRealValue
			{
				if ($current==null) {
					$current = createModelElementForParent(grammarAccess.getLiteralRealRule());
				}
				set(
					$current,
					"value",
					lv_value_0_0,
					"org.omg.kerml.expressions.xtext.KerMLExpressions.RealValue");
				afterParserOrEnumRuleCall();
			}
		)
	)
;

// Entry rule entryRuleRealValue
entryRuleRealValue returns [String current=null]:
	{ newCompositeNode(grammarAccess.getRealValueRule()); }
	iv_ruleRealValue=ruleRealValue
	{ $current=$iv_ruleRealValue.current.getText(); }
	EOF;

// Rule RealValue
ruleRealValue returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			(
				this_DECIMAL_VALUE_0=RULE_DECIMAL_VALUE
				{
					$current.merge(this_DECIMAL_VALUE_0);
				}
				{
					newLeafNode(this_DECIMAL_VALUE_0, grammarAccess.getRealValueAccess().getDECIMAL_VALUETerminalRuleCall_0_0());
				}
			)?
			kw='.'
			{
				$current.merge(kw);
				newLeafNode(kw, grammarAccess.getRealValueAccess().getFullStopKeyword_0_1());
			}
			(
				this_DECIMAL_VALUE_2=RULE_DECIMAL_VALUE
				{
					$current.merge(this_DECIMAL_VALUE_2);
				}
				{
					newLeafNode(this_DECIMAL_VALUE_2, grammarAccess.getRealValueAccess().getDECIMAL_VALUETerminalRuleCall_0_2_0());
				}
				    |
				this_EXP_VALUE_3=RULE_EXP_VALUE
				{
					$current.merge(this_EXP_VALUE_3);
				}
				{
					newLeafNode(this_EXP_VALUE_3, grammarAccess.getRealValueAccess().getEXP_VALUETerminalRuleCall_0_2_1());
				}
			)
		)
		    |
		this_EXP_VALUE_4=RULE_EXP_VALUE
		{
			$current.merge(this_EXP_VALUE_4);
		}
		{
			newLeafNode(this_EXP_VALUE_4, grammarAccess.getRealValueAccess().getEXP_VALUETerminalRuleCall_1());
		}
	)
;

// Entry rule entryRuleLiteralInfinity
entryRuleLiteralInfinity returns [EObject current=null]:
	{ newCompositeNode(grammarAccess.getLiteralInfinityRule()); }
	iv_ruleLiteralInfinity=ruleLiteralInfinity
	{ $current=$iv_ruleLiteralInfinity.current; }
	EOF;

// Rule LiteralInfinity
ruleLiteralInfinity returns [EObject current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				$current = forceCreateModelElement(
					grammarAccess.getLiteralInfinityAccess().getLiteralInfinityAction_0(),
					$current);
			}
		)
		otherlv_1='*'
		{
			newLeafNode(otherlv_1, grammarAccess.getLiteralInfinityAccess().getAsteriskKeyword_1());
		}
	)
;

// Entry rule entryRuleName
entryRuleName returns [String current=null]:
	{ newCompositeNode(grammarAccess.getNameRule()); }
	iv_ruleName=ruleName
	{ $current=$iv_ruleName.current.getText(); }
	EOF;

// Rule Name
ruleName returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		this_ID_0=RULE_ID
		{
			$current.merge(this_ID_0);
		}
		{
			newLeafNode(this_ID_0, grammarAccess.getNameAccess().getIDTerminalRuleCall_0());
		}
		    |
		this_UNRESTRICTED_NAME_1=RULE_UNRESTRICTED_NAME
		{
			$current.merge(this_UNRESTRICTED_NAME_1);
		}
		{
			newLeafNode(this_UNRESTRICTED_NAME_1, grammarAccess.getNameAccess().getUNRESTRICTED_NAMETerminalRuleCall_1());
		}
	)
;

// Entry rule entryRuleGlobalQualification
entryRuleGlobalQualification returns [String current=null]:
	{ newCompositeNode(grammarAccess.getGlobalQualificationRule()); }
	iv_ruleGlobalQualification=ruleGlobalQualification
	{ $current=$iv_ruleGlobalQualification.current.getText(); }
	EOF;

// Rule GlobalQualification
ruleGlobalQualification returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		kw='$'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getGlobalQualificationAccess().getDollarSignKeyword_0());
		}
		kw='::'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getGlobalQualificationAccess().getColonColonKeyword_1());
		}
	)
;

// Entry rule entryRuleQualification
entryRuleQualification returns [String current=null]:
	{ newCompositeNode(grammarAccess.getQualificationRule()); }
	iv_ruleQualification=ruleQualification
	{ $current=$iv_ruleQualification.current.getText(); }
	EOF;

// Rule Qualification
ruleQualification returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		{
			newCompositeNode(grammarAccess.getQualificationAccess().getNameParserRuleCall_0());
		}
		this_Name_0=ruleName
		{
			$current.merge(this_Name_0);
		}
		{
			afterParserOrEnumRuleCall();
		}
		kw='::'
		{
			$current.merge(kw);
			newLeafNode(kw, grammarAccess.getQualificationAccess().getColonColonKeyword_1());
		}
	)+
;

// Entry rule entryRuleQualifiedName
entryRuleQualifiedName returns [String current=null]:
	{ newCompositeNode(grammarAccess.getQualifiedNameRule()); }
	iv_ruleQualifiedName=ruleQualifiedName
	{ $current=$iv_ruleQualifiedName.current.getText(); }
	EOF;

// Rule QualifiedName
ruleQualifiedName returns [AntlrDatatypeRuleToken current=new AntlrDatatypeRuleToken()]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			{
				newCompositeNode(grammarAccess.getQualifiedNameAccess().getGlobalQualificationParserRuleCall_0());
			}
			this_GlobalQualification_0=ruleGlobalQualification
			{
				$current.merge(this_GlobalQualification_0);
			}
			{
				afterParserOrEnumRuleCall();
			}
		)?
		(
			{
				newCompositeNode(grammarAccess.getQualifiedNameAccess().getQualificationParserRuleCall_1());
			}
			this_Qualification_1=ruleQualification
			{
				$current.merge(this_Qualification_1);
			}
			{
				afterParserOrEnumRuleCall();
			}
		)?
		{
			newCompositeNode(grammarAccess.getQualifiedNameAccess().getNameParserRuleCall_2());
		}
		this_Name_2=ruleName
		{
			$current.merge(this_Name_2);
		}
		{
			afterParserOrEnumRuleCall();
		}
	)
;

// Rule FilterPackageMemberVisibility
ruleFilterPackageMemberVisibility returns [Enumerator current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		enumLiteral_0='['
		{
			$current = grammarAccess.getFilterPackageMemberVisibilityAccess().getPrivateEnumLiteralDeclaration().getEnumLiteral().getInstance();
			newLeafNode(enumLiteral_0, grammarAccess.getFilterPackageMemberVisibilityAccess().getPrivateEnumLiteralDeclaration());
		}
	)
;

// Rule VisibilityIndicator
ruleVisibilityIndicator returns [Enumerator current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			enumLiteral_0='public'
			{
				$current = grammarAccess.getVisibilityIndicatorAccess().getPublicEnumLiteralDeclaration_0().getEnumLiteral().getInstance();
				newLeafNode(enumLiteral_0, grammarAccess.getVisibilityIndicatorAccess().getPublicEnumLiteralDeclaration_0());
			}
		)
		    |
		(
			enumLiteral_1='private'
			{
				$current = grammarAccess.getVisibilityIndicatorAccess().getPrivateEnumLiteralDeclaration_1().getEnumLiteral().getInstance();
				newLeafNode(enumLiteral_1, grammarAccess.getVisibilityIndicatorAccess().getPrivateEnumLiteralDeclaration_1());
			}
		)
		    |
		(
			enumLiteral_2='protected'
			{
				$current = grammarAccess.getVisibilityIndicatorAccess().getProtectedEnumLiteralDeclaration_2().getEnumLiteral().getInstance();
				newLeafNode(enumLiteral_2, grammarAccess.getVisibilityIndicatorAccess().getProtectedEnumLiteralDeclaration_2());
			}
		)
	)
;

// Rule FeatureDirection
ruleFeatureDirection returns [Enumerator current=null]
@init {
	enterRule();
}
@after {
	leaveRule();
}:
	(
		(
			enumLiteral_0='in'
			{
				$current = grammarAccess.getFeatureDirectionAccess().getInEnumLiteralDeclaration_0().getEnumLiteral().getInstance();
				newLeafNode(enumLiteral_0, grammarAccess.getFeatureDirectionAccess().getInEnumLiteralDeclaration_0());
			}
		)
		    |
		(
			enumLiteral_1='out'
			{
				$current = grammarAccess.getFeatureDirectionAccess().getOutEnumLiteralDeclaration_1().getEnumLiteral().getInstance();
				newLeafNode(enumLiteral_1, grammarAccess.getFeatureDirectionAccess().getOutEnumLiteralDeclaration_1());
			}
		)
		    |
		(
			enumLiteral_2='inout'
			{
				$current = grammarAccess.getFeatureDirectionAccess().getInoutEnumLiteralDeclaration_2().getEnumLiteral().getInstance();
				newLeafNode(enumLiteral_2, grammarAccess.getFeatureDirectionAccess().getInoutEnumLiteralDeclaration_2());
			}
		)
	)
;

RULE_DECIMAL_VALUE : '0'..'9' ('0'..'9')*;

RULE_EXP_VALUE : RULE_DECIMAL_VALUE ('e'|'E') ('+'|'-')? RULE_DECIMAL_VALUE;

RULE_ID : ('a'..'z'|'A'..'Z'|'_') ('a'..'z'|'A'..'Z'|'_'|'0'..'9')*;

RULE_UNRESTRICTED_NAME : '\'' ('\\' ('b'|'t'|'n'|'f'|'r'|'"'|'\''|'\\')|~(('\\'|'\'')))* '\'';

RULE_STRING_VALUE : '"' ('\\' ('b'|'t'|'n'|'f'|'r'|'"'|'\''|'\\')|~(('\\'|'"')))* '"';

RULE_REGULAR_COMMENT : '/*' ( options {greedy=false;} : . )*'*/';

RULE_ML_NOTE : '//*' ( options {greedy=false;} : . )*'*/';

RULE_SL_NOTE : '//' (~(('\n'|'\r')) ~(('\n'|'\r'))*)? ('\r'? '\n')?;

RULE_WS : (' '|'\t'|'\r'|'\n')+;
