{"name": "example-client-webpack", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "example-client-webpack", "version": "0.0.0", "dependencies": {"monaco-languageclient-examples": "~2025.3.6"}, "devDependencies": {"css-loader": "~7.1.2", "http-server": "~14.1.1", "shx": "~0.3.4", "source-map-loader": "~5.0.0", "style-loader": "~4.0.0", "ts-loader": "~9.5.2", "webpack-cli": "~6.0.1"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/@chevrotain/cst-dts-gen": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/cst-dts-gen/-/cst-dts-gen-11.0.3.tgz", "integrity": "sha512-BvIKpRLeS/8UbfxXxgC33xOumsacaeCKAjAeLyOn7Pcp95HiRbrpl14S+9vaZLolnbssPIUuiUd8IvgkRyt6NQ==", "license": "Apache-2.0", "dependencies": {"@chevrotain/gast": "11.0.3", "@chevrotain/types": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/@chevrotain/gast": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/gast/-/gast-11.0.3.tgz", "integrity": "sha512-+qNfcoNk70PyS/uxmj3li5NiECO+2YKZZQMbmjTqRI3Qchu8Hig/Q9vgkHpI3alNjr7M+a2St5pw5w5F6NL5/Q==", "license": "Apache-2.0", "dependencies": {"@chevrotain/types": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/@chevrotain/regexp-to-ast": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/regexp-to-ast/-/regexp-to-ast-11.0.3.tgz", "integrity": "sha512-1fMHaBZxLFvWI067AVbGJav1eRY7N8DDvYCTwGBiE/ytKBgP8azTdgyrKyWZ9Mfh09eHWb5PgTSO8wi7U824RA==", "license": "Apache-2.0"}, "node_modules/@chevrotain/types": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/types/-/types-11.0.3.tgz", "integrity": "sha512-gsiM3G8b58kZC2HaWR50gu6Y1440cHiJ+i3JUvcp/35JchYejb2+5MVeJK0iKThYpAa/P2PYFV4hoi44HD+aHQ==", "license": "Apache-2.0"}, "node_modules/@chevrotain/utils": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/@chevrotain/utils/-/utils-11.0.3.tgz", "integrity": "sha512-YslZMgtJUyuMbZ+aKvfF3x1f5liK4mWNxghFRv7jqRR9C3R3fAOGTTKvxXDa2Y1s9zSbcpuO0cAxDYsc9SrXoQ==", "license": "Apache-2.0"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-R4UijfvKYNS6QvjALl7MdSmYAD43C4zCIzR+BxduCCnpOiajpP6zGbJCtXENJQTEW5omKBPRxZlNWHO1i/cymg==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-Gh4IS1cpzu98ifRU2sMPZgoOXlp6YuS4tZZBU4rygcnCiJVlzSg1IGF3/qWvGsYb4XdCiF/88qP++hEuOBVftg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-xKAMWUmNibtc0Ae2Js0045JmamqpecGxGQOJzxz6fdwDocEb7vhjoaRnDINeoGvtd7kiXXCCUiE2WRhpiSRG4A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-AAdh+y22C36NUNOyU5bRP2iNEzyGE6HXOy/5fuiCpFerV/rwMFtr0yeAq9haCPdFaYNspnkt3Fj8zSZzr+bU/w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-rK/EYD70iQ6XF4Lx7P4FSIbAe1X6BVFFu85mZSrJnWJdWlHZ8XU3HkxnY0dSZJc+4bBh3EvvbYVkoISSDjcuAg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-u9dGcfjGCSPE2zy4k1LqstH/oydUBJ5o91K/OewJkMVj27JFlP1p5ynWrGQgLvLEEjcRXOaI8r43qAu6lZOK1w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-ID0M2FbCsFdu3bR2id/GC8LxWR6gR0KDTFB9rMkmI/yNboGgO71G46R9LWnQ5iaw0MLS99zcTRn165hN5YT/gg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-XBECAkPyaHKfIoNAPH6HHKRGZ9Rl8j/k+gxbsr3vYeAEEWUkEXCKgzA3mnQuqOA8krsU7vFwsjN/ywQU3LkbPA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-nfjgdgxGKjn9HfMUPa8lA/yNMwj9DoqeATBW8r+Ew8MBJTDJguXfehBXX8ul3x8I7QknwKwPnHx+yT2jvUiGsQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-8eQ+qKVfgh9cojK+fsRrSRFLFHjfV4rwNPhAYp6fyB5JFJUdTyp8SDO2NJHcTJOsTIJDW3J+xRFgrcMEwB/N/g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-W3m13XvLSTXH/o1Dm0o552QFZ92u4NtcGX8rFoATecUkLQPxnBKtUxqlhBMVXp/eBjzNPDLBIvfG9azu4dq35w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-cZAfrwox+dWL0cs2W+V9i0q/96ZmhsqT0LdBNEyj3NGE/DTdhlQEC3hF3P9s/LFRwgKsECUfWCAnlhnUI2bfRw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-WCcpvOSAmPT/nDilAsqYx6U+x1Yj48YZQ+gB+XQ5WJFW1QIl2HZkkTk2vLXCva6T5ESAsfMGGivDKAcPBs/yCw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-RXlx72SlAe7mI/8g5J5zgAARFzL5fCtlBo1suCEmiM5f9S4Zn+HVcEjkkchduq+0JNS5WVO5oH+24NQ/t/Lz7Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-JV1rxYGOSsv2Hps1XpdeobUmI0wt4xcOKT0qFY1pzSt4PrJAwC6g1g+AU5W4Kh+Ya1CjqBJKkXxJ9Je/gecpOA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-LnXzby9mbOgtn239CdyAgRoPJFqhEBujyX7tUb+1QxfQGBZXrnbPAoXtDkIJhx76rjWHnGR7fRBArgBa4SRt2A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-FKtBIj8IeTBIb/pvFEFJxrMFltP/7ZUyOmkrbCvhoa3+0KVYrNhlL3vnldi+WOSTlRBWC7KHXXehtZ6oRB7ERA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-tM5GUYVKrgoqeY1eJ5yXxYPybiDwKWBzRZoOcrJcmlrUWXz8h3AoWRJ4Udt7FO2CjOCcyX0QzpWWypLhzEchAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2", "@vscode/iconv-lite-umd": "0.7.0", "jschardet": "3.1.4"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-tljZkApxt/PCWFSWIZ1wfilXi/Du1PxQXkvZGRZSLkEaNyJQ3XEfJtjelebpXW7ZRnYmyLWfMC+eP4UwVcqAiQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-kE0eB/sKEPljMj7LmbeQSs4NG0jxzmQ/luFWMEErnw7GqiyfTp8wl2XrPerLoQ51k2y496WzcO+KRAy+hUQm5w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-GPgJ0/gt1EV8OXpFKGD/DozVfQS/2nCJA9Q1f1fzM7Dr6Dv4anZaSvHGtd0nscVu3baEz73tGfBP8fOcn+Qtfw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-1PgXgl0LdCWM/mqOR7A9slWuTQpf7y9kivSxBIu6c0bJy3LxJeegNZ2EMZUenqR0imDRQTOh99FMY3L/M++65g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-j2l3zM65AsJFIsTyOxzAX1XY20IOPsWGHLRUmoem9eDnaPhwfhHQ7ia/j6UQMveBH8xEOm4IIovedFhAIaGI+Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-6QMu8OiCoucyKCVNwue53TYGJsTpB+0lOe3W1CRON4rJyoP+jZVkU3i7Qj5q9OxpNY4cOiFT13sa60lWjyLtjQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-Fp1q86xW/RsvoFI3FDK2oTzjjH3L0ybwUpHpojvdXs7qSe9xIABRdC2H+j6wqA48RWGvNfnzopw5x/AsAf3IWA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-897LD2JzqZ2mE5D1Z4gb9QIqeK1QdOkANFeXNfrcVBW3I/+Fl0Vx/JnKpDUKAd4L9B+aa7pztUkv1N5cjchItg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-+o0ivOGA5SShRp22CcbmW+qHoiUan/VN5joo4lgJ8bdCsEgTa59hWCddzx0KHgTjQxLuDlV9CmwulrsEvhJK0g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-aZTg+g286eUZfsu/Jxo70QOr8J4y6Jpwij/vu4Mh/i8nNiSF91TL67cWC3Cwbx2IERVyUSZxRiBYjWd25sRepQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-f8dl3Cza5K5kjoCR3rwnxHCj8FNRRCW2eGxr8/MsFx2OQ6rvBKsV2JatBeJv6DpanBcG9LNh7EfyJCQr772GDg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-uOilmDHGZXeO2y+RrgAFy1ZC6WERu4uF4fM6bl8nhfSeqeZ6ArlS3jsnaVCZp849nRqWGJbwlEro2PgUmJH5Bg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-NFzdN+mduRpev3iWvTWtErNXDFunp43Scvl46xAD1b9unoi+orcDAdUUNWbBltXFqMcM83C/xEskoUmnyYnEAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-STEjQ+Wt3eK423yJGGY23v+FU2zoUXjZ1j9ktHMvoTfwWNyrCWE0Sae55bDSmintBrg+/O43X1kcck1uxkg5Rg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-vCoR/kVApfn3IgKQNt3JM9Pb3I2t7iDvLKA7emek3LRQCuE+i2aLHIiQlhefcu5hCrNHhH1nfLO1P7lwiQkGlg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-hCgaESRGEDQHskDlRg18POBprzoQhfV9QCTiC7O8mNHpDLLL+3Xi3Ffmk3jZAERvTP1dndp35Wnxsk5/WREgIQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-L66aIitO0gZxVskynJBjfSJ6Kk4vmDbgG/5/1RJ104TsL0a02A3ojC1j+chU4b/NBNXhuRY7jiZqgdlLQwesmg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-B9qbbn24k0zZTC10cltPD5+BlR14gEoG5Vp2crp2awxtd136L4O2Xxjdl7/4vN81zNMsXXkjRM3NK1EmPYLyzw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-KSVLZMY0oEu6tAddYZ3cpYM8JsbKZUE0RXEk6WW2rTUL4offHp/LV1qNvQzdBH1uXU4ReFcgA8abF7k4mi6uxA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-/LMFDaoD37l3hVZvbTSzljebcajUlQrqv/wea/sGsRYn40pxNYlbr1BS0A3U3wYdWk0iW6YxeWu6rx4YG40Zcw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-RvVJDncM+AbsxqCNsJT7W5o6+A1wunlGkB8beIXemirxteBbQkoVhd9JkcH+Tm8BJvqtDil4SKX3zf3MP6IaXA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-yq0ELxTe31ivgH9hno0urKw369aCQL60GuOeTn8i8MkdMesYDHH1t7f8vFZBVTFmDyDBSVcoE1oeQfts0/aCqg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-Lbwz1oVdLpn/cIU6/FeZSMM9zXDFL6fYQa1uP2rJRzQuehrZtHjHmKGLu4kW9XrvIAFrvxKTqmZIAaB5n+mNZQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-WKGCm94vwwc3tzDkBcljWVvb0FwHoQdiY3MoOidAolihork9I1kADJxI25/xdijEKVFDSy/td2sqYjqVtONlHA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-d3oGEVxucAudJ/tSl1JteduABCKXii27ekbEm/t8Xo8A9ximAoTRJ5tX6ne04cbXLisWdTcTeSzxirR9BdNVqA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-cb7YE+yQVMNGOh1KI7E0VPm6M1bUVz3magAnwDXiSNXfTSjg8yLKBbJFUoAE/g18q+FY1PXg6DOS4wajWVCmAg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-W0580lGhTml4h/QbxGMM5zMTpaamUdZ/I/EF7t0Z/XCI31QIHzrVdw+8oRStHvnjCrz1587EyeKoUOC4jo4vEA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-WaqYkXMFauFHesL2bCqE7os8rY4MxQYzjILZRpspFcwJCf0ohujrkAVwYoCLLt7HOnG/xKmruF8I5mJIPAvm8w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-Eua39mWE3K+kKLXm9uSsg6fvBJgVr7BPxWXRVPc17Rm2GjJ+/4WMIyue88KkFuBUCp/Y9sqaVhT5AiR+6DS/sg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-c7qujmYpvQ9AmAvQoGjQ5GysvokdV8R+aDiosHDVtOfq9x3MVRkzdTNtlGL27UaauBKMpFFY7ZOM7rq/dnwZRw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-rf25cJ0J5useUnN1WpRqK+JXRFa6/s9nZ4LxcX7mIPvCV9VArfL4Q6Mc/ulUHdDe7ComvfmCHi5/TZpBzqCdTg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-Ndc3v4zkGc6w1lsQ3W1a/WLt4tAcJkyR3Q9a+Un4o7v+JAtpBAHe+l3Vu8Iq6fvmwonn0n0gaiD+s91X9vFzfw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-VDX/WY6oaD7/CukLt+4n7T6ZGMU9gu0MP4pSrnSWTml4psiPcM4e/MvOQdx0Gh9fRD3/GwsvgHIxg31LvVAixg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-YzLGDNQThRPmo+EbxapqhKgP3j6yqk84GpCHh02bWR8Ya1wpsvlRWPBDpZAEI+JmsqKE5VIRgYyiXTp0c7jmsQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-QSDFeX/hTk5c+3EOIjioG234EQikOCaKtqUVDf/CWu8kVjetxdAPRAR2c269R5WbrtDPDSBvxhquJk8yvOjQGw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-zop7+H3CEirMXdtZtjDQ3/+udWVWTf6hjHxGlGCrLKHIM1UoMci55SY2n+UVLsx2UTswo670SIjYge48CdLZPA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-ka4sYGK3SQ9jVPXSUP+SoCp1bMTIc90TL87pu+VMI9yo2NKq5f85y51Aibq/Vo5DBovkHB/FNSZMGAkWKf/mUA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-AVrF/77g2A/L38bJ2vxM4VnJsV+o24kMNOViWJoF8GT1VVDg3MjsWHLFPQOudGm64tWgx2X7CyVVGKBqXruuHg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-2UzdDFPya+d1LRrhyr+44/8IsIq5HvTfDOXQI6Yl3eGGAPQPTHbMd30tNl9uFftnG5Ha0AVYxwFDRJQ6Df14tA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-BxdBZz6ScRuo7a4LIN98VRpXEJKEx9fUyJ0Kri1ze/QsrwnGBz2IGF7jFoTP3mTzUtKKQPBuvghbzvK8z0tvLQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-D/Bos7Tx8sCsTFcnyDAO4ZlWIJuKnjY1mSKCb31bwcUDb0Izt+3Zsw+xN/4OOsHqZobskvA3kaJ3g22sCMwP9A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-QNUfOAH6utrekJoKIO9s+wLPLQK4VEQUxrFHD7Rrv2PHj8YIcOM9sKfSuICilLmDQz9tp3RRh9kO6BVjXMj5jQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-0mE/43ivJDlyCGrFavJml7GWJ5y5dM9Lwe75pRzCmeNXGKTUNrgDwX/wvAbZH5jo9OOLVqpHUmpsIqOfyG59RA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-mTMFpzR6ZY5eimtEbBUjlvnDgTnuU8SrYnmXQylXagoZ5ea/bDhZA/hh2WlgLrkwUJ+FgCrQUbyv4VQ40fEOcw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-wrFz50JBi7mdMCnmAQ5yQrl1W0ai7z84kKw2tqWlhTI8gAPqxfJqzXDG/ky4sRIns5kWQuU5yBYi3cYUbKefNg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-o+IJzhVvpVcfAEF0mNg9iUHyzYIdfKTftC6aojVv/xZAPf2M80dURmhCyCcGjxztGDHZl7e84cm0CSUOQ26RNw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-pmr0Vc+jmtZPP1tkyicMmTNclw7DQwdkkpSm0nN8JpnlRlmEyaw9nsuVODsjqcZhjOMUSDAACndgsqjN49EFYA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-RMubUplntAaj2hSj0PnPD3fkjspZ19dIFobkwtFMMZeVQqHQ74HcneiVfXr1yujhW8LlCYagp02teSjW6go9GA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-api": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-api/-/monaco-vscode-api-15.0.2.tgz", "integrity": "sha512-hoSiyyE1Jm+Gx7b4sQioE25CniuODomOqIsjkZwQcMb+N/FrTmSrSGDxx0GIZrvsLCmowI4S4vILDUKB4UAP7w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-base-service-override": "15.0.2", "@codingame/monaco-vscode-environment-service-override": "15.0.2", "@codingame/monaco-vscode-extensions-service-override": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2", "@codingame/monaco-vscode-host-service-override": "15.0.2", "@codingame/monaco-vscode-layout-service-override": "15.0.2", "@codingame/monaco-vscode-quickaccess-service-override": "15.0.2", "marked": "14.0.0"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-k4t1jvaxq8+1GBlwR0TuCHCHsVe/WRJy50e4Aj55W1IRaYyo8q4mw6aNbxN/ZoqFcxLjKCDiff70yeGjfexIwQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-JIwDNq6ta1ZO7ri3E1wjJvRsQtkY/ZhT4eCa1d8ipy5w9TsaJuLvPFPsjBGax2IA6GTCrxLxKOWsHfyBUQA0vw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-6A11la9Qz30uYzfNwNKyoi5xaA7sq3mrzHLGfNJmkjCKyp+ntTBiULkNxchNOqTbNXR0VD8M2QSoFkBAvk0pIA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-base-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-base-service-override/-/monaco-vscode-base-service-override-15.0.2.tgz", "integrity": "sha512-NApfW6rJUS/JmbGCcLpywRRUEhc2hXT3hbymjQGoJ/SfIAssV+rS0B+3vV2/PVc5It+lz+gK9s9w76CHCAsbgQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-KlcPmFOl4wkv5MVxcZ1c+GJqbnXn5wxhhWTbq73cIaMTz58FV1g5XlRfb87+W9Lekr8jur+4uficzSFYf8Dusw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-aM1FtyF1FU0Lqm0l2jZIkhpLgh5yX+tUksKLFxVfSgOpSOaGkZFxQgzRmekJQhgFgrRdukf7IcNb/dpk4GU/lQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-ghZ5ejDZhlH1FJz5j4NnY+hhmMfGvFiV2aw9LyaqssrplO22jvxEHMA9lM0xHEM4WcDKarsv+VaQ9tVIHUGpHQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-ucOOfIxAFe5lFD2dwf7YMesclMzlsW+ilZSqqFmdcmb3EF4aQ+WuemBQl34bmmn5L6jiGOvaN5UfYdv5DoLU/Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-bulk-edit-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-bulk-edit-service-override/-/monaco-vscode-bulk-edit-service-override-15.0.2.tgz", "integrity": "sha512-QISWI6Q0BPPxatVPtI63dQHXP1VOHrO6JKD9aqdXZo7+SL7bpszsqOvbejpf0J2WUIjOdvle+7mx0R0OYHLLjg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-tgNGU+PorIwbxCWEHjEwZMDYKAtMp7vu9IDkZZrFXx68Ol9n6949+3w5j3To8tMg6w6e1Y9YmpdDHfDXMVAXsQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-EcN1gJVQTqZ+Hj6Ij/w2zveDrRZV25HS8y/ufJ5xabm7f7b+3I2wH9t7UoYK1cTLJHwV0Ek2QW5QR2WbkCLxww==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-XqZN0s440ipfNmztYZKZ5rexUydQCNNnw9UsVhsh2VZYSWdUjmojxz6dn5uphHgWdIZv9/TiuE1VNtdEEs4tZA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-configuration-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-configuration-service-override/-/monaco-vscode-configuration-service-override-15.0.2.tgz", "integrity": "sha512-pC63gLhK4dszUSGWj1kjKL09K5GPkhX+IhshnZpbpzVIiLIdsKAOoKiRZylx3v6288SMWwvpX6ME7Of6Ol7V7A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-cpp-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-cpp-default-extension/-/monaco-vscode-cpp-default-extension-15.0.2.tgz", "integrity": "sha512-a/4uYe4gLVcFJyjeHXp/JQzFz1LB9v+eH3DJjRaWYP419EhIHbmfDdC6P8FvvdKy3UJ8hUgVzSfOZ6g2G2Hc+A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-nO+nKlZfoozH6WjGCnkS//8mQxrL4iI5P84vtmriYWiOz9Fj3CJVKFmu2ZBLV7ilD+QIXigXCa9vUtfH2JWNkw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-AiYTjTrbpvbyNR9G95cUmBvevNZKapKQS0IvRzFvBsI9ciZTvbTjsGWrj72m+trttUhiQ2ioh1qWpBfid7pPwg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-J5YNlKjwAm/nyESCm96GqezvOmMyRrfBGhvpAWAcln5awLFETTlr7x3XbNgHBLCqi3MnpdZTY+W+pR4zRmTCKA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-PLfB4PGtrRACUPhFdN5PtMkli9mC/xScrPdapS7f2LrLoGfiiX1qi3jn/ovCBCNd/45CCBFFK6TvJoHYp2PImQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-T13joFSRpmrmC7L0IvNb+xP1r061v0OMf8E/fbLbO7+LweuodX4nulkbBvSp0Rgx/75wcsT3jQ2LoHA8rZZHjg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-QZqXxOk85tHMQ1f+s+pI3vdl3XW4RMU+JWbifqfKbX/EaC7rbmlGC1qCrXMCt4IIDUkDfgANg6QcrnaXZpK/6Q==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-debug-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-debug-service-override/-/monaco-vscode-debug-service-override-15.0.2.tgz", "integrity": "sha512-2f1IFPwLjnaDUFL0/HMg+hILzfHArTwcVOTw4LWG6KDFkhtAJLXpuEAkUk9g3/25/Flcd9fsN8EXFwLjEhUwRw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-76p/DFTjmzcngmxnGxA3OybJhaTwcBX6j3tLObufLidzOURKrx+GfRZko7W5ZcteDToVs9EGQR1fALXkJYpOlg==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-W1CSlxHBEyCwhhc5Q7QUEb4YLebISFa8h4Ae/jEAMefxCaD0+DXArBh82IDlcNNesmnYBnm2pXW3NsLCpr1/Og==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-5fW7UH7G5UQZz3rgxu7f+ppNKi5bhCJKsyCh2ST/EHR+hls6lYXYvcsi41lJZivc4KGAEYLh1dx6pd6NDoo6Ig==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-L9+4QDctDTFUVGEOjyYuFRXvsuM1HU8ttPOEpBIFgkbNsi/0xqV2Lh/wKoECeWYPKZGfCXVaR8pg5d/63bOmEQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-rGIV6WdMp2LlpB6dA04TOiX48OpX/QFmevxL6V9RCz7eLOdWthIM8urq761xQI3wEUqQoZPNXPLotDRO0gTFFA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-editor-api": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-api/-/monaco-vscode-editor-api-15.0.2.tgz", "integrity": "sha512-to1b/4bqfMDUTYZzkDZUFxJr7xYWDZ1lRKPKCyqhxt8TRShAIsItEQ6rZ3HrhjKJR4T1prwDyG1N4eCObFX4/g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-editor-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-service-override/-/monaco-vscode-editor-service-override-15.0.2.tgz", "integrity": "sha512-utrHOJYb2++T1biVbDzSMbonuex6CMy8jZB1c74BYbl0TAHHazpKX3BRIg6u9A2QMGJ1smibsC+pe+XI3+AXAQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-environment-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-environment-service-override/-/monaco-vscode-environment-service-override-15.0.2.tgz", "integrity": "sha512-cTZHfj0oPKzyKJu8Gjc4E2oh2M6vHsFbmdrwtQ1+yCkOkmLaSjza7G6v6OBrGGhvdSTVWb8Fv1fWJSiBL2rpBA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-explorer-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-explorer-service-override/-/monaco-vscode-explorer-service-override-15.0.2.tgz", "integrity": "sha512-ojv0N/w+3gwoBfhusIpkzQo7stjgJIfmSL0RNOghxNwiovl+1oB2IsclYWWqMro/yzDhvPQGjIVBIjJLrtEXpg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-extension-api": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extension-api/-/monaco-vscode-extension-api-15.0.2.tgz", "integrity": "sha512-qdt2VEKR6aI5Z7KAw5ex/dXD72k6lmKi4V+Po++De0YdSPPzqo4+KaNLiHqcM2FWXbgox4J+qE8ul9CZ131SMg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-extensions-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-extensions-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extensions-service-override/-/monaco-vscode-extensions-service-override-15.0.2.tgz", "integrity": "sha512-7UtMt7FwcWQ05vlaMNXsFWRP3nwktKiGNqf68Dg3cAmcKMNRP1ajR5I/CnRdotzOoXd7v1Jjb4yygHS9+evBWg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-wlmIHh0VobtQYcPXZyxZoOJIvAZc5MMHIAgrO39X1O1BUgINcCmJChiFFlFJ6OocKqGksF5TBkKfeoD/j7F4XQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-YB+VoM7nu08w/4JpjZyqKD1EDmmdsTMSsoBWXIzNIH7sbmJLeXEQwcJ0jPfyLXSFu8jeubDZhidoHTa7MIONRg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@xterm/addon-clipboard": "0.2.0-beta.81", "@xterm/addon-image": "0.9.0-beta.98", "@xterm/addon-ligatures": "0.10.0-beta.98", "@xterm/addon-progress": "0.2.0-beta.4", "@xterm/addon-search": "0.16.0-beta.98", "@xterm/addon-serialize": "0.14.0-beta.98", "@xterm/addon-unicode11": "0.9.0-beta.98", "@xterm/addon-webgl": "0.19.0-beta.98"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-1p8jf03EnVsLkNFUtAJwwITBoN3kh8DPYihsqvznG7foFlR3XI6gj34oCSc23AlFPNvRZC8O+FuKCZ7o777+hg==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-NQYvXrruzWh6vKYfbrEvLBzthF1ffcPZcLuspc0GW11J9+ZJO1X1JXIymO69ANrkYJGGdLc/6E67LU/dCmku/Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-************************************-common": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-************************************-common/-/monaco-vscode-************************************-common-15.0.2.tgz", "integrity": "sha512-ot6TbkgL2YSkWbXU8FKrFji9z/Rvwg8STJibZ9mig0+VdhJKNnpJxsHqFcdFSGb5aHAcuUZ8i0RyDw1nakSQZg==", "license": "MIT"}, "node_modules/@codingame/monaco-vscode-files-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-files-service-override/-/monaco-vscode-files-service-override-15.0.2.tgz", "integrity": "sha512-gyT52tpPZlie9yM2y4a8Gd37Y12G6eIOy0b5FhcBd2l5S0b1APLavdf+31zDVQxZ6USjmCyZBURl6h19Y11JLg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-groovy-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-groovy-default-extension/-/monaco-vscode-groovy-default-extension-15.0.2.tgz", "integrity": "sha512-ltJ/SpZ/iCxaDi4wk02D868hpt7bKYO6/jHxVGFX9CCyPXQmjvx2+5zPbqlpWpP/26xedK6br3agmb3Ms4wAtw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-host-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-host-service-override/-/monaco-vscode-host-service-override-15.0.2.tgz", "integrity": "sha512-Vp3hcSar94Td1AEkrs6Q3aVgKx0+FEIesU132xNms7tJvDdMAlP1kH0c7lqZGjZdk91mml+r2pzQacjLLwQ5zg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-java-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-java-default-extension/-/monaco-vscode-java-default-extension-15.0.2.tgz", "integrity": "sha512-QLsGk/6lcMODlfBMJM9Z4acQwiSavhEODQghuPtKQuqzd/7tGeL5+tl68b6Wusg8oX3thI8TuKRaXa9SagkH/Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-javascript-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-javascript-default-extension/-/monaco-vscode-javascript-default-extension-15.0.2.tgz", "integrity": "sha512-/W+rv14rdtp/Cz1/FVkkUPP2Y+eBvMB0bCFWTnwzoPbS/5RXq9HoqS3MpohvMiLA/GEb6nGy+vV0AJtwLR89nw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-json-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-json-default-extension/-/monaco-vscode-json-default-extension-15.0.2.tgz", "integrity": "sha512-S8EFZ9W4PBtRFshCmj3zYRrJQP56tcSCMAb0WRuLZljQCBPr02NAeyzSmYQCyku0nFkc3LghtLwAqna84h44mw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-keybindings-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-keybindings-service-override/-/monaco-vscode-keybindings-service-override-15.0.2.tgz", "integrity": "sha512-gsSLHOGEY/QLwmqE1W8hzWBDmEY/nK11HN6rfvuVJhbkI7i5pqNLtFaNWus1iTw8gB+xjWXlziC+vg8AWjqgNw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-cs": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-cs/-/monaco-vscode-language-pack-cs-15.0.2.tgz", "integrity": "sha512-/cJyjswDFE+gJn0sRXdiLwjfqpS73Z2dFKQrv8xsX7UZDvn7G2Fvj+y9MUano0kLcdYQcNybUrOFpbY6tQbr5g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-de": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-de/-/monaco-vscode-language-pack-de-15.0.2.tgz", "integrity": "sha512-0kBqcXL6Y1+maU567/4N648AEfBFuS3emIYfh2BHsYO/GK6tVh1CabiocxD0Vwy110FivMt9QaD39UK0ESM+Bg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-es": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-es/-/monaco-vscode-language-pack-es-15.0.2.tgz", "integrity": "sha512-MKBr5Rgwdn6ikLDPbS3OPcVoQyrHk05K5znv8n1Aa8vplwUMGIwhVGQruvyZuDhwb/tHj42pvMcfI6KR1BXG/Q==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-fr": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-fr/-/monaco-vscode-language-pack-fr-15.0.2.tgz", "integrity": "sha512-uW1B8sDiiXf8jvYGNcSPCm7LLbb4YkRHwN4eM3c2W7xfRlR313ajqRYe2HCN7zp5ghAZEUS9pJytAMK2nGKO4w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-it": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-it/-/monaco-vscode-language-pack-it-15.0.2.tgz", "integrity": "sha512-nkkzdhHmlZQDBmO/ff+lfkP7/7VSsEK/myNpELLNGqylFE6Hz0odtlQZaldF2VA6BMj6z3cacBMn5Ru3Qr2cTw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-ja": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ja/-/monaco-vscode-language-pack-ja-15.0.2.tgz", "integrity": "sha512-IvH80s09OBvHNNy9e+ILQoWUnOxqMi+DLDch/y1oObpGQNR3relszHM56TvEPljc6DbDafh02NwiC154vByHmA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-ko": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ko/-/monaco-vscode-language-pack-ko-15.0.2.tgz", "integrity": "sha512-38YoVtGQUQogUSOokyVZia5c2J+Dv7YK90kREusZIPpH7bg0zkO85Dpa6HjgNf1LmlCI59mOdqRdXeiE/4zQ/A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-pl": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-pl/-/monaco-vscode-language-pack-pl-15.0.2.tgz", "integrity": "sha512-V+nwSAEzoGyX48y29zOf7g2Shl2VVaZJ6jl4KDb/C427S5+IkyC4q3htyJS0YxMcBAVBkp23/t+A0F7+8pMmxw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-pt-br": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-pt-br/-/monaco-vscode-language-pack-pt-br-15.0.2.tgz", "integrity": "sha512-<PERSON>rd0ORd9eZiRl1gLvxJ5i5t+C7TfLw9seMlhE7xTCEQ6jgbJ9a4cVsu/YDSRIGSXxlqr8bIpkH3ZLRp97MeAw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-qps-ploc": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-qps-ploc/-/monaco-vscode-language-pack-qps-ploc-15.0.2.tgz", "integrity": "sha512-PVn+/JevTRbO/IsQQwwr45M9y3RprG/rhD3hRXXPRsehjJx8pvuUrSsunmV5Va7m5Zz0LPRXZBlLTpoVWRYkCQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-ru": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-ru/-/monaco-vscode-language-pack-ru-15.0.2.tgz", "integrity": "sha512-YuGWXQnKxoSmUwa9449jc0xAWqCTHiu2Er8v3MI3wyccacokPdcNzqMvpmPlL6J5fyE/LEimbKxQZ0KkNOlbGw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-tr": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-tr/-/monaco-vscode-language-pack-tr-15.0.2.tgz", "integrity": "sha512-UK4/qaw0WAYJrJtk/vcWb5pWmSEzfp88UYD5lRdlNGyCteJoL7Fa/6xahHWKxzRleADHhCfxKfJpxZaRX3cZfw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-zh-hans": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-zh-hans/-/monaco-vscode-language-pack-zh-hans-15.0.2.tgz", "integrity": "sha512-2o7CjPQ77yY/g5uv2Nu3CZsRsexssSrdqvx6A3LHvQg67JtqYI/aeQg8vB7RIi8GttDqg5ReJrTrXJt8KFLwwA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-language-pack-zh-hant": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-language-pack-zh-hant/-/monaco-vscode-language-pack-zh-hant-15.0.2.tgz", "integrity": "sha512-5hY9cR43I6TeVBCybQZb/R44Cm5M1fdmCo4kqndtlPiRkAuMBir1AwCo8quMV7sdQGsYIVhqnXLhHrr5hcAIVQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-languages-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-languages-service-override/-/monaco-vscode-languages-service-override-15.0.2.tgz", "integrity": "sha512-jrf12nVSIpFdRbkDQBFWLK9fzOxpUCWJZK9i3p6Ta5qU59Ff2XR9ybSMjWJQMCn9ZnGZoH/hRWl4+ZZ2zi4Lng==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-layout-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-layout-service-override/-/monaco-vscode-layout-service-override-15.0.2.tgz", "integrity": "sha512-2CPl9z0MBN8HP71H5lf8uV60Z5Tgo1QdX9iQLQec0WWKBmQXL0Eid60F1pB1RT//ABPspebZV+C9me4NrYo/hg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-lifecycle-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-lifecycle-service-override/-/monaco-vscode-lifecycle-service-override-15.0.2.tgz", "integrity": "sha512-kXk5CW92DOIoYere02eLQ/zYpNMRhcPIbGvzWXX9bc4NA77JxCNMZqiR1w83KzIchXrvOl3ONeN1P7WbTIQbvA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-localization-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-localization-service-override/-/monaco-vscode-localization-service-override-15.0.2.tgz", "integrity": "sha512-kRKbOmHof6o9w0zkUCMOyNA9/hwVJuRqOI74WHKHF7efisJaurn1fqQbFhwNsO0uD0ax0taYjSvU1nI2iCMH8A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-log-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-log-service-override/-/monaco-vscode-log-service-override-15.0.2.tgz", "integrity": "sha512-2ihDMYGJ0gkR0Uv8Lqjky66IbOzqHh3EfNcY0Jwl3gkKnie5D0OC4FOP6JWMuYjfFMS97a5LTeiiJ7xoAjD42w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-environment-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-model-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-model-service-override/-/monaco-vscode-model-service-override-15.0.2.tgz", "integrity": "sha512-FnORHgAmY5+zx5pZgaVxnaj12CjA0j1FiSrYiRjWj//AvRcaGBTh8QuRtZZvB6wk6Lh6SgmMN5ALwbzdQuDkSA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-monarch-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-monarch-service-override/-/monaco-vscode-monarch-service-override-15.0.2.tgz", "integrity": "sha512-G7LFMgUrq1sIYR+5lsX8t5UZI7X6vvREvRlewaofr44R1+NlARkGKJ5S431ruOlVUf8YxwuwqjeuNWAFGDbbfA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-preferences-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-preferences-service-override/-/monaco-vscode-preferences-service-override-15.0.2.tgz", "integrity": "sha512-1Vohw10/0YtFsvgwy5rKVx7URG5JQqwFhjZFpcgJru5rcSZ8Gq15wmv1IwMEwUcQp/LQ84JyyYY+Ap6yvNjRNQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-python-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-python-default-extension/-/monaco-vscode-python-default-extension-15.0.2.tgz", "integrity": "sha512-ip8ZjA4CrDxVirbsdXQp4iA5CMAwyYtgkvrmxzIDaGNl4oZJ9gkXdMxofQQJsrqMXUxEEu23ZnkGGs3e4E1VPg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-quickaccess-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-quickaccess-service-override/-/monaco-vscode-quickaccess-service-override-15.0.2.tgz", "integrity": "sha512-KK/n7l1bJOSW1RKDL1DvIIWrxd3WRnhVtkEz4mNDMreGYsfbaUF5t6gw4pKP/r+blk9ZO/ZTm6+udZMUZ2gEnQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-remote-agent-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-remote-agent-service-override/-/monaco-vscode-remote-agent-service-override-15.0.2.tgz", "integrity": "sha512-YZT9zGncgA4mTFkA8/fGdpbP1cpOEzzTKZwCQg5F52FLOyDqiODj3cArWYaUt/AbVbL0fJB+NKU7DDMgYLHysA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-environment-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-search-result-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-search-result-default-extension/-/monaco-vscode-search-result-default-extension-15.0.2.tgz", "integrity": "sha512-UjgRQMN1DpWeWXt6YJDEv+V2cvNowrkTjnptu6xMG22UVCxd8y0DtBiVwpZAjKyoDCczSUM4CKJVq4Rn/uijDA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-search-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-search-service-override/-/monaco-vscode-search-service-override-15.0.2.tgz", "integrity": "sha512-rH+9Btqowdg9FcjWc6nfO/5JFD70OMNaf9vylVkj4g46QBCtr927Hq+esNBczyVLUbOtII2NP4nPNw8kKgic7w==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-secret-storage-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-secret-storage-service-override/-/monaco-vscode-secret-storage-service-override-15.0.2.tgz", "integrity": "sha512-zW2GVQqDaLfMGHVSEwm7Z/prNky8uj3mgKMeVV3Mx2L1Nq3Wzmp59j8sa7QIixqpFM8n/HyGOJZjaRksuLMr5A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-standalone-json-language-features": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-json-language-features/-/monaco-vscode-standalone-json-language-features-15.0.2.tgz", "integrity": "sha512-RRBxi7UsTKYW9fmvArcFly4CHZTwtngFhk9YbgO+ghYO21TeUlrkGisnBO5FuKJkHE/63nOSEz3kCUjzmvkUIg==", "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@15.0.2"}}, "node_modules/@codingame/monaco-vscode-standalone-languages": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-languages/-/monaco-vscode-standalone-languages-15.0.2.tgz", "integrity": "sha512-ah83ra+eIW/TlZoYOBmJWEqVzcMVFTtJtZrnsrut8O4Lw7LAQHJdq9Iy0Xno75ss+AIj5j4InLiyn3Y56jOlUQ==", "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@15.0.2"}}, "node_modules/@codingame/monaco-vscode-standalone-typescript-language-features": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-standalone-typescript-language-features/-/monaco-vscode-standalone-typescript-language-features-15.0.2.tgz", "integrity": "sha512-9XX7VIBKc3ht2GtoUnezrpfCETWMp4xXizj2MVzfsT+dppEQK93oTumMnCu+OvygW7PFLzzsJrZloNBLwGAvaw==", "license": "MIT", "dependencies": {"monaco-editor": "npm:@codingame/monaco-vscode-editor-api@15.0.2"}}, "node_modules/@codingame/monaco-vscode-storage-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-storage-service-override/-/monaco-vscode-storage-service-override-15.0.2.tgz", "integrity": "sha512-qmpAARkcn9r47bSOPP1Rw+O8NuIgFSq6jJdYos8XQ8AvPyCUwyB8KmnQlNpzqI1cy8dzX9ppf++VcxRxURRoBg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-terminal-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-terminal-service-override/-/monaco-vscode-terminal-service-override-15.0.2.tgz", "integrity": "sha512-4q4xGbttstM9DYyil2GorlsjrT4nF20g9rQMENkkD9huPRKEP1E1g++TeMv/I0sCaWDPVH1e6yQO3prHSiy1Rg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@xterm/xterm": "5.6.0-beta.98"}}, "node_modules/@codingame/monaco-vscode-testing-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-testing-service-override/-/monaco-vscode-testing-service-override-15.0.2.tgz", "integrity": "sha512-/6/wv4gFcl0gs1nexUhfhyLJCoSgQneSeIHyyKVoOuyc4dXaEyuFIgmvJbIsn2XGx4WxwsmptEx/CW80c6Ynog==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-terminal-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-textmate-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-textmate-service-override/-/monaco-vscode-textmate-service-override-15.0.2.tgz", "integrity": "sha512-3owPZ8QbpQGVIYw5xeoJQhEPvrHRyrNQ9Cp04MmbjXyj+PnNd6q9VtD6gK4vd+J5XXX0BtdaBS1EhsG1E4LNMA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2", "vscode-oniguruma": "1.7.0", "vscode-textmate": "9.2.0"}}, "node_modules/@codingame/monaco-vscode-theme-defaults-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-theme-defaults-default-extension/-/monaco-vscode-theme-defaults-default-extension-15.0.2.tgz", "integrity": "sha512-o66dXwgoNlj3rx9MT8TX2OtSugeK7bd8/PWlcwZLt6XrqE4T082mnFBPpGPpCOQXgsWJbYFTvbJMMuVM4XWfqA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-theme-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-theme-service-override/-/monaco-vscode-theme-service-override-15.0.2.tgz", "integrity": "sha512-vNi4Gk8okoLYJi77qXe5l8L4WGFHsUpgQYa5T+yPtfcupOX1ZEGr2W9DCjAzWPijWdbaL73kttFw/3QTs3z+tA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-files-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-typescript-basics-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-typescript-basics-default-extension/-/monaco-vscode-typescript-basics-default-extension-15.0.2.tgz", "integrity": "sha512-ze2c+XOsPvwghY9Hu6nc9zyR2pFrqVLPjP1cDgHeCb9u3IabD18MNfQ8FTTWVFSgpilUoEhbAb4oWWSxzM27JQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-typescript-language-features-default-extension": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-typescript-language-features-default-extension/-/monaco-vscode-typescript-language-features-default-extension-15.0.2.tgz", "integrity": "sha512-iAeVUjBN32/v9IW/7osm9v/1cjPGgcFHjA8xmmSn/ogN7j947Ycgx12Fw65jTrb/wBu7m11Vq0WMFYRCxQCYUA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-view-banner-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-banner-service-override/-/monaco-vscode-view-banner-service-override-15.0.2.tgz", "integrity": "sha512-L0ZjRIl+u8G6IHy2JvjEZp7RkGxFgJMnqTyJun1W3vINQe2eiVgM/7m+0Ac1pz6kOJC7cziRupa34LCidH4sJw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-view-common-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-common-service-override/-/monaco-vscode-view-common-service-override-15.0.2.tgz", "integrity": "sha512-tbKXC7T30bFDc+EoI9yAVcJ2ruVdoanskZRmcRUM3SJglELj4FRAVO9pmb/jthDWSQgiEdBEQ7v6UdU9l6aQcg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-bulk-edit-service-override": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-view-status-bar-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-status-bar-service-override/-/monaco-vscode-view-status-bar-service-override-15.0.2.tgz", "integrity": "sha512-/C09z2qfq/erWhu1msN+DbfsJAQrH4rM6MqkE/+zZsdcl6OqRFYvnV0aBBp9x3SXthnYWWZ7iwxX+lVCAq3MBQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-view-title-bar-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-view-title-bar-service-override/-/monaco-vscode-view-title-bar-service-override-15.0.2.tgz", "integrity": "sha512-9uB8ADGv071CNDHNbat7WsJPXCeB6yRvgKt0q1f10f4NJFR+IbLidGVcdwLwzeNeELHEZBIe6WrPxWAaiWeqiA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-views-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-views-service-override/-/monaco-vscode-views-service-override-15.0.2.tgz", "integrity": "sha512-jbZ6AV9jnF22Q29DAl8nuJcQeOvDjXJrkT8OEMXs3dYAdZqkg7Y+P3ggW2G+EuMo2ytNrfhLBez8It7jJS+tAQ==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-keybindings-service-override": "15.0.2", "@codingame/monaco-vscode-layout-service-override": "15.0.2", "@codingame/monaco-vscode-quickaccess-service-override": "15.0.2", "@codingame/monaco-vscode-view-common-service-override": "15.0.2"}}, "node_modules/@codingame/monaco-vscode-workbench-service-override": {"version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-workbench-service-override/-/monaco-vscode-workbench-service-override-15.0.2.tgz", "integrity": "sha512-w7SU4pQw1DWhMEZjWL+XIjxr/3v3hJdZRX5QDk8AQXvPZ+m4HxrPRHY8LF/kuPwHuB4oYrKQvU7WKy/OAS/PSw==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-keybindings-service-override": "15.0.2", "@codingame/monaco-vscode-quickaccess-service-override": "15.0.2", "@codingame/monaco-vscode-view-banner-service-override": "15.0.2", "@codingame/monaco-vscode-view-common-service-override": "15.0.2", "@codingame/monaco-vscode-view-status-bar-service-override": "15.0.2", "@codingame/monaco-vscode-view-title-bar-service-override": "15.0.2"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.6.3.tgz", "integrity": "sha512-4B4OijXeVNOPZlYA2oEwWOTkzyltLao+xbotHQeqN++Rv27Y6s818+n2Qkp8q+Fxhn0t/5lA5X1Mxktud8eayQ==", "dev": true, "license": "MIT", "engines": {"node": ">=14.17.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.8", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "integrity": "sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "integrity": "sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "resolved": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz", "integrity": "sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "integrity": "sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "integrity": "sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@typefox/monaco-editor-react": {"version": "6.6.0", "resolved": "https://registry.npmjs.org/@typefox/monaco-editor-react/-/monaco-editor-react-6.6.0.tgz", "integrity": "sha512-8cqr4CCHQgdAbF+cxZNg7QOF4e3Oy2QAM7buTqiwAaKJxbWL5eZJPAO49fHYd3a/DpyUUZxjPURRoV788/zEeA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-editor-api": "~15.0.2", "monaco-editor-wrapper": "~6.6.0", "react": ">=18.0.0 || <20.0.0"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/@types/eslint": {"version": "9.6.1", "resolved": "https://registry.npmjs.org/@types/eslint/-/eslint-9.6.1.tgz", "integrity": "sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.7", "resolved": "https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz", "integrity": "sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/estree": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.6.tgz", "integrity": "sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz", "integrity": "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@types/node": {"version": "22.10.2", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.10.2.tgz", "integrity": "sha512-Xxr6BBRCAOQixvonOye19wnzyDiUtTeqldOOmj3CkeblonbccA12PFwlufvRdrpjXxqnmUaeiU5EOA+7s5diUQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"undici-types": "~6.20.0"}}, "node_modules/@vscode/iconv-lite-umd": {"version": "0.7.0", "resolved": "https://registry.npmjs.org/@vscode/iconv-lite-umd/-/iconv-lite-umd-0.7.0.tgz", "integrity": "sha512-bRRFxLfg5dtAyl5XyiVWz/ZBPahpOpPrNYnnHpOpUZvam4tKH35wdhP4Kj6PbM0+KdliOsPzbGWpkxcdpNB/sg==", "license": "MIT"}, "node_modules/@vscode/l10n": {"version": "0.0.18", "resolved": "https://registry.npmjs.org/@vscode/l10n/-/l10n-0.0.18.tgz", "integrity": "sha512-KYSIHVmslkaCDyw013pphY+d7x1qV8IZupYfeIfzNA+nsaWHbn5uPuQRvdRFsa9zFzGeudPuoGoZ1Op4jrJXIQ==", "license": "MIT"}, "node_modules/@webassemblyjs/ast": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.14.1.tgz", "integrity": "sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.13.2.tgz", "integrity": "sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.13.2.tgz", "integrity": "sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.14.1.tgz", "integrity": "sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.13.2.tgz", "integrity": "sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.13.2.tgz", "integrity": "sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.14.1.tgz", "integrity": "sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.13.2.tgz", "integrity": "sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.13.2.tgz", "integrity": "sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==", "dev": true, "license": "Apache-2.0", "peer": true, "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "resolved": "https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.13.2.tgz", "integrity": "sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==", "dev": true, "license": "MIT", "peer": true}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.14.1.tgz", "integrity": "sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.14.1.tgz", "integrity": "sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.14.1.tgz", "integrity": "sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.14.1.tgz", "integrity": "sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "resolved": "https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.14.1.tgz", "integrity": "sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "node_modules/@webpack-cli/configtest": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/@webpack-cli/configtest/-/configtest-3.0.1.tgz", "integrity": "sha512-u8d0pJ5YFgneF/GuvEiDA61Tf1VDomHHYMjv/wc9XzYj7nopltpG96nXN5dJRstxZhcNpV1g+nT6CydO7pHbjA==", "dev": true, "license": "MIT", "engines": {"node": ">=18.12.0"}, "peerDependencies": {"webpack": "^5.82.0", "webpack-cli": "6.x.x"}}, "node_modules/@webpack-cli/info": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/@webpack-cli/info/-/info-3.0.1.tgz", "integrity": "sha512-coEmDzc2u/ffMvuW9aCjoRzNSPDl/XLuhPdlFRpT9tZHmJ/039az33CE7uH+8s0uL1j5ZNtfdv0HkfaKRBGJsQ==", "dev": true, "license": "MIT", "engines": {"node": ">=18.12.0"}, "peerDependencies": {"webpack": "^5.82.0", "webpack-cli": "6.x.x"}}, "node_modules/@webpack-cli/serve": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/@webpack-cli/serve/-/serve-3.0.1.tgz", "integrity": "sha512-sbgw03xQaCLiT6gcY/6u3qBDn01CWw/nbaXl3gTdTFuJJ75Gffv3E3DBpgvY2fkkrdS1fpjaXNOmJlnbtKauKg==", "dev": true, "license": "MIT", "engines": {"node": ">=18.12.0"}, "peerDependencies": {"webpack": "^5.82.0", "webpack-cli": "6.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}}, "node_modules/@xterm/addon-clipboard": {"version": "0.2.0-beta.81", "resolved": "https://registry.npmjs.org/@xterm/addon-clipboard/-/addon-clipboard-0.2.0-beta.81.tgz", "integrity": "sha512-vDxRyBO9VHzsl+gUQsDlUM9o2ZxSJKzE2eYQtuILKcf5D0EXYI86aMwKT/1iguX41vcMg42WXQBQ0TLWZ2MyTQ==", "license": "MIT", "dependencies": {"js-base64": "^3.7.5"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-image": {"version": "0.9.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/addon-image/-/addon-image-0.9.0-beta.98.tgz", "integrity": "sha512-yJaezwUc1Y3QYCmYSpjFW9IzMTLPSqrRCgdPnQ0JbCAVASRVmH6DLRfeikheJCvoXW6VUwMmGkb3fSplTxiV1w==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-ligatures": {"version": "0.10.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/addon-ligatures/-/addon-ligatures-0.10.0-beta.98.tgz", "integrity": "sha512-1zYeS9OUBR2ThG7dsxsGKOqeSlUo+DNTd5aaV5ZFbKQsQ1w+sQCaL73ZrXp1kuVK7pBiP5NCo4ffcXfzcztztA==", "license": "MIT", "dependencies": {"font-finder": "^1.1.0", "font-ligatures": "^1.4.1"}, "engines": {"node": ">8.0.0"}, "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-progress": {"version": "0.2.0-beta.4", "resolved": "https://registry.npmjs.org/@xterm/addon-progress/-/addon-progress-0.2.0-beta.4.tgz", "integrity": "sha512-7t1nlaANdEjUBVvuTTs5gw6UQgB+unFLwSGGnYXIvdQroYdkXQXSSATSqpYKVCd/6QFhBerzdB2VwPM5L5lxIw==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-search": {"version": "0.16.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/addon-search/-/addon-search-0.16.0-beta.98.tgz", "integrity": "sha512-7gtx7eYvwFLxlb5q2IKxa7jG1KEinVwTlT3ijnSsPawwn7rGi6nR135rGiR8DAjEV0GUO406ICeoYyVbgiFNwQ==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-serialize": {"version": "0.14.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/addon-serialize/-/addon-serialize-0.14.0-beta.98.tgz", "integrity": "sha512-Tsr8j3wnun2raYR1DgsNClQP/I5a85u/uW/5EiYH+/iPPua6EWJvPlr5Q6TCU/cdIKW1o27Z3L5/mw0pfMUXrQ==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-unicode11": {"version": "0.9.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/addon-unicode11/-/addon-unicode11-0.9.0-beta.98.tgz", "integrity": "sha512-3GjjEYAPWAEGE1CaTFDjQxKcY5NiHHPmeufTsRp3IL5850ZiaMMq9bIL2WSdoFIbVgfbxh5mRy2cJPdu9m0uRQ==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/addon-webgl": {"version": "0.19.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/addon-webgl/-/addon-webgl-0.19.0-beta.98.tgz", "integrity": "sha512-09FbNHgN2ad/8JI+AEyg8C3msyk04ET1FihQIpTeWPfd2LJIAdps7G4St2+qzZbhlFkR6m9Dgrgh/AC2uegh8A==", "license": "MIT", "peerDependencies": {"@xterm/xterm": "^5.6.0-beta.98"}}, "node_modules/@xterm/xterm": {"version": "5.6.0-beta.98", "resolved": "https://registry.npmjs.org/@xterm/xterm/-/xterm-5.6.0-beta.98.tgz", "integrity": "sha512-fJexj3XKDAMGsR8KKaiEhGrtJD1eRANbT3094E3KSgvbHRa3524tSFvDCx5+5KRE/hYaECmi0knAUIWJCvSPTg==", "license": "MIT"}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/@xtuc/long": {"version": "4.2.2", "resolved": "https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==", "dev": true, "license": "Apache-2.0", "peer": true}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.14.0.tgz", "integrity": "sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==", "dev": true, "license": "MIT", "peer": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz", "integrity": "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz", "integrity": "sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-formats/node_modules/ajv": {"version": "8.17.1", "resolved": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "dev": true, "license": "MIT", "peer": true}, "node_modules/ajv-keywords": {"version": "3.5.2", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz", "integrity": "sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==", "dev": true, "license": "MIT", "peer": true, "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz", "integrity": "sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==", "license": "MIT"}, "node_modules/async": {"version": "2.6.4", "resolved": "https://registry.npmjs.org/async/-/async-2.6.4.tgz", "integrity": "sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "license": "MIT"}, "node_modules/basic-auth": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/basic-auth/-/basic-auth-2.0.1.tgz", "integrity": "sha512-NF+epuEdnUYVlGuhaxbbq+dvJttwLnGY+YixlXlME5KpQ5W3CnXA5cVTneY3SPbPDRkcjMbifrwmFYcClgOZeg==", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.1.2"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser": {"version": "1.20.3", "resolved": "https://registry.npmjs.org/body-parser/-/body-parser-1.20.3.tgz", "integrity": "sha512-7rAxByjUMqQ3/bHJy7D6OGXvx/MMc4IqBn/X0fcM1QUcAItpZrBEYhWGem+tzXH90c+G01ypMcYJBO9Y30203g==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.24.3", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.24.3.tgz", "integrity": "sha512-1CPmv8iobE2fyRMV97dAcMVegvvWKxmq94hkLiAkUGwKVTyDLw33K+ZxiFrREKmmps4rIw6grcCFCnTMSZ/YiA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "peer": true, "dependencies": {"caniuse-lite": "^1.0.30001688", "electron-to-chromium": "^1.5.73", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true, "license": "MIT", "peer": true}, "node_modules/bytes": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "integrity": "sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/call-bind-apply-helpers": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.1.tgz", "integrity": "sha512-BhYE+WDaywFg2TBWYNXAE+8B1ATnThNBqXHP5nQu0jWJdVvY2hvkpyB3qOmtmDePiS5/BDQ8wASEWGMWRG148g==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/call-bound": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.3.tgz", "integrity": "sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/caniuse-lite": {"version": "1.0.30001690", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001690.tgz", "integrity": "sha512-5ExiE3qQN6oF8Clf8ifIDcMRCRE/dMGcETG/XGMD8/XiXm6HXQgQTh1yZYLXXpSOsEUlJm1Xr7kGULZTuGtP/w==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0", "peer": true}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chevrotain": {"version": "11.0.3", "resolved": "https://registry.npmjs.org/chevrotain/-/chevrotain-11.0.3.tgz", "integrity": "sha512-ci2iJH6LeIkvP9eJW6gpueU8cnZhv85ELY8w8WiFtNjMHA5ad6pQLaJo9mEly/9qUyCpvqX8/POVUTf18/HFdw==", "license": "Apache-2.0", "dependencies": {"@chevrotain/cst-dts-gen": "11.0.3", "@chevrotain/gast": "11.0.3", "@chevrotain/regexp-to-ast": "11.0.3", "@chevrotain/types": "11.0.3", "@chevrotain/utils": "11.0.3", "lodash-es": "4.17.21"}}, "node_modules/chevrotain-allstar": {"version": "0.3.1", "resolved": "https://registry.npmjs.org/chevrotain-allstar/-/chevrotain-allstar-0.3.1.tgz", "integrity": "sha512-b7g+y9A0v4mxCW1qUhf3BSVPg+/NvGErk/dOkrDaHA0nQIQGAtrOjlX//9OQtRlSCy+x9rfB5N8yC71lH1nvMw==", "license": "MIT", "dependencies": {"lodash-es": "^4.17.21"}, "peerDependencies": {"chevrotain": "^11.0.0"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.4.tgz", "integrity": "sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.0"}}, "node_modules/clone-deep": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz", "integrity": "sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true, "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "resolved": "https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz", "integrity": "sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "2.20.3", "resolved": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==", "dev": true, "license": "MIT", "peer": true}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true, "license": "MIT"}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-disposition/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/content-type": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz", "integrity": "sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie": {"version": "0.7.1", "resolved": "https://registry.npmjs.org/cookie/-/cookie-0.7.1.tgz", "integrity": "sha512-6DnInpx7SJ2AK3+CTUE/ZM0vWTUboZCegxhC2xiIydHR9jNuTAASBrfEpHhiGOZw/nX51bHt6YQl8jsGo4y/0w==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz", "integrity": "sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==", "license": "MIT"}, "node_modules/core-util-is": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "resolved": "https://registry.npmjs.org/cors/-/cors-2.8.5.tgz", "integrity": "sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==", "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/corser": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/corser/-/corser-2.0.1.tgz", "integrity": "sha512-utCYNzRSQIZNPIcGZdQc92UVJYAhtGAteCFg0yRaFm8f0P+CPtyGyHXJcGXnffjCybUCEx3FQ2G7U3/o9eIkVQ==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/css-loader": {"version": "7.1.2", "resolved": "https://registry.npmjs.org/css-loader/-/css-loader-7.1.2.tgz", "integrity": "sha512-6WvYYn7l/XEGN8Xu2vWFt9nVzrCn39vKyTEFf/ExEyoksJjjSZV/0/35XPlMbpnr6VGhZIUg5yJrL8tGfes/FA==", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.33", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.0.5", "postcss-modules-scope": "^3.2.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.5.4"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.27.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz", "integrity": "sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/debug": {"version": "2.6.9", "resolved": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "integrity": "sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "integrity": "sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/dunder-proto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "integrity": "sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.74", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.74.tgz", "integrity": "sha512-ck3//9RC+6oss/1Bh9tiAVFy5vfSKbRHAFh7Z3/eTRkEqJeWgymloShB17Vg3Z4nmDNp35vAd1BZ6CMW4Wt6Iw==", "dev": true, "license": "ISC", "peer": true}, "node_modules/encodeurl": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "integrity": "sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/enhanced-resolve": {"version": "5.17.1", "resolved": "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.17.1.tgz", "integrity": "sha512-LMHl3dXhTcfv8gM4kEzIUeTQ+7fpdA0l2tUf34BddXPkz2A5xJ5L/Pchd5BL6rdccM9QGvu0sWZzK1Z1t4wwyg==", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/envinfo": {"version": "7.14.0", "resolved": "https://registry.npmjs.org/envinfo/-/envinfo-7.14.0.tgz", "integrity": "sha512-CO40UI41xDQzhLB1hWyqUKgFhs250pNcGbyGKe1l/e4FSaI/+YE4IMG76GDt0In67WLPACIITC+sOi08x4wIvg==", "dev": true, "license": "MIT", "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "node_modules/es-define-property": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz", "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz", "integrity": "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.5.4", "resolved": "https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.4.tgz", "integrity": "sha512-MVNK56NiMrOwitFB7cqDwq0CQutbw+0BvLshJSse0MUNU+y1FC3bUS/AQg7oUng+/wKrrki7JfmwtVHkVfPLlw==", "dev": true, "license": "MIT", "peer": true}, "node_modules/es-object-atoms": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.0.0.tgz", "integrity": "sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "integrity": "sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==", "license": "MIT"}, "node_modules/eslint-scope": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=4.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "integrity": "sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz", "integrity": "sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/events/-/events-3.3.0.tgz", "integrity": "sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.8.x"}}, "node_modules/express": {"version": "4.21.2", "resolved": "https://registry.npmjs.org/express/-/express-4.21.2.tgz", "integrity": "sha512-28HqgMZAmih1Czt9ny7qr6ek2qddF4FclbMzwhCREB6OFfH+rXAnuNCwo1/wFvrtbgsQDb4kSbX9de9lFbrXnA==", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "node_modules/express/node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==", "dev": true, "license": "MIT", "peer": true}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "dev": true, "license": "MIT", "peer": true}, "node_modules/fast-uri": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/fast-uri/-/fast-uri-3.0.3.tgz", "integrity": "sha512-aLrHthzCjH5He4Z2H9YZ+v6Ujb9ocRuW6ZzkJQOrTxleEijANq4v1TsaPaVG1PZcuurEzrLcWRyYBYXD5cEiaw==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/fastest-levenshtein": {"version": "1.0.16", "resolved": "https://registry.npmjs.org/fastest-levenshtein/-/fastest-levenshtein-1.0.16.tgz", "integrity": "sha512-eRnCtTTtGZFpQCwhJiUOuxPQWRXVKYDn0b2PeHfXL6/Zi53SLAzAHfVhVWK2AryC/WH05kGfxhFIPvTF0SXQzg==", "dev": true, "license": "MIT", "engines": {"node": ">= 4.9.1"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.3.1", "resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.3.1.tgz", "integrity": "sha512-6BN9trH7bp3qvnrRyzsBz+g3lZxTNZTbVO2EV1CS0WIcDbawYVdYvGflME/9QP0h0pYlCDBCTjYa9nZzMDpyxQ==", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/flat": {"version": "5.0.2", "resolved": "https://registry.npmjs.org/flat/-/flat-5.0.2.tgz", "integrity": "sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/follow-redirects": {"version": "1.15.9", "resolved": "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.9.tgz", "integrity": "sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/font-finder": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/font-finder/-/font-finder-1.1.0.tgz", "integrity": "sha512-wpCL2uIbi6GurJbU7ZlQ3nGd61Ho+dSU6U83/xJT5UPFfN35EeCW/rOtS+5k+IuEZu2SYmHzDIPL9eA5tSYRAw==", "license": "MIT", "dependencies": {"get-system-fonts": "^2.0.0", "promise-stream-reader": "^1.0.1"}, "engines": {"node": ">8.0.0"}}, "node_modules/font-ligatures": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/font-ligatures/-/font-ligatures-1.4.1.tgz", "integrity": "sha512-7W6zlfyhvCqShZ5ReUWqmSd9vBaUudW0Hxis+tqUjtHhsPU+L3Grf8mcZAtCiXHTzorhwdRTId2WeH/88gdFkw==", "license": "MIT", "dependencies": {"font-finder": "^1.0.3", "lru-cache": "^6.0.0", "opentype.js": "^0.8.0"}, "engines": {"node": ">8.0.0"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "integrity": "sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-intrinsic": {"version": "1.2.6", "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.6.tgz", "integrity": "sha512-qxsEs+9A+u85HhllWJJFicJfPDhRmjzoYdl64aMWW9yRIJmSyxdn8IEkuIM530/7T+lv0TIHd8L6Q/ra0tEoeA==", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "dunder-proto": "^1.0.0", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "function-bind": "^1.1.2", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-system-fonts": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/get-system-fonts/-/get-system-fonts-2.0.2.tgz", "integrity": "sha512-zzlgaYnHMIEgHRrfC7x0Qp0Ylhw/sHpM6MHXeVBTYIsvGf5GpbnClB+Q6rAPdn+0gd2oZZIo6Tj3EaWrt4VhDQ==", "license": "MIT", "engines": {"node": ">8.0.0"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz", "integrity": "sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true}, "node_modules/gopd": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz", "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true, "license": "ISC"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-symbols": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz", "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/he/-/he-1.2.0.tgz", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/html-encoding-sniffer": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/html-encoding-sniffer/-/html-encoding-sniffer-3.0.0.tgz", "integrity": "sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^2.0.0"}, "engines": {"node": ">=12"}}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz", "integrity": "sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-server": {"version": "14.1.1", "resolved": "https://registry.npmjs.org/http-server/-/http-server-14.1.1.tgz", "integrity": "sha512-+cbxadF40UXd9T01zUHgA+rlo2Bg1Srer4+B4NwIHdaGxAGGv59nYRnGGDJ9LBk7alpS0US+J+bLLdQOOkJq4A==", "dev": true, "license": "MIT", "dependencies": {"basic-auth": "^2.0.1", "chalk": "^4.1.2", "corser": "^2.0.1", "he": "^1.2.0", "html-encoding-sniffer": "^3.0.0", "http-proxy": "^1.18.1", "mime": "^1.6.0", "minimist": "^1.2.6", "opener": "^1.5.1", "portfinder": "^1.0.28", "secure-compare": "3.0.1", "union": "~0.5.0", "url-join": "^4.0.1"}, "bin": {"http-server": "bin/http-server"}, "engines": {"node": ">=12"}}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz", "integrity": "sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/immediate": {"version": "3.0.6", "resolved": "https://registry.npmjs.org/immediate/-/immediate-3.0.6.tgz", "integrity": "sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==", "license": "MIT"}, "node_modules/import-local": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz", "integrity": "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "license": "ISC"}, "node_modules/interpret": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/interpret/-/interpret-1.4.0.tgz", "integrity": "sha512-agE4QfB2Lkp9uICn7BAqoscw4SZP9kTE2hxiFI3jBPmXJfdqiahTbUuKGsMoN2GtqL9AxhYioAcVvgsb1HvRbA==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-core-module": {"version": "2.16.0", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.0.tgz", "integrity": "sha512-urTSINYfAYgcbLb0yDQ6egFm6h3Mo1DcF9EkyXSRjjzdHbsulg01qhwWuXdOoUBuTkbQ80KDboXa0vFJ+BDH+g==", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz", "integrity": "sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz", "integrity": "sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/jest-worker": {"version": "27.5.1", "resolved": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "integrity": "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/js-base64": {"version": "3.7.7", "resolved": "https://registry.npmjs.org/js-base64/-/js-base64-3.7.7.tgz", "integrity": "sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/jschardet": {"version": "3.1.4", "resolved": "https://registry.npmjs.org/jschardet/-/jschardet-3.1.4.tgz", "integrity": "sha512-/kmVISmrwVwtyYU40iQUOp3SUPk2dhNCMsZBQX0R1/jZ8maaXJ/oZIzUOiyOqcgtLnETFKYChbJ5iDC/eWmFHg==", "license": "LGPL-2.1+", "engines": {"node": ">=0.1.90"}}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dev": true, "license": "MIT", "peer": true}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==", "dev": true, "license": "MIT", "peer": true}, "node_modules/jsonc-parser": {"version": "3.3.1", "resolved": "https://registry.npmjs.org/jsonc-parser/-/jsonc-parser-3.3.1.tgz", "integrity": "sha512-HUgH65KyejrUFPvHFPbqOY0rsFip3Bo5wb4ngvdi1EpCYWUQDC5V+Y7mZws+DLkr4M//zQJoanu1SP+87Dv1oQ==", "license": "MIT"}, "node_modules/jszip": {"version": "3.10.1", "resolved": "https://registry.npmjs.org/jszip/-/jszip-3.10.1.tgz", "integrity": "sha512-xXDvecyTpGLrqFrvkrUSoxxfJI5AH7U8zxxtVclpsUtMCq4JQ290LY8AW5c7Ggnr/Y/oK+bQMbqK2qmtk3pN4g==", "license": "(MIT OR GPL-3.0-or-later)", "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz", "integrity": "sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/langium": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/langium/-/langium-3.4.0.tgz", "integrity": "sha512-7xufsaF5jYGFMXHOTka8bN48b9FHn2vZGL2R+PGgyi+JY/xgimUFDKYcz/h4gm5m8p3sSRtZDh+sK2U63K0MNg==", "license": "MIT", "dependencies": {"chevrotain": "~11.0.3", "chevrotain-allstar": "~0.3.0", "vscode-languageserver": "~9.0.1", "vscode-languageserver-textdocument": "~1.0.11", "vscode-uri": "~3.0.8"}, "engines": {"node": ">=18.0.0"}}, "node_modules/lie": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/lie/-/lie-3.3.0.tgz", "integrity": "sha512-UaiMJzeWRlEujzAuw5LokY1L5ecNQYZKfmyZ9L7wDHb/p5etKaxXhohBcrw0EYby+G/NA52vRSN4N39dxHAIwQ==", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/loader-runner": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz", "integrity": "sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6.11.5"}}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "dev": true, "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz", "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==", "license": "MIT"}, "node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/marked": {"version": "14.0.0", "resolved": "https://registry.npmjs.org/marked/-/marked-14.0.0.tgz", "integrity": "sha512-uIj4+faQ+MgHgwUW1l2PsPglZLOLOT1uErt06dAPtx2kjteLAkbsd/0FiYg/MGS+i7ZKLb7w2WClxHkzOOuryQ==", "license": "MIT", "bin": {"marked": "bin/marked.js"}, "engines": {"node": ">= 18"}}, "node_modules/math-intrinsics": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz", "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz", "integrity": "sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.3.tgz", "integrity": "sha512-gaNvAS7TZ897/rVaZ0nMtAyxNyi/pdbjbAwUpFQpN70GqnVfOiXpeUUMKRBmzXaSQ8DdTX4/0ms62r2K+hE6mQ==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "dev": true, "license": "MIT", "peer": true}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/methods/-/methods-1.1.2.tgz", "integrity": "sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "integrity": "sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "integrity": "sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/monaco-editor": {"name": "@codingame/monaco-vscode-editor-api", "version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-editor-api/-/monaco-vscode-editor-api-15.0.2.tgz", "integrity": "sha512-to1b/4bqfMDUTYZzkDZUFxJr7xYWDZ1lRKPKCyqhxt8TRShAIsItEQ6rZ3HrhjKJR4T1prwDyG1N4eCObFX4/g==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2"}}, "node_modules/monaco-editor-wrapper": {"version": "6.6.0", "resolved": "https://registry.npmjs.org/monaco-editor-wrapper/-/monaco-editor-wrapper-6.6.0.tgz", "integrity": "sha512-VkLf/zaiAphMq0VlTmlVWAswD4/zDbYDUDAro7bpWsgM7vLxDjm5boF+nHY+vcy585CVX+ce0t5uWI10Njmu9A==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "~15.0.2", "@codingame/monaco-vscode-editor-api": "~15.0.2", "@codingame/monaco-vscode-editor-service-override": "~15.0.2", "@codingame/monaco-vscode-extension-api": "~15.0.2", "@codingame/monaco-vscode-language-pack-cs": "~15.0.2", "@codingame/monaco-vscode-language-pack-de": "~15.0.2", "@codingame/monaco-vscode-language-pack-es": "~15.0.2", "@codingame/monaco-vscode-language-pack-fr": "~15.0.2", "@codingame/monaco-vscode-language-pack-it": "~15.0.2", "@codingame/monaco-vscode-language-pack-ja": "~15.0.2", "@codingame/monaco-vscode-language-pack-ko": "~15.0.2", "@codingame/monaco-vscode-language-pack-pl": "~15.0.2", "@codingame/monaco-vscode-language-pack-pt-br": "~15.0.2", "@codingame/monaco-vscode-language-pack-qps-ploc": "~15.0.2", "@codingame/monaco-vscode-language-pack-ru": "~15.0.2", "@codingame/monaco-vscode-language-pack-tr": "~15.0.2", "@codingame/monaco-vscode-language-pack-zh-hans": "~15.0.2", "@codingame/monaco-vscode-language-pack-zh-hant": "~15.0.2", "@codingame/monaco-vscode-monarch-service-override": "~15.0.2", "@codingame/monaco-vscode-textmate-service-override": "~15.0.2", "@codingame/monaco-vscode-theme-defaults-default-extension": "~15.0.2", "@codingame/monaco-vscode-theme-service-override": "~15.0.2", "@codingame/monaco-vscode-views-service-override": "~15.0.2", "@codingame/monaco-vscode-workbench-service-override": "~15.0.2", "monaco-languageclient": "~9.5.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~15.0.2", "vscode-languageclient": "~9.0.1", "vscode-languageserver-protocol": "~3.17.5", "vscode-ws-jsonrpc": "~3.4.0"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/monaco-languageclient": {"version": "9.5.0", "resolved": "https://registry.npmjs.org/monaco-languageclient/-/monaco-languageclient-9.5.0.tgz", "integrity": "sha512-kl9DHbKxNh3fe5MDOZZLobIkU8q7BNUIEW1wUcnIW1V+Ti+pqbbgLEEM4sWQaVkMMaJE2ySoPtF3VhDh5rF3Rg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-api": "~15.0.2", "@codingame/monaco-vscode-configuration-service-override": "~15.0.2", "@codingame/monaco-vscode-editor-api": "~15.0.2", "@codingame/monaco-vscode-editor-service-override": "~15.0.2", "@codingame/monaco-vscode-extension-api": "~15.0.2", "@codingame/monaco-vscode-extensions-service-override": "~15.0.2", "@codingame/monaco-vscode-languages-service-override": "~15.0.2", "@codingame/monaco-vscode-localization-service-override": "~15.0.2", "@codingame/monaco-vscode-log-service-override": "~15.0.2", "@codingame/monaco-vscode-model-service-override": "~15.0.2", "vscode": "npm:@codingame/monaco-vscode-extension-api@~15.0.2", "vscode-languageclient": "~9.0.1"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/monaco-languageclient-examples": {"version": "2025.3.6", "resolved": "https://registry.npmjs.org/monaco-languageclient-examples/-/monaco-languageclient-examples-2025.3.6.tgz", "integrity": "sha512-TGUeLLmDYBTeJVeZo51G2yMR4hpwsUl9/PpjyZmfaDGlNJGX+81vCbXRMBO9+LphmPgNUyT/KSE3HaCFx3WDAA==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-configuration-service-override": "~15.0.2", "@codingame/monaco-vscode-cpp-default-extension": "~15.0.2", "@codingame/monaco-vscode-debug-service-override": "~15.0.2", "@codingame/monaco-vscode-editor-api": "~15.0.2", "@codingame/monaco-vscode-environment-service-override": "~15.0.2", "@codingame/monaco-vscode-explorer-service-override": "~15.0.2", "@codingame/monaco-vscode-files-service-override": "~15.0.2", "@codingame/monaco-vscode-groovy-default-extension": "~15.0.2", "@codingame/monaco-vscode-java-default-extension": "~15.0.2", "@codingame/monaco-vscode-javascript-default-extension": "~15.0.2", "@codingame/monaco-vscode-json-default-extension": "~15.0.2", "@codingame/monaco-vscode-keybindings-service-override": "~15.0.2", "@codingame/monaco-vscode-lifecycle-service-override": "~15.0.2", "@codingame/monaco-vscode-localization-service-override": "~15.0.2", "@codingame/monaco-vscode-preferences-service-override": "~15.0.2", "@codingame/monaco-vscode-python-default-extension": "~15.0.2", "@codingame/monaco-vscode-remote-agent-service-override": "~15.0.2", "@codingame/monaco-vscode-search-result-default-extension": "~15.0.2", "@codingame/monaco-vscode-search-service-override": "~15.0.2", "@codingame/monaco-vscode-secret-storage-service-override": "~15.0.2", "@codingame/monaco-vscode-standalone-json-language-features": "~15.0.2", "@codingame/monaco-vscode-standalone-languages": "~15.0.2", "@codingame/monaco-vscode-standalone-typescript-language-features": "~15.0.2", "@codingame/monaco-vscode-storage-service-override": "~15.0.2", "@codingame/monaco-vscode-testing-service-override": "~15.0.2", "@codingame/monaco-vscode-textmate-service-override": "~15.0.2", "@codingame/monaco-vscode-theme-defaults-default-extension": "~15.0.2", "@codingame/monaco-vscode-theme-service-override": "~15.0.2", "@codingame/monaco-vscode-typescript-basics-default-extension": "~15.0.2", "@codingame/monaco-vscode-typescript-language-features-default-extension": "~15.0.2", "@codingame/monaco-vscode-views-service-override": "~15.0.2", "@typefox/monaco-editor-react": "~6.6.0", "cors": "^2.8.5", "express": "~4.21.2", "jszip": "~3.10.1", "langium": "~3.4.0", "monaco-editor-wrapper": "~6.6.0", "monaco-languageclient": "~9.5.0", "pyright": "~1.1.396", "react": "~19.0.0", "react-dom": "~19.0.0", "request-light": "~0.8.0", "vscode": "npm:@codingame/monaco-vscode-extension-api@~15.0.2", "vscode-json-languageservice": "~5.4.2", "vscode-languageclient": "~9.0.1", "vscode-languageserver": "~9.0.1", "vscode-uri": "~3.1.0", "vscode-ws-jsonrpc": "~3.4.0", "ws": "~8.18.0", "wtd-core": "~4.0.1"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/monaco-languageclient-examples/node_modules/vscode-uri": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.1.0.tgz", "integrity": "sha512-/BpdSx+yCQGnCvecbyXdxHDkuk55/G3xwnC0GqY4gmQ3j+A+g8kzzgB4Nk/SINjqn6+waqw3EgbVF2QKExkRxQ==", "license": "MIT"}, "node_modules/ms": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "license": "MIT"}, "node_modules/nanoid": {"version": "3.3.8", "resolved": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.8.tgz", "integrity": "sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz", "integrity": "sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==", "dev": true, "license": "MIT", "peer": true}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true, "license": "MIT", "peer": true}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.3", "resolved": "https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.3.tgz", "integrity": "sha512-kDCGIbxkDSXE3euJZZXzc6to7fCrKHNI/hSRQnRuQ+BWjFNzZwiFF8fj/6o2t2G9/jTj8PSIYTfCLelLZEeRpA==", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/opener": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/opener/-/opener-1.5.2.tgz", "integrity": "sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==", "dev": true, "license": "(WTFPL OR MIT)", "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/opentype.js": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/opentype.js/-/opentype.js-0.8.0.tgz", "integrity": "sha512-FQHR4oGP+a0m/f6yHoRpBOIbn/5ZWxKd4D/djHVJu8+KpBTYrJda0b7mLcgDEMWXE9xBCJm+qb0yv6FcvPjukg==", "license": "MIT", "dependencies": {"tiny-inflate": "^1.0.2"}, "bin": {"ot": "bin/ot"}}, "node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pako": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/pako/-/pako-1.0.11.tgz", "integrity": "sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==", "license": "(MIT AND Zlib)"}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "integrity": "sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true, "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.12", "resolved": "https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.12.tgz", "integrity": "sha512-RA1GjUVMnvYFxuqovrEqZoxxW5NUZqbwKtYz/Tt7nXerk0LbLblQmrsgdeOxV5SFHf0UDggjS/bSeOZwt1pmEQ==", "license": "MIT"}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/portfinder": {"version": "1.0.32", "resolved": "https://registry.npmjs.org/portfinder/-/portfinder-1.0.32.tgz", "integrity": "sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.7", "resolved": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "integrity": "sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/portfinder/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true, "license": "MIT"}, "node_modules/postcss": {"version": "8.4.49", "resolved": "https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz", "integrity": "sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz", "integrity": "sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.2.0.tgz", "integrity": "sha512-5kcJm/zk+GJDSfw+V/42fJ5fhjL5YbFDl8nVdXkJPLLW+Vf9mTD5Xe0wqIaDnLuL2U6cDNpTr+UQ+v2HWIBhzw==", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^7.0.0", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope": {"version": "3.2.1", "resolved": "https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.2.1.tgz", "integrity": "sha512-m9jZstCVaqGjTAuny8MdgE88scJnCiQSlSrOWcTQgM2t32UBe+MUmFSO5t7VMSfAf/FJKImAxBav8ooCHJXCJA==", "dev": true, "license": "ISC", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz", "integrity": "sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-selector-parser": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-7.0.0.tgz", "integrity": "sha512-9RbEr1Y7FFfptd/1eEdntyjMwLeghW1bHX9GWjXo19vx4ytPQhANltvVxDggzJl7mnWM+dX28kb6cyS/4iQjlQ==", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz", "integrity": "sha512-1N<PERSON>s6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==", "dev": true, "license": "MIT"}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz", "integrity": "sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==", "license": "MIT"}, "node_modules/promise-stream-reader": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/promise-stream-reader/-/promise-stream-reader-1.0.1.tgz", "integrity": "sha512-Tnxit5trUjBAqqZCGWwjyxhmgMN4hGrtpW3Oc/tRI4bpm/O2+ej72BB08l6JBnGQgVDGCLvHFGjGgQS6vzhwXg==", "license": "MIT", "engines": {"node": ">8.0.0"}}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "integrity": "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=6"}}, "node_modules/pyright": {"version": "1.1.396", "resolved": "https://registry.npmjs.org/pyright/-/pyright-1.1.396.tgz", "integrity": "sha512-+/8GN9ZRlqS/EFUjSW3yb2FN9XF7KjGpnLVYLtfTPDiiH+tfua898acKenUTGYbdfvSf7J0GD/g1b5RItnyYPw==", "license": "MIT", "bin": {"pyright": "index.js", "pyright-langserver": "langserver.index.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"fsevents": "~2.3.3"}}, "node_modules/qs": {"version": "6.13.0", "resolved": "https://registry.npmjs.org/qs/-/qs-6.13.0.tgz", "integrity": "sha512-+38qI9SOr8tfZ4QmJNplMUxqjbe7LKvvZgWdExBOmd+egZTtjLB67Gu0HRX3u/XOq7UU2Nx6nsjvS16Z9uwfpg==", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz", "integrity": "sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "integrity": "sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz", "integrity": "sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/react": {"version": "19.0.0", "resolved": "https://registry.npmjs.org/react/-/react-19.0.0.tgz", "integrity": "sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/react-dom": {"version": "19.0.0", "resolved": "https://registry.npmjs.org/react-dom/-/react-dom-19.0.0.tgz", "integrity": "sha512-4GV5sHFG0e/0AD4X+ySy6UJd3jVl1iNsNHdpad0qhABJ11twS3TTBnseqsKurKcsNqCEFeGL3uLpVChpIO3QfQ==", "license": "MIT", "dependencies": {"scheduler": "^0.25.0"}, "peerDependencies": {"react": "^19.0.0"}}, "node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/rechoir": {"version": "0.6.2", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.6.2.tgz", "integrity": "sha512-HFM8rkZ+i3zrV+4LQjwQ0W+ez98pApMGM3HUrN04j3CqzPOzl9nmP15Y8YXNm8QHGv/eacOVEjqhmWpkRV0NAw==", "dev": true, "dependencies": {"resolve": "^1.1.6"}, "engines": {"node": ">= 0.10"}}, "node_modules/request-light": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/request-light/-/request-light-0.8.0.tgz", "integrity": "sha512-bH6E4PMmsEXYrLX6Kr1vu+xI3HproB1vECAwaPSJeroLE1kpWE3HR27uB4icx+6YORu1ajqBJXxuedv8ZQg5Lw==", "license": "MIT"}, "node_modules/require-from-string": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz", "integrity": "sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.22.9", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.9.tgz", "integrity": "sha512-QxrmX1DzraFIi9PxdG5VkRfRwIgjwyud+z/iBwfRRrVmHc+P9Q7u2lSSpQ6bjr2gy5lrqIiU9vb6iAeGf2400A==", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "license": "MIT"}, "node_modules/scheduler": {"version": "0.25.0", "resolved": "https://registry.npmjs.org/scheduler/-/scheduler-0.25.0.tgz", "integrity": "sha512-xFVuu11jh+xcO7JOAGJNOXld8/TcEHK/4CituBUeUb5hqxJLj9YuemAEuvm9gQ/+pgXYfbQuqAkiYu+u7YEsNA==", "license": "MIT"}, "node_modules/schema-utils": {"version": "3.3.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz", "integrity": "sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/secure-compare": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/secure-compare/-/secure-compare-3.0.1.tgz", "integrity": "sha512-<PERSON>ckIIV90rPDcBcglUwXPF3kg0P0qmPsPXAj6BBEENQE1p5yA1xfmDJzfi1Tappj37Pv2mVbKpL3Z1T+Nn7k1Qw==", "dev": true, "license": "MIT"}, "node_modules/semver": {"version": "7.6.3", "resolved": "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz", "integrity": "sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==", "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/send": {"version": "0.19.0", "resolved": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "integrity": "sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "integrity": "sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "license": "MIT"}, "node_modules/serialize-javascript": {"version": "6.0.2", "resolved": "https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz", "integrity": "sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true, "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-static": {"version": "1.16.2", "resolved": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "integrity": "sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/setimmediate/-/setimmediate-1.0.5.tgz", "integrity": "sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==", "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "integrity": "sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==", "license": "ISC"}, "node_modules/shallow-clone": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz", "integrity": "sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shelljs": {"version": "0.8.5", "resolved": "https://registry.npmjs.org/shelljs/-/shelljs-0.8.5.tgz", "integrity": "sha512-TiwcRcrkhHvbrZbnRcFYMLl30Dfov3HKqzp5tO5b4pt6G/SezKcYhmDg15zXVBswHmctSAQKznqNW2LO5tTDow==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"glob": "^7.0.0", "interpret": "^1.0.0", "rechoir": "^0.6.2"}, "bin": {"shjs": "bin/shjs"}, "engines": {"node": ">=4"}}, "node_modules/shx": {"version": "0.3.4", "resolved": "https://registry.npmjs.org/shx/-/shx-0.3.4.tgz", "integrity": "sha512-N6A9MLVqjxZYcVn8hLmtneQWIJtp8IKzMP4eMnx+nqkvXoqinUPCbUFLp2UcWTEIUONhlk0ewxr/jaVGlc+J+g==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.3", "shelljs": "^0.8.5"}, "bin": {"shx": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/side-channel": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/side-channel/-/side-channel-1.1.0.tgz", "integrity": "sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-list": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-map": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/side-channel-map/-/side-channel-map-1.0.1.tgz", "integrity": "sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel-weakmap": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz", "integrity": "sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==", "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/source-map": {"version": "0.7.4", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.7.4.tgz", "integrity": "sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/source-map-js": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "integrity": "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-loader": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/source-map-loader/-/source-map-loader-5.0.0.tgz", "integrity": "sha512-k2Dur7CbSLcAH73sBcIkV5xjPV4SzqO1NJ7+XaQl8if3VODDUj3FNchNGpqgJSKbvUfJuhVdv8K2Eu8/TNl2eA==", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "^0.6.3", "source-map-js": "^1.0.2"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.72.1"}}, "node_modules/source-map-loader/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "peer": true, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "integrity": "sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/style-loader": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/style-loader/-/style-loader-4.0.0.tgz", "integrity": "sha512-*******************************************************+gL9y6sNdN95uEOS83Y55SqHcP7MzLA==", "dev": true, "license": "MIT", "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.27.0"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tapable": {"version": "2.2.1", "resolved": "https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz", "integrity": "sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "5.37.0", "resolved": "https://registry.npmjs.org/terser/-/terser-5.37.0.tgz", "integrity": "sha512-B8wRRkmre4ERucLM/uXx4MOV5cbnOlVAqUst+1+iLKPI0dOgFO28f84ptoQt9HEI537PMzfYa/d+GEPKTRXmYA==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.11", "resolved": "https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.11.tgz", "integrity": "sha512-RVCsMfuD0+cTt3EwX8hSl2Ks56EbFHWmhluwcqoPKtBnfjiT6olaq7PRIRfhyU8nnC2MrnDrBLfrD/RGE+cVXQ==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser-webpack-plugin/node_modules/ajv": {"version": "8.17.1", "resolved": "https://registry.npmjs.org/ajv/-/ajv-8.17.1.tgz", "integrity": "sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/terser-webpack-plugin/node_modules/ajv-keywords": {"version": "5.1.0", "resolved": "https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz", "integrity": "sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/terser-webpack-plugin/node_modules/json-schema-traverse": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==", "dev": true, "license": "MIT", "peer": true}, "node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/schema-utils/-/schema-utils-4.3.0.tgz", "integrity": "sha512-Gf9qqc58SpCA/xdziiHz35F4GNIWYWZrEshUc/G/r5BnLph6xpKuLeoJoQuj5WfBIx/eQLf+hmVPYHaxJu7V2g==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/tiny-inflate": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/tiny-inflate/-/tiny-inflate-1.0.3.tgz", "integrity": "sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==", "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/ts-loader": {"version": "9.5.2", "resolved": "https://registry.npmjs.org/ts-loader/-/ts-loader-9.5.2.tgz", "integrity": "sha512-Qo4piXvOTWcMGIgRiuFa6nHNm+54HbYaZCKqc9eeZCLRy3XqafQgwX2F7mofrbJG3g7EEb+lkiR+z2Lic2s3Zw==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "enhanced-resolve": "^5.0.0", "micromatch": "^4.0.0", "semver": "^7.3.4", "source-map": "^0.7.4"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"typescript": "*", "webpack": "^5.0.0"}}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz", "integrity": "sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typescript": {"version": "5.7.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.7.2.tgz", "integrity": "sha512-i5t66RHxDvVN40HfDd1PsEThGNnlMCMT3jMUuoh9/0TaqWevNontacunWyN02LA9/fIbEWlcHZcgTKb9QoaLfg==", "dev": true, "license": "Apache-2.0", "peer": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.20.0", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.20.0.tgz", "integrity": "sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==", "dev": true, "license": "MIT", "peer": true}, "node_modules/union": {"version": "0.5.0", "resolved": "https://registry.npmjs.org/union/-/union-0.5.0.tgz", "integrity": "sha512-N6uOhuW6zO95P3Mel2I2zMsbsanvvtgn6jVqJv4vbVcz/JN0OkL9suomjQGmWtxJQXOCqUJvquc1sMeNz/IwlA==", "dev": true, "dependencies": {"qs": "^6.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "integrity": "sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.1.tgz", "integrity": "sha512-R8UzCaa9Az+38REPiJ1tXlImTJXlVfgHZsglwBD/k6nj76ctsH1E3q4doGrukiLQd3sGQYu56r5+lo5r94l29A==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "peer": true, "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.0"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "peer": true, "dependencies": {"punycode": "^2.1.0"}}, "node_modules/url-join": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/url-join/-/url-join-4.0.1.tgz", "integrity": "sha512-jk1+QP6ZJqyOiuEI9AEWQfju/nB2Pw466kbA0LEZljHwKeMgd9WrAEgEGxjPDD2+TNbbb37rTyhEfrCXfuKXnA==", "dev": true, "license": "MIT"}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "integrity": "sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "integrity": "sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vscode": {"name": "@codingame/monaco-vscode-extension-api", "version": "15.0.2", "resolved": "https://registry.npmjs.org/@codingame/monaco-vscode-extension-api/-/monaco-vscode-extension-api-15.0.2.tgz", "integrity": "sha512-qdt2VEKR6aI5Z7KAw5ex/dXD72k6lmKi4V+Po++De0YdSPPzqo4+KaNLiHqcM2FWXbgox4J+qE8ul9CZ131SMg==", "license": "MIT", "dependencies": {"@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-api": "15.0.2", "@codingame/monaco-vscode-************************************-common": "15.0.2", "@codingame/monaco-vscode-extensions-service-override": "15.0.2"}}, "node_modules/vscode-json-languageservice": {"version": "5.4.2", "resolved": "https://registry.npmjs.org/vscode-json-languageservice/-/vscode-json-languageservice-5.4.2.tgz", "integrity": "sha512-2qujUseKRbLEwLXvEOFAxaz3y1ssdNCXXi95LRdG8AFchJHSnmI2qCg9ixoYxbJtSehIrXOmkhV87Y9lIivOgQ==", "license": "MIT", "dependencies": {"@vscode/l10n": "^0.0.18", "jsonc-parser": "^3.3.1", "vscode-languageserver-textdocument": "^1.0.12", "vscode-languageserver-types": "^3.17.5", "vscode-uri": "^3.0.8"}}, "node_modules/vscode-jsonrpc": {"version": "8.2.0", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-8.2.0.tgz", "integrity": "sha512-C+r0eKJUIfiDIfwJhria30+TYWPtuHJXHtI7J0YlOmKAo7ogxP20T0zxB7HZQIFhIyvoBPwWskjxrvAtfjyZfA==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/vscode-languageclient": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/vscode-languageclient/-/vscode-languageclient-9.0.1.tgz", "integrity": "sha512-JZiimVdvimEuHh5olxhxkht09m3JzUGwggb5eRUkzzJhZ2KjCN0nh55VfiED9oez9DyF8/fz1g1iBV3h+0Z2EA==", "license": "MIT", "dependencies": {"minimatch": "^5.1.0", "semver": "^7.3.7", "vscode-languageserver-protocol": "3.17.5"}, "engines": {"vscode": "^1.82.0"}}, "node_modules/vscode-languageclient/node_modules/brace-expansion": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "integrity": "sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/vscode-languageclient/node_modules/minimatch": {"version": "5.1.6", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/vscode-languageserver": {"version": "9.0.1", "resolved": "https://registry.npmjs.org/vscode-languageserver/-/vscode-languageserver-9.0.1.tgz", "integrity": "sha512-woByF3PDpkHFUreUa7Hos7+pUWdeWMXRd26+ZX2A8cFx6v/JPTtd4/uN0/jB6XQHYaOlHbio03NTHCqrgG5n7g==", "license": "MIT", "dependencies": {"vscode-languageserver-protocol": "3.17.5"}, "bin": {"installServerIntoExtension": "bin/installServerIntoExtension"}}, "node_modules/vscode-languageserver-protocol": {"version": "3.17.5", "resolved": "https://registry.npmjs.org/vscode-languageserver-protocol/-/vscode-languageserver-protocol-3.17.5.tgz", "integrity": "sha512-mb1bvRJN8SVznADSGWM9u/b07H7Ecg0I3OgXDuLdn307rl/J3A9YD6/eYOssqhecL27hK1IPZAsaqh00i/Jljg==", "license": "MIT", "dependencies": {"vscode-jsonrpc": "8.2.0", "vscode-languageserver-types": "3.17.5"}}, "node_modules/vscode-languageserver-textdocument": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/vscode-languageserver-textdocument/-/vscode-languageserver-textdocument-1.0.12.tgz", "integrity": "sha512-cxWNPesCnQCcMPeenjKKsOCKQZ/L6Tv19DTRIGuLWe32lyzWhihGVJ/rcckZXJxfdKCFvRLS3fpBIsV/ZGX4zA==", "license": "MIT"}, "node_modules/vscode-languageserver-types": {"version": "3.17.5", "resolved": "https://registry.npmjs.org/vscode-languageserver-types/-/vscode-languageserver-types-3.17.5.tgz", "integrity": "sha512-Ld1VelNuX9pdF39h2Hgaeb5hEZM2Z3jUrrMgWQAu82jMtZp7p3vJT3BzToKtZI7NgQssZje5o0zryOrhQvzQAg==", "license": "MIT"}, "node_modules/vscode-oniguruma": {"version": "1.7.0", "resolved": "https://registry.npmjs.org/vscode-oniguruma/-/vscode-oniguruma-1.7.0.tgz", "integrity": "sha512-L9WMGRfrjOhgHSdOYgCt/yRMsXzLDJSL7BPrOZt73gU0iWO4mpqzqQzOz5srxqTvMBaR0XZTSrVWo4j55Rc6cA==", "license": "MIT"}, "node_modules/vscode-textmate": {"version": "9.2.0", "resolved": "https://registry.npmjs.org/vscode-textmate/-/vscode-textmate-9.2.0.tgz", "integrity": "sha512-rkvG4SraZQaPSN/5XjwKswdU0OP9MF28QjrYzUBbhb8QyG3ljB1Ky996m++jiI7KdiAP2CkBiQZd9pqEDTClqA==", "license": "MIT"}, "node_modules/vscode-uri": {"version": "3.0.8", "resolved": "https://registry.npmjs.org/vscode-uri/-/vscode-uri-3.0.8.tgz", "integrity": "sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==", "license": "MIT"}, "node_modules/vscode-ws-jsonrpc": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/vscode-ws-jsonrpc/-/vscode-ws-jsonrpc-3.4.0.tgz", "integrity": "sha512-jkNZvX0LdHt4skPxMw/jFePr3jRCJU6ZmO28oPoQ7RwNSkwU3uN8mgtxACYEbOY68bYmi/b/uJzhxewKCz1P4w==", "license": "MIT", "dependencies": {"vscode-jsonrpc": "~8.2.1"}, "engines": {"node": ">=18.19.0", "npm": ">=10.2.3"}}, "node_modules/vscode-ws-jsonrpc/node_modules/vscode-jsonrpc": {"version": "8.2.1", "resolved": "https://registry.npmjs.org/vscode-jsonrpc/-/vscode-jsonrpc-8.2.1.tgz", "integrity": "sha512-kdjOSJ2lLIn7r1rtrMbbNCHjyMPfRnowdKjBQ+mGq6NAW5QY2bEZC/khaC5OR8svbbjvLEaIXkOq45e2X9BIbQ==", "license": "MIT", "engines": {"node": ">=14.0.0"}}, "node_modules/watchpack": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/watchpack/-/watchpack-2.4.2.tgz", "integrity": "sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/webpack": {"version": "5.97.1", "resolved": "https://registry.npmjs.org/webpack/-/webpack-5.97.1.tgz", "integrity": "sha512-EksG6gFY3L1eFMROS/7Wzgrii5mBAFe4rIr3r2BTfo7bcc+DWwFZ4OJ/miOuHJO/A85HwyI4eQ0F6IKXesO7Fg==", "dev": true, "license": "MIT", "peer": true, "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.2.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.10", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-cli": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/webpack-cli/-/webpack-cli-6.0.1.tgz", "integrity": "sha512-MfwFQ6SfwinsUVi0rNJm7rHZ31GyTcpVE5pgVA3hwFRb7COD4TzjUUwhGWKfO50+xdc2MQPuEBBJoqIMGt3JDw==", "dev": true, "license": "MIT", "dependencies": {"@discoveryjs/json-ext": "^0.6.1", "@webpack-cli/configtest": "^3.0.1", "@webpack-cli/info": "^3.0.1", "@webpack-cli/serve": "^3.0.1", "colorette": "^2.0.14", "commander": "^12.1.0", "cross-spawn": "^7.0.3", "envinfo": "^7.14.0", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^3.1.1", "rechoir": "^0.8.0", "webpack-merge": "^6.0.1"}, "bin": {"webpack-cli": "bin/cli.js"}, "engines": {"node": ">=18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.82.0"}, "peerDependenciesMeta": {"webpack-bundle-analyzer": {"optional": true}, "webpack-dev-server": {"optional": true}}}, "node_modules/webpack-cli/node_modules/commander": {"version": "12.1.0", "resolved": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "integrity": "sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/webpack-cli/node_modules/interpret": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/interpret/-/interpret-3.1.1.tgz", "integrity": "sha512-6xwYfHbajpoF0xLW+iwLkhwgvLoZDfjYfoFNu8ftMoXINzwuymNLd9u/KmwtdT2GbR+/Cz66otEGEVVUHX9QLQ==", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/webpack-cli/node_modules/rechoir": {"version": "0.8.0", "resolved": "https://registry.npmjs.org/rechoir/-/rechoir-0.8.0.tgz", "integrity": "sha512-/vxpCXddiX8NGfGO/mTafwjq4aFa/71pvamip0++IQk3zG8cbCj0fifNPrjjF1XMXUne91jL9OoxmdykoEtifQ==", "dev": true, "license": "MIT", "dependencies": {"resolve": "^1.20.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/webpack-merge": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/webpack-merge/-/webpack-merge-6.0.1.tgz", "integrity": "sha512-hXXvrjtx2PLYx4qruKl+kyRSLc52V+cCvMxRjmKwoA+CBbbF5GfIBtR6kCvl0fYGqTUPKB+1ktVmTHqMOzgCBg==", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.1"}, "engines": {"node": ">=18.0.0"}}, "node_modules/webpack-sources": {"version": "3.2.3", "resolved": "https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz", "integrity": "sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==", "dev": true, "license": "MIT", "peer": true, "engines": {"node": ">=10.13.0"}}, "node_modules/whatwg-encoding": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/whatwg-encoding/-/whatwg-encoding-2.0.0.tgz", "integrity": "sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=12"}}, "node_modules/whatwg-encoding/node_modules/iconv-lite": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.6.3.tgz", "integrity": "sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wildcard": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz", "integrity": "sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==", "dev": true, "license": "MIT"}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "8.18.0", "resolved": "https://registry.npmjs.org/ws/-/ws-8.18.0.tgz", "integrity": "sha512-8VbfWfHLbbwu3+N6OKsOMpBdT4kXPDDB9cJk2bJ6mh9ucxdlnNvH1e+roYkKmN9Nxw2yjz7VzeO9oOz2zJ04Pw==", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/wtd-core": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/wtd-core/-/wtd-core-4.0.1.tgz", "integrity": "sha512-q6sV6Slw47bwlhwbztot0MklWaVzywUAi0wAKWwOuL/LTY4IpVFgoHQ+cnlhG2ZUms/OkJUhyfhsfoHNYkKjzA==", "license": "MIT"}, "node_modules/yallist": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==", "license": "ISC"}}}