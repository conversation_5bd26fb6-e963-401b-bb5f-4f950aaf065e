// LangGraph 工作流执行引擎
import { StateGraph, END, START } from "@langchain/langgraph";
import { HumanMessage } from "@langchain/core/messages";
import { 
  WorkflowDefinition, 
  WorkflowState, 
  workflowRegistry 
} from './workflow-definitions';

// 执行结果接口
export interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  steps: ExecutionStep[];
}

export interface ExecutionStep {
  stepName: string;
  timestamp: number;
  duration: number;
  input: any;
  output: any;
  error?: string;
}

// 工作流执行器类
export class WorkflowExecutor {
  private executionHistory: Map<string, ExecutionResult[]> = new Map();

  // 执行单个工作流
  async executeWorkflow(
    workflowId: string, 
    input: Partial<WorkflowState>
  ): Promise<ExecutionResult> {
    const startTime = Date.now();
    const steps: ExecutionStep[] = [];

    try {
      const workflowDef = workflowRegistry.get(workflowId);
      if (!workflowDef) {
        throw new Error(`Workflow not found: ${workflowId}`);
      }

      // 检查依赖
      if (workflowDef.dependencies) {
        for (const depId of workflowDef.dependencies) {
          if (!workflowRegistry.has(depId)) {
            throw new Error(`Dependency workflow not found: ${depId}`);
          }
        }
      }

      // 创建状态图
      const graph = this.createStateGraph(workflowDef);
      
      // 初始化状态
      const initialState: WorkflowState = {
        messages: input.messages || [new HumanMessage("开始执行工作流")],
        sysmlCode: input.sysmlCode,
        currentStep: workflowDef.entryPoint,
        ...input
      };

      // 执行工作流
      const result = await graph.invoke(initialState);
      
      const executionTime = Date.now() - startTime;
      
      const executionResult: ExecutionResult = {
        success: true,
        result,
        executionTime,
        steps
      };

      // 保存执行历史
      this.saveExecutionHistory(workflowId, executionResult);

      return executionResult;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      const executionResult: ExecutionResult = {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        executionTime,
        steps
      };

      this.saveExecutionHistory(workflowId, executionResult);
      
      return executionResult;
    }
  }

  // 执行工作流链
  async executeWorkflowChain(
    workflowIds: string[],
    input: Partial<WorkflowState>
  ): Promise<ExecutionResult[]> {
    const results: ExecutionResult[] = [];
    let currentState = input;

    for (const workflowId of workflowIds) {
      const result = await this.executeWorkflow(workflowId, currentState);
      results.push(result);

      if (!result.success) {
        break; // 如果某个工作流失败，停止执行链
      }

      // 将前一个工作流的结果作为下一个的输入
      currentState = {
        ...currentState,
        ...result.result,
        messages: result.result?.messages || currentState.messages
      };
    }

    return results;
  }

  // 并行执行多个工作流
  async executeWorkflowsParallel(
    workflowConfigs: Array<{ id: string; input: Partial<WorkflowState> }>
  ): Promise<ExecutionResult[]> {
    const promises = workflowConfigs.map(config => 
      this.executeWorkflow(config.id, config.input)
    );

    return Promise.all(promises);
  }

  // 创建状态图
  private createStateGraph(workflowDef: WorkflowDefinition): StateGraph<WorkflowState> {
    const graph = new StateGraph<WorkflowState>({
      channels: {
        messages: {
          reducer: (left, right) => left.concat(right),
          default: () => []
        },
        sysmlCode: {
          reducer: (left, right) => right ?? left,
          default: () => undefined
        },
        analysisResult: {
          reducer: (left, right) => right ?? left,
          default: () => undefined
        },
        generatedCode: {
          reducer: (left, right) => right ?? left,
          default: () => undefined
        },
        validationResult: {
          reducer: (left, right) => right ?? left,
          default: () => undefined
        },
        currentStep: {
          reducer: (left, right) => right ?? left,
          default: () => undefined
        },
        error: {
          reducer: (left, right) => right ?? left,
          default: () => undefined
        },
        metadata: {
          reducer: (left, right) => ({ ...left, ...right }),
          default: () => ({})
        }
      }
    });

    // 添加节点
    Object.entries(workflowDef.nodes).forEach(([nodeName, nodeFunction]) => {
      graph.addNode(nodeName, nodeFunction);
    });

    // 添加边
    Object.entries(workflowDef.edges).forEach(([fromNode, toNode]) => {
      if (Array.isArray(toNode)) {
        // 条件边
        graph.addConditionalEdges(
          fromNode,
          (state: WorkflowState) => {
            // 基于状态决定下一个节点
            if (state.validationResult === 'valid') {
              return 'complete';
            } else if (state.validationResult === 'invalid') {
              return 'fix_syntax';
            }
            return toNode[0]; // 默认第一个选项
          },
          toNode.reduce((acc, node) => {
            acc[node] = node;
            return acc;
          }, {} as Record<string, string>)
        );
      } else {
        graph.addEdge(fromNode, toNode);
      }
    });

    // 设置入口点
    graph.setEntryPoint(workflowDef.entryPoint);

    return graph.compile();
  }

  // 保存执行历史
  private saveExecutionHistory(workflowId: string, result: ExecutionResult) {
    if (!this.executionHistory.has(workflowId)) {
      this.executionHistory.set(workflowId, []);
    }
    
    const history = this.executionHistory.get(workflowId)!;
    history.push(result);
    
    // 只保留最近的 10 次执行记录
    if (history.length > 10) {
      history.shift();
    }
  }

  // 获取执行历史
  getExecutionHistory(workflowId: string): ExecutionResult[] {
    return this.executionHistory.get(workflowId) || [];
  }

  // 获取所有工作流的状态
  getAllWorkflowStatus(): Record<string, { 
    lastExecution?: ExecutionResult; 
    totalExecutions: number; 
    successRate: number; 
  }> {
    const status: Record<string, any> = {};
    
    for (const [workflowId, history] of this.executionHistory.entries()) {
      const successCount = history.filter(r => r.success).length;
      const totalCount = history.length;
      
      status[workflowId] = {
        lastExecution: history[history.length - 1],
        totalExecutions: totalCount,
        successRate: totalCount > 0 ? successCount / totalCount : 0
      };
    }
    
    return status;
  }

  // 清除执行历史
  clearExecutionHistory(workflowId?: string) {
    if (workflowId) {
      this.executionHistory.delete(workflowId);
    } else {
      this.executionHistory.clear();
    }
  }
}

// 单例执行器
let workflowExecutor: WorkflowExecutor | null = null;

export function getWorkflowExecutor(): WorkflowExecutor {
  if (!workflowExecutor) {
    workflowExecutor = new WorkflowExecutor();
  }
  return workflowExecutor;
}

// 便捷函数
export async function executeWorkflow(
  workflowId: string, 
  input: Partial<WorkflowState>
): Promise<ExecutionResult> {
  const executor = getWorkflowExecutor();
  return executor.executeWorkflow(workflowId, input);
}

export async function executeWorkflowChain(
  workflowIds: string[],
  input: Partial<WorkflowState>
): Promise<ExecutionResult[]> {
  const executor = getWorkflowExecutor();
  return executor.executeWorkflowChain(workflowIds, input);
}
