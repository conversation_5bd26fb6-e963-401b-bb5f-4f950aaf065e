//* 
XPECT_SETUP org.omg.kerml.xpect.tests.visibility.KerMLVisibilityTest
	ResourceSet {
		ThisFile {}
		File {from ="/src/DependencyVisibilityPackage.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/src/DependencyVisibilityPackage.kerml"}
			}
		}
	}
END_SETUP 
*/
package Classes {
	public import VisibilityPackage::c_Public::*;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_private'." at "c_private"
	feature a : c_private;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_protected'." at "c_protected"
	feature b : c_protected;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Public::c_private'." at "c_Public::c_private"
	feature c : c_Public::c_private;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Public::c_protected'." at "c_Public::c_protected"
	feature d : c_Public::c_protected;
	//XPECT errors --> "Couldn't resolve reference to Type 'VisibilityPackage::c_Public::c_private'." at "VisibilityPackage::c_Public::c_private"
	feature e : VisibilityPackage::c_Public::c_private;
	//XPECT errors --> "Couldn't resolve reference to Type 'VisibilityPackage::c_Public::c_protected'." at "VisibilityPackage::c_Public::c_protected"
	feature f : VisibilityPackage::c_Public::c_protected;
	//XPECT errors --> "Couldn't resolve reference to Type 'c_Public::c_public'." at "c_Public::c_public"
	feature g : c_Public::c_public;
}
