/*
 * generated by Xtext 2.12.0
 */
package org.omg.kerml.xtext.ui.outline

import org.eclipse.xtext.ui.editor.outline.IOutlineNode
import org.eclipse.xtext.ui.editor.outline.impl.DefaultOutlineTreeProvider
import org.omg.sysml.lang.sysml.Annotation
import org.omg.sysml.lang.sysml.Conjugation
import org.omg.sysml.lang.sysml.Element
import org.omg.sysml.lang.sysml.FeatureMembership
import org.omg.sysml.lang.sysml.LiteralBoolean
import org.omg.sysml.lang.sysml.LiteralString
import org.omg.sysml.lang.sysml.Membership
import org.omg.sysml.lang.sysml.NullExpression
import org.omg.sysml.lang.sysml.OperatorExpression
import org.omg.sysml.lang.sysml.Relationship
import org.omg.sysml.lang.sysml.Type
import org.omg.sysml.lang.sysml.VisibilityKind
import org.omg.sysml.lang.sysml.Comment
import org.omg.sysml.lang.sysml.SysMLPackage
import org.omg.sysml.lang.sysml.TextualRepresentation
import org.omg.sysml.lang.sysml.Association
import org.omg.sysml.lang.sysml.Connector
import org.omg.sysml.lang.sysml.TypeFeaturing
import org.omg.sysml.lang.sysml.Namespace
import java.net.URLDecoder
import org.omg.sysml.lang.sysml.Feature
import org.omg.sysml.lang.sysml.Expression
import org.eclipse.swt.graphics.Image
import org.eclipse.emf.ecore.EObject
import org.eclipse.xtext.ui.editor.outline.impl.AbstractOutlineNode
import org.eclipse.xtext.ui.editor.outline.impl.DocumentRootNode
import org.omg.sysml.util.ElementUtil
import org.omg.sysml.util.TypeUtil
import org.omg.sysml.util.FeatureUtil
import org.omg.sysml.lang.sysml.LiteralRational
import org.omg.sysml.lang.sysml.LiteralInfinity
import org.omg.sysml.lang.sysml.Specialization
import org.omg.sysml.lang.sysml.LiteralInteger
import org.omg.sysml.lang.sysml.FeatureChaining
import org.omg.sysml.lang.sysml.Disjoining
import org.omg.sysml.lang.sysml.FeatureValue
import org.omg.sysml.lang.sysml.OwningMembership
import org.omg.sysml.lang.sysml.FeatureInverting
import org.omg.sysml.lang.sysml.LibraryPackage
import org.omg.sysml.lang.sysml.MembershipImport
import org.omg.sysml.lang.sysml.NamespaceImport
import org.omg.sysml.lang.sysml.BindingConnector

/**
 * Customization of the default outline structure.
 *
 * See https://www.eclipse.org/Xtext/documentation/310_eclipse_support.html#outline
 */
class KerMLOutlineTreeProvider extends DefaultOutlineTreeProvider {
	
	def String metaclassText(Element element) {
		ElementUtil.transform(element);
		var text = element?.eClass.name
		if (element.isLibraryElement) {
			text += ' lib'
		}
		text
	}
	
	def String idText(Element element) {
		var text = ""
		if (element?.declaredShortName !== null) {
			text += ' <' + element.declaredShortName + '>'
		}
		val name = element?.getName
		if (name !== null) {
			text += ' ' + name
		}
		text
	}

	def String _text(Element element) {
		element.metaclassText + element.idText
	}
	
	def String _text(Namespace namespace) {
		if (namespace.eContainer !== null || namespace.eResource === null)
			(namespace as Element)._text
		else 
			'Root ' + URLDecoder.decode(namespace.eResource.URI.lastSegment, "UTF-8")
	}
	
	def String _text(VisibilityKind visibility) {
		visibility.toString
	}
	
	def String prefixText(Membership membership) {
		var text = membership.metaclassText
		if (membership instanceof OwningMembership) {
			text += ' owns'
		}
		if (membership.visibility !== null) {
			text += ' ' + membership.visibility._text
		}
		text
	}
	
	def String nameText(Membership membership) {
		var text = ''
		val shortName = membership.memberShortName
		if (shortName !== null) {
			text += ' <' + shortName + '>'
		}
		val name = membership.memberName
		if (name !== null) {
			text += ' ' + name
		}
		text
	}
	
	def String _text(Membership membership) {
		membership.prefixText + membership.nameText
	}
	
	def String _text(FeatureValue featureValue) {
		var text = featureValue.metaclassText
		if (featureValue.isDefault) {
			text += ' default'
		}
		if (featureValue.isInitial) {
			text += ' initial'
		}
		text
	}	
	
	def String _text(MembershipImport import_) {
		var text = import_.metaclassText
		if (import_.visibility !== null) {
			text += ' ' + import_.visibility._text
		}
		if (import_.importedMembership?.memberName !== null) {
			text += ' ' + import_.importedMembership.memberName
		}
		if (import_.isRecursive) {
			text += "::**"
		}
		text
	}
	
	def String _text(NamespaceImport import_) {
		var text = import_.metaclassText
		if (import_.visibility !== null) {
			text += ' ' + import_.visibility._text
		}
		if (import_.importedNamespace?.declaredName !== null) {
			text += ' ' + import_.importedNamespace.declaredName
		}
		text += "::*"
		if (import_.isRecursive) {
			text += "::**"
		}
		text
	}
	
	def String typePrefixText(Type type) {
		var text = type.metaclassText
		if (type.isAbstract) {
			text += ' abstract'
		}
		text
	}
	
	def String _text(Type type) {
		if (type instanceof Feature) (type as Feature)._text
		else type.typePrefixText + type.idText
	}
	
	def String featurePrefixText(Feature feature) {
		var text = feature.metaclassText
		if (feature.direction !== null) {
			text += ' ' + feature.direction
		}
		if (feature.isAbstract) {
			text += ' abstract'
		}
		if (feature.isComposite) {
			text += ' composite'
		}
		if (feature.isPortion) {
			text += ' portion'
		}
		if (feature.isVariable) {
			text += ' var'
		}
		if (feature.isConstant) {
			text += ' const'
		}
		if (feature.isDerived) {
			text += ' derived'
		}
		if (feature.isEnd) {
			text += ' end'
		}
		text
	}
	
	def String featureIdText(Feature feature) {
		var idText = feature.idText
		if (idText == "" && !feature.ownedFeatureChaining.empty) {
			for (chainingFeature: feature.chainingFeature) {
				if (idText == "") {
					idText = chainingFeature.idText
				} else {
					var nextId = chainingFeature.idText
					if (!nextId.isEmpty) {
						nextId = nextId.substring(1);
					}
					idText += "." + nextId
				}
			}
		}
		idText
	}
	
	def String _text(Feature feature) {
		feature.featurePrefixText + feature.featureIdText
	}
	
	def String _text(Expression expression) {
		var text = (expression as Feature)._text
		if (expression.isModelLevelEvaluable) {
			text += " model-level"
		}
		text
	}
	
	def String _text(LiteralString literal) {
		literal.metaclassText + ' ' + literal.value
	}
	
	def String _text(LiteralBoolean literal) {
		literal.metaclassText + ' ' + literal.value
	}
	
	def String _text(LiteralInteger literal) {
		literal.metaclassText + ' ' + literal.getValue
	}
	
	def String _text(LiteralRational literal) {
		literal.metaclassText + ' ' + literal.getValue
	}
	
	def String _text(LiteralInfinity literal) {
		literal.metaclassText + ' *'
	}
	
	def String _text(NullExpression expression) {
		expression.metaclassText + ' null'
	}
	
	def String _text(LibraryPackage pkg) {
		var prefixText = pkg.metaclassText
		if (pkg.isStandard) {
			prefixText += ' std'
		}
		prefixText + pkg.idText
	}
	
	def boolean _isLeaf(Relationship relationship) {
		false
	}
	
	def void _createChildren(IOutlineNode parentNode, Relationship relationship) {
		createRelatedElements(parentNode, relationship)
		super._createChildren(parentNode, relationship)
	}
	
	def createRelatedElements(IOutlineNode parentNode, Relationship relationship) {
		for (source: relationship.source) {
			createNode(parentNode, source, 
				_image(source), 'from ' + textDispatcher.invoke(source), 
				!(source instanceof Feature) || (source as Feature).ownedFeatureChaining.empty
			)
		}
		for (target: relationship.target) {
			createNode(parentNode, target, 
				_image(target), 'to ' + textDispatcher.invoke(target), 
				!(target instanceof Feature) || (target as Feature).ownedFeatureChaining.empty
			)
		}
	}
	
	def boolean _isLeaf(Annotation annotation) {
		false
	}
	
	def void _createChildren(IOutlineNode parentNode, Annotation annotation) {
		super._createChildren(parentNode, annotation)
		if (annotation.annotatedElement !== null) {
			createNode(parentNode, annotation.annotatedElement, 
				_image(annotation.annotatedElement), annotation.annotatedElement._text, 
				true
			)
		}
	}
	
	def boolean _isLeaf(Comment comment) {
		comment.body === null && super._isLeaf(comment)
	}
	
	def void _createChildren(IOutlineNode parentNode, Comment comment) {
		if (comment.locale !== null) {
			createEStructuralFeatureNode(parentNode, comment, 
				SysMLPackage.eINSTANCE.comment_Locale, 
				_image(comment.locale), "locale " + comment.locale, true
			)
		}
		if (comment.body !== null) {
			createEStructuralFeatureNode(parentNode, comment, 
				SysMLPackage.eINSTANCE.comment_Body, 
				_image(comment.body), comment.body, true
			)
		}
		super._createChildren(parentNode, comment)
	}
	

	def boolean _isLeaf(TextualRepresentation rep) {
		rep.language === null && rep.body === null && super._isLeaf(rep)
	}
	
	def void _createChildren(IOutlineNode parentNode, TextualRepresentation rep) {
		if (rep.language !== null) {
			createEStructuralFeatureNode(parentNode, rep, 
				SysMLPackage.eINSTANCE.textualRepresentation_Language, 
				_image(rep.language), "language " + rep.language, true
			)
		}
		if (rep.body !== null) {
			createEStructuralFeatureNode(parentNode, rep, 
				SysMLPackage.eINSTANCE.textualRepresentation_Body, 
				_image(rep.body), rep.body, true
			)
		}
		super._createChildren(parentNode, rep)
	}	

	def boolean _isLeaf(Membership membership) {
		false
	}
	
	def void _createChildren(IOutlineNode parentNode, Membership membership) {
		super._createChildren(parentNode, membership)
		var memberElement = membership.memberElement;
		if (!(membership instanceof OwningMembership) && 
				memberElement !== null) {
			createNode(parentNode, memberElement, 
				memberElement._image, memberElement._text, 
				true
			)
		}
	}
	
	def boolean _isLeaf(FeatureMembership membership) {
		false
	}
	
	// Display a FeatureMembership like a Membership, rather than like a TypeFeaturing.
	def void _createChildren(IOutlineNode parentNode, FeatureMembership membership) {
		_createChildren(parentNode, membership as Membership)
	}
	
	def boolean _isLeaf(MembershipImport _import) {
		_import.importedMembership === null
	}
	
	def void _createChildren(IOutlineNode parentNode, MembershipImport _import) {
		super._createChildren(parentNode, _import)
		var importedMembership = _import.importedMembership;
		if (importedMembership !== null) {
			createNode(parentNode, importedMembership, 
				importedMembership._image, importedMembership._text, true
			)
		}
	}
	
	def boolean _isLeaf(NamespaceImport _import) {
		_import.importedNamespace === null && _import.ownedRelatedElement.isEmpty
	}
	
	def void _createChildren(IOutlineNode parentNode, NamespaceImport _import) {
		super._createChildren(parentNode, _import)
		var importedNamespace = _import.importedNamespace;
		if (importedNamespace !== null && importedNamespace.owningRelationship !== _import) {
			createNode(parentNode, importedNamespace, 
				importedNamespace._image, importedNamespace._text, true
			)
		}
	}
	
	def boolean _isLeaf(TypeFeaturing featuring) {
		featuring.featuringType === null
	}
	
	def void _createChildren(IOutlineNode parentNode, TypeFeaturing featuring) {
		val featureOfType = featuring.featureOfType
		if (featureOfType !== null && featureOfType !== featuring.eContainer) {
			createNode(parentNode, featureOfType, 
				featureOfType._image, featureOfType._text, 
				true
			)			
		}
		val featuringType = featuring.featuringType
		if (featuringType !== null) {
			createNode(parentNode, featuringType, 
				featuringType._image, featuringType._text, 
				featuringType.getOwningRelationship() !== featuring
			)
		}
	}
	
	def boolean _isLeaf(FeatureChaining chaining) {
		chaining.chainingFeature === null
	}
	
	def void _createChildren(IOutlineNode parentNode, FeatureChaining chaining) {
		if (chaining.chainingFeature !== null) {
			createNode(parentNode, chaining.chainingFeature, 
				chaining.chainingFeature._image, chaining.chainingFeature._text, 
				chaining.chainingFeature.owningRelationship != chaining
			)
			
		}
	}
	
	def boolean _isLeaf(Specialization specialization) {
		specialization.getGeneral === null
	}
	
	def void _createChildren(IOutlineNode parentNode, Specialization specialization) {
		val specific = specialization.specific
		if (specific !== null && specific !== specialization.eContainer) {
			createNode(parentNode, specific, specific._image, specific._text,  
				specific.owningRelationship !== specialization)	
		}
		val general = specialization.general
		if (general !== null) {
			createNode(parentNode, general, general._image, general._text, 
				general.owningRelationship !== specialization)
		}
	}
	
	def boolean _isLeaf(Conjugation conjugation) {
		conjugation.originalType === null
	}
	
	def void _createChildren(IOutlineNode parentNode, Conjugation conjugation) {
		val conjugatedType = conjugation.conjugatedType
		if (conjugatedType !== null && conjugatedType !== conjugation.eContainer) {
			createNode(parentNode, conjugatedType, 
				conjugatedType._image, conjugatedType._text, 
				!(conjugatedType instanceof Feature) || (conjugatedType as Feature).ownedFeatureChaining.empty
			)			
		}
		val originalType = conjugation.originalType
		if (originalType !== null) {
			createNode(parentNode, originalType, 
				_image(originalType), originalType._text, 
				!(originalType instanceof Feature) || (originalType as Feature).ownedFeatureChaining.empty
			)
		}
	}
	
	def boolean _isLeaf(Disjoining disjoining) {
		disjoining.disjoiningType === null
	}
	
	def void _createChildren(IOutlineNode parentNode, Disjoining disjoining) {
		val typeDisjoined = disjoining.typeDisjoined
		if (typeDisjoined !== null && typeDisjoined !== disjoining.eContainer) {
			createNode(parentNode, typeDisjoined, typeDisjoined._image, typeDisjoined._text,  
				!(typeDisjoined instanceof Feature) || (typeDisjoined as Feature).ownedFeatureChaining.empty)			
		}
		val disjoiningType = disjoining.disjoiningType
		if (disjoiningType !== null) {
			createNode(parentNode, disjoiningType, disjoiningType._image, disjoiningType._text, 
				!(disjoiningType instanceof Feature) || (disjoiningType as Feature).ownedFeatureChaining.empty)
		}
	}
	
	def boolean _isLeaf(FeatureInverting inverting) {
		inverting.invertingFeature === null
	}
	
	def void _createChildren(IOutlineNode parentNode, FeatureInverting inverting) {
		val featureInverted = inverting.featureInverted
		if (featureInverted !== null && featureInverted !== inverting.eContainer) {
			createNode(parentNode, featureInverted, featureInverted._image, featureInverted._text,  
				featureInverted.ownedFeatureChaining.empty)			
		}
		val invertingFeature = inverting.invertingFeature
		if (invertingFeature !== null) {
			createNode(parentNode, invertingFeature, invertingFeature._image, invertingFeature._text, 
				invertingFeature.ownedFeatureChaining.empty)
		}
	}
	
	def _isLeaf(Type type) {
	    _isLeaf(type as Namespace) && TypeUtil.isImplicitGeneralTypesEmpty(type) 	
	}
	
	def void _createChildren(IOutlineNode parentNode, Type type) {		
		//ImplicitFieldAdapter.getOrCreateAdapter(type).
		createImplicitGeneralizationNodes(parentNode, type)
		if (type instanceof Feature) {
			createImplicitTypeFeaturingNodes(parentNode, type)
		}
		_createChildren(parentNode, type as Namespace)
		createImplicitBindingConnectorNodes(parentNode, type)
	}
	
	def createImplicitGeneralizationNodes(IOutlineNode parentNode, Type type) {
		TypeUtil.forEachImplicitGeneralTypeOf(type, [eClass, generalType |
			/*
			 * TODO here image dispatcher should be called with a type that
			 * returns that appropriate icon for generalizations, but there
			 * are no such icons added yet; in the future, the generalType
			 * reference might return an unexpected icon if at a later point
			 * type-specific icons are added.
			 */
			val implicitNode = new ImplicitNode(parentNode, 
				imageDispatcher.invoke(generalType), eClass
			)
			
			// Traversal does not know about the new node, children have to be created here
			if (generalType !== null) {
				createNode(implicitNode, generalType, 
					generalType._image, textDispatcher.invoke(generalType).toString(), 
					!(generalType instanceof Feature) || (generalType as Feature).ownedFeatureChaining.isEmpty
				)
			}
		])
	}
	
	def createImplicitTypeFeaturingNodes(IOutlineNode parentNode, Feature feature) {
		FeatureUtil.forEachImplicitFeaturingTypeOf(feature, [featuringType |
			/*
			 * TODO here image dispatcher should be called with a type that
			 * returns that appropriate icon for type featuring, but there
			 * are no such icons added yet.
			 */
			val implicitNode = new ImplicitNode(parentNode, 
				imageDispatcher.invoke(featuringType), SysMLPackage.Literals.TYPE_FEATURING
			)
			// Traversal does not know about the new node, children have to be created here
			if (featuringType !== null) {
				createNode(implicitNode, featuringType, 
					featuringType._image, featuringType._text, 
					featuringType.owningRelationship !== null || featuringType instanceof BindingConnector
				)
			}
		])
	}
	
	def createImplicitBindingConnectorNodes(IOutlineNode parentNode, Type type) {
		TypeUtil.forEachImplicitBindingConnectorOf(type, [connector, eClass |
			/*
			 * TODO here image dispatcher should be called with a type that
			 * returns that appropriate icon for membership, but there
			 * are no such icons added yet.
			 */
			val implicitNode = new ImplicitNode(parentNode, 
				imageDispatcher.invoke(connector), eClass)
			implicitNode.createNode(connector, imageDispatcher.invoke(connector), connector.eClass.getName, false)
		])
	}
	
	def _isLeaf(Association association) {
		false
	}
	
	def _createChildren(IOutlineNode parentNode, Association association) {
		createRelatedElements(parentNode, association)
		_createChildren(parentNode, association as Type)
	}

	def _isLeaf(Connector connector) {
		false
	}
	
	def _createChildren(IOutlineNode parentNode, Connector connector) {
		createRelatedElements(parentNode, connector)
		_createChildren(parentNode, connector as Type)
	}

	def void _createChildren(IOutlineNode parentNode, OperatorExpression expression) {
		createImplicitGeneralizationNodes(parentNode, expression)
		createImplicitTypeFeaturingNodes(parentNode, expression)
		for (Relationship relationship : expression.ownedRelationship) {
			createNode(parentNode, relationship, 
				_image(relationship), 
				if (relationship instanceof Membership) (relationship as Membership)._text 
				else relationship._text, 
				false
			);
		}
		createImplicitBindingConnectorNodes(parentNode, expression)
	}
	
	override _createNode(DocumentRootNode parentNode, EObject modelElement) {
		var text = textDispatcher.invoke(modelElement);
		if (text === null) {
			text = modelElement.eResource().getURI().trimFileExtension().lastSegment();
		}
		createNode(parentNode, modelElement, imageDispatcher.invoke(modelElement), text.toString,
				isLeafDispatcher.invoke(modelElement));
	}
	
	override _createNode(IOutlineNode parentNode, EObject modelElement) {
		var Object text = textDispatcher.invoke(modelElement);
		val isLeaf = isLeafDispatcher.invoke(modelElement);
		if (text === null && isLeaf)
			return;
		val Image image = imageDispatcher.invoke(modelElement);
		createNode(parentNode, modelElement, image, text.toString, isLeaf);
	}
	
	protected def AbstractOutlineNode createNode(IOutlineNode parentNode, EObject modelElement, Image image, String text,
			boolean isLeaf) {
		if (modelElement.eResource !== null) {
			super.createEObjectNode(parentNode, modelElement, image, text, isLeaf)
		} else {
			val node = new ImplicitNode(parentNode, image, text)
			if (modelElement instanceof Element) {
				ElementUtil.transform(modelElement);
			}
			if (!isLeaf) {
				createChildrenDispatcher.invoke(node, modelElement)
			}
			node
		}
	}
	
}
