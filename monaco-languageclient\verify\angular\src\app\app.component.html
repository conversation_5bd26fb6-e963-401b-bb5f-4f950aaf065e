<h2>Angular Monaco Editor demo with saving code to a mock HTTP server </h2>
<div class="editor-container">
    <monaco-angular-wrapper
        [wrapperConfig]="wrapperConfig()"
        [monacoEditorId]="editorId"
        [editorInlineStyle]="editorInlineStyle()"
        (onTextChanged)="onTextChanged($event)"
    ></monaco-angular-wrapper>

    <button class="save-button" (click)="save()">Save</button>
</div>
