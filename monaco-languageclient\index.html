<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>monaco-languageclient Examples</title>
    <link rel="stylesheet" href="packages/examples/style.css">
</head>

<body>
    <div style="padding: 5px">
        <h2>Examples</h2>

        Please run <b><code>npm run build</code></b> if you haven't done it already.

        <h2>Monaco Languaclient</h2>

        <h3>JSON</h3>

        Please execute <b><code>npm run start:example:server:json</code></b> beforehand:<br>
        <a href="./packages/examples/bare.html">JSON Language Client & Language Server (Web Socket)</a>
        <br>

        <h2>Monaco Editor Wrapper</h2>

        <h3>JSON</h3>

        Please execute <b><code>npm run start:example:server:json</code></b> beforehand:<br>
        <a href="./packages/examples/json.html">JSON Language Client & Language Server (Web Socket)</a>
        <br>
        <a href="./packages/examples/browser.html">Language Client Pure Browser Example</a>
        <br>

        <h3>Langium</h3>

        Langium Grammar DSL: <a href="./packages/examples/langium_extended.html">Extended Mode</a> - <a href="./packages/examples/langium_classic.html">Classic Mode</a>
        <br>
        <a href="./packages/examples/statemachine.html">Langium Statemachine Client & Language Server (Worker)</a>
        <br>
        Localizations:
        <a href="./packages/examples/statemachine.html?locale=cs">cs</a> -
        <a href="./packages/examples/statemachine.html?locale=de">de</a> -
        <a href="./packages/examples/statemachine.html?locale=es">es</a> -
        <a href="./packages/examples/statemachine.html?locale=fr">fr</a> -
        <a href="./packages/examples/statemachine.html?locale=it">it</a> -
        <a href="./packages/examples/statemachine.html?locale=ja">ja</a> -
        <a href="./packages/examples/statemachine.html?locale=ko">ko</a> -
        <a href="./packages/examples/statemachine.html?locale=pl">pl</a> -
        <a href="./packages/examples/statemachine.html?locale=pt-br">pt-br</a> -
        <a href="./packages/examples/statemachine.html?locale=qps-ploc">qps-ploc</a> -
        <a href="./packages/examples/statemachine.html?locale=ru">ru</a> -
        <a href="./packages/examples/statemachine.html?locale=tr">tr</a> -
        <a href="./packages/examples/statemachine.html?locale=zh-hans">zh-hans</a> -
        <a href="./packages/examples/statemachine.html?locale=zh-hant">zh-hant</a>
        <br>

        <h3>Python</h3>
        Please execute <b><code>npm run start:example:server:python</code></b> beforehand.<br>
        Debugger requires docker. Please execute <b><code>docker compose -f ./packages/examples/resources/debugger/docker-compose.yml up -d</code></b> beforehand.<br>
        <a href="./packages/examples/python.html">Python Language Client & Pyright Language Server (Web Socket)</a><br>

        <h3>Java / Eclipse JDS LS</h3>
        Requires docker. Please execute <b><code>docker compose -f ./packages/examples/resources/eclipse.jdt.ls/docker-compose.yml up -d</code></b> beforehand:<br>
        <a href="./packages/examples/eclipse.jdt.ls.html">Java Language Client & Language Server (Web Socket)</a>
        <br>

        <h3>Groovy</h3>
        Requires docker. Please execute <b><code>docker compose -f ./packages/examples/resources/groovy/docker-compose.yml up -d</code></b> beforehand:<br>
        <a href="./packages/examples/groovy.html">Groovy Language Client & Language Server (Web Socket)</a>
        <br>

        <h3>Cpp / Clangd</h3>
        <a href="./packages/examples/clangd.html">Cpp Language Client & Clangd Language Server (Worker/Wasm)</a>
        <br>

        <h3>Multiple Languageclients</h3>
        Please execute <b><code>npm run start:example:server:python</code></b> and <b><code>npm run start:example:server:json</code></b> beforehand:<br>
        <a href="./packages/examples/two_langauge_clients.html">Json & Python Languageclients & Language Server (Web Socket)</a>
        <br>

        <h3>Application Playground</h3>
        <a href="./packages/examples/appPlayground.html">Application Playground</a>

        <h3>TypeScript</h3>
        <a href="./packages/examples/tsExtHost.html">TypeScript Extension Host Worker</a>
        <br>

        <h2>Monaco Editor React</h2>
        Please execute <b><code>npm run start:example:server:python</code></b> beforehand.<br>
        Debugger requires docker. Please execute <b><code>docker compose -f ./packages/examples/resources/debugger/docker-compose.yml up -d</code></b> beforehand.<br>
        <a href="./packages/examples/react_appPlayground.html">React: Application Playground</a>
        <br>
        <a href="./packages/examples/react_statemachine.html">React: Langium Statemachine Language Client & Language Server (Worker)</a>
        <br><br>
        Please execute  <b><code>npm run start:example:server:python</code></b> beforehand:<br>
        <a href="./packages/examples/react_python.html">React: Python Language Client & Language Server (Web Socket)</a>
        <br>

        <h2>Verification</h2>
        <h3>Angular</h2>
        Please start  <b><code>cd verify/angular && npm run verify</code></b> beforehand:<br>
        <a href="http://localhost:4200">JSON Language Client & Language Server (Web Socket) (Angular Dev Server)</a>
        <h3>Next.js</h3>
        Please start <b><code>cd verify/next && npm run verify</code></b> beforehand:<br>
        <a href="http://localhost:8081">JSON Language Client & Language Server (Web Socket) (Next.js Dev Server)</a>
        <h3>Webpack</h3>
        Please start <b><code>cd verify/webpack && npm run verify</code></b> beforehand:<br>
        <a href="http://localhost:8082">JSON Language Client & Language Server (Web Socket) (webpack build)</a>
        <h3>Vite</h3>
        Please start <b><code>cd verify/vite && npm run verify</code></b> beforehand:<br>
        <a href="http://localhost:8083">JSON Language Client & Language Server (Web Socket) (vite build)</a>
        </div>
</body>

</html>
