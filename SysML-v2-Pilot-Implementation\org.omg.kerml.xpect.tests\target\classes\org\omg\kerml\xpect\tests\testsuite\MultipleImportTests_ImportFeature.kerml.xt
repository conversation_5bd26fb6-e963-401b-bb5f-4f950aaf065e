//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
		File {from ="/src/DependencyOuterPackage.kerml"}
		File {from ="/src/DependencyMultipleMembership.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
				File {from ="/src/DependencyOuterPackage.kerml"}
				File {from ="/src/DependencyMultipleMembership.kerml"}
			}
		}
	}
END_SETUP 
*/
// XPECT noErrors ---> ""
package test{
	//* XPECT scope at OuterPackage2::C::b ---
		D, D.self, D.self.that, D.try, D.try.a1, D.try.self, D.try.that,
		D.try.that.self, OuterPackage, OuterPackage.A, OuterPackage.A.a1, OuterPackage.B,
		OuterPackage.B.b, OuterPackage.B.b.a1, OuterPackage2, OuterPackage2.B, OuterPackage2.B.b,
		OuterPackage2.B.b.a1, OuterPackage2.C, OuterPackage2.C.b, OuterPackage2.C.b.a1, OuterPackage2.C.c,
		OuterPackage2.C.c.self, OuterPackage2.C.c.that, OuterPackage2.C.c.that.self, b, b.a1, test, test.D,
		test.D.self, test.D.self.that, test.D.try, test.D.try.a1, test.D.try.self, test.D.try.that,
		test.D.try.that.self, test.b, test.b.a1
	--- */
	public import OuterPackage2::C::b;
	classifier D{
		//XPECT linkedName at b --> OuterPackage.B.b
		//* XPECT scope at b ---
		D, D.self, D.self.that, D.try, D.try.a1, D.try.self, D.try.that,
		D.try.that.self, OuterPackage.A, OuterPackage.A.a1, OuterPackage.B, OuterPackage.B.b,
		OuterPackage.B.b.a1, OuterPackage2.B, OuterPackage2.B.b, OuterPackage2.B.b.a1, OuterPackage2.C,
		OuterPackage2.C.b, OuterPackage2.C.b.a1, OuterPackage2.C.c, OuterPackage2.C.c.self,
		OuterPackage2.C.c.that, OuterPackage2.C.c.that.self, b, b.a1, self, self.that, test.D, test.D.self,
		test.D.self.that, test.D.try, test.D.try.a1, test.D.try.self, test.D.try.that,
		test.D.try.that.self, test.b, test.b.a1, try, try.a1, try.self, try.that, try.that.self
		--- */
		feature try : b;
	}
}
//* Note: difference between two scopes are
   OuterPackage, OuterPackage2, test, Base --------------- scope at OuterPacakge2::C::b
   try, try.a1 --------------------------------------------scope at b
 */
