grammar SysML

type VisibilityKind = VisibilityKind_private | VisibilityKind_protected | VisibilityKind_public;
type VisibilityKind_private = 'private';
type VisibilityKind_protected = 'protected';
type VisibilityKind_public = 'public';

type FeatureDirectionKind = FeatureDirectionKind_in | FeatureDirectionKind_inout | FeatureDirectionKind_out;
type FeatureDirectionKind_in = 'in';
type FeatureDirectionKind_inout = 'inout';
type FeatureDirectionKind_out = 'out';

type PortionKind = PortionKind_timeslice | PortionKind_snapshot;
type PortionKind_timeslice = 'timeslice';
type PortionKind_snapshot = 'snapshot';

type TriggerKind = TriggerKind_when | TriggerKind_at | TriggerKind_after;
type TriggerKind_when = 'when';
type TriggerKind_at = 'at';
type TriggerKind_after = 'after';

type StateSubactionKind = StateSubactionKind_entry | StateSubactionKind_do | StateSubactionKind_exit;
type StateSubactionKind_entry = 'entry';
type StateSubactionKind_do = 'do';
type StateSubactionKind_exit = 'exit';

type TransitionFeatureKind = TransitionFeatureKind_trigger | TransitionFeatureKind_guard | TransitionFeatureKind_effect;
type TransitionFeatureKind_trigger = 'trigger';
type TransitionFeatureKind_guard = 'guard';
type TransitionFeatureKind_effect = 'effect';

type RequirementConstraintKind = RequirementConstraintKind_assumption | RequirementConstraintKind_requirement;
type RequirementConstraintKind_assumption = 'assume';
type RequirementConstraintKind_requirement = 'require';
type RequirementConstraintKind_verify = 'verify';

interface Element {
	ownedRelationship: Relationship[]
    owningRelationship?: @Relationship
    declaredShortName?: string
    declaredName?: string
}

interface Namespace extends Element {
}

interface Relationship extends Element {
	owningRelatedElement?: @Element
	ownedRelatedElement?: Element[]
}

interface Dependency extends Relationship {
	client: @Element[]
	supplier: @Element[]
}

interface Annotation extends Relationship {
	annotatedElement?: @Element
}

interface Membership extends Relationship {
    memberShortName?: string
    memberName?: string
    memberElement?: @Element
    visibility?: VisibilityKind
}

interface OwningMembership extends Membership {
}

interface AnnotatingElement extends Element {
}

interface Comment extends AnnotatingElement {
	locale?: string
	body?: string
}

interface Documentation extends Comment {
}

interface TextualRepresentation extends AnnotatingElement {
	language: string
	body: string
}

interface Type extends Namespace {
	isAbstract: boolean
}

interface Classifier extends Type {
}

interface Definition extends Classifier {
	isVariation?: boolean
}

interface Class extends Classifier {
}

interface Structure extends Class {
}

interface OccurrenceDefinition extends Definition, Class {
	isIndividual?: boolean
}

interface ItemDefinition extends OccurrenceDefinition, Structure {
}

interface Metaclass extends Structure {
}

interface MetadataDefinition extends ItemDefinition, Metaclass {
}

interface Usage extends Feature {
	isReference?: boolean
}

interface OccurrenceUsage extends Usage {
	isIndividual?: boolean
	portionKind?: PortionKind
}

interface ItemUsage extends OccurrenceUsage {
}

interface MetadataFeature extends Feature, AnnotatingElement {
}

interface MetadataUsage extends ItemUsage, MetadataFeature {
}

interface Feature extends Type {
    isNonunique?: boolean
    isOrdered?: boolean
	isDerived?: boolean
    isEnd?: boolean
    isPortion?: boolean
	isVariation?: boolean
	isConstant?: boolean
    isComposite?: boolean
    direction?: FeatureDirectionKind
}

interface Specialization extends Relationship {
	
}

interface FeatureTyping extends Specialization {
	^type: @Type
}

interface FeatureMembership extends OwningMembership {
}

interface ReferenceUsage extends Usage {
}

interface Package extends Namespace {
}

interface LibraryPackage extends Package {
	isStandard: boolean
}

interface ElementFilterMembership extends OwningMembership {
}

interface Import extends Relationship {
    visibility?: VisibilityKind
	isImportAll?: boolean
    isRecursive?: boolean
}

interface MembershipImport extends Import {
	importedMembership?: @Membership
}

interface NamespaceImport extends Import {
	importedNamespace?: @Namespace
}

interface Subclassification extends Specialization {
	superclassifier?: @Classifier
	subclassifier?: @Classifier
}

interface Subsetting extends Specialization {
	subsettedFeature?: @Feature
}

interface ReferenceSubsetting extends Subsetting {
	referencedFeature?: @Feature
}

interface CrossSubsetting extends Subsetting {
	crossedFeature?: @Feature
}

interface Redefinition extends Subsetting {
	redefiningFeature?: @Feature
	redefinedFeature?: @Feature
}

interface Multiplicity extends Feature {
}

interface MultiplicityRange extends Multiplicity {
}

interface VariantMembership extends OwningMembership {
}

interface FeatureValue extends OwningMembership {
	isInitial?: boolean
	isDefault?: boolean
}

interface DataType extends Classifier {
}

interface AttributeDefinition extends Definition, DataType {
}

interface AttributeUsage extends Usage {
}

interface EnumerationDefinition extends AttributeDefinition {
}

interface EnumerationUsage extends AttributeUsage {
}

interface EventOccurrenceUsage extends OccurrenceUsage {
}

interface Connector extends Feature, Relationship {
}

interface ConnectorAsUsage extends Usage, Connector {
}

interface SuccessionAsUsage extends ConnectorAsUsage, Succession {
}

interface Succession extends Connector {
}

interface EndFeatureMembership extends FeatureMembership {
}

interface PartDefinition extends ItemDefinition {
}

interface PartUsage extends ItemUsage {
}

interface PortDefinition extends OccurrenceDefinition, Structure {
}

interface ConjugatedPortDefinition extends PortDefinition {
}

interface Conjugation extends Relationship {
	
}

interface PortConjugation extends Conjugation {
	
}

interface ConjugatedPortTyping extends FeatureTyping {
	conjugatedPortDefinition?: @ConjugatedPortDefinition
}

interface PortUsage extends OccurrenceUsage {
}

interface BindingConnector extends Connector {
}

interface BindingConnectorAsUsage extends ConnectorAsUsage, BindingConnector {
}

interface Association extends Classifier, Relationship {
}

interface AssociationStructure extends Association, Structure {
}

interface ConnectionDefinition extends PartDefinition, AssociationStructure {
}

interface ConnectionUsage extends ConnectorAsUsage, PartUsage {
}

interface InterfaceDefinition extends ConnectionDefinition {
}

interface InterfaceUsage extends ConnectionUsage {
}

interface AllocationDefinition extends ConnectionDefinition {
}

interface AllocationUsage extends ConnectionUsage {
}

interface Behavior extends Class {
}

interface ActionDefinition extends OccurrenceDefinition, Behavior {
}

interface Interaction extends Association, Behavior {
}

interface FlowDefinition extends ActionDefinition, Interaction {
}

interface Step extends Feature {
}

interface Flow extends Connector, Step {
}

interface ActionUsage extends OccurrenceUsage, Step {
}

interface FlowUsage extends ConnectorAsUsage, ActionUsage, Flow {
}

interface ParameterMembership extends FeatureMembership {
}

interface SuccessionFlow extends Flow, Succession {
}

interface SuccessionFlowUsage extends FlowUsage, SuccessionFlow {
}

interface PayloadFeature extends Feature {
}

interface FlowEnd extends Feature {
}

interface PerformActionUsage extends ActionUsage, EventOccurrenceUsage {
}

interface AcceptActionUsage extends ActionUsage {
}

interface Expression extends Step {
}

interface InstantiationExpression extends Expression {
}

interface InvocationExpression extends InstantiationExpression {
}

interface TriggerInvocationExpression extends InvocationExpression {
	kind?: TriggerKind
}

interface SendActionUsage extends ActionUsage {
}

interface AssignmentActionUsage extends ActionUsage {
}

interface OperatorExpression extends InvocationExpression {
	operator?: string
    operand?: Expression[]
}

interface FeatureChainExpression extends OperatorExpression {
}


interface CollectExpression extends OperatorExpression {
}

interface SelectExpression extends OperatorExpression {
}

interface IfActionUsage extends ActionUsage {
}

interface LoopActionUsage extends ActionUsage {
}

interface WhileLoopActionUsage extends LoopActionUsage {
}

interface ForLoopActionUsage extends LoopActionUsage {
}

interface TerminateActionUsage extends ActionUsage {
}

interface ControlNode extends ActionUsage {
}

interface MergeNode extends ControlNode {
}

interface DecisionNode extends ControlNode {
}

interface JoinNode extends ControlNode {
}

interface ForkNode extends ControlNode {
}

interface TransitionUsage extends ActionUsage {
}

interface StateDefinition extends ActionDefinition {
	isParallel?: boolean
}

interface StateSubactionMembership extends FeatureMembership {
	kind?: StateSubactionKind
}

interface StateUsage extends ActionUsage {
	isParallel?: boolean
}

interface ExhibitStateUsage extends StateUsage, PerformActionUsage {
}

interface TransitionFeatureMembership extends FeatureMembership {
	kind?: TransitionFeatureKind
}

interface SysMLFunction extends Behavior {
}

interface CalculationDefinition extends ActionDefinition, SysMLFunction {
}

interface ReturnParameterMembership extends ParameterMembership {
}

interface ResultExpressionMembership extends FeatureMembership {
}

interface CalculationUsage extends ActionUsage, Expression {
}

interface Predicate extends SysMLFunction {
}

interface ConstraintDefinition extends OccurrenceDefinition, Predicate {
}

interface BooleanExpression extends Expression {
}

interface MetadataAccessExpression extends Expression {

}

interface ConstraintUsage extends OccurrenceUsage, BooleanExpression {
}

interface Invariant extends BooleanExpression {
	isNegated?: boolean
}

interface AssertConstraintUsage extends ConstraintUsage, Invariant {
}

interface RequirementDefinition extends ConstraintDefinition {
	
}

interface SubjectMembership extends ParameterMembership {
}

interface RequirementConstraintMembership extends FeatureMembership {
	kind?: RequirementConstraintKind
}

interface FramedConcernMembership extends RequirementConstraintMembership {
}

interface RequirementUsage extends ConstraintUsage {
	
}

interface ConcernUsage extends RequirementUsage {
}

interface ActorMembership extends ParameterMembership {
}

interface StakeholderMembership extends ParameterMembership {
}

interface SatisfyRequirementUsage extends RequirementUsage, AssertConstraintUsage {
}

interface FeatureReferenceExpression extends Expression {
}

interface ConcernDefinition extends RequirementDefinition {
}

interface CaseDefinition extends CalculationDefinition {
}

interface ObjectiveMembership extends FeatureMembership {
}

interface CaseUsage extends CalculationUsage {
}

interface AnalysisCaseDefinition extends CaseDefinition {
}

interface AnalysisCaseUsage extends CaseUsage {
}

interface VerificationCaseDefinition extends CaseDefinition {
}

interface VerificationCaseUsage extends CaseUsage {
}

interface RequirementVerificationMembership extends RequirementConstraintMembership {
}

interface UseCaseDefinition extends CaseDefinition {
}

interface UseCaseUsage extends CaseUsage {
}

interface IncludeUseCaseUsage extends UseCaseUsage, PerformActionUsage {
}

interface ViewDefinition extends PartDefinition {
}

interface ViewRenderingMembership extends FeatureMembership {
}

interface RenderingUsage extends PartUsage {
}

interface ViewUsage extends PartUsage {
}

interface Expose extends Import {
}

interface MembershipExpose extends MembershipImport, Expose {
}

interface NamespaceExpose extends NamespaceImport, Expose {
}

interface ViewpointDefinition extends RequirementDefinition {
}

interface ViewpointUsage extends RequirementUsage {
}

interface RenderingDefinition extends PartDefinition {
}

interface FeatureChaining extends Relationship {
    chainingFeature?: string
}

entry RootNamespace returns Namespace:
    {Namespace} PackageBodyElement*  
;

fragment Identification returns Element:
    '<' declaredShortName=Name  '>' (declaredName=Name )?  | declaredName=Name  
;

fragment RelationshipBody returns Relationship:
    ';' | '{' (ownedRelationship+=OwnedAnnotation )* '}'  
;

Dependency returns Dependency:
    (ownedRelationship+=PrefixMetadataAnnotation )* 
    'dependency' (Identification? 'from' )? 
    client+=[Element:QualifiedName ] (',' client+=[Element:QualifiedName ] )* 
    'to' supplier+=[Element:QualifiedName ] (',' supplier+=[Element:QualifiedName ] )* RelationshipBody  
;

Annotation returns Annotation:
    annotatedElement=[Element:QualifiedName ] 
;

OwnedAnnotation returns Annotation:
    ownedRelatedElement+=AnnotatingElement  
;

AnnotatingMember returns OwningMembership:
    ownedRelatedElement+=AnnotatingElement  
;

AnnotatingElement returns AnnotatingElement:
    Comment | Documentation | TextualRepresentation | MetadataUsage 
;

Comment returns Comment:
    ('comment' Identification? ('about' ownedRelationship+=Annotation  (',' ownedRelationship+=Annotation  )* )? )? 
    ('locale' locale=STRING_VALUE  )? 
    body=REGULAR_COMMENT   
;

Documentation returns Documentation:
    'doc' Identification? ('locale' locale=STRING_VALUE  )? body=REGULAR_COMMENT   
;

TextualRepresentation returns TextualRepresentation:
    ('rep' Identification? )? 'language' language=STRING_VALUE  body=REGULAR_COMMENT   
;

MetadataKeyword returns string:
    'metadata' 
;

MetadataDefKeyword returns string:
    MetadataKeyword 'def'  
;

MetadataUsageKeyword returns string:
    MetadataKeyword | '@' 
;

MetadataDefinition returns MetadataDefinition:
    (isAbstract?='abstract' )? DefinitionExtensionKeyword* MetadataDefKeyword Definition  
;

PrefixMetadataAnnotation returns Annotation:
    '#' ownedRelatedElement+=PrefixMetadataUsage   
;

PrefixMetadataMember returns OwningMembership:
    '#' ownedRelatedElement+=PrefixMetadataUsage   
;

PrefixMetadataUsage returns MetadataUsage:
    ownedRelationship+=MetadataTyping  
;

MetadataUsage returns MetadataUsage:
    UsageExtensionKeyword* MetadataUsageKeyword MetadataUsageDeclaration 
    ('about' ownedRelationship+=Annotation  (',' ownedRelationship+=Annotation  )* )? 
    MetadataBody  
;

fragment MetadataUsageDeclaration returns MetadataUsage:
    (Identification? DefinedByKeyword )? ownedRelationship+=MetadataTyping   
;

MetadataTyping returns FeatureTyping:
    ^type=[Metaclass:QualifiedName ] 
;

fragment MetadataBody returns Usage:
    ';' 
    | 
    '{' 
        (
            ownedRelationship+=DefinitionMember  | 
            ownedRelationship+=MetadataBodyUsageMember  | 
            ownedRelationship+=AliasMember  | 
            ownedRelationship+=Import  
        )*
    '}'  
;

MetadataBodyUsageMember returns FeatureMembership:
    ownedRelatedElement+=MetadataBodyUsage  
;

MetadataBodyUsage returns ReferenceUsage:
    'ref'? (':>>' | 'redefines' )?ownedRelationship+=OwnedRedefinition  FeatureSpecializationPart? ValuePart? MetadataBody  
;

Package returns Package:
    (ownedRelationship+=PrefixMetadataMember )* PackageDeclaration PackageBody  
;

LibraryPackage returns LibraryPackage:
    (isStandard?='standard' )? 'library' (ownedRelationship+=PrefixMetadataMember )* PackageDeclaration PackageBody  
;

fragment PackageDeclaration returns Package:
    'package' Identification?  
;

fragment PackageBody returns Package:
    ';' 
    | 
    '{' 
        (
            ownedRelationship+=PackageMember  | 
            ownedRelationship+=ElementFilterMember  | 
            ownedRelationship+=AliasMember  | 
            ownedRelationship+=Import  
        )*
    '}'  
;

fragment PackageBodyElement returns Namespace:
    ownedRelationship+=PackageMember  | 
    ownedRelationship+=ElementFilterMember  | 
    ownedRelationship+=AliasMember  | 
    ownedRelationship+=Import  
;

// fragment MemberPrefix returns Membership:
//     (visibility=VisibilityIndicator )? 
//     // ('public' | 'private' | 'protected')
// ;

PackageMember returns OwningMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    (
        ownedRelatedElement+=DefinitionElement  | 
        ownedRelatedElement+=UsageElement  
    ) 
;

OwnedExpressionMember returns FeatureMembership:
    ownedRelatedElement+=OwnedExpression  
;

OwnedExpression returns Expression:
    ConditionalExpression 
;

OwnedExpressionReference returns FeatureReferenceExpression:
    ownedRelationship+=OwnedExpressionMember  
;

ConditionalExpression returns Expression:
    NullCoalescingExpression 
    | 
    {OperatorExpression} 
    operator=ConditionalOperator  
    operand+=NullCoalescingExpression  
    '?' operand+=OwnedExpressionReference  
    'else' operand+=OwnedExpressionReference   
;

ConditionalOperator returns string:
    'if' 
;

NullCoalescingExpression returns Expression:
    ImpliesExpression 
    (
        {OperatorExpression.operand+=current} 
        operator=NullCoalescingOperator  
        operand+=ImpliesExpressionReference  
    )*  
;

NullCoalescingOperator returns string:
    '??' 
;

ImpliesExpressionReference returns FeatureReferenceExpression:
    ownedRelationship+=ImpliesExpressionMember  
;

ImpliesExpressionMember returns FeatureMembership:
    ownedRelatedElement+=ImpliesExpression  
;

ImpliesExpression returns Expression:
    OrExpression 
    (
        {OperatorExpression.operand+=current} 
        operator=ImpliesOperator operand+=OrExpressionReference  
    )*  
;

ImpliesOperator returns string:
    'implies' 
;

OrExpressionReference returns FeatureReferenceExpression:
    ownedRelationship+=OrExpressionMember  
;

OrExpressionMember returns FeatureMembership:
    ownedRelatedElement+=OrExpression  
;

OrExpression returns Expression:
    XorExpression 
    (
        {OperatorExpression.operand+=current} 
        (
            operator=OrOperator  operand+=XorExpression | 
            operator=ConditionalOrOperator  operand+=XorExpressionReference   
        )
    )*  
;

OrOperator returns string:
    '|' 
;

ConditionalOrOperator returns string:
    'or' 
;

XorExpressionReference returns FeatureReferenceExpression:
    ownedRelationship+=XorExpressionMember  
;

XorExpressionMember returns FeatureMembership:
    ownedRelatedElement+=XorExpression  
;

XorExpression returns Expression:
    AndExpression ({OperatorExpression.operand+=current} operator=XorOperator  operand+=AndExpression  )*  
;

XorOperator returns string:
    'xor' 
;

AndExpression returns Expression:
    EqualityExpression 
    (
        {OperatorExpression.operand+=current} 
        (
            operator=AndOperator  
            operand+=EqualityExpression | 
            operator=ConditionalAndOperator  
            operand+=EqualityExpressionReference   
        )
    )*  
;

AndOperator returns string:
    '&' 
;

ConditionalAndOperator returns string:
    'and' 
;

EqualityExpressionReference returns FeatureReferenceExpression:
    ownedRelationship+=EqualityExpressionMember  
;

EqualityExpressionMember returns FeatureMembership:
    ownedRelatedElement+=EqualityExpression  
;

EqualityExpression returns Expression:
    ClassificationExpression 
    (
        {OperatorExpression.operand+=current} 
        operator=EqualityOperator  
        operand+=ClassificationExpression  
    )*  
;

EqualityOperator returns string:
    '==' | '!=' | '===' | '!==' 
;

ClassificationExpression returns Expression:
    RelationalExpression 
    (
        {OperatorExpression.operand+=current} 
        operator=ClassificationTestOperator  
        ownedRelationship+=TypeReferenceMember 
        | {OperatorExpression.operand+=current} 
        operator=CastOperator  ownedRelationship+=TypeResultMember   
    )? 
    | 
    {OperatorExpression} 
    operand+=SelfReferenceExpression  
    operator=ClassificationTestOperator  
    ownedRelationship+=TypeReferenceMember | 
    {OperatorExpression} 
    operand+=MetadataReference 
    operator=MetaClassificationTestOperator 
    ownedRelationship+=TypeReferenceMember  | 
    {OperatorExpression} 
    operand+=SelfReferenceExpression 
    operator=CastOperator 
    ownedRelationship+=TypeResultMember   | 
    {OperatorExpression} 
    operand+=MetadataReference  
    operator=MetaCastOperator  
    ownedRelationship+=TypeResultMember   
;

ClassificationTestOperator returns string:
    'hastype' | 'istype' | '@' 
;

MetaClassificationTestOperator returns string:
    '@@' 
;

CastOperator returns string:
    'as' 
;

MetaCastOperator returns string:
    'meta' 
;

MetadataReference returns MetadataAccessExpression:
    ownedRelationship+=ElementReferenceMember  
;

TypeReferenceMember returns ParameterMembership:
    ownedRelatedElement+=TypeReference  
;

TypeResultMember returns ReturnParameterMembership:
    ownedRelatedElement+=TypeReference  
;

TypeReference returns Feature:
    ownedRelationship+=ReferenceTyping  
;

ReferenceTyping returns FeatureTyping:
    ^type=[Type:QualifiedName ] 
;

SelfReferenceExpression returns FeatureReferenceExpression:
    ownedRelationship+=SelfReferenceMember  
;

SelfReferenceMember returns ReturnParameterMembership:
    ownedRelatedElement+=EmptyFeature  
;

EmptyFeature returns Feature:
    {Feature} 
;

RelationalExpression returns Expression:
    RangeExpression ({OperatorExpression.operand+=current} operator=RelationalOperator  operand+=RangeExpression  )*  
;

RelationalOperator returns string:
    '<' | '>' | '<=' | '>=' 
;

RangeExpression returns Expression:
    AdditiveExpression ({OperatorExpression.operand+=current} operator='..'  operand+=AdditiveExpression  )?  
;

AdditiveExpression returns Expression:
    MultiplicativeExpression ({OperatorExpression.operand+=current} operator=AdditiveOperator  operand+=MultiplicativeExpression  )*  
;

AdditiveOperator returns string:
    '+' | '-' 
;

MultiplicativeExpression returns Expression:
    ExponentiationExpression ({OperatorExpression.operand+=current} operator=MultiplicativeOperator  operand+=ExponentiationExpression  )*  
;

MultiplicativeOperator returns string:
    '*' | '/' | '%' 
;

ExponentiationExpression returns Expression:
    UnaryExpression ({OperatorExpression.operand+=current} operator=ExponentiationOperator  operand+=ExponentiationExpression  )?  
;

ExponentiationOperator returns string:
    '**' | '^' 
;

UnaryExpression returns Expression:
    {OperatorExpression} operator=UnaryOperator  operand+=ExtentExpression   | ExtentExpression 
;

UnaryOperator returns string:
    '+' | '-' | '~' | 'not' 
;

ExtentExpression returns Expression:
    {OperatorExpression} operator='all'  ownedRelationship+=TypeResultMember   | PrimaryExpression 
;

PrimaryExpression returns Expression:
    BaseExpression 
    ({FeatureChainExpression.operand+=current} '.' ownedRelationship+=FeatureChainMember  )? 
    (
        ( 
            {OperatorExpression.operand+=current} operator='['  operand+=SequenceExpression  ']'  | 
            {CollectExpression.operand+=current} '.' operand+=BodyExpression   | 
            {SelectExpression.operand+=current} '.?' operand+=BodyExpression   )
            ({FeatureChainExpression.operand+=current} '.' ownedRelationship+=FeatureChainMember  )? 
    )*  
;

FunctionReferenceExpression returns FeatureReferenceExpression:
    ownedRelationship+=FunctionReferenceMember  
;

FunctionReferenceMember returns FeatureMembership:
    ownedRelatedElement+=FunctionReference  
;

FunctionReference returns Expression:
    ownedRelationship+=ReferenceTyping  
;

FeatureChainMember returns Membership:
    memberElement=[Feature:QualifiedName ] | {OwningMembership} ownedRelatedElement+=OwnedFeatureChain   
;

OwnedFeatureChain returns Feature:
    FeatureChain 
;

BaseExpression returns Expression:
    NullExpression | LiteralExpression | FeatureReferenceExpression | 
    MetadataAccessExpression | InvocationExpression | 
    ConstructorExpression | BodyExpression | '(' SequenceExpression ')'  
;

BodyExpression returns FeatureReferenceExpression:
    ownedRelationship+=ExpressionBodyMember  
;

ExpressionBodyMember returns FeatureMembership:
    ownedRelatedElement+=KerExpressionBody  
;

KerExpressionBody returns Expression:
    '{' (ownedRelationship+=BodyParameterMember  ';' )* 
    ownedRelationship+=KerResultExpressionMember  '}'  
;

KerResultExpressionMember returns ResultExpressionMembership:
    ownedRelatedElement+=OwnedExpression  
;

BodyParameterMember returns ParameterMembership:
    'in' ownedRelatedElement+=BodyParameter   
;

BodyParameter returns Feature:
    declaredName=Name  
;

SequenceExpression returns Expression:
    OwnedExpression (',' | {OperatorExpression.operand+=current} operator=','  operand+=SequenceExpression   )? 
;

FeatureReferenceExpression returns FeatureReferenceExpression:
    ownedRelationship+=FeatureReferenceMember  
;

FeatureReferenceMember returns Membership:
    memberElement=[Feature:QualifiedName ] 
;

MetadataAccessExpression returns MetadataAccessExpression:
    ownedRelationship+=ElementReferenceMember  '.' 'metadata'  
;

ElementReferenceMember returns Membership:
    memberElement=[Element:QualifiedName ] 
;

InvocationExpression returns InvocationExpression:
    ownedRelationship+=InstantiatedTypeMember  ArgumentList  
;

ConstructorExpression returns Expression:
    'new' ownedRelationship+=InstantiatedTypeMember  ownedRelationship+=ConstructorResultMember   
;

ConstructorResultMember returns ReturnParameterMembership:
    ownedRelatedElement+=ConstructorResult  
;

ConstructorResult returns Feature:
    ArgumentList 
;

InstantiatedTypeMember returns Membership:
    memberElement=[Type:QualifiedName ] | {OwningMembership} ownedRelatedElement+=OwnedFeatureChain   
;

fragment FeatureChain returns Feature:
    ownedRelationship+=OwnedFeatureChaining  ('.' ownedRelationship+=OwnedFeatureChaining  )+  
;

OwnedFeatureChaining returns FeatureChaining:
    chainingFeature=QualifiedName
;

fragment ArgumentList returns Feature:
    '(' (PositionalArgumentList | NamedArgumentList )?')'  
;

fragment PositionalArgumentList returns Feature:
    ownedRelationship+=ArgumentMember  (',' ownedRelationship+=ArgumentMember  )*  
;

ArgumentMember returns ParameterMembership:
    ownedRelatedElement+=Argument  
;

Argument returns Feature:
    ownedRelationship+=ArgumentValue  
;

fragment NamedArgumentList returns Feature:
    ownedRelationship+=NamedArgumentMember  (',' ownedRelationship+=NamedArgumentMember  )*  
;

NamedArgumentMember returns ParameterMembership:
    ownedRelatedElement+=NamedArgument  
;

NamedArgument returns Feature:
    ownedRelationship+=ParameterRedefinition  '=' ownedRelationship+=ArgumentValue   
;

ParameterRedefinition returns Redefinition:
    redefinedFeature=[Feature:QualifiedName ] 
;

ArgumentValue returns FeatureValue:
    ownedRelatedElement+=OwnedExpression  
;

NullExpression returns string:
    'null' 
;

LiteralExpression returns Expression:
    LiteralBoolean | LiteralString | LiteralInteger | LiteralReal
;

LiteralBoolean:
    value=BooleanValue  
;

BooleanValue returns boolean:
    'true' | 'false' 
;

LiteralString:
    value=STRING_VALUE  
;

LiteralInteger:
    value=DECIMAL_VALUE  
;

LiteralReal:
    value=RealValue  
;

RealValue returns number:
    DECIMAL_VALUE? '.' (DECIMAL_VALUE | EXP_VALUE ) | EXP_VALUE 
;

ElementFilterMember returns ElementFilterMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'filter' ownedRelatedElement+=OwnedExpression  ';'  
;

AliasMember returns Membership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'alias' 
    ('<' memberShortName=Name  '>' )? (memberName=Name )? 
    'for' memberElement=[Element:QualifiedName ] RelationshipBody  
;

fragment ImportPrefix returns Import:
    // visibility=VisibilityIndicator  
    ('public' | 'private' | 'protected')
    'import' (isImportAll?='all' )?  
;

Import returns Import:
    (MembershipImport | NamespaceImport )RelationshipBody  
;

MembershipImport returns MembershipImport:
    ImportPrefix ImportedMembership  
;

fragment ImportedMembership returns MembershipImport:
    importedMembership=[Membership:QualifiedName ] ('::' isRecursive?='**'  )?  
;

NamespaceImport returns NamespaceImport:
    ImportPrefix (ImportedNamespace | ownedRelatedElement+=FilterPackage  ) 
;

fragment ImportedNamespace returns NamespaceImport:
    importedNamespace=[Namespace:QualifiedName ] '::' '*' ('::' isRecursive?='**'  )?  
;

FilterPackage returns Package:
    ownedRelationship+=FilterPackageImport  (ownedRelationship+=FilterPackageMember )+  
;

FilterPackageImport returns Import:
    FilterPackageMembershipImport | FilterPackageNamespaceImport 
;

FilterPackageMembershipImport returns MembershipImport:
    ImportedMembership 
;

FilterPackageNamespaceImport returns NamespaceImport:
    ImportedNamespace 
;

FilterPackageMember returns ElementFilterMembership:
    // visibility=FilterPackageMemberVisibility  
    '[' ownedRelatedElement+=OwnedExpression  ']'  
;

// FilterPackageMemberVisibility returns VisibilityKind:
//     FilterPackageMemberVisibility_private
// ;
// FilterPackageMemberVisibility_private returns string: '[' ;

VisibilityIndicator returns VisibilityKind:
    VisibilityIndicator_public | VisibilityIndicator_private | VisibilityIndicator_protected
;
VisibilityIndicator_public returns VisibilityKind_public: 'public' ;
VisibilityIndicator_private returns VisibilityKind_private: 'private' ;
VisibilityIndicator_protected returns VisibilityKind_protected: 'protected' ;

DefinitionElement returns Element:
    Package | LibraryPackage | AnnotatingElement | Dependency | 
    AttributeDefinition | EnumerationDefinition | OccurrenceDefinition | 
    IndividualDefinition | ItemDefinition | MetadataDefinition | 
    PartDefinition | ConnectionDefinition | FlowDefinition | InterfaceDefinition | 
    AllocationDefinition | PortDefinition | ActionDefinition | CalculationDefinition | 
    StateDefinition | ConstraintDefinition | RequirementDefinition | ConcernDefinition | 
    CaseDefinition | AnalysisCaseDefinition | VerificationCaseDefinition | 
    UseCaseDefinition | ViewDefinition | ViewpointDefinition | RenderingDefinition | ExtendedDefinition 
;

UsageElement returns Usage:
    NonOccurrenceUsageElement | OccurrenceUsageElement 
;

fragment SubclassificationPart returns Classifier:
    SpecializesKeyword ownedRelationship+=OwnedSubclassification  (',' ownedRelationship+=OwnedSubclassification  )*  
;

SpecializesKeyword returns string:
    ':>' | 'specializes' 
;

OwnedSubclassification returns Subclassification:
    superclassifier=[Classifier:QualifiedName ] 
;

fragment FeatureDeclaration returns Feature:
    Identification FeatureSpecializationPart?  | FeatureSpecializationPart 
;

fragment FeatureSpecializationPart returns Feature:
    FeatureSpecialization+ MultiplicityPart? FeatureSpecialization*  | MultiplicityPart FeatureSpecialization*  
;

fragment MultiplicityPart returns Feature:
    ownedRelationship+=OwnedMultiplicity  
    | 
    (
        (ownedRelationship+=OwnedMultiplicity )? 
        (
            isOrdered?='ordered'  isNonunique?='nonunique' ?  | 
            isNonunique?='nonunique'  isOrdered?='ordered' ?  
        )
    ) 
;

fragment FeatureSpecialization returns Feature:
    Typings | Subsettings | References | Crosses | Redefinitions 
;

fragment Typings returns Feature:
    TypedBy (',' ownedRelationship+=FeatureTyping  )*  
;

fragment TypedBy returns Feature:
    DefinedByKeyword ownedRelationship+=FeatureTyping   
;

DefinedByKeyword returns string:
    ':' | 'defined' 'by'  
;

fragment Subsettings returns Feature:
    Subsets (',' ownedRelationship+=OwnedSubsetting  )*  
;

fragment Subsets returns Feature:
    SubsetsKeyword ownedRelationship+=OwnedSubsetting   
;

SubsetsKeyword returns string:
    ':>' | 'subsets' 
;

fragment References returns Feature:
    ReferencesKeyword ownedRelationship+=OwnedReferenceSubsetting   
;

ReferencesKeyword returns string:
    '::>' | 'references' 
;

fragment Crosses returns Feature:
    CrossesKeyword ownedRelationship+=OwnedCrossSubsetting   
;

CrossesKeyword returns string:
    '=>' | 'crosses' 
;

fragment Redefines returns Feature:
    RedefinesKeyword ownedRelationship+=OwnedRedefinition   
;

RedefinesKeyword returns string:
    ':>>' | 'redefines' 
;

FeatureTyping returns FeatureTyping:
    OwnedFeatureTyping | ConjugatedPortTyping 
;

OwnedFeatureTyping returns FeatureTyping:
    ^type=[Type:QualifiedName ] | ownedRelatedElement+=OwnedFeatureChain  
;

OwnedSubsetting returns Subsetting:
    subsettedFeature=[Feature:QualifiedName ] | ownedRelatedElement+=OwnedFeatureChain  
;

OwnedReferenceSubsetting returns ReferenceSubsetting:
    referencedFeature=[Feature:QualifiedName ] | ownedRelatedElement+=OwnedFeatureChain  
;

OwnedCrossSubsetting returns CrossSubsetting:
    crossedFeature=[Feature:QualifiedName ] | ownedRelatedElement+=OwnedFeatureChain  
;

fragment Redefinitions returns Feature:
    Redefines (',' ownedRelationship+=OwnedRedefinition  )*  
;

OwnedRedefinition returns Redefinition:
    redefinedFeature=[Feature:QualifiedName ] | ownedRelatedElement+=OwnedFeatureChain  
;

OwnedMultiplicity returns OwningMembership:
    ownedRelatedElement+=MultiplicityRange  
;

MultiplicityRange returns MultiplicityRange:
    '[' ownedRelationship+=MultiplicityExpressionMember  ('..' ownedRelationship+=MultiplicityExpressionMember  )? ']'  
;

MultiplicityExpressionMember returns OwningMembership:
    ownedRelatedElement+=(LiteralExpression | FeatureReferenceExpression ) 
;

fragment BasicDefinitionPrefix returns Definition:
    isAbstract?='abstract'  | isVariation?='variation'  
;

fragment DefinitionExtensionKeyword returns Definition:
    ownedRelationship+=PrefixMetadataMember  
;

fragment DefinitionPrefix returns Definition:
    BasicDefinitionPrefix? DefinitionExtensionKeyword*  
;

fragment Definition returns Definition:
    DefinitionDeclaration DefinitionBody  
;

fragment DefinitionDeclaration returns Definition:
    Identification? SubclassificationPart?  
;

fragment DefinitionBody returns Type:
    ';' | '{' DefinitionBodyItem* '}'  
;

fragment DefinitionBodyItem returns Type:
    ownedRelationship+=DefinitionMember  | 
    ownedRelationship+=VariantUsageMember  | 
    ownedRelationship+=NonOccurrenceUsageMember  | 
    (ownedRelationship+=EmptySuccessionMember )? ownedRelationship+=OccurrenceUsageMember   | 
    ownedRelationship+=AliasMember  | 
    ownedRelationship+=Import  
;

DefinitionMember returns OwningMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=DefinitionElement   
;

VariantUsageMember returns VariantMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'variant' ownedRelatedElement+=VariantUsageElement   
;

NonOccurrenceUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=NonOccurrenceUsageElement   
;

OccurrenceUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=OccurrenceUsageElement   
;

StructureUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=StructureUsageElement   
;

BehaviorUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=BehaviorUsageElement   
;

FeatureDirection returns FeatureDirectionKind:
    FeatureDirection_in | FeatureDirection_out | FeatureDirection_inout
;
FeatureDirection_in returns FeatureDirectionKind_in: 'in' ;
FeatureDirection_out returns FeatureDirectionKind_out: 'out' ;
FeatureDirection_inout returns FeatureDirectionKind_inout: 'inout' ;

fragment RefPrefix returns Usage:
    ('in' | 'out' | 'inout')? 
    (isDerived?='derived' )? 
    (isAbstract?='abstract'  | isVariation?='variation'  )?
    (isConstant?='constant' )?  
;

fragment BasicUsagePrefix returns Usage:
    RefPrefix (isReference?='ref' )?  
;

fragment EndUsagePrefix returns Usage:
    isEnd?='end'  (ownedRelationship+=OwnedCrossFeatureMember )?  
;

fragment UnextendedUsagePrefix returns Usage:
    EndUsagePrefix | BasicUsagePrefix 
;

fragment UsageExtensionKeyword returns Usage:
    ownedRelationship+=PrefixMetadataMember  
;

fragment UsagePrefix returns Usage:
    UnextendedUsagePrefix UsageExtensionKeyword*  
;

OwnedCrossFeatureMember returns OwningMembership:
    ownedRelatedElement+=OwnedCrossFeature  
;

OwnedCrossFeature returns ReferenceUsage:
    BasicUsagePrefix UsageDeclaration  
;

fragment Usage returns Usage:
    UsageDeclaration? UsageCompletion  
;

fragment UsageDeclaration returns Usage:
    FeatureDeclaration 
;

fragment UsageCompletion returns Usage:
    ValuePart? UsageBody  
;

fragment UsageBody returns Usage:
    DefinitionBody 
;

fragment ValuePart returns Feature:
    ownedRelationship+=FeatureValue  
;

FeatureValue returns FeatureValue:
    ('=' | isInitial?=':='  | isDefault?='default'  ('=' | isInitial?=':='  )? )ownedRelatedElement+=OwnedExpression   
;

ReferenceKeyword returns string:
    'ref' 
;

ReferenceUsageKeyword returns string:
    ReferenceKeyword 
;

DefaultReferenceUsage returns ReferenceUsage:
    (isEnd?='end' )? RefPrefix UsageDeclaration ValuePart? UsageBody  
;

ReferenceUsage returns ReferenceUsage:
    (EndUsagePrefix | RefPrefix )ReferenceUsageKeyword Usage  
;

VariantReference returns ReferenceUsage:
    ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecialization* UsageBody  
;

NonOccurrenceUsageElement returns Usage:
    DefaultReferenceUsage | ReferenceUsage | AttributeUsage | 
    EnumerationUsage | BindingConnectorAsUsage | 
    SuccessionAsUsage | ExtendedUsage 
;

OccurrenceUsageElement returns Usage:
    StructureUsageElement | BehaviorUsageElement 
;

StructureUsageElement returns Usage:
    OccurrenceUsage | IndividualUsage | PortionUsage | EventOccurrenceUsage | 
    ItemUsage | PartUsage | ViewUsage | RenderingUsage | PortUsage | 
    ConnectionUsage | InterfaceUsage | AllocationUsage | Message | 
    FlowUsage | SuccessionFlowUsage 
;

BehaviorUsageElement returns Usage:
    ActionUsage | CalculationUsage | StateUsage | ConstraintUsage | 
    RequirementUsage | ConcernUsage | CaseUsage | AnalysisCaseUsage | 
    VerificationCaseUsage | UseCaseUsage | ViewpointUsage | 
    PerformActionUsage | ExhibitStateUsage | IncludeUseCaseUsage | 
    AssertConstraintUsage | SatisfyRequirementUsage 
;

VariantUsageElement returns Usage:
    VariantReference | ReferenceUsage | AttributeUsage | 
    BindingConnectorAsUsage | SuccessionAsUsage | OccurrenceUsage | 
    IndividualUsage | PortionUsage | EventOccurrenceUsage | 
    ItemUsage | PartUsage | ViewUsage | RenderingUsage | PortUsage | 
    ConnectionUsage | InterfaceUsage | AllocationUsage | Message | 
    FlowUsage | SuccessionFlowUsage | BehaviorUsageElement 
;

ExtendedDefinition returns Definition:
    BasicDefinitionPrefix? DefinitionExtensionKeyword+ 'def' Definition  
;

ExtendedUsage returns Usage:
    UnextendedUsagePrefix UsageExtensionKeyword+ Usage  
;

AttributeKeyword returns string:
    'attribute' 
;

AttributeDefKeyword returns string:
    AttributeKeyword 'def'  
;

AttributeUsageKeyword returns string:
    AttributeKeyword 
;

AttributeDefinition returns AttributeDefinition:
    DefinitionPrefix AttributeDefKeyword Definition  
;

AttributeUsage returns AttributeUsage:
    UsagePrefix AttributeUsageKeyword Usage  
;

EnumerationKeyword returns string:
    'enum' 
;

EnumerationDefKeyword returns string:
    EnumerationKeyword 'def'  
;

EnumerationUsageKeyword returns string:
    EnumerationKeyword 
;

EnumerationDefinition returns EnumerationDefinition:
    DefinitionExtensionKeyword* EnumerationDefKeyword DefinitionDeclaration EnumerationBody  
;

fragment EnumerationBody returns EnumerationDefinition:
    ';' | '{' (ownedRelationship+=AnnotatingMember  | ownedRelationship+=EnumerationUsageMember  )*'}'  
;

EnumerationUsageMember returns VariantMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=EnumeratedValue   
;

EnumeratedValue returns EnumerationUsage:
    UsageExtensionKeyword* EnumerationUsageKeyword? Usage  
;

EnumerationUsage returns EnumerationUsage:
    UsagePrefix EnumerationUsageKeyword Usage  
;

OccurrenceKeyword returns string:
    'occurrence' 
;

OccurrenceDefKeyword returns string:
    OccurrenceKeyword 'def'  
;

fragment OccurrenceDefinitionPrefix returns OccurrenceDefinition:
    BasicDefinitionPrefix? 
    (isIndividual?='individual'  ownedRelationship+=EmptyMultiplicityMember  )? 
    DefinitionExtensionKeyword*  
;

OccurrenceDefinition returns OccurrenceDefinition:
    OccurrenceDefinitionPrefix OccurrenceDefKeyword Definition  
;

IndividualDefinition returns OccurrenceDefinition:
    BasicDefinitionPrefix? isIndividual?='individual'  
    ownedRelationship+=EmptyMultiplicityMember  DefinitionExtensionKeyword* 'def' Definition  
;

EmptyMultiplicityMember returns OwningMembership:
    ownedRelatedElement+=EmptyMultiplicity  
;

EmptyMultiplicity returns Multiplicity:
    {Multiplicity} 
;

OccurrenceUsageKeyword returns string:
    OccurrenceKeyword 
;

fragment OccurrenceUsagePrefix returns OccurrenceUsage:
    (EndUsagePrefix | BasicUsagePrefix (isIndividual?='individual' )? (portionKind=PortionKind )?  )UsageExtensionKeyword*  
;

OccurrenceUsage returns OccurrenceUsage:
    OccurrenceUsagePrefix OccurrenceUsageKeyword Usage  
;

IndividualUsage returns OccurrenceUsage:
    BasicUsagePrefix isIndividual?='individual'  UsageExtensionKeyword* Usage  
;

PortionUsage returns OccurrenceUsage:
    BasicUsagePrefix (isIndividual?='individual' )? portionKind=PortionKind  UsageExtensionKeyword* Usage  
;

PortionKind returns PortionKind:
    PortionKind_snapshot | PortionKind_timeslice
;
PortionKind_snapshot returns PortionKind_snapshot: 'snapshot' ;
PortionKind_timeslice returns PortionKind_timeslice: 'timeslice' ;

EventOccurrenceUsage returns EventOccurrenceUsage:
    OccurrenceUsagePrefix 'event' 
    (
        ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecializationPart?  | 
        OccurrenceUsageKeyword UsageDeclaration?  
    )
    UsageCompletion  
;

EmptySuccessionMember returns FeatureMembership:
    ownedRelatedElement+=EmptySuccession  
;

EmptySuccession returns SuccessionAsUsage:
    'then' ownedRelationship+=MultiplicitySourceEndMember  ownedRelationship+=EmptyTargetEndMember   
;

MultiplicitySourceEndMember returns EndFeatureMembership:
    ownedRelatedElement+=MultiplicitySourceEnd  
;

MultiplicitySourceEnd returns ReferenceUsage:
    {ReferenceUsage} (ownedRelationship+=OwnedMultiplicity )?  
;

EmptyTargetEndMember returns EndFeatureMembership:
    ownedRelatedElement+=EmptyTargetEnd  
;

EmptyTargetEnd returns ReferenceUsage:
    {ReferenceUsage} 
;

ItemKeyword returns string:
    'item' 
;

ItemDefKeyword returns string:
    ItemKeyword 'def'  
;

ItemUsageKeyword returns string:
    ItemKeyword 
;

ItemDefinition returns ItemDefinition:
    OccurrenceDefinitionPrefix ItemDefKeyword Definition  
;

ItemUsage returns ItemUsage:
    OccurrenceUsagePrefix ItemUsageKeyword Usage  
;

PartKeyword returns string:
    'part' 
;

PartDefKeyword returns string:
    PartKeyword 'def'  
;

PartUsageKeyword returns string:
    PartKeyword 
;

PartDefinition returns PartDefinition:
    OccurrenceDefinitionPrefix PartDefKeyword Definition  
;

PartUsage returns PartUsage:
    OccurrenceUsagePrefix PartUsageKeyword Usage  
;

PortKeyword returns string:
    'port' 
;

PortDefKeyword returns string:
    PortKeyword 'def'  
;

PortDefinition returns PortDefinition:
    DefinitionPrefix PortDefKeyword Definition ownedRelationship+=ConjugatedPortDefinitionMember   
;

ConjugatedPortDefinitionMember returns OwningMembership:
    ownedRelatedElement+=ConjugatedPortDefinition  
;

ConjugatedPortDefinition returns ConjugatedPortDefinition:
    ownedRelationship+=PortConjugation  
;

PortConjugation returns PortConjugation:
    {PortConjugation} 
;

ConjugatedPortTyping returns ConjugatedPortTyping:
    conjugatedPortDefinition=[ConjugatedPortDefinition:ConjugatedQualifiedName ] 
;

ConjugatedQualifiedName returns string:
    '~' QualifiedName  
;

PortUsageKeyword returns string:
    PortKeyword 
;

PortUsage returns PortUsage:
    OccurrenceUsagePrefix PortUsageKeyword Usage  
;

ConnectorEndMember returns EndFeatureMembership:
    ownedRelatedElement+=ConnectorEnd  
;

ConnectorEnd returns ReferenceUsage:
    (ownedRelationship+=OwnedCrossMultiplicityMember )? 
    (declaredName=Name  ReferencesKeyword )? 
    ownedRelationship+=OwnedReferenceSubsetting   
;

OwnedCrossMultiplicityMember returns OwningMembership:
    ownedRelatedElement+=OwnedCrossMultiplicity  
;

OwnedCrossMultiplicity returns Feature:
    ownedRelationship+=OwnedMultiplicity  
;

BindingKeyword returns string:
    'binding' 
;

BindingConnectorAsUsage returns BindingConnectorAsUsage:
    UsagePrefix (BindingKeyword UsageDeclaration? )? 
    'bind' ownedRelationship+=ConnectorEndMember  '=' ownedRelationship+=ConnectorEndMember  DefinitionBody  
;

SuccessionKeyword returns string:
    'succession' 
;

SuccessionAsUsage returns SuccessionAsUsage:
    UsagePrefix (SuccessionKeyword UsageDeclaration? )? 
    'first' ownedRelationship+=ConnectorEndMember  
    'then' ownedRelationship+=ConnectorEndMember  DefinitionBody  
;

ConnectionKeyword returns string:
    'connection' 
;

ConnectionDefKeyword returns string:
    ConnectionKeyword 'def'  
;

ConnectionDefinition returns ConnectionDefinition:
    OccurrenceDefinitionPrefix ConnectionDefKeyword Definition  
;

ConnectorKeyword returns string:
    'connect' 
;

ConnectionUsageKeyword returns string:
    ConnectionKeyword 
;

ConnectionUsage returns ConnectionUsage:
    OccurrenceUsagePrefix 
    (
        ConnectionUsageKeyword UsageDeclaration? ValuePart? (ConnectorKeyword ConnectorPart )?  | 
        ConnectorKeyword ConnectorPart  
    )
    UsageBody  
;

fragment ConnectorPart returns ConnectionUsage:
    BinaryConnectorPart | NaryConnectorPart 
;

fragment BinaryConnectorPart returns Connector:
    ownedRelationship+=ConnectorEndMember  'to' ownedRelationship+=ConnectorEndMember   
;

fragment NaryConnectorPart returns Connector:
    '(' ownedRelationship+=ConnectorEndMember  ',' ownedRelationship+=ConnectorEndMember  (',' ownedRelationship+=ConnectorEndMember  )* ')'  
;

EmptySourceEndMember returns EndFeatureMembership:
    ownedRelatedElement+=EmptySourceEnd  
;

EmptySourceEnd returns ReferenceUsage:
    {ReferenceUsage} 
;

InterfaceKeyword returns string:
    'interface' 
;

InterfaceDefKeyword returns string:
    InterfaceKeyword 'def'  
;

InterfaceDefinition returns InterfaceDefinition:
    OccurrenceDefinitionPrefix InterfaceDefKeyword DefinitionDeclaration InterfaceBody  
;

fragment InterfaceBody returns Type:
    ';' | '{' InterfaceBodyItem* '}'  
;

fragment InterfaceBodyItem returns Type:
    ownedRelationship+=DefinitionMember  | 
    ownedRelationship+=VariantUsageMember  | 
    ownedRelationship+=InterfaceNonOccurrenceUsageMember  | 
    (ownedRelationship+=EmptySuccessionMember )? ownedRelationship+=InterfaceOccurrenceUsageMember   | 
    ownedRelationship+=AliasMember  | 
    ownedRelationship+=Import  
;

InterfaceNonOccurrenceUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=InterfaceNonOccurrenceUsageElement   
;

InterfaceNonOccurrenceUsageElement returns Usage:
    ReferenceUsage | AttributeUsage | EnumerationUsage | BindingConnectorAsUsage | SuccessionAsUsage 
;

InterfaceOccurrenceUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=InterfaceOccurrenceUsageElement   
;

InterfaceOccurrenceUsageElement returns Usage:
    DefaultInterfaceEnd | StructureUsageElement | BehaviorUsageElement 
;

DefaultInterfaceEnd returns PortUsage:
    isEnd?='end'  Usage  
;

InterfaceUsageKeyword returns string:
    InterfaceKeyword 
;

InterfaceUsage returns InterfaceUsage:
    OccurrenceUsagePrefix InterfaceUsageKeyword InterfaceUsageDeclaration InterfaceBody  
;

fragment InterfaceUsageDeclaration returns InterfaceUsage:
    UsageDeclaration? (ConnectorKeyword InterfacePart )?  | InterfacePart 
;

fragment InterfacePart returns ConnectionUsage:
    BinaryInterfacePart | NaryInterfacePart 
;

fragment BinaryInterfacePart returns Connector:
    ownedRelationship+=InterfaceEndMember  'to' ownedRelationship+=InterfaceEndMember   
;

fragment NaryInterfacePart returns Connector:
    '(' 
        ownedRelationship+=InterfaceEndMember  ',' 
        ownedRelationship+=InterfaceEndMember  
        (',' ownedRelationship+=InterfaceEndMember  )* 
    ')'  
;

InterfaceEndMember returns EndFeatureMembership:
    ownedRelatedElement+=InterfaceEnd  
;

InterfaceEnd returns PortUsage:
    (ownedRelationship+=OwnedCrossMultiplicityMember )? 
    (declaredName=Name  ReferencesKeyword )? 
    ownedRelationship+=OwnedReferenceSubsetting   
;

AllocationKeyword returns string:
    'allocation' 
;

AllocationDefKeyword returns string:
    AllocationKeyword 'def'  
;

AllocationDefinition returns AllocationDefinition:
    OccurrenceDefinitionPrefix AllocationDefKeyword Definition  
;

AllocationUsageKeyword returns string:
    AllocationKeyword 
;

AllocateKeyword returns string:
    'allocate' 
;

AllocationUsage returns AllocationUsage:
    OccurrenceUsagePrefix AllocationUsageDeclaration UsageBody  
;

fragment AllocationUsageDeclaration returns AllocationUsage:
    AllocationUsageKeyword UsageDeclaration? (AllocateKeyword ConnectorPart )?  | AllocateKeyword ConnectorPart  
;

FlowKeyword returns string:
    'flow' 
;

FlowDefKeyword returns string:
    FlowKeyword 'def'  
;

FlowDefinition returns FlowDefinition:
    OccurrenceDefinitionPrefix FlowDefKeyword Definition  
;

MessageKeyword returns string:
    'message' 
;

Message returns FlowUsage:
    OccurrenceUsagePrefix MessageKeyword MessageDeclaration DefinitionBody  
;

fragment MessageDeclaration returns FlowUsage:
    UsageDeclaration? ValuePart? 
    ('of' ownedRelationship+=PayloadFeatureMember  )? 

    ('from' ownedRelationship+=MessageEventMember  'to' ownedRelationship+=MessageEventMember  )?  
    | 
    ownedRelationship+=MessageEventMember  'to' ownedRelationship+=MessageEventMember   
;

MessageEventMember returns ParameterMembership:
    ownedRelatedElement+=MessageEvent  
;

MessageEvent returns EventOccurrenceUsage:
    ownedRelationship+=OwnedReferenceSubsetting  
;

FlowUsage returns FlowUsage:
    OccurrenceUsagePrefix FlowKeyword FlowDeclaration DefinitionBody  
;

SuccessionFlowKeyword returns string:
    SuccessionKeyword FlowKeyword  
;

SuccessionFlowUsage returns SuccessionFlowUsage:
    OccurrenceUsagePrefix SuccessionFlowKeyword FlowDeclaration DefinitionBody  
;

fragment FlowDeclaration returns FlowUsage:
    UsageDeclaration? ValuePart? 
    ('of' ownedRelationship+=PayloadFeatureMember  )? 
    ('from' ownedRelationship+=FlowEndMember  'to' ownedRelationship+=FlowEndMember  )?  
    | 
    ownedRelationship+=FlowEndMember  'to' ownedRelationship+=FlowEndMember   
;

PayloadFeatureMember returns FeatureMembership:
    ownedRelatedElement+=PayloadFeature  
;

PayloadFeature returns PayloadFeature:
    Payload 
;

fragment Payload returns Feature:
    Identification? PayloadFeatureSpecializationPart ValuePart?  | 
    Identification? ValuePart  | 
    ownedRelationship+=OwnedFeatureTyping  (ownedRelationship+=OwnedMultiplicity )?  | 
    ownedRelationship+=OwnedMultiplicity  ownedRelationship+=OwnedFeatureTyping   
;

fragment PayloadFeatureSpecializationPart returns Feature:
    FeatureSpecialization+ MultiplicityPart? FeatureSpecialization*  | MultiplicityPart FeatureSpecialization+  
;

FlowEndMember returns EndFeatureMembership:
    ownedRelatedElement+=FlowEnd  
;

FlowEnd returns FlowEnd:
    (ownedRelationship+=FlowEndSubsetting )? ownedRelationship+=FlowFeatureMember   
;

FlowEndSubsetting returns ReferenceSubsetting:
    referencedFeature=[Feature:QualifiedName ] '.'  | ownedRelatedElement+=FeatureChainPrefix  
;

FeatureChainPrefix returns Feature:
    (ownedRelationship+=OwnedFeatureChaining  '.' )+ ownedRelationship+=OwnedFeatureChaining  '.'  
;

FlowFeatureMember returns FeatureMembership:
    ownedRelatedElement+=FlowFeature  
;

FlowFeature returns ReferenceUsage:
    ownedRelationship+=FlowRedefinition  
;

FlowRedefinition returns Redefinition:
    redefinedFeature=[Feature:QualifiedName ] 
;

ActionKeyword returns string:
    'action' 
;

ActionDefKeyword returns string:
    ActionKeyword 'def'  
;

ActionDefinition returns ActionDefinition:
    OccurrenceDefinitionPrefix ActionDefKeyword DefinitionDeclaration ActionBody  
;

fragment ActionBody returns Type:
    ';' | '{' ActionBodyItem* '}'  
;

fragment ActionBodyItem returns Type:
    ownedRelationship+=Import  | ownedRelationship+=AliasMember  | 
    ownedRelationship+=DefinitionMember  | ownedRelationship+=VariantUsageMember  | 
    ownedRelationship+=NonOccurrenceUsageMember  | 
    (ownedRelationship+=EmptySuccessionMember )? ownedRelationship+=StructureUsageMember   | 
    ownedRelationship+=InitialNodeMember  (ownedRelationship+=TargetSuccessionMember )*  | 
    (ownedRelationship+=EmptySuccessionMember )? ownedRelationship+=(BehaviorUsageMember | ActionNodeMember ) (ownedRelationship+=TargetSuccessionMember )*  | 
    ownedRelationship+=GuardedSuccessionMember  
;

InitialNodeMember returns Membership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'first' memberElement=[Feature:QualifiedName ] RelationshipBody  
;

ActionNodeMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=ActionNode   
;

TargetSuccessionMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=ActionTargetSuccession   
;

GuardedSuccessionMember returns FeatureMembership:
    ownedRelatedElement+=GuardedSuccession  
;

ActionUsageKeyword returns string:
    ActionKeyword 
;

ActionUsage returns ActionUsage:
    OccurrenceUsagePrefix ActionUsageKeyword ActionUsageDeclaration ActionBody  
;

PerformActionUsage returns PerformActionUsage:
    OccurrenceUsagePrefix 'perform' PerformActionUsageDeclaration ActionBody  
;

fragment PerformActionUsageDeclaration returns ActionUsage:
    (ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecializationPart?  | ActionUsageKeyword UsageDeclaration?  )
    ValuePart?  
;

fragment ActionUsageDeclaration returns ActionUsage:
    UsageDeclaration? ValuePart?  
;

ActionNode returns ActionUsage:
    SendNode | AcceptNode | AssignmentNode | IfNode | WhileLoopNode | ForLoopNode | TerminateNode | ControlNode 
;

fragment ActionNodeUsageDeclaration returns ActionUsage:
    ActionUsageKeyword UsageDeclaration?  
;

fragment ActionNodePrefix returns ActionUsage:
    OccurrenceUsagePrefix ActionNodeUsageDeclaration?  
;

AcceptNode returns AcceptActionUsage:
    OccurrenceUsagePrefix AcceptNodeDeclaration ActionBody  
;

fragment AcceptNodeDeclaration returns ActionUsage:
    ActionNodeUsageDeclaration? 'accept' AcceptParameterPart  
;

fragment AcceptParameterPart returns ActionUsage:
    ownedRelationship+=PayloadParameterMember  ('via' ownedRelationship+=NodeParameterMember  )?  
;

PayloadParameterMember returns ParameterMembership:
    ownedRelatedElement+=PayloadParameter  
;

PayloadParameter returns ReferenceUsage:
    Payload | Identification? PayloadFeatureSpecializationPart? TriggerValuePart  
;

fragment TriggerValuePart returns Feature:
    ownedRelationship+=TriggerFeatureValue  
;

TriggerFeatureValue returns FeatureValue:
    ownedRelatedElement+=TriggerExpression  
;

TriggerExpression returns TriggerInvocationExpression:
    ('at' | 'after'   ownedRelationship+=ArgumentMember)   | 
    ('when'  ownedRelationship+=ArgumentExpressionMember) 
;

TimeTriggerKind returns TriggerKind:
    'at' | 'after'
;

ChangeTriggerKind returns TriggerKind:
    'when' 
;

ArgumentExpressionMember returns ParameterMembership:
    ownedRelatedElement+=ArgumentExpression  
;

ArgumentExpression returns Feature:
    ownedRelationship+=ArgumentExpressionValue  
;

ArgumentExpressionValue returns FeatureValue:
    ownedRelatedElement+=OwnedExpressionReference  
;

SendNode returns SendActionUsage:
    OccurrenceUsagePrefix ActionNodeUsageDeclaration? 'send' 
    (
        ActionBody | 
        (ownedRelationship+=NodeParameterMember  SenderReceiverPart?  | ownedRelationship+=EmptyParameterMember  SenderReceiverPart   ActionBody ) 
    ) 
;

fragment SendNodeDeclaration returns ActionUsage:
    ActionNodeUsageDeclaration? 'send' ownedRelationship+=NodeParameterMember  SenderReceiverPart?  
;

fragment SenderReceiverPart returns ActionUsage:
    'via' ownedRelationship+=NodeParameterMember  ('to' ownedRelationship+=NodeParameterMember  )?  | 
    ownedRelationship+=EmptyParameterMember  'to' ownedRelationship+=NodeParameterMember   
;

NodeParameterMember returns ParameterMembership:
    ownedRelatedElement+=NodeParameter  
;

NodeParameter returns ReferenceUsage:
    ownedRelationship+=FeatureBinding  
;

FeatureBinding returns FeatureValue:
    ownedRelatedElement+=OwnedExpression  
;

AssignmentNode returns AssignmentActionUsage:
    OccurrenceUsagePrefix AssignmentNodeDeclaration ActionBody  
;

fragment AssignmentNodeDeclaration returns ActionUsage:
    ActionNodeUsageDeclaration? 
    'assign' ownedRelationship+=AssignmentTargetMember  
    ownedRelationship+=FeatureChainMember  ':=' ownedRelationship+=NodeParameterMember   
;

AssignmentTargetMember returns ParameterMembership:
    ownedRelatedElement+=TargetParameter  
;

TargetParameter returns ReferenceUsage:
    (ownedRelationship+=TargetBinding  '.' )? ownedRelationship+=TargetFeatureMember   
;

TargetFeatureMember returns FeatureMembership:
    ownedRelatedElement+=TargetFeature  
;

TargetFeature returns ReferenceUsage:
    ownedRelationship+=TargetAccessedFeatureMember  
;

TargetAccessedFeatureMember returns FeatureMembership:
    ownedRelatedElement+=EmptyUsage  
;

TargetBinding returns FeatureValue:
    ownedRelatedElement+=TargetExpression  
;

TargetExpression returns Expression:
    BaseExpression 
    (
        ({FeatureChainExpression.operand+=current} '.' ownedRelationship+=FeatureChainMember  )? 
        (
            {OperatorExpression.operand+=current} 
            operator='['  operand+=SequenceExpression  ']'  | {OperatorExpression.operand+=current} 
            '->' ownedRelationship+=ReferenceTyping  
            (ownedRelationship+=ExpressionBodyMember  | ownedRelationship+=FunctionReferenceMember  | ArgumentList ) | 
            {CollectExpression.operand+=current} '.' ownedRelationship+=ExpressionBodyMember   | 
            {SelectExpression.operand+=current} '.?' ownedRelationship+=ExpressionBodyMember  
         )
    )*  
;

ExpressionParameterMember returns ParameterMembership:
    ownedRelatedElement+=OwnedExpression  
;

IfNode returns IfActionUsage:
    ActionNodePrefix 
    'if' ownedRelationship+=ExpressionParameterMember  ownedRelationship+=ActionBodyParameterMember  
    ('else' ownedRelationship+=(ActionBodyParameterMember | IfNodeParameterMember ) )?  
;

ActionBodyParameterMember returns ParameterMembership:
    ownedRelatedElement+=ActionBodyParameter  
;

ActionBodyParameter returns ActionUsage:
    {ActionUsage} (ActionUsageKeyword UsageDeclaration? )? '{' ActionBodyItem* '}'  
;

IfNodeParameterMember returns ParameterMembership:
    ownedRelatedElement+=IfNode  
;

WhileLoopNode returns WhileLoopActionUsage:
    ActionNodePrefix ('while' ownedRelationship+=ExpressionParameterMember   | 
    'loop' ownedRelationship+=EmptyParameterMember   )ownedRelationship+=ActionBodyParameterMember  
    ('until' ownedRelationship+=ExpressionParameterMember  ';' )?  
;

ForLoopNode returns ForLoopActionUsage:
    ActionNodePrefix 
    'for' ownedRelationship+=ForVariableDeclarationMember  
    'in' ownedRelationship+=NodeParameterMember  ownedRelationship+=ActionBodyParameterMember   
;

ForVariableDeclarationMember returns FeatureMembership:
    ownedRelatedElement+=ForVariableDeclaration  
;

ForVariableDeclaration returns ReferenceUsage:
    UsageDeclaration 
;

TerminateNode returns TerminateActionUsage:
    OccurrenceUsagePrefix ActionNodeUsageDeclaration? 
    'terminate' (ActionBody | ownedRelationship+=NodeParameterMember  ActionBody  ) 
;

ControlNode returns ControlNode:
    MergeNode | DecisionNode | JoinNode | ForkNode 
;

fragment ControlNodePrefix returns OccurrenceUsage:
    RefPrefix (isIndividual?='individual' )? (portionKind=PortionKind )? UsageExtensionKeyword*  
;

MergeNode returns MergeNode:
    ControlNodePrefix 'merge' UsageDeclaration? ActionNodeBody  
;

DecisionNode returns DecisionNode:
    ControlNodePrefix 'decide' UsageDeclaration? ActionNodeBody  
;

JoinNode returns JoinNode:
    ControlNodePrefix 'join' UsageDeclaration? ActionNodeBody  
;

ForkNode returns ForkNode:
    ControlNodePrefix 'fork' UsageDeclaration? ActionNodeBody  
;

fragment ActionNodeBody returns ControlNode:
    ';' | '{' (ownedRelationship+=AnnotatingMember )* '}'  
;

EmptyParameterMember returns ParameterMembership:
    ownedRelatedElement+=EmptyUsage  
;

EmptyUsage returns ReferenceUsage:
    {ReferenceUsage} 
;

ActionTargetSuccession returns Usage:
    (TargetSuccession | GuardedTargetSuccession | DefaultTargetSuccession )UsageBody  
;

TargetSuccession returns SuccessionAsUsage:
    ownedRelationship+=MultiplicitySourceEndMember  'then' ownedRelationship+=ConnectorEndMember   
;

GuardedTargetSuccession returns TransitionUsage:
    ownedRelationship+=EmptyParameterMember  ownedRelationship+=GuardExpressionMember  'then' ownedRelationship+=TransitionSuccessionMember   
;

DefaultTargetSuccession returns TransitionUsage:
    ownedRelationship+=EmptyParameterMember  'else' ownedRelationship+=TransitionSuccessionMember   
;

GuardedSuccession returns TransitionUsage:
    (SuccessionKeyword UsageDeclaration )? 
    'first' ownedRelationship+=TransitionSourceMember  ownedRelationship+=EmptyParameterMember  ownedRelationship+=GuardExpressionMember  
    'then' ownedRelationship+=TransitionSuccessionMember  UsageBody  
;

StateKeyword returns string:
    'state' 
;

StateDefKeyword returns string:
    StateKeyword 'def'  
;

StateDefinition returns StateDefinition:
    OccurrenceDefinitionPrefix StateDefKeyword DefinitionDeclaration StateDefBody  
;

fragment StateDefBody returns StateDefinition:
    ';' | (isParallel?='parallel' )? '{' StateBodyPart '}'  
;

fragment StateBodyPart returns Type:
    StateBodyItem* 
;

fragment StateBodyItem returns Type:
    ownedRelationship+=Import  | 
    ownedRelationship+=AliasMember  | 
    ownedRelationship+=DefinitionMember  | 
    ownedRelationship+=VariantUsageMember  | 
    ownedRelationship+=NonOccurrenceUsageMember  | 
    (ownedRelationship+=EmptySuccessionMember )? ownedRelationship+=StructureUsageMember   | 
    (ownedRelationship+=EmptySuccessionMember )? ownedRelationship+=BehaviorUsageMember  (ownedRelationship+=TargetTransitionUsageMember )*  | 
    ownedRelationship+=TransitionUsageMember  | 
    ownedRelationship+=EntryActionMember  (ownedRelationship+=EntryTransitionMember )*  | 
    ownedRelationship+=DoActionMember  | 
    ownedRelationship+=ExitActionMember  
;

EntryActionMember returns StateSubactionMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    kind=EntryActionKind  ownedRelatedElement+=StateActionUsage   
;

EntryActionKind returns StateSubactionKind:
    'entry' 
;

DoActionMember returns StateSubactionMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    kind=DoActionKind  ownedRelatedElement+=StateActionUsage   
;

DoActionKind returns StateSubactionKind:
    'do' 
;

ExitActionMember returns StateSubactionMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    kind=ExitActionKind  ownedRelatedElement+=StateActionUsage   
;

ExitActionKind returns StateSubactionKind:
    'exit' 
;

EntryTransitionMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    (ownedRelatedElement+=GuardedTargetSuccession  | 'then' ownedRelatedElement+=TransitionSuccession   )';'  
;

StateActionUsage returns ActionUsage:
    EmptyActionUsage ';'  | PerformedActionUsage ActionBody  
;

EmptyActionUsage returns ActionUsage:
    {ActionUsage} 
;

PerformedActionUsage returns ActionUsage:
    {PerformActionUsage} PerformActionUsageDeclaration  | 
    {AcceptActionUsage} AcceptNodeDeclaration  | 
    {SendActionUsage} SendNodeDeclaration  | 
    {AssignmentActionUsage} AssignmentNodeDeclaration  
;

TransitionUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=TransitionUsage   
;

TargetTransitionUsageMember returns FeatureMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=TargetTransitionUsage   
;

StateUsageKeyword returns string:
    StateKeyword 
;

StateUsage returns StateUsage:
    OccurrenceUsagePrefix StateUsageKeyword ActionUsageDeclaration StateUsageBody  
;

fragment StateUsageBody returns StateUsage:
    ';' | (isParallel?='parallel' )? '{' StateBodyPart '}'  
;

ExhibitStateUsage returns ExhibitStateUsage:
    OccurrenceUsagePrefix 'exhibit' (ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecializationPart?  | 
    StateUsageKeyword UsageDeclaration?  )ValuePart? StateUsageBody  
;

TransitionUsageKeyword returns string:
    'transition' 
;

TransitionUsage returns TransitionUsage:
    TransitionUsageKeyword (UsageDeclaration? 'first' )? 
    ownedRelationship+=TransitionSourceMember  
    ownedRelationship+=EmptyParameterMember  
    (ownedRelationship+=EmptyParameterMember  ownedRelationship+=TriggerActionMember  )? 
    (ownedRelationship+=GuardExpressionMember )? (ownedRelationship+=EffectBehaviorMember )? 
    'then' ownedRelationship+=TransitionSuccessionMember  ActionBody  
;

TargetTransitionUsage returns TransitionUsage:
    ownedRelationship+=EmptyParameterMember  
    (
        TransitionUsageKeyword 
        (ownedRelationship+=EmptyParameterMember  ownedRelationship+=TriggerActionMember  )? (ownedRelationship+=GuardExpressionMember )? (ownedRelationship+=EffectBehaviorMember )?  
        | 
        ownedRelationship+=EmptyParameterMember  ownedRelationship+=TriggerActionMember  (ownedRelationship+=GuardExpressionMember )? (ownedRelationship+=EffectBehaviorMember )?  
        | 
        ownedRelationship+=GuardExpressionMember  (ownedRelationship+=EffectBehaviorMember )?  
    )?
    'then' ownedRelationship+=TransitionSuccessionMember  ActionBody  
;

TransitionSourceMember returns Membership:
    memberElement=[Feature:QualifiedName ] | {OwningMembership} ownedRelatedElement+=OwnedFeatureChain   
;

TriggerActionMember returns TransitionFeatureMembership:
    'accept'  ownedRelatedElement+=TriggerAction   
;

TriggerFeatureKind returns TransitionFeatureKind:
    TriggerFeatureKind_trigger
;
TriggerFeatureKind_trigger returns string: 'accept' ;

TriggerAction returns AcceptActionUsage:
    AcceptParameterPart 
;

GuardExpressionMember returns TransitionFeatureMembership:
    'if'  ownedRelatedElement+=OwnedExpression   
;

GuardFeatureKind returns TransitionFeatureKind:
    GuardFeatureKind_guard
;
GuardFeatureKind_guard returns string: 'if' ;

EffectBehaviorMember returns TransitionFeatureMembership:
    'do'  ownedRelatedElement+=EffectBehaviorUsage   
;

EffectFeatureKind returns TransitionFeatureKind:
    EffectFeatureKind_effect
;
EffectFeatureKind_effect returns string: 'do' ;

EffectBehaviorUsage returns ActionUsage:
    EmptyActionUsage | PerformedActionUsage ('{' ActionBodyItem* '}' )?  
;

TransitionSuccessionMember returns OwningMembership:
    ownedRelatedElement+=TransitionSuccession  
;

TransitionSuccession returns SuccessionAsUsage:
    ownedRelationship+=EmptySourceEndMember  ownedRelationship+=ConnectorEndMember   
;

CalculationKeyword returns string:
    'calc' 
;

CalculationDefKeyword returns string:
    CalculationKeyword 'def'  
;

CalculationDefinition returns CalculationDefinition:
    OccurrenceDefinitionPrefix CalculationDefKeyword DefinitionDeclaration CalculationBody  
;

fragment CalculationBody returns Type:
    ';' | '{' CalculationBodyPart '}'  
;

fragment CalculationBodyPart returns Type:
    CalculationBodyItem* (ownedRelationship+=ResultExpressionMember )?  
;

fragment CalculationBodyItem returns Type:
    ActionBodyItem | ownedRelationship+=ReturnParameterMember  
;

ReturnParameterMember returns ReturnParameterMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'return' ownedRelatedElement+=UsageElement   
;

ResultExpressionMember returns ResultExpressionMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=OwnedExpression   
;

CalculationUsageKeyword returns string:
    CalculationKeyword 
;

CalculationUsage returns CalculationUsage:
    OccurrenceUsagePrefix CalculationUsageKeyword ActionUsageDeclaration CalculationBody  
;

ConstraintKeyword returns string:
    'constraint' 
;

ConstraintDefKeyword returns string:
    ConstraintKeyword 'def'  
;

ConstraintDefinition returns ConstraintDefinition:
    OccurrenceDefinitionPrefix ConstraintDefKeyword DefinitionDeclaration CalculationBody  
;

ConstraintUsageKeyword returns string:
    ConstraintKeyword 
;

ConstraintUsage returns ConstraintUsage:
    OccurrenceUsagePrefix ConstraintUsageKeyword ConstraintUsageDeclaration CalculationBody  
;

AssertConstraintUsage returns AssertConstraintUsage:
    OccurrenceUsagePrefix 
    'assert' (isNegated?='not' )? 
    (ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecializationPart?  | 
    ConstraintUsageKeyword ConstraintUsageDeclaration  )CalculationBody  
;

fragment ConstraintUsageDeclaration returns ConstraintUsage:
    UsageDeclaration? ValuePart?  
;

RequirementKeyword returns string:
    'requirement' 
;

RequirementDefKeyword returns string:
    RequirementKeyword 'def'  
;

RequirementDefinition returns RequirementDefinition:
    OccurrenceDefinitionPrefix RequirementDefKeyword DefinitionDeclaration RequirementBody  
;

fragment RequirementBody returns Type:
    ';' | '{' RequirementBodyItem* '}'  
;

fragment RequirementBodyItem returns Type:
    DefinitionBodyItem | 
    ownedRelationship+=SubjectMember  | 
    ownedRelationship+=RequirementConstraintMember  | 
    ownedRelationship+=FramedConcernMember  | 
    ownedRelationship+=RequirementVerificationMember  | 
    ownedRelationship+=ActorMember  | 
    ownedRelationship+=StakeholderMember  
;

SubjectMember returns SubjectMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=SubjectUsage   
;

SubjectUsage returns ReferenceUsage:
    'subject' UsageExtensionKeyword* Usage  
;

RequirementConstraintMember returns RequirementConstraintMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ('assume' | 'require')  ownedRelatedElement+=RequirementConstraintUsage   
;

// ReqConstraintKind returns RequirementConstraintKind:
//     ReqConstraintKind_assumption | 
//     ReqConstraintKind_requirement
// ;
// ReqConstraintKind_assumption returns string: 'assume' ;
// ReqConstraintKind_requirement returns string: 'require' ;

RequirementConstraintUsage returns ConstraintUsage:
    ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecialization* CalculationBody  | 
    (UsageExtensionKeyword* ConstraintUsageKeyword  | 
    UsageExtensionKeyword+ )ConstraintUsageDeclaration CalculationBody  
;

FramedConcernMember returns FramedConcernMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'frame'  ownedRelatedElement+=FramedConcernUsage   
;

FramedConcernKind returns RequirementConstraintKind:
    FramedConcernKind_requirement
;
FramedConcernKind_requirement returns string: 'frame' ;

FramedConcernUsage returns ConcernUsage:
    ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecialization* RequirementBody  | 
    (UsageExtensionKeyword* ConcernUsageKeyword  | 
    UsageExtensionKeyword+ )ConstraintUsageDeclaration CalculationBody  
;

ActorMember returns ActorMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=ActorUsage   
;

ActorUsage returns PartUsage:
    'actor' UsageExtensionKeyword* Usage  
;

StakeholderMember returns StakeholderMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    ownedRelatedElement+=StakeholderUsage   
;

StakeholderUsage returns PartUsage:
    'stakeholder' UsageExtensionKeyword* Usage  
;

RequirementUsageKeyword returns string:
    RequirementKeyword 
;

RequirementUsage returns RequirementUsage:
    OccurrenceUsagePrefix RequirementUsageKeyword ConstraintUsageDeclaration RequirementBody  
;

SatisfyRequirementUsage returns SatisfyRequirementUsage:
    OccurrenceUsagePrefix 'assert'? (isNegated?='not' )? 'satisfy' (ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecializationPart?  | 
    RequirementUsageKeyword UsageDeclaration?  )ValuePart? ('by' ownedRelationship+=SatisfactionSubjectMember  )? RequirementBody  
;

SatisfactionSubjectMember returns SubjectMembership:
    ownedRelatedElement+=SatisfactionParameter  
;

SatisfactionParameter returns ReferenceUsage:
    ownedRelationship+=SatisfactionFeatureValue  
;

SatisfactionFeatureValue returns FeatureValue:
    ownedRelatedElement+=SatisfactionReferenceExpression  
;

SatisfactionReferenceExpression returns FeatureReferenceExpression:
    ownedRelationship+=FeatureChainMember  
;

ConcernKeyword returns string:
    'concern' 
;

ConcernDefKeyword returns string:
    ConcernKeyword 'def'  
;

ConcernDefinition returns ConcernDefinition:
    OccurrenceDefinitionPrefix ConcernDefKeyword DefinitionDeclaration RequirementBody  
;

ConcernUsageKeyword returns string:
    ConcernKeyword 
;

ConcernUsage returns ConcernUsage:
    OccurrenceUsagePrefix ConcernUsageKeyword ConstraintUsageDeclaration RequirementBody  
;

CaseKeyword returns string:
    'case' 
;

CaseDefKeyword returns string:
    CaseKeyword 'def'  
;

CaseDefinition returns CaseDefinition:
    OccurrenceDefinitionPrefix CaseDefKeyword DefinitionDeclaration CaseBody  
;

fragment CaseBody returns Type:
    ';' | 
    '{' CaseBodyItem* (ownedRelationship+=ResultExpressionMember )? '}'  
;

fragment CaseBodyItem returns Type:
    CalculationBodyItem | 
    ownedRelationship+=SubjectMember  | 
    ownedRelationship+=ActorMember  | 
    ownedRelationship+=ObjectiveMember  
;

ObjectiveMember returns ObjectiveMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'objective' ownedRelatedElement+=ObjectiveRequirementUsage   
;

ObjectiveRequirementUsage returns RequirementUsage:
    UsageExtensionKeyword* ConstraintUsageDeclaration RequirementBody  
;

CaseUsageKeyword returns string:
    CaseKeyword 
;

CaseUsage returns CaseUsage:
    OccurrenceUsagePrefix CaseUsageKeyword ActionUsageDeclaration CaseBody  
;

AnalysisCaseKeyword returns string:
    'analysis' 
;

AnalysisCaseDefKeyword returns string:
    AnalysisCaseKeyword 'def'  
;

AnalysisCaseUsageKeyword returns string:
    AnalysisCaseKeyword 
;

AnalysisCaseDefinition returns AnalysisCaseDefinition:
    OccurrenceDefinitionPrefix AnalysisCaseDefKeyword DefinitionDeclaration CaseBody  
;

AnalysisCaseUsage returns AnalysisCaseUsage:
    OccurrenceUsagePrefix AnalysisCaseUsageKeyword ActionUsageDeclaration CaseBody  
;

VerificationCaseKeyword returns string:
    'verification' 
;

VerificationCaseDefKeyword returns string:
    VerificationCaseKeyword 'def'  
;

VerificationCaseUsageKeyword returns string:
    VerificationCaseKeyword 
;

VerificationCaseDefinition returns VerificationCaseDefinition:
    OccurrenceDefinitionPrefix VerificationCaseDefKeyword DefinitionDeclaration CaseBody  
;

VerificationCaseUsage returns VerificationCaseUsage:
    OccurrenceUsagePrefix VerificationCaseUsageKeyword ActionUsageDeclaration CaseBody  
;

RequirementVerificationMember returns RequirementVerificationMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'verify'  ownedRelatedElement+=RequirementVerificationUsage   
;

RequirementVerificationUsage returns RequirementUsage:
    ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecialization* RequirementBody  | 
    (UsageExtensionKeyword* RequirementUsageKeyword  | UsageExtensionKeyword+ )ConstraintUsageDeclaration RequirementBody  
;

UseCaseKeyword returns string:
    'use' 'case'  
;

UseCaseDefKeyword returns string:
    UseCaseKeyword 'def'  
;

UseCaseUsageKeyword returns string:
    UseCaseKeyword 
;

UseCaseDefinition returns UseCaseDefinition:
    OccurrenceDefinitionPrefix UseCaseDefKeyword DefinitionDeclaration CaseBody  
;

UseCaseUsage returns UseCaseUsage:
    OccurrenceUsagePrefix UseCaseUsageKeyword ActionUsageDeclaration CaseBody  
;

IncludeUseCaseUsage returns IncludeUseCaseUsage:
    OccurrenceUsagePrefix 'include' 
    (
        ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecializationPart?  | 
        UseCaseUsageKeyword UsageDeclaration?  
    )
    ValuePart? CaseBody  
;

ViewKeyword returns string:
    'view' 
;

ViewDefKeyword returns string:
    ViewKeyword 'def'  
;

ViewDefinition returns ViewDefinition:
    OccurrenceDefinitionPrefix ViewDefKeyword DefinitionDeclaration ViewDefinitionBody  
;

fragment ViewDefinitionBody returns ViewDefinition:
    ';' | '{' ViewDefinitionBodyItem* '}'  
;

fragment ViewDefinitionBodyItem returns ViewDefinition:
    DefinitionBodyItem | 
    ownedRelationship+=ElementFilterMember  | 
    ownedRelationship+=ViewRenderingMember  
;

ViewRenderingMember returns ViewRenderingMembership:
    // MemberPrefix 
    ('public' | 'private' | 'protected')?
    'render' ownedRelatedElement+=ViewRenderingUsage   
;

ViewRenderingUsage returns RenderingUsage:
    ownedRelationship+=OwnedReferenceSubsetting  FeatureSpecialization* UsageBody  | 
    (UsageExtensionKeyword* 'rendering'  | UsageExtensionKeyword+ )Usage  
;

ViewUsageKeyword returns string:
    ViewKeyword 
;

ViewUsage returns ViewUsage:
    OccurrenceUsagePrefix ViewUsageKeyword UsageDeclaration? ValuePart? ViewBody  
;

fragment ViewBody returns ViewUsage:
    ';' | '{' ViewBodyItem* '}'  
;

fragment ViewBodyItem returns ViewUsage:
    DefinitionBodyItem | ownedRelationship+=ElementFilterMember  | 
    ownedRelationship+=Expose  | ownedRelationship+=ViewRenderingMember  
;

// fragment ExposePrefix returns Expose:
//     visibility=ExposeVisibilityKind  
// ;

// ExposeVisibilityKind returns VisibilityKind:
//     ExposeVisibilityKind_protected
// ;
// ExposeVisibilityKind_protected returns string: 'expose' ;

Expose returns Expose:
    (MembershipExpose | NamespaceExpose )RelationshipBody  
;

MembershipExpose returns MembershipExpose:
    'expose' ImportedMembership  
;

NamespaceExpose returns NamespaceExpose:
    'expose' (ImportedNamespace | ownedRelatedElement+=FilterPackage  ) 
;

ViewpointKeyword returns string:
    'viewpoint' 
;

ViewpointDefKeyword returns string:
    ViewpointKeyword 'def'  
;

ViewpointUsageKeyword returns string:
    ViewpointKeyword 
;

ViewpointDefinition returns ViewpointDefinition:
    OccurrenceDefinitionPrefix ViewpointDefKeyword DefinitionDeclaration RequirementBody  
;

ViewpointUsage returns ViewpointUsage:
    OccurrenceUsagePrefix ViewpointUsageKeyword ConstraintUsageDeclaration RequirementBody  
;

RenderingKeyword returns string:
    'rendering' 
;

RenderingDefKeyword returns string:
    RenderingKeyword 'def'  
;

RenderingDefinition returns RenderingDefinition:
    OccurrenceDefinitionPrefix RenderingDefKeyword Definition  
;

RenderingUsageKeyword returns string:
    RenderingKeyword 
;

RenderingUsage returns RenderingUsage:
    OccurrenceUsagePrefix RenderingUsageKeyword Usage  
;

ExpressionBody returns Expression:
    CalculationBody 
;

Name returns string:
    ID | UNRESTRICTED_NAME 
;

GlobalQualification returns string:
    '$' '::'  
;

Qualification returns string:
    (Name '::' )+ 
;

QualifiedName returns string:
    GlobalQualification? Qualification? Name  
;

terminal DECIMAL_VALUE returns number:'0' ..'9' '0' ..'9' * ;
terminal EXP_VALUE returns string:DECIMAL_VALUE ('e' | 'E' )('+' | '-' )?DECIMAL_VALUE  ;
terminal ID returns string:('a' ..'z' | 'A' ..'Z' | '_' )('a' ..'z' | 'A' ..'Z' | '_' | '0' ..'9' )* ;
terminal UNRESTRICTED_NAME returns string:'\'' ('\\' ('b' | 't' | 'n' | 'f' | 'r' | '"' | "'" | '\\' ) |  !('\\' | '\'' ))*'\''  ;
terminal STRING_VALUE returns string:'"' ('\\' ('b' | 't' | 'n' | 'f' | 'r' | '"' | "'" | '\\' ) |  !('\\' | '"' ))*'"'  ;
terminal REGULAR_COMMENT returns string:'/*'  -> '*/'  ;
hidden terminal ML_NOTE returns string:'//*'  -> '*/'  ;
hidden terminal SL_NOTE returns string:'//' ( !('\n' | '\r' ) !('\n' | '\r' ))? ('\r'? '\n' )?  ;
hidden terminal WS returns string:(' ' | '\t' | '\r' | '\n' )+;


