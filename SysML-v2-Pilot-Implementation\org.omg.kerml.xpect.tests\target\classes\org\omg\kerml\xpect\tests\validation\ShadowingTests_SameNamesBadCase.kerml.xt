//* 
XPECT_SETUP org.omg.kerml.xpect.tests.parsing.KerMLParsingTest
	ResourceSet {
		ThisFile {}
		File {from ="/library/Base.kerml"}
	}
	Workspace {
		JavaProject {
			SrcFolder {
				ThisFile {}
				File {from ="/library/Base.kerml"}
			}
		}
	}
END_SETUP 
*/

package test{
	classifier A{
		classifier a1{}
	}
	classifier B specializes A{
		classifier A{
			classifier a2{}
		}
		// XPECT errors ---> "Couldn't resolve reference to Classifier 'A::a1'." at "A::a1"
		classifier b specializes A::a1{}
	}
}
